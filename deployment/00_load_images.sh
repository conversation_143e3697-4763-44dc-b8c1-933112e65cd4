#!/bin/sh

currentPath=`pwd`
echo "currentPath: $currentPath"
echo "start build images"
echo ""
 

#echo "Extract tar file"
#tar -xvzf $currentPath/cabcharge_fe.tar.gz

echo "stop all docker containers"
docker stop $(docker ps -q) --force

echo "remove all docker images"
docker rmi $(docker images -q) --force

echo "Start Loading docker images"
docker load -i $currentPath/ccp_fe*.tar 

docker images
docker ps
echo "Finish loading iamges"

#echo "Start docker compose"
##docker compose -f ./docker-compose.yml  up
#nohup docker compose up > output.log 2>&1  &
#
#
#echo "Finish docker compose"

#echo "Cabcharge portal is ready!"

