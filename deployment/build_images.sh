#!/bin/sh

currentPath=$(pwd)
buildPath=$currentPath/..
packageFolder=ccp_fe
echo "currentPath: $currentPath"
echo "start create docker images"
echo ""
 
echo "Build Path: $buildPath"

## Read version from package.json
version=$( cat "$buildPath/package.json" | grep version | awk -F: '{ print $2 }' | sed 's/[",]//g;s/ //g')
echo version: "$version"

##remove all docker images with prefix cdg/ccp-*
docker rmi "$(docker images -q ccp_fe*) --force"

##Re-create $buildPath/$packageFolder folder if it does not exist
rm -rf "$currentPath/$packageFolder"
mkdir "$currentPath/$packageFolder" 
rm "$currentPath"/cabcharge_fe*.tar.gz


##run gradlew build to create docker images
cd "$buildPath" || exit
docker build -t ccp_fe:2.03 . -f DockerfileOnPrem

cd "$currentPath"/$packageFolder/ || exit
##save all docker images -f "reference=cdg/ccp-*" to individual tar file
for image in $(docker images -q ccp_*); do
  exportTar=$(docker inspect --format='{{.RepoTags}}' "$image" | sed 's/\[//g;s/\]//g;s/,//g;s/ //g;s/^cdg\///;s/\:/_/g;').tar
  exportImageTag=$(docker inspect --format='{{.RepoTags}}' "$image" | sed 's/\[//g;s/\]//g;s/,//g;s/ //g;s/\:/:/g;')

  echo "$exportTar"
  echo "$exportImageTag"
  docker save "$image" -o "$exportTar" "$exportImageTag"
  echo "Save image $image to $exportTar"
done
echo "Finish saving images"

echo "Packing docker images into single tar"
cd "$currentPath"/ || exit 
##package all tar files into one tar file
cp "$currentPath"/0*.sh $packageFolder/.
cp "$currentPath"/cdg* $packageFolder/.
cp "$currentPath"/nginx.* $packageFolder/.
cp "$currentPath"/docker-compose/docker-compose.yml $packageFolder/.

##change shell to execute
chmod +x $packageFolder/0*.sh

tar -cvzf "$currentPath"/cabcharge_fe_"$version".tar.gz $packageFolder/

echo "path:"  "$currentPath"/$packageFolder


##remove package folder
rm  -rf "$currentPath"/$packageFolder

echo "Finish Packing images into single tar"
echo "build images successfully!"
echo "Please copy cabcharge_fe.tar into Remote server and deploy it via docker compose!"
