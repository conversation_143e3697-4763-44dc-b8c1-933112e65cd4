# auto detects a good number of processes to run
worker_processes auto;

#Provides the configuration file context in which the directives that affect connection processing are specified.
events {
    # Sets the maximum number of simultaneous connections that can be opened by a worker process.
    worker_connections 8000;
    # Tells the worker to accept multiple connections at a time
    multi_accept on;
}



http {
    proxy_headers_hash_max_size 1024;
    proxy_headers_hash_bucket_size 128;


    # what times to include
    include       /etc/nginx/mime.types;
    # what is the default one
    default_type  application/octet-stream;

    # Sets the path, format, and configuration for a buffered log write
    log_format compression '$remote_addr - $remote_user [$time_local] '
        '"$request" $status $upstream_addr '
        '"$http_referer" "$http_user_agent"';



map $http_origin $cors_allow_origin {
    default "";
    "~^https?://(cdgtaxi\.com\.sg|([a-z0-9.-]+)\.cdgtaxi\.com\.sg)$" $http_origin;
    "~^https?://([a-z0-9.-]+)\.googleapis\.com$" $http_origin;
}



    server {
        listen 443 ssl;
        server_name cabchargeasiauat.cdgtaxi.com.sg;

        # Remove the 'X-Frame-Options: DENY' header set by Spring Boot
        # proxy_hide_header Strict-Transport-Security;
        # proxy_hide_header Content-Security-Policy;
        # proxy_hide_header X-Frame-Options;
        # proxy_hide_header X-Content-Type-Options;
        # proxy_hide_header Referrer-Policy;


        # add security headers

        # Add Strict-Transport-Security header (response only)
        # add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # Add Content-Security-Policy header (response only)
#         add_header Content-Security-Policy "default-src 'none'; script-src 'self' *.cdgtaxi.com.sg *.googleapis.com 'nonce-random123' 'strict-dynamic'; style-src 'self'; img-src 'self' *.cdgtaxi.com.sg *.
# googleapis.com data:; font-src 'self'; object-src 'none'; base-uri 'self'; frame-ancestors 'none'; form-action 'self'; connect-src 'self'; upgrade-insecure-requests; block-all-mixed-content; require-trusted
# -types-for 'script';" always;



        # Add X-Frame-Options header (response only)
        #  add_header X-Frame-Options "SAMEORIGIN" always;

        # Add X-Content-Type-Options header (response only)
        # add_header X-Content-Type-Options "nosniff" always;

        # Add Referrer-Policy header (response only)
        # add_header Referrer-Policy "origin-when-cross-origin" always;


        #
        #

        ssl_certificate cdg.crt;

        # generate key by command line: openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365
        # decrypted key from key.pem to decrypted_key.pem
        ssl_certificate_key cdg.key ; 
        ssl_protocols TLSv1.2;
        ssl_ciphers 'EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH';
        ssl_prefer_server_ciphers on;

        # listen 80;
        # save logs here
        access_log /var/log/nginx/access.log compression;

        # where the root here
        root /usr/share/nginx/html;
        # what file to server as index
        index index.html index.htm;

        # Error handling
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;

        location = /404.html {
            root /usr/share/nginx/html;
        }

        location = /50x.html {
            root /usr/share/nginx/html;
        }


        location = / {
            # Handle the root URL here.
            # First attempt to serve request as file, then
            # as directory, then fall back to redirecting to index.html
            try_files $uri $uri/ /portal-ai/index.html;
        }

        location / {
          # Handle the api URL here.
           proxy_pass http://************:15000$request_uri;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme; 
        }


       location ~ ^/(api|token|cabcharge)(/|$) {


        proxy_pass http://************:15000$request_uri;

       
	# Remove the 'X-Frame-Options: DENY' header set by Spring Boot
	proxy_hide_header Strict-Transport-Security;
	proxy_hide_header Content-Security-Policy;
	proxy_hide_header X-Frame-Options;
	proxy_hide_header X-Content-Type-Options;
	proxy_hide_header Referrer-Policy;
        proxy_hide_header Access-Control-Allow-Origin;

    proxy_cookie_path / "/; Secure; HttpOnly; SameSite=Strict";

    # Forward client auth/cookies
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Authorization $http_authorization;
    proxy_set_header Cookie $http_cookie;


    # Secure CORS headers
    add_header Access-Control-Allow-Origin $cors_allow_origin always;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE" always;
    add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
    add_header Access-Control-Allow-Credentials true always;


    # Add Strict-Transport-Security header (response only)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Add Content-Security-Policy header (response only)
    add_header Content-Security-Policy "default-src 'none'; script-src 'self' *.cdgtaxi.com.sg *.googleapis.com 'nonce-random123' 'strict-dynamic'; style-src 'self'; img-src 'self' *.cdgtaxi.com.sg *.google
apis.com data:; font-src 'self'; object-src 'none'; base-uri 'self'; frame-ancestors 'none'; form-action 'self'; connect-src 'self'; upgrade-insecure-requests; block-all-mixed-content; require-trusted-types
-for 'script';" always;



    # Add X-Frame-Options header (response only)
    add_header X-Frame-Options "SAMEORIGIN" always;

    # Add X-Content-Type-Options header (response only)
    add_header X-Content-Type-Options "nosniff" always;

    # Add Referrer-Policy header (response only)
    add_header Referrer-Policy "origin-when-cross-origin" always;

     #

      }

        # Media: images, icons, video, audio, HTC
        location ~* \.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm|htc)$ {
          expires 1M;
          access_log off;
          add_header Cache-Control "public";
        }

        # Javascript and CSS files
        location ~* \.(?:css|js)$ {
            try_files $uri =404;
            expires 1y;
            access_log off;
            add_header Cache-Control "public";
        }

        # Any route containing a file extension (e.g. /devicesfile.js)
        location ~ ^.+\..+$ {
            try_files $uri =404;
        }

        
          
    }
}
