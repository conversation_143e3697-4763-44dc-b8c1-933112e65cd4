 
const trustedTypes = (window as any).trustedTypes;



import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic'; 

import { AppModule } from './app/app.module';
import { environment } from './environments/environment';


if (environment.production) {
  enableProdMode();
}
 

let trustedTypesPolicy;
if (trustedTypes) {
  trustedTypesPolicy = trustedTypes.createPolicy('myAppPolicy', {
    createHTML: (input: string) => input,
    createScriptURL: (input: string) => input,
  });
}

platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.error(err));
