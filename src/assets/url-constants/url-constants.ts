export class UrlConstants {
    public static WSO2 ='';
    public static WSO2Apikey ='';
    public static WSO2OS ='';

    public static GET_AUTH = '/token';

    public static LOGIN = '/api/auth/signin';
    public static CHANGE_PASSWORD = '/api/auth/changepassword';
    public static PDF_DOWNLOAD = '/api/account/downloadInvoice';
    public static GET_INVOICE_DETAILS = '/api/account/getInvoiceDetails';
    public static FORGOT_PASSWORD = '/api/auth/forgotpassword?accessId=';
    public static USER_ROLE = '/api/auth/menurole?role=';
    // public static USER_ROLE = '/api/auth/menurole?';
    public static ADD_TO_MAILINGLIST = '/api/module/addtomailinglist';
    public static DELETE_FROM_MAILINGLIST = '/api/module/deletefrommailinglist';
    public static RETRIVE_ALL_EMAIL = '/api/module/retrieveemailsettings ';
    public static RETRIVE_ALL_EMAIL_BY_NAME = '/api/module/retrieveemailsettingsbyname?mailingListName=';
    public static ADD_PUBLIC_HOLIDAY = '/api/module/addpublicholiday';
    public static UPDATE_PUBLIC_HOLIDAY = '/api/module/updatepublicholiday';
    public static DELETE_PUBLIC_HOLIDAY = '/api/module/deletepublicholiday?dateTobeDeleted=';
    public static GET_HOLIDAY_BY_YR = '/api/module/retrievepublicholidaybyyear?year=';
    public static GET_HOLIDAY_BY_DESC = '/api/module/retrievepublicholidaybydesc?description=';
    public static REQUESTEVOUCHER ='/cabcharge/onlineapp/requestEVoucherData';
    public static GET_ACL_MATRIX = '/api/module/getGlobalACL';
    public static ASSIGNCARD_SUBMIT='/cabcharge/onlineapp/assigncard';
    public static LOAD_MY_SETTINGS = '/api/account/loadMySettings';
    public static CHECKLIMIT_ACCOUNT='/cabcharge/onlineapp/viewCheckCreditLimit'
    public static DELETE_MY_SETTINGS = '/api/account/deleteMySettings';
    public static GET_VIEWTRANS_ECARD ='/api/account/getTransactioByCardEvoucher';
    public static GET_PRODUCTTYPE ='/api/account/getProductTypeValues';
    public static GET_CORPSETUPPRODUCT='/api/account/getDownloadCardholderListProducts';
    public static GET_CARDLIST ='/api/account/getCardList';
    public static SEARCH_CARDLIST ='/api/account/searchCards ';
    public static SUPPLY_SUBMIT='/cabcharge/onlineapp/submitSupplementaryCardData'
    public static GET_DOWNLOAD ='/api/account/downloadTransactioByCardEvoucher';
    public static GET_ERECIEPT ='/api/account/downloadTransactionByCardEvoucherEreciept';
    public static GET_TRANSACTIONSUMMARY='/api/account/downloadTransactionSummaryByInvoiceNo';
    public static GET_CARDDETAILSUSAGE ='/api/account/getCardsByProductTypes'
    public static GET_CARDDETAILSASSIGNCARD='/cabcharge/onlineapp/getProductDetails?cardNumber='
    public static GET_DOWNLOADBYACCOUNTPRODUCT='/api/account/getDownloadCardholderListProducts';
    public static GET_DOWNLOADBYACCDEPT='/api/account/getDownloadCardHolderDivAndDeptList';
    public static ENQUIRYFORM='/cabcharge/onlineapp/submitEnquiryPage'
    public static GET_SUPPLEMENTARY='/cabcharge/onlineapp/fetchMasterListDataDropDown?accessId=';
    // public static GET_LOSTCARD='/cabcharge/onlineapp/account/reportLostCard';
    public static GET_LOSTCARD='/cabcharge/onlineapp/reportLostCard';
    // public static GET_FAULTYCARD='/cabcharge/onlineapp/account/reportFaultyCard';
    public static GET_FAULTYCARD='/cabcharge/onlineapp/reportFaultyCard';
    public static LOAD_JOBTYPE='/api/policy/loadCreatePolicy';
    public static SUBMITSETUPRULES='/api/policy/submitCorporatePolicy';
    public static GET_CORPPOLICY_DIV_DEPT='/api/policy/getCorpPolicyDivAndDeptList';
    // public static GET_ACL_MATRIX_INFO='/api/module/fetchAclMatrixInfo?accessId=';
    public static GET_CUSTOM_ACL = '/api/module/getCustomACL?accessid=';
    public static UPDATE_ACL_MATRIX = '/api/module/addGlobalACL';
    public static UPDATE_CUSTOM_ACL = '/api/module/addCustomACL';
    public static GET_BY_ACCESS_ID = '/api/module/loadbyAccessId?accessId=';
    public static RETRIEVE_OTP_BY_EMAIL = '/api/module/retrieveotpbyemail?emailId=';
    // public static GET_BY_ACCESS_ID = '/superadmin/1.0/loadbyAccessId?accessId=';
    public static DELETE_CONTACT_PERSON = '/api/module/removeContactPersonDetails?contactPersonId=';
    public static DELETE_CARD_DETAILS = '/api/module/removeCardDetails?accessId=';
    public static DELETE_ACCESS_ID = '/api/module/deleteAccessId?accessId=';
    public static DEACTIVATE_ACCESS_ID = '/api/module/deactivateAccessId?accessId=';
    public static ACTIVATE_ACCESS_ID = '/api/module/activateAccessId?accessId=';
    public static REGISTER_CORP_ADMIN = '/api/cpuserreg/registerCorpAdmin';
    public static REGISTER_PERSONAL_CARDHOLDER = '/api/cpuserreg/registerCardHolder';
    public static VERIFY_CORP_ADMIN = '/api/cpuserreg/verifyCorpAdmin';
    public static VERIFY_PERSONAL_CARDHOLDER = '/api/cpuserreg/verifyCardHolder';
    public static VALIDATE_CORP_ADMIN_OTP = '/api/cpuserreg/validateCorpAdminOtp';
    public static VALIDATE_PERSONAL_CARDHOLDER_OTP = '/api/cpuserreg/validateCardHolderOtp';
    public static REGISTER_CONFIRM = '/api/cpuserreg/validateCorpAdminConfirmationLink';
    public static RESEND_EMAIL_WITH_OTP_PERSONAL_CARDHOLDER = '/api/cpuserreg/resendEmailWithOtpCardHolder';
    public static RESEND_EMAIL_WITH_OTP_CORP_ADMIN = '/api/cpuserreg/resendEmailWithOtpCorpAdmin';
    public static RESEND_EMAIL_WITH_OTP_PERSONAL_CARDHOLDER_LINK = '/api/cpuserreg/resendEmailWithOtpCardHolderFromLink';
    public static RESEND_EMAIL_WITH_OTP_CORP_ADMIN_LINK = '/api/cpuserreg/resendEmailWithOtpCorpAdminFromLink';
    public static GET_STATEMENT_OF_ACCOUNTS = '/api/account/statementofaccount';
    public static GET_DIV_DEPT_FOR_MASTER_STATEMENTS ='/api/account/getDivDeptForMaster';
    public static GET_USEAGE_REPORT = '/api/account/viewUsageReport';
    public static GET_USEAGE_REPORT_MONTHLY ='/api/account/viewUsageReportMonth';
    public static USEAGE_REPORT_SEARCH ='/api/account/viewUsageReportSearch';
    public static USEAGE_REPORT_MONTH_DIV_DEPT ='/api/account/viewUsageReportMonthDivDept';
    public static DOWNLOAD_TRIP_REPORT_BY_INVOICE ='/api/account/dowloadTripReportByUsingInvoiceNo';
    public static DOWNLOAD_TRIP_REPORT_BY_TRIPDATE ='/api/account/dowloadTripReportByUsingTripDate';
    public static GET_DIV_DEPTLIST ='/api/account/getDivAndDeptList';
    public static GET_TRIP_REPORT_PRODUCTS ='/api/account/getTripReportProducts';
    //have to check the product and card api 8/7/21
    public static FETCH_PRODUCTS_CARD ='/api/account/getAvailableProductTypes';
    public static GET_CARDS_BY_PRODUCT_TYPE ='/api/account/getCardsByProductTypes';
    public static CARD_STATUS_REPORTS ='/api/account/downloadViewCardSattusReport';
    public static DOWNLOAD_CARD_HOLDERLIST_BYCARD ='/api/account/downloadCardHolderListByCard';
    public static VIEW_PROFILE ='/cabcharge/onlineapp/viewUserProfileData';
    public static UPDATE_PROFILE_CROPADMIN ='/cabcharge/onlineapp/updateCorpAdminUserProfileData';
    public static UPDATE_PROFILE_INDIVIDUALCARDHOLDER ='/cabcharge/onlineapp/updateCICHUserProfileData';
    public static UPDATE_PROFILE_PERSCARDHOLDER= '/cabcharge/onlineapp/updateAPPUserProfileData';
    public static UPDATE_PROFILE_PERSCARDHOLDER_SUBAPP='/cabcharge/onlineapp/updateSAPPUserProfileData';
    public static SUBMIT_CORPORATE_POLICY ='/api/policy/submitCorporatePolicy';
    public static SAVE_CORPORATE_POLICY ='/api/policy/saveCorporatePolicyAfterConfirmation';
    public static LOAD_EXISTING_POLICY ='/api/policy/loadExistingPolicy';
    public static DELETE_SETUP_POLICIES='/api/policy/endCorporatePolicy';
    public static UPDATE_SETUP_POLICIES='/api/policy/updateCorporatePolicy';
    public static GET_ONLINE_APP_CARDVIEW='/cabcharge/onlineapp/onlineApplicationCardView';
    public static GET_ONLINE_APP_CARD_SUBMIT='/cabcharge/onlineapp/onlineApplicationCardSubmit';
    public static GET_RETURN_VIEW_CARD='/cabcharge/onlineapp/viewCard';
    public static GET_RETURN_CARD_SUBMIT='/cabcharge/onlineapp/returnCard';
    public static GET_INVOICE_NO='/api/account/getInvoiceNumberList'
    public static GET_CARD_NO='/api/account/getCardNumberList'
    public static DOWNLOAD_ERECEIPT='/api/account/downloadEreceipt';
    public static VIEW_FEEDBACK_FORM = '/cabcharge/onlineapp/viewFeedbackForm';
    public static FILE_UPLOAD = '/cabcharge/onlineapp/FileUpload';
    public static ERECIEPT_REQUEST_PRODUCT = '/api/account/getEreceiptRequestProducts';
    public static CICH_CUSTOMER_DROPDOWN = '/api/account/cichcustomerdropdown';
}
export class RoleConstants {
    public static ROLE_SUPERADMINUSER = 'ROLE_SUPERADMINUSER'    //Super Admin user
    public static ROLE_MASTERUSER = 'ROLE_MASTERUSER'			//Master user
    public static ROLE_CORPADMIN = 'ROLE_CORPADMIN' 			//Corporate Admin
    public static ROLE_CORPCARDHOLDER = 'ROLE_CORPCARDHOLDER' 	//Corporate Individual Card Holder
    public static ROLE_PERSCARDHOLDER = 'ROLE_PERSCARDHOLDER'		//Personal Card Holder
    public static ROLE_SUBAPPLICANT = 'ROLE_SUBAPPLICANT'
}
