
// Color Variables
//
// Color Variables should follow the `$app-component-state-size` formula for
// consistent naming. Ex: $app-nav-link-disabled
// app stands for app  = Application
// For primary, use prime
//For secondary, use accent
//For teratiary, use mild
//For fourth ,use warn


//color
$white:    #fff !default;
$gray: #808080!default;
$lightGrey:#4d4d4d !default;
$blue:    #007bff !default;
$indigo:  #6610f2 !default;
$purple:  #6f42c1 !default;
$pink:    #e83e8c !default;
$red:     #ff0000 !default;
$orange:  #fd7e14 !default;
$yellow:  #ffc107 !default;
$green:   #28a745 !default;
$teal:    #20c997 !default;
$cyan:    #17a2b8 !default;
$warn-orange: #ea3d13 !default;
$black: #000;



//COLORS IN APPLICATION
$prime:#006BA8;
$aux:#f2f2f2;
$hue:#DA6C2A;
$mild:#E9F7FF;
$accent:#fff;
$warn: $red;
//MAIN COLORS
$cdgc-prime:  $prime!default;
$cdgc-accent: $accent !default;
$cdgc-mild:$mild !default;
$cdgc-aux: $aux !default;
$cdgc-hue: $hue !default;
$cdgc-warn: $warn !default;

$cdgc-prime-100:lighten($cdgc-prime,40%)!default; //#007ecc
$cdgc-prime-200:lighten($cdgc-prime,60%)!default; //#33b1ff
$cdgc-prime-300:lighten($cdgc-prime,80%)!default; //#99d8ff
$cdgc-prime-400:lighten($cdgc-prime,90%)!default; //#ccebff
$cdgc-prime-500:$cdgc-prime!default;

$cdgc-primes: () !default;
$cdgc-primes: map-merge((
  "100": $cdgc-prime-100,
  "200": $cdgc-prime-200,
  "300": $cdgc-prime-300,
  "400": $cdgc-prime-400,
), $cdgc-primes);


$cdgc-mild-100:lighten($cdgc-mild,15%)!default; //#002e4d
$cdgc-mild-200:lighten($cdgc-mild,20%)!default;//#003d66
$cdgc-mild-300:lighten($cdgc-mild,25%)!default;//#004d80
$cdgc-mild-400:lighten($cdgc-mild,35%)!default;//#006bb3
$cdgc-mild-450:lighten($cdgc-mild,45%)!default;//#008ae6
$cdgc-mild-500:lighten($cdgc-mild,50%)!default;//#0099ff
$cdgc-mild-600:darken($cdgc-mild,30%)!default; //#005c99
$cdgc-mild-700:darken($cdgc-mild, 0%)!default; //#000000
$cdgc-mild-800:lighten($cdgc-mild, 55%)!default;

$cdgc-milds: () !default;
$cdgc-milds: map-merge((
  "100": $cdgc-mild-100,
  "200": $cdgc-mild-200,
  "300": $cdgc-mild-300,
  "400": $cdgc-mild-400,
), $cdgc-milds);


//FONT-COLOR
$cdgc-font-prime: $cdgc-prime !default; //#0077c0;
$cdgc-font-accent: $cdgc-accent !default;//#fff
$cdgc-font-mild: $cdgc-mild !default;//#ccebff
$cdgc-font-warn: $cdgc-warn !default;//#002e4d
$cdgc-font-dark: lighten($black,40%);

//MAIN BACKGROUND
$cdgc-bg-prime:  $cdgc-prime !default; //#ccebff
$cdgc-bg-mild:  $cdgc-mild !default; //#ccebff
$cdgc-bg-accent: $cdgc-accent !default; //#fff
$cdgc-bg-hue: $cdgc-hue !default; //#fff

//BORDER-COLOR
$cdgc-border-prime: $cdgc-prime !default;
$cdgc-border-aux: darken($cdgc-aux, 8%)!default;
$cdgc-border-mild: $cdgc-mild-700 !default;
$cdgc-border-hue: $cdgc-hue !default; //#fff

$success:       $green!default;
$info:          $cyan !default;
$warning:       $warn-orange !default;
$danger:        $red !default;

//SEARCH COLOR
$search-bg-color: $cdgc-prime-300!default;
$search-border-color:$cdgc-prime-200 !default;

//box shadow color
$box-shadow: lighten($gray, 36.47) !default; //#ddd

//DROP DOWN HOVER
// $select-hover: lighten(desaturate($cdgc-prime, 20.13), 40.00); // #87bded

$select-hover: $cdgc-prime;

//Table
$cdgc-table-row-color:lighten($gray,45%);//#f3f3f3
//Charts

$cdgc-chart-deployment-color:desaturate($blue,50%); //#407dbf





