// Font Variables
//
// Font Variables should follow the `$app-component-state-size` formula for
// consistent naming. Ex: $app-modal-content-box-shadow-xs
// msc stands for app  = APPLICATION, 
// For primary, use prime
//For secondary, use accent
//For teratiary, use mild
//

//Font
// $cdgsz-font-family:'Source Sans Pro Regular';
// $cdgsz-font-family:'Roboto';
$cdgsz-font-family:'SFNS Display Regular';
$cdgsz-font-family-sans: 'Source Sans Pro Regular';
$cdgsz-font-family-prime: $cdgsz-font-family-sans !default;


$cdgsz-font-size-prime:              1rem !default; //1 rem =16px;
$cdgsz-font-size-xxxl:              ($cdgsz-font-size-prime * 3.75) !default;//60px
$cdgsz-font-size-xxl:              ($cdgsz-font-size-prime * 2.5) !default;//40px
$cdgsz-font-size-xl:                ($cdgsz-font-size-prime * 1.375) !default;//22px
$cdgsz-font-size-lg:                ($cdgsz-font-size-prime * 1.25) !default;//20px
$cdgsz-font-size-md:                ($cdgsz-font-size-prime * 1.125) !default;//18px
$cdgsz-font-size-sm:                ($cdgsz-font-size-prime * .875) !default;//14px
$cdgsz-font-size-xs:                ($cdgsz-font-size-prime * .75) !default;//12px
$cdgsz-font-size-xxs:               ($cdgsz-font-size-prime * .625) !default;//10px
$cdgsz-font-size-xxxs:               ($cdgsz-font-size-prime * .5) !default;//8px

$cdgsz-font-weight-light:           100 !default;
$cdgsz-font-weight-normal:          400 !default;
$cdgsz-font-weight-intermediate:    500 !default;
$cdgsz-font-weight-bold:            600 !default;

$cdgsz-line-height-diff : 0.5;
$cdgsz-line-height-prime:           1.5 !default;
$cdgsz-line-height-lg:              calc(#{$cdgsz-line-height-prime} + #{$cdgsz-line-height-diff}) !default;
$cdgsz-line-height-sm:              calc(#{$cdgsz-line-height-prime} - #{$cdgsz-line-height-diff}) !default;

$cdgsz-height-prime : 1rem !default;
$cdgsz-height-lg : ($cdgsz-height-prime * 1.875) !default;
$cdgsz-height-sm : ($cdgsz-height-prime * .5) !default;
$cdgsz-height-xs : ($cdgsz-height-prime * .25) !default;
