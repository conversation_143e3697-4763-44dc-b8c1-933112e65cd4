@import 'variables.scss';
@import 'colors.scss';
@import 'sizing.scss';

html,
body {
    height: 100%;
    margin: 0;
    font-family: $cdgsz-font-family;
    background-color: $cdgc-background-color;
    font-size: $cdgsz-font-size-prime;
}

.container {
    height: 100vh;
}

.main-container {
    position: absolute;
    top: 84px;
    left: 300px; //chnage by navani for sidenav
    right: 0;
    bottom: 0;
    padding: 20px;
    background-color: #f7f7f7;
    // background-color: $cdgc-background-color;
    overflow-y: scroll; //added on 3-10-2021
}

.main-icon-container {
    position: absolute;
    top: 84px;
    left: 71px;
    right: 0;
    bottom: 0;
    padding: 20px;
    background-color: #f7f7f7;
    // background-color: $cdgc-background-color;
    overflow-y: scroll; //added on 3-10-2021
}

.main {
    height: calc(100vh - 110px) !important;
}

.main-content {
    padding-left: 80px !important;
    padding-right: 80px !important;
    padding-top: 25px !important;
}

.product-inp {
    width: 320px !important;

}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px white inset !important
}

//mdc_update
.mat-mdc-form-field-error {
    color: $danger !important;
    font-weight: $cdgsz-font-weight-bold !important;
    display: contents; //error_field_sizing
}

.mat-error {
    color: $danger !important;
    font-weight: $cdgsz-font-weight-bold !important;
}


//mdc_update
.mat-mdc-raised-button {
    background: $cdgc-prime !important;
    color: $cdgc-accent !important ;
    border-radius: 15px !important;
    font-size: $cdgsz-font-size-lg !important;
    padding-right: 24px !important;
    padding-left: 24px !important;
    letter-spacing: normal;
}

//mdc_update
.mat-mdc-mini-fab.mat-accent {
    background-color: $cdgc-accent !important;
    border: 1px solid $cdgc-prime !important;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/

//need to check
.mat-mdc-mini-fab .mat-button-wrapper {
    padding: 0px 0px 5px 0px !important;
    display: inline-block;
    line-height: 24px;
}

.inp {
    width: 100% !important;
}

.txtarea {
    width: 100% !important;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
//outline classname updated
.mat-form-field-appearance-outline .mdc-notched-outline {
    border: 2px solid $cdgc-border-aux !important;
    border-radius: 10px !important;
    top: 0px !important;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
//outline classname updated
.mat-form-field-appearance-outline--focused .mdc-notched-outline {
    border: 2px solid $cdgc-border-prime !important;
    border-radius: 10px !important;
    top: 0px !important;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
//outline classname updated
.mat-form-field-appearance-outline:hover .mdc-notched-outline {
    border: 2px solid $cdgc-border-prime !important;
    border-radius: 10px !important;
    top: 0px !important;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
//outline classname updated
.mat-form-field-appearance-outline .mdc-notched-outline__leading,
.mat-form-field-appearance-outline .mdc-notched-outline__notch,
.mat-form-field-appearance-outline .mdc-notched-outline__trailing {
    border: 1px solid transparent !important;
    min-width: 5px;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
//infix classname updated
.mat-form-field-appearance-outline .mat-mdc-form-field-text-infix {
    padding: 0px !important;
}

/* default and hover color */
// .mat-form-field-appearance-outline .mat-form-field-outline-thick {
//     color: rgba(212, 63, 63, 0.87);
// }
//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
//outline classname updated
.mat-form-field-appearance-outline.mat-focused .mdc-notched-outline--notched {
    color: transparent;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
//outline classname updated
.mat-form-field-appearance-outline:hover .mdc-notched-outline--notched {
    color: transparent;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .mat-form-field-appearance-outline .mdc-notched-outline__trailing {
    border-radius: 0px !important;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .mat-form-field-appearance-outline .mdc-notched-outline__leading {
    border-radius: 0px !important;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .mat-form-field-appearance-outline.mat-focused .mdc-notched-outline__trailing {
    border-radius: 0px !important;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .mat-form-field-appearance-outline.mat-focused .mdc-notched-outline__leading {
    border-radius: 0px !important;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
.mat-form-field-appearance-outline .mat-mdc-form-field-flex {
    background-color: white;
    border: transparent;
    border-radius: 10px;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .mat-form-field-appearance-outline .mat-mdc-form-field-disabled {  //need to check 
    background-color: rgb(245, 238, 238) !important;
    border: transparent;
    border-radius: 10px;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
//need_Change
.inp.mat-form-field-appearance-outline .mat-form-field-prefix,
.mat-form-field-appearance-outline .mat-form-field-suffix {
    top: 7px !important;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
//need_change
.mat-form-field-appearance-outline .mat-form-field-prefix,
.mat-form-field-appearance-outline .mat-form-field-suffix {
    // top: unset !important;
    // bottom: 63px !important;
    margin-right: 10px;
}

.asterisk {
    color: $red;
}

/*Toogle Style*/
/* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,
.mat-button-toggle-group-appearance-standard {
    border-radius: 5px !important;
    border: 1px solid transparent !important;

}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
.mat-button-toggle.mat-button-toggle-group-checked.mat-button-toggle-appearance-standard {
    border-radius: 5px !important;
    background-color: $cdgc-hue !important;
    color: $cdgc-font-accent !important;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
.mat-button-toggle-appearance-standard .mdc-button-toggle__label {
    line-height: 30px !important;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
.mat-button-toggle-appearance-standard {
    color: $lightGrey !important;
    border-radius: 5px !important;
    border: 1px solid $cdgc-border-hue;
    // background: white;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
::ng-deep .mat-button-toggle-group-appearance-standard .mat-button-toggle+.mat-button-toggle {
    border-radius: 5px !important;
    border: 1px solid $cdgc-border-hue !important;
}

// ::ng-deep .mat-button-toggle-group-appearance-standard .mat-button-toggle + .mat-button-toggle {
//     border-radius: 10px;
//     border: 1px solid $cdgc-border-hue;
// }
// ::ng-deep.mat-button-toggle-appearance-standard {
//     color: #4d4d4d;
//     border: 1px solid red;
// }

/* mat chip*/
//mdc_update
 .mat-mdc-chip {
    background-color: $cdgc-prime !important;
    color: $cdgc-accent !important;
    border-radius: 4px;
    min-height: 22px !important;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
.mat-mdc-select-arrow {
    margin-top: 10px !important;
    color: $lightGrey !important;
}

/** Word Break*/
.word-break {
    word-break: break-all !important;
}

/* Customised Scroll Bar*/
::-webkit-scrollbar {
    overflow-y: scroll !important;
    width: 5px !important;
}

::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px $cdgc-accent !important;
    border-radius: 10px !important;
}

::-webkit-scrollbar-thumb {
    background-color: $gray !important;
    border-radius: 10px !important;
}

::-webkit-scrollbar-thumb:hover {
    background-color: $lightGrey !important;
}

::ng-deep.customize-mat-card {
    width: 379px;
    height: 120px;
    background: $cdgc-bg-mild !important ;
    border: 1px solid $cdgc-bg-prime !important;
    box-sizing: border-box;
    border-radius: 2px;
    padding: 15px !important; //changed by navani - Admin dasboard
}

.custom-padding-left {
    padding-left: 65px !important;
}

@media screen and (max-width: 959px) {
    .main-content {
        padding-left: 0px !important;
        padding-right: 0px !important;
        padding-top: 0px !important;
    }
}

//FOR SMALL SCREEN
@media screen and (max-width: 500px) {
    .customize-mat-card {
        width: 279px;
    }
}

@media screen and (max-width: 600px) {
    .main-container {
        top: $cdgsz-topnav-height-xs !important; //change required
    }

    //mdc_update
    /* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
    .mat-button-toggle-appearance-standard .mdc-button-toggle__label {
        padding: 0px 5px !important;
    }

    .custom-padding-left {
        padding-left: 20px !important;
    }

    .product-inp {
        width: 320px !important;

    }

    .main-icon-container {
        left: 0px !important;
    }
}

@media screen and (max-width: 960px) {
    .main-icon-container {
        left: 0px !important;
    }
}

.mb-20 {
    margin-bottom: 20px;
}

.mb-10 {
    margin-bottom: 10px;
}

.mb-5 {
    margin-bottom: 5px;
}

.ml-20 {
    margin-left: 20px;
}

.mr-20 {
    margin-right: 20px;
}

.ml-10 {
    margin-left: 10px;
}

.mr-10 {
    margin-right: 10px;
}

.mt-20 {
    margin-top: 20px;
}

.mt-15 {
    margin-top: 15px;
}

.mt-10 {
    margin-top: 10px;
}

.mt-5 {
    margin-top: 5px;
}

.pull-right {
    float: right !important;
}

.pull-left {
    float: left !important;
}

.visible-md {
    display: none !important;
}

.visible-sm {
    display: none !important;
}

.visible-xs {
    display: none !important;
}

.hidden-sm {
    display: block !important;
}

@media screen and (max-width: 992px) {
    .visible-md {
        display: block !important;
    }

    .hidden-md {
        display: none !important;
    }
}

@media screen and (max-width: 768px) {
    .visible-sm {
        display: block !important;
    }

    .hidden-sm {
        display: none !important;
    }
}

@media screen and (max-width: 600px) {
    .visible-xs {
        display: block !important;
    }

    .hidden-xs {
        display: none !important;
    }
}

.hidden-ipad {
    display: block !important;
}

.visible-ipad {
    display: none !important;
}

@media screen and (min-width: 600px) and (max-width: 992px) {
    .visible-ipad {
        display: block !important;
    }

    .hidden-ipad {
        display: none !important;
    }
}

.align-content-center {
    display: flex;
    /* or inline-flex */
    align-items: center;
    justify-content: center;
}

.align-center {
    display: flex;
    /* or inline-flex */
    align-items: center;
}

.main-heading {
    font-size: $cdgsz-font-size-xl;
    font-weight: bold;
    margin: 0px 0px 10px 0px;
}

.main-sub-heading {
    font-size: $cdgsz-font-size-md;
    font-weight: bolder;
    margin: 10px 0px;
}

.heading {
    font-size: $cdgsz-font-size-xl !important;
    font-weight: $cdgsz-font-weight-bold !important;
    margin: 5px 0px !important;
}

.sub-heading {
    font-size: $cdgsz-font-size-sm;
    font-weight: bold;
    margin: 5px 0px;
}

.sub-title {
    font-size: $cdgsz-font-size-xs;
    margin: 0px 0px;
}

.mini-label {
    font-size: $cdgsz-font-size-xs;
    font-weight: 100;
    margin: 0px 0px;
}

.rotate-90 {
    transform: rotate(00deg);
}

.rotate-180 {
    transform: rotate(180deg);
}

.rotate-270 {
    transform: rotate(270deg);
}

.cursor-pointer {
    cursor: pointer;
}

//mdc_update
/* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
::ng-deep .mat-mdc-select-arrow {
    border-left: 0px solid transparent !important;
    border-top: 0px solid !important;
    border-style: solid !important;
    border-right: 2px solid !important;
    //border-width: 0 2px 2px 0 !important;
    border-top-width: 0 !important;
    border-right-width: 2px !important;
    border-bottom-width: 2px !important;
    border-left-width: 0 !important;
    content: "";
    display: inline-block !important;
    padding: 3px !important;
    transform: rotate(45deg) !important;
    vertical-align: middle !important;
    // color: red !important;
}

::ng-deep .mat-sort-header-arrow {
    transform: none !important;
    opacity: 1 !important;
    color: $cdgc-bg-accent;
    content: "";
}

.mat-mdc-text-field-wrapper {
    padding-left: 0 !important;
    padding-right: 0 !important;
}
//changed by navani
:host ::ng-deep .mat-mdc-select-arrow {
    height: 1px !important;
    width: 1px !important;
    margin-right: 15px !important;
    margin-top:0px !important; // change by navani
    color: #000000a3 !important;
    
  }

::ng-deep .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix{
    padding-left: 10px !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }

  ::ng-deep .mat-mdc-select-arrow svg {
    display: none !important;
  }
  ::ng-deep .mat-mdc-form-field-infix{
    min-height: 10px !important;
  }

  ::ng-deep .mat-form-field-appearance-outline .mdc-text-field--disabled .mat-mdc-form-field-flex {
    // background-color:pink !important;
    background-color: #E0E0E0 !important;
    border: transparent !important;
    border-radius: 10px !important;
}

:host ::ng-deep .mat-mdc-chip.mdc-evolution-chip .--with-trailing-action .mat-mdc-chip-action-label{
    font-size: 12px !important;
    color: #f7f7f7 !important;
}

:host ::ng-deep  .mat-mdc-dialog-content{
    padding: 0px 15px  !important;
    align-self: center !important;
}

.mat-mdc-header-row{
    background-color: #006BA8 !important;
}