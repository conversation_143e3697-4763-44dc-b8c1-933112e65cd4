@use '@angular/material' as mat;
@use '@ng-matero/extensions' as mtx; //for migration
@import 'colors.scss';
@import 'variables.scss';
@import 'sizing.scss';

@font-face {
  font-family: $cdgsz-font-family;
  src: url("../assets/fonts/Source_Sans_Pro/SourceSansPro-Regular.ttf");
}

// @import '@angular/material/prebuilt-themes/indigo-pink.css'; //for migration
// @import '@angular/material/theming';
//for migration


// mat.define for migration

$custom-typography: mat.define-typography-config($font-family: $cdgsz-font-family,
    $headline-1: mat.define-typography-level(32px, 48px, 700),
    $body-2: mat.define-typography-level(16px, 24px, 500),
  );
// @include angular-material-typography($custom-typography);
@include mat.typography-hierarchy($custom-typography); // typography error solution
// @include mat.all-component-typographies($custom-typography);
@include mat.core();


$mat-custom: (
  50: $cdgc-prime,
  100: $cdgc-accent,
  700: $cdgc-warn,

  contrast: (50: $cdgc-accent,
    100: $cdgc-prime,
    700: $cdgc-prime-400,
  )
);
$mat-dark-custom: (
  50:$hue,
  100:$yellow,
  700:$red,
  contrast: (50: $hue,
    100: $yellow,
    700: $cdgc-prime-400,
  )
);
$custom-primary: mat.define-palette($mat-custom, 50);
$custom-accent: mat.define-palette($mat-custom, 100);
$custom-warn: mat.define-palette($mat-custom, 700);

//for version update
$custom-theme: mat.define-light-theme((color:(primary:$custom-primary,
        accent:$custom-accent,
        warn:$custom-warn),
      typography: $custom-typography, // for version update.
      density:0));

// @include angular-material-theme($custom-theme);
@include mat.all-component-themes($custom-theme); //mat v15 changes
// @include mat.typography-heirarchy($custom-theme);
@include mat.all-legacy-component-themes($custom-theme);
@include mtx.all-component-themes($custom-theme);




// for dark theme
$custom-primary-dark: mat.define-palette($mat-dark-custom, 50);
$custom-accent-dark: mat.define-palette($mat-dark-custom, 100);
$custom-warn-dark: mat.define-palette($mat-custom, 700);

$custom-theme-dark: mat.define-dark-theme((color:(primary: $custom-primary-dark,
        accent: $custom-accent-dark, warn: $custom-warn-dark)));

.dark-theme-mode {
  // @include angular-material-theme($custom-theme-dark);
  @include mat.all-component-colors($custom-theme-dark);
  // @include mtx.all-component-themes($custom-theme-dark);

  // @include mat.all-component-themes($custom-theme-dark);

}