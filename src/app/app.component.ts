import { Component, HostListener, Compiler } from '@angular/core';

import { NotificationService } from './shared/services/notification.service';
import { CdgSharedService } from './shared/services/cdg-shared.service'
import { NavigationEnd, Router } from '@angular/router';
import { SessionTimeoutService } from './shared/services/session-timeout.service';
import { filter } from 'rxjs/operators';
import { LocalStorageService } from './shared/services/local-storage.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent {
  title = 'cdg-cabcharge';
 
  constructor(public localStorage: LocalStorageService,public sessionService: SessionTimeoutService,private _compiler: Compiler, private notifyService: NotificationService, private cdgService: CdgSharedService, public router: Router) {

   }
  @HostListener('window:popstate', ['$event'])
  onPopState(event: any) {
    location.reload()
  }
  ngOnInit(): void {
    console.log(window.location.protocol,window.location.href,environment.production)
    if(environment.production){
     if(window.location.protocol === 'http:'){
       console.log(window.location.href.replace('http','https'))
       window.location.href=window.location.href.replace('http','https')
     }
    }
    console.log(window.location.protocol,window.location.href,environment.production)
    this.router.events.pipe(filter((rs:any):rs is NavigationEnd => rs instanceof NavigationEnd)).subscribe(event=>{
      if(event.id === 1 && event.url === event.urlAfterRedirects && this.localStorage.localStorageGet("isLoginSuccessfull") && this.localStorage.localStorageGet("sessionExpiryToken")){
        this.sessionService.stopTimer();
        this.sessionService.startTimer(this.localStorage.localStorageGet('remainingSessionTime'))
      }
    })
  }
}
