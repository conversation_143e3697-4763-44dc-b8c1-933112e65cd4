import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LandingComponent } from './landing/landing.component';
import { LayoutComponent } from './layout/layout.component';
import { LoginComponent } from './login/login.component';
import { AccessControlComponent } from './modules/access-control/access-control.component';
import { AccessIdComponent } from './modules/access-id/access-id.component';
import { EmailConfigComponent } from './modules/email-config/email-config.component';

// import { EmailConfigurationComponent } from './modules/email-configuration/email-configuration.component';
// import { PhConfigurationComponent } from './modules/ph-configuration/ph-configuration.component';
import { PublicPagesComponent } from './modules/public-pages/public-pages.component';

import { DashboardComponent } from './modules/dashboard/dashboard.component'
import { ModulesComponent } from './modules/modules.component';
import { ForgotPasswordComponent } from './modules/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './modules/online-services/reset-password/reset-password.component';
import { VerifyOtpComponent } from './modules/verify-otp/verify-otp.component';
import { ResetSuccessComponent } from './modules/reset-success/reset-success.component';
import { RegisterComponent } from './register/register.component';
import { PhConfigComponent } from './modules/ph-config/ph-config.component';
// import { AddPhConfigComponent } from './modules/add-ph-config/add-ph-config.component';
import { AccessRightsComponent } from './/modules/access-rights/access-rights.component';
import { EmailConfigEditComponent } from './modules/email-config/email-config-edit/email-config-edit.component';
import { RegistrationOtpVerifyComponent } from './register/registration-otp-verify/registration-otp-verify.component';

import { RetrieveOtpComponent } from './/modules/retrieve-otp/retrieve-otp.component';
import { CabchargePersonalCardComponent } from './modules/public-products/cabcharge-personal-card/cabcharge-personal-card.component';
import { AccountStatementsComponent } from './modules/view-statement-usage/account-statements/account-statements.component';
import { InvoicesComponent } from './modules/view-statement-usage/invoices/invoices.component';
import { ViewStatementInvoicesComponent } from './modules/view-statement-invoices/view-statement-invoices.component';
import { CabchargeEVoucherComponent } from './modules/public-products/cabcharge-e-voucher/cabcharge-e-voucher.component';
import { AccessIdEditComponent } from './modules/access-id/access-id-edit/access-id-edit.component';
import { TaxiVouchersComponent } from './modules/public-products/taxi-vouchers/taxi-vouchers.component';
import { UsagereportComponent } from './modules/view-statement-usage/usagereport/usagereport.component';
import { CreditlimitandbalanceComponent } from './modules/online-services/creditlimitandbalance/creditlimitandbalance.component';
import { MyAccountComponent } from './modules/my-account/my-account.component';
import { DownloadTripReportComponent } from './modules/view-statement-usage/download-trip-report/download-trip-report.component';
import { AddEditPhConfigComponent } from './modules/ph-config/add-edit-ph-config/add-edit-ph-config.component';
import { EditProfileComponent } from './modules/online-services/edit-profile/edit-profile.component';
import { ChangePasswordComponent } from './modules/online-services/change-password/change-password.component';
import { TransactionSummaryComponent } from './modules/view-statement-usage/transaction-summary/transaction-summary.component';
import { ViewCardStatusUsageComponent } from './modules/view-statement-usage/view-card-status-usage/view-card-status-usage.component';
import { DownloadCardHolderListComponent } from './modules/view-statement-usage/download-card-holder-list/download-card-holder-list.component';
import { EnquiryComponent } from './modules/enquiry/enquiry.component';
import { ReportLostFaultyCardComponent } from './modules/online-services/report-lost-faulty-card/report-lost-faulty-card.component';
import { RequestEvoucherComponent } from './modules/online-services/request-evoucher/request-evoucher.component';
import { AssignCardToUserComponent } from './modules/online-services/assign-card-to-user/assign-card-to-user.component';
import { ConfirmRequestEvoucherComponent } from './modules/online-services/request-evoucher/confirm-request-evoucher/confirm-request-evoucher.component';
import { TopUpHistoryComponent } from './modules/view-statement-usage/top-up-history/top-up-history.component';
import { FeedbackFormComponent } from './modules/online-services/feedback-form/feedback-form.component';
import { SelfServiceTopUpComponent } from './modules/online-services/self-service-top-up/self-service-top-up.component';
import { DepartmentalCardComponent } from './modules/online-application/departmental-card/departmental-card.component';
import { VirtualIndDeptCardComponent } from './modules/online-application/virtual-ind-dept-card/virtual-ind-dept-card.component';
import { IndividualCardComponent } from './modules/online-application/individual-card/individual-card.component';
import { LoyaltyRewardsRedemptionComponent } from './modules/online-services/loyalty-rewards-redemption/loyalty-rewards-redemption.component';
import { CorporateSetupRulesComponent } from './modules/corporate-setup-rules/corporate-setup-rules.component';
import { SetupPoliciesComponent } from './modules/setup-policies/setup-policies.component';
import { CorpSetupRulesComponent } from './modules/setup-policies/corp-setup-rules/corp-setup-rules.component';
import { SetupPoliciesListComponent } from './modules/setup-policies/setup-policies-list/setup-policies-list.component';
import { ViewTransactionCardEvoucherComponent } from './modules/view-statement-usage/view-transaction-card-evoucher/view-transaction-card-evoucher.component';
import { VirtualCardComponent } from './modules/online-application/virtual-card/virtual-card.component';
import { SupplementaryCardFormComponent } from './modules/application-form/supplementary-card-form/supplementary-card-form.component';
import {SupplementaryCardPreviewComponent} from './modules/application-form/supplementary-card-preview/supplementary-card-preview.component';
import { MySettingsComponent } from './modules/view-statement-usage/view-transaction-card-evoucher/my-settings/my-settings.component';
import { LoginRolesComponent } from './login/login-roles/login-roles.component';
import { AuthGuard } from './shared/guard/auth.guard';
import { EReceiptRequestComponent } from './modules/view-statement-usage/e-receipt-request/e-receipt-request.component';
import { MonthlySummaryComponent } from './modules/view-statement-usage/usagereport/monthly-summary/monthly-summary.component';
import { DivisionMonthlySummaryComponent } from './modules/view-statement-usage/usagereport/division-monthly-summary/division-monthly-summary.component';
import { VirtualIndDeptPreviewComponent } from './modules/online-application/virtual-ind-dept-preview/virtual-ind-dept-preview.component';
import { RegisterConfirmComponent } from './register/register-confirm/register-confirm.component';
import { SelfServiceTopUpDraftComponent } from './modules/online-services/self-service-top-up-draft/self-service-top-up-draft.component';
import { SelfServiceErrorCardComponent } from './modules/online-services/self-service-top-up/self-service-error-card/self-service-error-card.component';

const routes: Routes = [
    // {
    //     path: '',
    //     component: LoginComponent,
    //     pathMatch: 'full'
    // },
    {
        path: 'login',
        component: LoginComponent
    },
    {
        path: 'login/login-roles',
        component: LoginRolesComponent,
        canActivate: [AuthGuard]
    },
    {
        path: 'register',
        component: RegisterComponent

    },
    {
        path: 'forgotPwd',
        component: ForgotPasswordComponent
    },
    {
        path: 'resetPwd',
        component: ResetPasswordComponent
    },
    {
        path: 'verifyOtp',
        component: VerifyOtpComponent
    },
    {
        path: 'reset-success',
        component: ResetSuccessComponent
    },
    {
        path: 'layout',
        component: LayoutComponent,
        children: [
            {
                path: 'admindashboard',
                component: DashboardComponent,
                canActivate: [AuthGuard]
            },
            {
                path: 'dashboard',
                component: MyAccountComponent,
                canActivate: [AuthGuard]
            },
            {
                path: 'accesscontrol',
                component: AccessControlComponent,
                canActivate: [AuthGuard]
            },
            {
                path: 'Accessid',
                component: AccessIdComponent,
                canActivate: [AuthGuard]
            },
            {
                path: 'Accessid/accessid-edit',
                component: AccessIdEditComponent,
                canActivate: [AuthGuard]
            },
            {
                path: 'Emailconfig',
                component: EmailConfigComponent,
                canActivate: [AuthGuard]
            },          
            {
                path: 'retrieveOtp',
                // path: 'RetrieveOtp',
                component: RetrieveOtpComponent,
                canActivate: [AuthGuard]
            },
            {
                path: 'emailEdit',
                component: EmailConfigEditComponent,
                canActivate: [AuthGuard]
            },
            {
                path: 'ph',
                component: PhConfigComponent,
                canActivate: [AuthGuard]
            },
            {
                path: 'ph/:add',
                component: AddEditPhConfigComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'ph/:edit',
                component: AddEditPhConfigComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'reportLostFaultyCard',
                component: ReportLostFaultyCardComponent,
                canActivate:[AuthGuard]
            },
            // {
            //         path: 'addPh',
            //         component: AddPhConfigComponent
            //     },
            {
                path: 'publicpages',
                component: PublicPagesComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'eReceiptRequest',
                component: EReceiptRequestComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'rights',
                component: AccessRightsComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'products',
                component: CabchargePersonalCardComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'viewtransaction',
                component: ViewTransactionCardEvoucherComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'view_transaction_by_card_setting',
                component: MySettingsComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'taxiVoucher',
                component: TaxiVouchersComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'usageReport',
                component: UsagereportComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'usageReport/monthlySummary',
                component: MonthlySummaryComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'usageReport/divMonthlySummary',
                component: DivisionMonthlySummaryComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'creditlimitbalance',
                component: CreditlimitandbalanceComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'downloadtripreport',
                component: DownloadTripReportComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'invoices',
                component: InvoicesComponent,
                canActivate:[AuthGuard]

            },
            {
                path: 'accStatement',
                component: AccountStatementsComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'editprofile',
                component: EditProfileComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'changeProfile',
                component: ChangePasswordComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'transactionsummary',
                component: TransactionSummaryComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'card-status-usage',
                component: ViewCardStatusUsageComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'downloadCardHolder',
                component: DownloadCardHolderListComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'assigncardtouser',
                component: AssignCardToUserComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'requestevoucher',
                component: RequestEvoucherComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'reportlostfaultyCard',
                component: ReportLostFaultyCardComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'confirmRequestEvoucher',
                component: ConfirmRequestEvoucherComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'topuphistory',
                component: TopUpHistoryComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'feedback',
                component: FeedbackFormComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'selftopup',
                component: SelfServiceTopUpComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'selftopup/errorCards',
                component: SelfServiceErrorCardComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'selftopupdraft',
                component: SelfServiceTopUpDraftComponent,
                canActivate:[AuthGuard]
            },
            // {
            //     path: 'departmentalCard',
            //     component: DepartmentalCardComponent,
            //     canActivate:[AuthGuard]
            // },
            {
                path: 'onlineApplication',
                component: VirtualIndDeptCardComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'onlineApplication/preview',
                component: VirtualIndDeptPreviewComponent,
                canActivate:[AuthGuard]
            },
           
            // {
            //     path: 'individualCard',
            //     component: IndividualCardComponent,
            //     canActivate:[AuthGuard]
            // },
            {
                path: 'loyaltyRewardsRedemption',
                component: LoyaltyRewardsRedemptionComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'setuppolicies',
                component: SetupPoliciesComponent,
                canActivate:[AuthGuard],
                // children:[
                //     {
                //         path: 'setuprules',
                //         component: CorporateSetupRulesComponent,
                //         canActivate:[AuthGuard]
                //     }
                // ]
            },
            {
                path: 'setuppolicies/listsetuppolicies',
                component: SetupPoliciesListComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'setuppolicies/setuprules',
                component: CorpSetupRulesComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'virtualcard',
                component: VirtualCardComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'supplementarycard',
                component: SupplementaryCardFormComponent,
                canActivate:[AuthGuard]
            },
            {
                path: 'supplementarycard/preview',
                component: SupplementaryCardPreviewComponent,
                canActivate:[AuthGuard]  
            },
        ]
    },

    // {
    //     path: 'register/registerVerifyOTP',
    //     component: RegistrationOtpVerifyComponent
    // },
    {path: 'register/registerVerifyOTP/:register',
                component:  RegistrationOtpVerifyComponent ,
                
    },   
    {
        path: 'register/:registerReviewOTP/:email',
                component: RetrieveOtpComponent,

    },
    {
        path: 'register/:confirmation',
        component: RegisterConfirmComponent,

    },
    {
        path: 'viewSI',
        component: ViewStatementInvoicesComponent
    },
    {
        path: 'enquiry',
        component: EnquiryComponent
    },
    // {
    //     path: 'reportLostFaultyCard',
    //     component: ReportLostFaultyCardComponent
    // },
    // {
    //     path:'requestevoucher',
    //     component:RequestEvoucherComponent
    // },
    // {
    //     path:'confirmRequestEvoucher',
    //     component:ConfirmRequestEvoucherComponent
    // },



    // {
    //     path: 'forgotPwd',
    //     component: ForgotPasswordComponent
    // },
    // {
    //     path: 'resetPwd',
    //     component: ResetPasswordComponent
    // },
    // {
    //     path: 'verifyOtp',
    //     component: VerifyOtpComponent
    // },
    // {
    //     path: 'reset-success',
    //     component: ResetSuccessComponent
    // },
    // {
    //     path: 'ph',
    //     component: PhConfigComponent
    // },
    // {
    //     path: 'addPh',
    //     component: AddPhConfigComponent
    // }
];

@NgModule({
    imports: [RouterModule.forRoot(routes)],
    exports: [RouterModule],
})
export class AppRoutingModule { }












