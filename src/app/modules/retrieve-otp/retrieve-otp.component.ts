import { Component, OnInit , SecurityContext} from '@angular/core';
import { Location } from '@angular/common'
import { NgForm, FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms'
import { NotificationService } from './../../shared/services/notification.service'
import { Router } from '@angular/router';
import { CdgSharedService } from './../../shared/services/cdg-shared.service'
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { CommonModalComponent } from 'src/app/shared/shared-modules/common-modal/common-modal.component';
import { DomSanitizer , SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-retrieve-otp', 
  templateUrl: './retrieve-otp.component.html',
  styleUrl: './retrieve-otp.component.scss'
})
export class RetrieveOtpComponent implements OnInit {
  emailform:FormGroup

  status: any = "";
  // id: any;
  // userDetails: any;
  selectedCard: any;
  showRetriveOTPDetails : boolean;
  otpDetails: any; 

  constructor(public dialog: MatDialog, private location: Location, private http: HttpClient, private router: Router, public cdgService: CdgSharedService, private fb: FormBuilder, private notifyService: NotificationService, private apiService: ApiServiceService, private sanitizer: DomSanitizer) {
    this.emailform= this.fb.group({
      emailId: ['', [Validators.required, Validators.email]],
    });
   }
 

  ngOnInit(): void {
    this.showRetriveOTPDetails = false;
    console.log(this.router.url)
  }

  sanitizeHtml(html: any): SafeHtml {
    const sanitizedHtml: SafeHtml | null = this.sanitizer.sanitize(SecurityContext.HTML, html);
    const safeHtml: SafeHtml = sanitizedHtml as SafeHtml;
    return safeHtml;
  }

  submit() {
    if (this.emailform.invalid) {
      return;
    }
    else {
       console.log("email" + this.emailform.value.emailId)
       this.cdgService.showMatrix = false;
       let opt;
       this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.RETRIEVE_OTP_BY_EMAIL + this.emailform.value.emailId).subscribe((response: any) => {
         if (response.status === 200) {
           //Update otpDetails value
           this.otpDetails = response.body.message;
           this.showRetriveOTPDetails = true;
         }
       },
         e => {
           this.notifyService.showError(e.errorMessage, "Error")

         })

    }
  }


  goToPrevPage() {
    this.router.navigateByUrl('/layout/admindashboard');
    this.cdgService.showMatrix = false;
  }

}
