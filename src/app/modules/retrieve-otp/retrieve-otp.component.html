<div fxLayout="column" fxLayoutGap="10px" class="main-content">
	<!-- <div fxLayout="row" fxHide.lt-md> -->
	<div fxLayout="row">
		<div fxFlex fxLayout="row" fxLayoutGap="15px">
			<div class="cursor-pointer">
				<mat-icon (click)="goToPrevPage()">arrow_back</mat-icon>
			</div>
			<div>
				<span class="title">Retrieve OTP</span>
			</div>
		</div>
	</div>
	<div>
		<span>Get customer latest OTP</span>
	</div>
	<div>
		<form [formGroup]="emailform">
			<mat-form-field appearance="outline" class="inp-width">

				<input id="emailId" matInput formControlName="emailId" placeholder="Enter Email" required>
				<mat-icon matSuffix (click)="submit()" class="cursor-pointer">arrow_forward</mat-icon>
			 
				<mat-error *ngIf="emailform.controls.emailId.hasError('required')">
					Please Enter Email
				</mat-error>
				<mat-error *ngIf="emailform.controls.emailId.hasError('email')">
					Please Enter Valid Email
				</mat-error>
			</mat-form-field>
		</form></div>
		<div *ngIf="showRetriveOTPDetails">
			<!-- Display OTP details -->
			<div fxLayout="row" id="otp-details" class="otp-style">
				<div fxFlex="80%" class="otp-style">
					<span [innerHTML]="sanitizeHtml(otpDetails)"></span>
		
				</div>
		
		
			</div>
    </div> 
	  
</div>


 