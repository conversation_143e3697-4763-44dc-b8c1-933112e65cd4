<div fxLayout="row" fxLayoutGap="20px">
	<div fxFlex="70%" fxFlex.lt-md="100%" fxFlex.md="100%" fxLayout="column" fxLayoutGap="10px">
		<div  fxLayout="row" fxLayoutGap.lt-md="30px" fxLayoutGap="10px">
			<div fxFlex="5%">
				<mat-icon class="material-icons-outlined card-icon">credit_card</mat-icon>
			</div>
			<div fxFlex="95%" fxLayoutAlign="start center">
				<span class="header pb5">My Cards</span>
			</div>
		</div>
		<div fxLayout="row" fxLayoutGap="30px" class="main-carousel-div pl10 pr10">
			<div fxFlex="10%" class="pt30" fxHide.lt-md fxHide.md>
				<button mat-raised-button class="add-btn">
					<mat-icon class="add-icon">add</mat-icon>
				</button>
			</div>
			<div fxFlex="90%">
				<mat-carousel timings="250ms ease-in" [autoplay]="false" color="primary" maxWidth="auto" [loop]="true"
					[hideArrows]="true" [hideIndicators]="true" [useKeyboard]="false" [useMouseWheel]="true"
					orientation="rtl">
					<mat-carousel-slide #matCarouselSlide overlayColor="#fff" [hideOverlay]="false"
						*ngFor="let slide of existingCardsList; let i = index">
						<div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="30px" fxLayoutAlign="center center"
							class="mt35">
							<ng-container *ngFor="let card of slide.cardList; let i = index">
								<mat-card appearance="outlined" class="card">
									<img mat-card-image src={{card.img}} alt="product" height="170px">
									<mat-card-content class="prodTitle">{{card.cardName}}</mat-card-content>
								</mat-card>
							</ng-container>
						</div>
					</mat-carousel-slide>
				</mat-carousel>

			</div>
		</div>
		<div fxLayout="row">
			<mat-button-toggle-group fxFlex fxLayoutAlign="center center" #toggleGroup="matButtonToggleGroup"
				[value]="selectedVal" class="toggleGroup" fxLayout.lt-sm="column">
				<mat-button-toggle value="recentTransactions" class="toggle-div recentTransactions"> 
					<div fxLayout="row" fxLayoutGap="15px">
						<img src="/assets/images/recent transactions.svg"  width="40px" height="40px" alt="">
						<!-- <mat-icon class="material-icons-outlined toggle-icon" fxHide.lt-md fxHide.md>domain</mat-icon> -->
						<p class="login-type">Recent Transactions</p>
					</div>

				</mat-button-toggle>
				<mat-button-toggle value="downloadInvoices" class="toggle-div downloadInvoices">
					<div fxLayout="row" fxLayoutGap="15px">
						<img src="/assets/images/download invoice.svg"  width="40px" height="40px" alt="">
						<!-- <mat-icon class="material-icons-outlined toggle-icon" fxHide.lt-md fxHide.md>person</mat-icon> -->
						<p class="login-type">Download Invoices</p>
					</div>
				</mat-button-toggle>
				<mat-button-toggle value="statementOfAccounts" class="toggle-div">
					<div fxLayout="row" fxLayoutGap="15px">
						<img src="/assets/images/statement of account.svg" width="40px" height="40px" alt="">
						<!-- <mat-icon class="material-icons-outlined toggle-icon" fxHide.lt-md fxHide.md>person</mat-icon> -->
						<p class="login-type">Statement Of Accounts</p>
					</div>
				</mat-button-toggle>
			</mat-button-toggle-group>
		</div>
		<div *ngIf="toggleGroup.value == 'recentTransactions'">
			<app-recent-transactions></app-recent-transactions>
		</div>
		<div *ngIf="toggleGroup.value == 'downloadInvoices'">
			<app-download-invoices></app-download-invoices>
		</div>
		<div *ngIf="toggleGroup.value == 'statementOfAccounts'">
			<app-account-statements></app-account-statements>
		</div>
	</div>
	<div fxFlex="30%" fxLayout="column" fxHide.lt-md fxHide.md fxLayoutGap="20px">
		<div fxLayout="row" fxLayoutGap.lt-md="30px" fxLayoutGap="30px">
			<div fxFlex="5%">
				<mat-icon class="material-icons-outlined card-icon">assignment_ind</mat-icon>
			</div>
			<div fxFlex="95%" class="pt10">
				<span class="header ">Account Info</span>
			</div>
		</div>
		<div fxLayout="column" fxLayoutAlign="center center">
			<div>
				<mat-icon class="material-icons-outlined apartment-icon">apartment</mat-icon>
			</div>
			<div class="pb5">
				<span class="header ">Alphabeth Company</span>
			</div>
		</div>
		<mat-divider></mat-divider>
		<div fxLayout="row" fxLayoutGap="50px">
			<div fxFlex fxLayout="column" fxLayoutGap="10px">
				<span class="sub-header">Number Of Cards</span>
				<span class="data">{{noCards}}</span>
			</div>
			<div fxFlex fxLayout="column" fxLayoutGap="10px">
				<span class="sub-header">All Transactions</span>
				<span class="data">{{allTransactions}}</span>
			</div>
		</div>
		<div fxLayout="row" fxLayoutGap="50px">
			<div fxFlex fxLayout="column" fxLayoutGap="10px">
				<span class="sub-header">Average Transactions</span>
				<span class="data">$ {{avgTransaction}}</span>
			</div>
			<div fxFlex fxLayout="column" fxLayoutGap="10px">
				<span class="sub-header">Maximum Payments</span>
				<span class="data">$ {{maxPayment}}</span>
			</div>
		</div>
		<div fxLayout="row" fxLayoutGap="50px">
			<div fxFlex fxLayout="column" fxLayoutGap="10px">
				<span class="sub-header">Total Credit Limit</span>
				<span class="data">$ {{creditLimit}}</span>
			</div>
			<div fxFlex fxLayout="column" fxLayoutGap="10px">
				<span class="sub-header">Total Credit Balance</span>
				<span class="data">$ {{creditBal}}</span>
			</div>
		</div>
		<mat-divider></mat-divider>
		<div fxLayout="column" fxLayoutGap="5px">
			<div>
				<span class="header ">Last Payment Details</span>
			</div>
			<div fxLayout="row" fxLayoutGap="50px">
				<div fxFlex fxLayout="column" fxLayoutGap="10px">
					<span class="sub-header">Amount Paid</span>
					<span class="data">$ {{amtpaid}}</span>
				</div>
				<div fxFlex fxLayout="column" fxLayoutGap="10px">
					<span class="sub-header">Date</span>
					<span class="data">{{date | date:'d MMM YYYY'}}</span>
				</div>
			</div>
		</div>
		<mat-divider></mat-divider>
		<div fxLayout="column" fxLayoutGap="5px">
			<div fxLayout="row" fxLayoutGap="30px">
				<div >
				<span class="header" >Last Payment Details</span>
			</div>
				<div fxFlex fxLayout="row" fxLayoutAlign="end center">
					<mat-button-toggle-group  fxLayoutAlign="center center" #toggleGroups="matButtonToggleGroup"
						[value]="selectedchart">
						<mat-button-toggle value="chart" class="chart-icon-div">
								<mat-icon class="material-icons-outlined chart-icon">bar_chart</mat-icon>		
						</mat-button-toggle>
						<mat-button-toggle value="app" class="chart-icon-div">
								<mat-icon class="material-icons-outlined chart-icon">apps</mat-icon>
						</mat-button-toggle>
					</mat-button-toggle-group>
				</div>
			</div>
			<div *ngIf="toggleGroups.value == 'chart'" fxLayout="column">
				<div fxLayout="row" fxLayoutAlign="end start" fxLayoutGap="15px">
					<div fxLayout="column" fxLayoutGap="10px">
						<span class="date-title pt10">From Date:</span>
						<mat-form-field appearance="outline" class="inp">
							<input matInput [matDatepicker]="picker1" [(ngModel)]="selectedStartDate">
							<mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
							<mat-datepicker #picker1></mat-datepicker>
						</mat-form-field>
					</div>
					<div fxLayout="column" fxLayoutGap="10px">
						<span class="date-title pt10">To Date:</span>
						<mat-form-field appearance="outline" class="inp">
							<input matInput [matDatepicker]="picker2" [(ngModel)]="selectedEndtDate" (ngModelChange)="filterChart()">
							<mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
							<mat-datepicker #picker2></mat-datepicker>
						</mat-form-field>
					</div>
				</div>
				<canvas #lineCanvas></canvas>
			</div>
			<div *ngIf="toggleGroups.value == 'app'">
				app
			</div>
			
		</div>
	</div>

</div>