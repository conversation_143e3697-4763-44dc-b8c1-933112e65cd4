@import '@angular/material/theming';
@import './../../../styles/colors.scss';
@import './../../../styles/sizing.scss';
@import './../../../styles/main.scss';

.header{
    color: $lightGrey !important;
    font-size: 24px;
    font-weight:$cdgsz-font-weight-normal ;
}
.sub-header{
    font-size: 14px;
    color: $lightGrey !important;
}
.data{
    font-size: 20px;
    color: $lightGrey !important;
}
// .mat-icon{
//     color: $lightGrey !important;
//     font-size: 40px !important;
// }
.card-icon{
    color: $lightGrey !important;
    font-size: 40px !important;
}

::ng-deep .carousel{
    height:300px !important;
    min-height:300px !important;
}
::ng-deep.carousel-slide{
    height:300px !important;
    min-height:300px !important;
}
::ng-deep .carousel-inner{
    height:300px !important;
    min-height:300px !important;
}
::ng-deep .carousel-inner>.item{
    height:300px !important;
    min-height:300px !important;
}
::ng-deep .carousel-inner>.item>a>img, .carousel-inner>.item>img{
    line-height: 1;
}
::ng-deep .carousel-slide-overlay{
    height:300px !important;
}
.card{
    width: 271px !important;
    height: 203px !important;
    // background-color: black;
}
.add-btn{
    width: 87px;
    height: 87px;
    border: 2px dashed $lightGrey !important;
    background-color: $cdgc-bg-accent !important;
}
.add-icon{
    color: $lightGrey !important;
    font-size: 24px !important;
}
.main-carousel-div{
    border-radius: 10px !important;
    background-color: $cdgc-bg-accent !important;
}
.pt30{
    padding-top: 30px;
}
.login-type{
    font-size: $cdgsz-font-size-prime;
    font-weight: $cdgsz-font-weight-normal;
    margin-bottom: 0px !important;
    padding-top: 10px;
}
.toggle-div{
    padding: 3px 15px 3px 15px;
}
.toggle-icon{
    font-size: 35px !important;
}
.apartment-icon{
    font-size: 190px !important;
    width: 198px;
    color: $cdgc-font-prime !important;
}
.inp{
    width: 155px !important;
}
.chart-icon-div{
    padding: 3px 0px 3px 0px;

}
.chart-icon{
    font-size: 24px !important;
}
@media screen and (max-width: 800px) {
    .toggle-div{
        padding: 0px !important;
    }
    .recentTransactions{
        padding: 0px 9px !important;
        margin:10px; //added on 4-29-2021
    }
    .downloadInvoices{
        padding: 0px 10px !important;
        margin:10px; //added on 4-29-2021
    }
    .login-type{
        font-size: 14px;
        padding-top: 5px;
    }
}
