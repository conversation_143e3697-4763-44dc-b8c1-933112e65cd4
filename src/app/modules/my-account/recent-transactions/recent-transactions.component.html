<div fxLayout="column" fxLayoutGap="15px">
    <div fxLayout="row" fxLayoutAlign="end start" fxLayoutGap="15px">
        <div fxLayout="row" fxLayoutGap="10px">
            <span class="date-title pt10">From Date:</span>
            <mat-form-field appearance="outline" class="inp">
                <input matInput [matDatepicker]="picker1" [(ngModel)]="selectedStartDate" >
                <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                <mat-datepicker #picker1></mat-datepicker>
            </mat-form-field>
        </div>
        <div fxLayout="row" fxLayoutGap="10px">
            <span class="date-title pt10">To Date:</span>
            <mat-form-field appearance="outline" class="inp">
                <input matInput [matDatepicker]="picker2" [(ngModel)]="selectedEndtDate" (ngModelChange)="getEndDateRange()">
                <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                <mat-datepicker #picker2></mat-datepicker>
            </mat-form-field>
        </div>
    </div>
    <div fxLayout="column" fxLayoutGap="15px">
        <ng-container *ngFor="let data of transactions;let i=index">
            <mat-card appearance="outlined">
                <div fxLayout="row" fxLayoutGap="10px">
                    <div fxFlex="10%" class="icon-div" fxLayoutAlign="center center" fxHide.lt-md fxHide.md><mat-icon class="material-icons-outlined car-ic">directions_car</mat-icon></div>
                    <div fxFlex="60%" fxLayoutGap="10px" fxLayout="column">
                            <span>{{data.destination}}</span>
                            <span class="carduser">{{data.cardUsed}}</span>

                    </div>
                    <div fxFlex="30%" fxLayout="column" fxLayoutAlign="start end">
                        <span>$ {{data.fare}}</span>
                            <span class="carduser">{{data.date | date:'d MMM, h:mm a'}}</span>
                    </div>

                </div>
            </mat-card>
        </ng-container>
    </div>
</div>