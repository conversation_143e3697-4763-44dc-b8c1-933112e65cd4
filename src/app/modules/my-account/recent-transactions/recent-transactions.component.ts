import { Component, OnInit, Pipe } from '@angular/core';
import { DatePipe } from '@angular/common';
import { FormControl } from '@angular/forms';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as _moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core'
export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'MM-DD-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
}
@Component({
  selector: 'app-recent-transactions',
  templateUrl: './recent-transactions.component.html',
  styleUrls: ['./recent-transactions.component.scss'],
  providers: [DatePipe, { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] },{provide:MAT_DATE_FORMATS,useValue: MY_FORMATS}]
})
export class RecentTransactionsComponent implements OnInit {
  selectedStartDate: any;
  selectedEndtDate: any;
  transactions = [
    {
      destination: "Ride to Martina Bay Stands",
      cardUsed: "Corporate Card",
      fare: "36.50",
      date: '03-15-2021'
    },
    {
      destination: "Ride to Work",
      cardUsed: "Corporate Card",
      fare: "25.71",
      date: '03-15-2021'
    },
    {
      destination: "Ride to Martina Bay Stands",
      cardUsed: "Corporate Card",
      fare: "36.50",
      date: '03-13-2021'
    },
    {
      destination: "Ride to Work",
      cardUsed: "Corporate Card",
      fare: "25.71",
      date: '02-14-2021'
    },
    {
      destination: "Ride to Martina Bay Stands",
      cardUsed: "Corporate Card",
      fare: "36.50",
      date: '02-10-2021'
    },
    {
      destination: "Ride to Work",
      cardUsed: "Corporate Card",
      fare: "25.71",
      date: '01-30-2021'
    },
    {
      destination: "Ride to Martina Bay Stands",
      cardUsed: "Corporate Card",
      fare: "36.50",
      date: '01-30-2021'
    },
    {
      destination: "Ride to Work",
      cardUsed: "Corporate Card",
      fare: "25.71",
      date: '01-30-2020'
    }, {
      destination: "Ride to Martina Bay Stands",
      cardUsed: "Corporate Card",
      fare: "36.50",
      date: '01-30-2020'
    }
  ]
  // maxDate: Date;
  constructor(private datePipe: DatePipe) { }

  ngOnInit(): void {
    this.selectedStartDate = new Date();
    this.selectedEndtDate = new Date();
  }
  getEndDateRange() {
    let arr: any = []
    this.transactions.forEach(element => {
      const date = element.date.split('-');
      if (new Date(this.selectedStartDate).getFullYear() <= Number(date[2]) && new Date(this.selectedStartDate).getDate() <= Number(date[1]) && new Date(this.selectedStartDate).getMonth() + 1 <= Number(date[0]) && new Date(this.selectedEndtDate).getFullYear() >= Number(date[2]) && new Date(this.selectedEndtDate).getDate() >= Number(date[1]) && new Date(this.selectedEndtDate).getMonth() + 1 >= Number(date[0])) {
        arr.push(element)
        console.log(element, new Date(this.selectedStartDate).getDay(), Number(date[1]), new Date(this.selectedEndtDate).getDay())
      }
    });
    this.transactions = arr
    console.log(arr, this.transactions)
  }
}
