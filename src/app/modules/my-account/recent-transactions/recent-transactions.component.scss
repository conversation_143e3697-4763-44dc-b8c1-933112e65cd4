@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
:host ::ng-deep .mat-form-field-appearance-outline .mat-mdc-form-field-text-infix {
    padding: 0 0 0 0!important;
    bottom: 6px !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
::ng-deep  .mat-mdc-form-field:not(.mat-form-field-appearance-legacy) .mat-mdc-form-field-suffix .mat-mdc-icon-button {
    bottom: 6px !important;
}
.inp{
    width: 173px !important;
}
.icon-div{
background-color: $cdgc-bg-prime;
color:$cdgc-bg-accent ;
}
.car-ic{
    height: 49px !important;
    width: 49px !important;
    font-size: 50px !important;
}
.carduser{
    font-size: 12px;
}
@media screen and (max-width: 600px) {
    .inp{
        width: 149px !important;
    }
    .date-title{
        font-size: 12px;
    }
}