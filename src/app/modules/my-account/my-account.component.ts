import { Component, OnInit, ViewChild ,AfterViewInit, ElementRef} from '@angular/core';
// import { Mat<PERSON><PERSON><PERSON><PERSON>, MatCarouselComponent } from '@ngbmodule/material-carousel';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as _moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { Chart } from 'chart.js';
export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
}
@Component({
  selector: 'app-my-account',
  templateUrl: './my-account.component.html',
  styleUrls: ['./my-account.component.scss'],
  providers: [{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] }, { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]

})
export class MyAccountComponent implements OnInit {
  public selectedVal: string = 'downloadInvoices';
  public selectedchart: string = 'chart';
  noCards: any = 4;
  allTransactions: any = 107;
  avgTransaction: any = 15.00;
  maxPayment: any = 4999;
  creditLimit: any = "185,0000.00";
  creditBal: any = "154,235.89";
  amtpaid = "15,000.00";
  date = "03/14/2021"
  selectedStartDate: any;
  selectedEndtDate: any;
  @ViewChild('lineCanvas') lineCanvas: ElementRef;
  lineChart: any;
  dataSet:any[]=[];
  existingCardsList = [
    {
      page: 1,
      cardList: [
        {
          img: "/assets/images/cab-charge-label.png",
          cardName: "Carcharge Personal Card"
        },
        {
          img: "/assets/images/cab-charge-label.png",
          cardName: "Carcharge Corporate Card"
        }
      ]
    },
    {
      page: 2,
      cardList: [
        {
          img: "/assets/images/cab-charge-label.png",
          cardName: "Open value E-voucher"
        }
      ]
    }
  ]
  labels:any[]=['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
  cardList=[
    {cardName:"Corporate Card",data:[65, 59, 80, 81, 56, 55]},
    {cardName:"Personal Card",data:[50, 56, 86, 81, 5, 55]}

  ]
  constructor(public cdgService: CdgSharedService) {
    this.dataSet=[
      {
        label: 'Corporate Card',
        fill: false,
        lineTension: 0.1,
        backgroundColor: 'rgba(75,192,192,0.4)',
        borderColor: 'rgba(75,192,192,1)',
        borderCapStyle: 'butt',
        borderDash: [],
        borderDashOffset: 0.0,
        borderJoinStyle: 'miter',
        pointBorderColor: 'rgba(75,192,192,1)',
        pointBackgroundColor: '#fff',
        pointBorderWidth: 1,
        pointHoverRadius: 5,
        pointHoverBackgroundColor: 'rgba(75,192,192,1)',
        pointHoverBorderColor: 'rgba(220,220,220,1)',
        pointHoverBorderWidth: 2,
        pointRadius: 1,
        pointHitRadius: 10,
        data: [65, 59, 80, 81, 56, 55],
        spanGaps:false,
      },
      {
        label: 'Personal Card',
        fill: false,
        lineTension: 0.1,
        backgroundColor: '#DA6C2A',
        borderColor: '#DA6C2A',
        borderCapStyle: 'butt',
        borderDash: [],
        borderDashOffset: 0.0,
        borderJoinStyle: 'miter',
        pointBorderColor: 'red',
        pointBackgroundColor: '#fff',
        pointBorderWidth: 1,
        pointHoverRadius: 5,
        pointHoverBackgroundColor: 'rgba(75,192,192,1)',
        pointHoverBorderColor: 'rgba(220,220,220,1)',
        pointHoverBorderWidth: 2,
        pointRadius: 1,
        pointHitRadius: 10,
        data: [50, 56, 86, 81, 5, 55],
        spanGaps:false,
      }
    ]
   }
  
  ngOnInit(): void {
    this.selectedStartDate = new Date();
    this.selectedEndtDate = new Date();
  }
  ngAfterViewInit(){
    this.lineChartMethod();

  }
  lineChartMethod() {
    this.lineChart = new Chart(this.lineCanvas.nativeElement, {
      type: 'line', data: {
        labels: this.labels,
        datasets: this.dataSet
        
      },
      options:{
          legend:{
            display:false
          }
      }
    })
  }
  filterChart(){
    console.log(new Date(this.selectedStartDate).getMonth(),new Date(this.selectedEndtDate).getMonth(),this.lineChart.data.datasets)
    // this.dataSet.forEach((element,i) => {
    //   if(new Date(this.selectedStartDate).getMonth() <= i )
    // 
  }
}
