import { Component, OnInit } from '@angular/core';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as _moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core'
import { DatePipe } from '@angular/common';
import { FormBuilder } from '@angular/forms';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';

export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
}
@Component({
  selector: 'app-corporate-setup-rules',
  templateUrl: './corporate-setup-rules.component.html',
  styleUrls: ['./corporate-setup-rules.component.scss'],
  providers: [DatePipe, { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] }, { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]
})
export class CorporateSetupRulesComponent implements OnInit {
  productArr:any = [];
  firstdigit: any;
  lastdigit: any=[];
  list: any;
  displaycard: any;
  setupEditForm: any = {
    description: "",
    locationPolicies: "",
    timePolicies:"",
    amountCap:'',
    accountName:"",
    jobType:""
  };
  data = {
    policyName: "",
    radioValue: "",
    divsion: "",
    department:"",
    producttype:"",
    cardNo: "",
    startDate: "",
    endDate: "",
    time: [
      {
        day: [
          { name: 'Mon', checked: false },
          { name: 'Tue', checked: false },
          { name: 'Wed', checked: false },
          { name: 'Thr', checked: false },
          { name: 'Fri', checked: false },
          { name: 'Sat', checked: false },
          { name: 'Sun', checked: false },
          { name: 'Ph', checked: false }
        ],
        from: {
          hr: 0,
          min: 0
        },
        to: {
          hr: 0,
          min: 0
        }
      },
    ],
    locations: [
      {
        location: "",
        mode: [
          { name: 'Pickup', checked: false },
          { name: 'Destination', checked: false }
        ],
        distance: 2
      }
    ],
    amt: "",
    jobType: [
      { name: '',id:'', checked: false },
      { name: '',id:'', checked: false },
      { name: '',id:'', checked: false }
    ],
    vehicleType: [
      { name: '',id:'', checked: false },
      { name: '',id:'',  checked: false }
    ],
    fareType: [
      { name: '',id:'',  checked: false },
      { name: '', id:'', checked: false }
    ],
  }
  radioArr = [
    { value: "accby", viewValue: "By Account No." },
    { value: "cardno", viewValue: "By Card No." }
  ]
  divitionArr = [
    { value: 'cabincrew', viewValue: 'CABIN CREW (1003)' },
    { value: 'corporateoffice', viewValue: 'CORPORATE OFFICE (1005)' },
    { value: 'csm1', viewValue: 'CSM1 (1004)' }
  ];
  departmentArr = [
    { value: 'cabincrew', viewValue: 'CABIN CREW (1003)' },
    { value: 'corporateoffice', viewValue: 'CORPORATE OFFICE (1005)' },
    { value: 'csm1', viewValue: 'CSM1 (1004)' }
  ];
  // cardArr = ['601089-6511-1239', '601089-6511-1237', '601089-6511-1235', '601089-6511-1233', '601089-6511-1231', '601089-6511-1230'];
  cardArr :any=[];
  days = [
    { name: 'Mon', checked: false },
    { name: 'Tue', checked: false },
    { name: 'Wed', checked: false },
    { name: 'Thr', checked: false },
    { name: 'Fri', checked: false },
    { name: 'Sat', checked: false },
    { name: 'Sun', checked: false },
    { name: 'Ph', checked: false }
  ]
  isAnyTime: boolean = false;
  accountnumber: any;
  jobtype: any;
  jobcode: any;
  faretype: any;
  farecode: any;
  vechtype: any;
  vechcode: any;
  constructor(private datePipe: DatePipe,public localStorage: LocalStorageService,private apiService: ApiServiceService,private fb: FormBuilder, public cdgService: CdgSharedService, private notifyService: NotificationService) { }

  ngOnInit(): void {
    if(this.localStorage.localStorageGet("customerNoSelected")!=null){
      this.accountnumber=this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if(this.accountnumber=this.localStorage.localStorageGet("custNo")!=null){
      this.accountnumber=this.localStorage.localStorageGet("custNo").view;
    }
    else {
      this.accountnumber= this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
    }
    }
    let prodtypeobj={
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role":  this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accountNo":this.accountnumber,
      "masterAccountNo":Number(this.accountnumber)
    }
        
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2  + UrlConstants.GET_PRODUCTTYPE,prodtypeobj).subscribe((response: any) => {
          if (response.status === 200) {
            console.log(response)
            let opt = response.body.productType;
            
            opt.forEach((element: any) => {
              this.productArr.push(element);
              console.log(this.productArr);
            });
          }
        });
    this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2  + UrlConstants.LOAD_JOBTYPE).subscribe((response: any) => {
      if (response.status === 200) {
        console.log(response)
        this.data.jobType=[];
        this.data.vehicleType=[];
        this.data.fareType=[];
        response.body.jobTypeList.forEach((element:any) => {
          this.data.jobType.push ({'name':element.jobTypeName,'id':element.jobTypeCode,'checked':false});
        });
        response.body.fareTypeList.forEach((element:any) => {
          this.data.fareType.push ({'name':element.fareTypeName,'id':element.fareTypeCode, 'checked':false});
        });
        response.body.vehicleTypeList.forEach((element:any) => {
          this.data.vehicleType.push ({'name':element.vehicleTypeName,'id':element.vehicleTypeCode, 'checked':false});
        });
     
      
      }
    });
  
    
    
    this.setupEditForm.description = this.cdgService.setupRulesData.description;
    this.setupEditForm.locationPolicies = this.cdgService.setupRulesData.locationPolicies;
    this.setupEditForm.timePolicies = this.cdgService.setupRulesData.timePolicies;
    this.setupEditForm.amountCap = this.cdgService.setupRulesData.amountCap;
    this.setupEditForm.accountName = this.cdgService.setupRulesData.accName;
    this.setupEditForm.jobType = this.cdgService.setupRulesData.jobType;
    console.log(this.cdgService.setupRulesData);
  }
  onSelect(item: any) {
    this.displaycard="";
   // this.showcarderror=false;
    if (item.value) {
     
let cardnumberobj={
  
  "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
  "role":  this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
  "roleId":this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
  "productType" :item.value,
  "accountNo":this.accountnumber
    

}
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2  + UrlConstants.GET_CARDLIST,cardnumberobj).subscribe((response: any) => {
        if (response.status === 200) {
          console.log(response)
          let opt = response.body.cardNo;
         // if(response.body.cardNo.length>1){
          this.list=response.body.cardNo;
          this.firstdigit=String(opt[0]).substr(0,10);
          this.displaycard= this.firstdigit.substr(0,6) +"-" +this.firstdigit.substr(6,4);
          
        
          // opt.forEach((element:any) => {
          //   this.lastdigit=(String(element).substr(-6));
          //   this.cardArr=this.lastdigit;
          // });
  
        }
    });

    }
    else{
      //this.isDisableRadioButton = false;
    }
}
keyPress(event : any){
    
    
  this.cardArr=[];
  let obj={
    "accessId" : this.localStorage.localStorageGet("loginUserDetails").accessId,
    "role" : this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
    "roleId":this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
    "accountNo":this.accountnumber,
  "cardNoList":  this.list,
  "cardNoLike" :  this.firstdigit + this.data.cardNo
//   "accessId" : "<EMAIL>",
//   "role" :"ROLE_CORPADMIN",
// "cardNoList": [   
//       "************9642",    
//       "****************",
//       "****************",
//       "****************"
//   ],
// "cardNoLike" : "************"
    
}
this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2  + UrlConstants.SEARCH_CARDLIST,obj).subscribe((response: any) => {
  if (response.status === 200) {
    console.log(response)
    let opt = response.body.cardNo;
    this.cardArr=[];
    opt.forEach((element:any) => {
      this.lastdigit.push((String(element).substr(-6)));
      this.cardArr=this.lastdigit;
     // this.cardArr [(String(element).substr(-6))];
       // this.cardArr=[this.lastdigit];
            
      });
      this.lastdigit = [];
  }
});

}

  checkboxChecked(i: number, j: number, val: any) {
    this.data.time.forEach((element, k) => {
      if (i === k) {
        element.day.forEach((value, l) => {
          if (value.name === val.name) {
            value.checked = val.checked
          }
        });
      }
    });
  }
  addTime(){
    if(this.data.time.length < 15){
    this.data.time.push({
      day: [
        { name: 'Mon', checked: false },
        { name: 'Tue', checked: false },
        { name: 'Wed', checked: false },
        { name: 'Thr', checked: false },
        { name: 'Fri', checked: false },
        { name: 'Sat', checked: false },
        { name: 'Sun', checked: false },
        { name: 'Ph', checked: false }
      ],
      from: {
        hr: 0,
        min: 0
      },
      to: {
        hr: 0,
        min: 0
      }
    })
  }
  else{
    this.notifyService.showInfo("Max Limit reached for Time Policy",'Warning')
  }
  }
  onChecked(isAnyTime:boolean){
    if(isAnyTime){
      this.isAnyTime = true;
    }
   
  }
  deleteTime(index:number){
    this.data.time.splice(index, 1)
  }
  addLocation(){
    if(this.data.locations.length < 10){
    this.data.locations.push({
      location: "",
      mode: [
        { name: 'Pickup', checked: false },
        { name: 'Destination', checked: false }
      ],
      distance: 2
    });
  }
    else{
      this.notifyService.showInfo("Max Limit reached for Location Policy",'Warning')
    }
  }
  deleteLocation(index:number){
    this.data.locations.splice(index, 1)
  }
  cancel() {
    // this.notifyService.showSuccess('Rules Added Successfully', 'Success')
  }
  submit() {
    this.data.jobType.filter((element:any)=>{
   if(element.checked===true){
     this.jobtype=element.name;
     this.jobcode=element.id;
   }
 
    });
    this.data.fareType.filter((element:any)=>{
      if(element.checked===true){
        this.faretype=element.name;
        this.farecode=element.id;
      }
    
    });
    this.data.vehicleType.filter((element:any)=>{
      if(element.checked===true){
        this.vechtype=element.name;
        this.vechcode=element.id;
      }
      
    });
    let obj={
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "accountNo": this.accountnumber,
      "amountCap": this.setupEditForm.amountCap,
      "cardList": [
        {
          "cardNo":  this.firstdigit + this.data.cardNo,
          "cardNoFrom": null,
          "cardNoTo": null,
          "productTypeId": this.data.producttype
        }
      ],
      "customerNo": "166195",
      "deptCode": null,
      "divisionCode": null,
      "errorMessage": "",
      "fareTypeList": [
        {
          "fareTypeCode":  this.farecode,
          "fareTypeId": null,
          "fareTypeName":  this.faretype
        }
      ],
      "jobTypeList": [
        {
          "jobTypeCode": this.jobcode,
          "jobTypeId": null,
          "jobTypeName": this.jobtype
        }
      ],
      
      "mainAccountSelected": false,
      "policyBy": "C",
      "policyEndDate": "30-06-2030",
      "policyId": null,
      "policyName": "TCS CDG POLICY 1234",
      "policyNameBeforeUpdate": "",
      "policyStartDate": "01-07-2021",
      "role": "ROLE_CORPADMIN",
      "routePolicyList": [
        {
          "destDesc": "COMFORT BUILDING 383 SIN MING DRIVE 575717",
          "destLat": 1.359651,
          "destLong": 103.83756,
          "pickupDesc": "CPF TAMPINES BUILDING 1 TAMPINES CENTRAL 5 529508",
          "pickupLat": 1.********,
          "pickupLong": 103.********,
          "radius": 6,                //radius can't be less than 2(to be handled by UI)
          "routeId": null
        }
      ],
      "successMessage": "",
      "timePolicyList": [
        {
          "anytime": false,
          "friday": false,
          "monday": true,
          "publicHoliday": true,
          "saturday": false,
          "sunday": false,
          "thursday": false,
          "timeFrom": "12:30",
          "timeId": null,
          "timeTo": "22:30",
          "tuesday": false,
          "wednesday": false
        },
      {
          "anytime": true,
          "friday": false,
          "monday": false,
          "publicHoliday": false,
          "saturday": false,
          "sunday": false,
          "thursday": false,
          "timeFrom": "",
          "timeId": null,
          "timeTo": "",
          "tuesday": false,
          "wednesday": false
        }
      ],
      "vehicleTypeList": [
        {
          "vehicleTypeCode": this.vechcode,
          "vehicleTypeId": null,
          "vehicleTypeName": this.vechtype
        }
     
      ]
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2  + UrlConstants.SUBMITSETUPRULES,obj).subscribe((response: any) => {
      if (response.status === 200) {
        console.log(response)
        let opt= response.body;
      }
    });
    this.notifyService.showSuccess('Rules Added Successfully', 'Success')
  }
}
