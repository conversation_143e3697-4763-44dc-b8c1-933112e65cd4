<div fxLayout="column" fxLayoutGap="50px">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="main-icon">list</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">Manage Corporate Rule</span>
        </div>
    </div>
    <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
        <div fxFlex fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap.lt-md="30px">
            <div fxFlex fxLayoutAlign="start center">
                <span class="sub-header">New Rule</span>
            </div>
            <div fxFlex fxLayoutAlign="end center" fxLayout="row" fxLayoutGap="15px">
                <div>
                    <button mat-raised-button class="cancel-btn" (click)="cancel()">CANCEL
                    </button>
                </div>
                <div>
                    <button mat-raised-button class="submit-btn" (click)="submit()">SUBMIT
                    </button>
                </div>
            </div>
        </div>
        <div fxFlex class="header-div">
            <span>Details</span>
        </div>
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">Policy Name:</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%">
                <mat-form-field appearance="outline" class="inp">
                    <input matInput name="policyName" [(ngModel)]="data.policyName" #policyName="ngModel">
                </mat-form-field>
            </div>
        </div>
        <div  fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
            <mat-radio-group [(ngModel)]="data.radioValue" fxLayoutGap="30px">
                <mat-radio-button *ngFor="let val of radioArr" [value]="val.viewValue" labelPosition="before">
                    {{val.viewValue}}
                </mat-radio-button>
            </mat-radio-group>
        </div>
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
            *ngIf="data.radioValue == 'By Account No.'">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">Division :</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%">
                <mat-form-field appearance="outline" class="inp">
                    <mat-select name="divsion" [(ngModel)]="data.divsion" placeholder="Please Select"
                        #divsion="ngModel">

                        <mat-option *ngFor="let acl of divitionArr" [value]="acl.viewValue">
                            {{acl.viewValue}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
        </div>
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
            *ngIf="data.radioValue == 'By Account No.'">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">Department :</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%">
                <mat-form-field appearance="outline" class="inp">
                    <mat-select name="department" [(ngModel)]="data.department" placeholder="Please Select"
                        #department="ngModel">

                        <mat-option *ngFor="let acl of departmentArr" [value]="acl.viewValue">
                            {{acl.viewValue}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
        </div>
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
            *ngIf="data.radioValue == 'By Card No.'">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">Card No. :</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%">
                <mat-form-field appearance="outline" class="inp">
                    <mat-select name="cardNo" [(ngModel)]="data.cardNo" placeholder="Please Select" #cardNo="ngModel">

                        <mat-option *ngFor="let acl of cardArr" [value]="acl">
                            {{acl}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
        </div>
        <div fxLayout="row" fxLayout.lt-md="column" fxLayoutGap="80px" fxLayoutGap.lt-md="20px" class="pl20">
            <div fxLayout="row" fxLayoutGap="50px">
                <span fxFlex.lt-md="35%" class="date-title pt10">Start Date:</span>
                <mat-form-field appearance="outline" class="date-inp">
                    <input matInput [matDatepicker]="picker1" name="startDate" [(ngModel)]="data.startDate"
                        #startDate="ngModel">
                    <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                    <mat-datepicker #picker1></mat-datepicker>
                </mat-form-field>
            </div>
            <div fxLayout="row" fxLayoutGap="50px" >
                <span fxFlex.lt-md="35%" class="date-title pt10">To :</span>
                <mat-form-field appearance="outline" class="date-inp">
                    <input matInput [matDatepicker]="picker2" name="endDate" [(ngModel)]="data.endDate"
                        #endDate="ngModel">
                    <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                    <mat-datepicker #picker2></mat-datepicker>
                </mat-form-field>
            </div>

        </div>
        <div fxFlex class="header-div">
            <span>Time</span>
        </div>
        <ng-container *ngFor="let value of data.time; let i = index">
            <div fxLayout="row" fxFlex class="pl20">
                <div  fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <ng-container *ngFor="let val of value.day;let j=index">
                        <mat-checkbox color="primary" name="day{{i}}" [(ngModel)]="val.checked" #day="ngModel"
                        labelPosition="before" value="val.checked">
                        {{val.name}}
                    </mat-checkbox>
                    </ng-container>
                </div>
            </div>
            
                <div  fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 20px;">
                    <ng-container>
                        <mat-checkbox color="primary"
                        labelPosition="after" [(ngModel)]="isAnyTime" (change)="onChecked(isAnyTime)">
                       AnyTime
                    </mat-checkbox>
                    </ng-container>
                </div>
            
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20" *ngIf="!isAnyTime">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">From:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%" fxLayoutGap="15px">
                    <mat-form-field appearance="outline" class="num-inp">
                        <input matInput type="number" min="0" max="23" name="fromHr{{i}}" onkeydown="return false"
                            [(ngModel)]="value.from.hr" #fromHr="ngModel">
                    </mat-form-field>
                    <mat-form-field appearance="outline" class="num-inp">
                        <input matInput name="fromMin{{i}}" type="number" min="0" max="59" [(ngModel)]="value.from.min"
                            #fromMin="ngModel">
                    </mat-form-field>
                </div>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20" *ngIf="!isAnyTime">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">To:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%" fxLayoutGap="15px">
                    <mat-form-field appearance="outline" class="num-inp">
                        <input matInput type="number" min="0" max="23" name="toHr{{i}}" onkeydown="return false"
                            [(ngModel)]="value.to.hr" #toHr="ngModel">
                    </mat-form-field>
                    <mat-form-field appearance="outline" class="num-inp">
                        <input matInput name="toMin{{i}}" type="number" min="0" max="59" onkeydown="return false"
                            [(ngModel)]="value.to.min" #toMin="ngModel">
                    </mat-form-field>
                </div>
            </div>
            <div fxFlex fxLayoutAlign="end center" *ngIf="i > 0">
                <button mat-raised-button class="add-btn" (click)="deleteTime(i)">
                    <mat-icon class="add-icon">delete</mat-icon>DELETE
                </button>
            </div>
        </ng-container>
        <div fxFlex>
            <button mat-raised-button class="add-btn" (click)="addTime()">
                <mat-icon class="add-icon">add</mat-icon>Add
            </button>
        </div>
        <div fxFlex class="header-div">
            <span>Location</span>
        </div>

        <div fxLayout.gt-md="row" fxLayout.md="row" fxLayout.lt-md="column"  fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
            <div fxFlex="50%" fxLayout="column" fxLayoutAlign="start start">
                <ng-container *ngFor="let value of data.locations; let j = index">
                    <div  fxLayoutGap="20px" fxLayout="column" class="pb20">
                    <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="40px">
                        <div>
                        <mat-form-field appearance="outline" class="location-inp">
                            <input matInput name="location{{j}}" [(ngModel)]="value.location" #location="ngModel" placeholder="Please Enter Location">
                        </mat-form-field>
                    </div>
                    <div fxLayout="row" fxLayoutAlign="center center">
                        <mat-form-field appearance="outline" class="num-inp">
                            <input matInput name="distance{{j}}" type="number" min="2" max="59" onkeydown="return false" [(ngModel)]="value.distance" #distance="ngModel">
                        </mat-form-field>
                        <span>Km</span>
                    </div>
                    </div>
                    <div>
                        <ng-container *ngFor="let val of value.mode;let k=index" >
                            <mat-checkbox color="primary" name="mode{{j}}" [(ngModel)]="val.checked" #mode="ngModel"
                                labelPosition="before" value="val.checked" class="pr20">
                                {{val.name}}
                            </mat-checkbox>
                        </ng-container>
                    </div>
                    <div fxLayoutGap="5px" fxLayoutAlign="end center" *ngIf="j > 0">
                        <button mat-raised-button class="add-btn" (click)="deleteLocation(j)">
                            <mat-icon class="add-icon">delete</mat-icon>DELETE
                        </button>
                    </div>
                </div>
            </ng-container>
            </div>
            <div fxFlex="50%">
                <button mat-raised-button class="add-btn" (click)="addLocation()">
                    <mat-icon class="add-icon">add</mat-icon>Add
                </button>
            </div>
        </div>
        <!-- </ng-container> -->
        <div fxFlex class="header-div">
            <span>Rules</span>
        </div>
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">Amount Cap ($):</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%">
                <mat-form-field appearance="outline" class="inp">
                    <input matInput pattern="((\d+)((\.\d{1,2})?))$" name="amt" [(ngModel)]="data.amt" #amt="ngModel">
                    <mat-error *ngIf="amt.invalid"  class="pt15">
                        <div *ngIf="amt.hasError('pattern') ">
                            Please Enter Valid Amount
                        </div>
                    </mat-error>
                </mat-form-field>
            </div>
        </div>
        <div fxFlex class="pl20">
            <span class="heading">Job Type:</span>
        </div>
        <!-- <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
            <ng-container *ngFor="let val of data.jobType;let j=index">
                <mat-checkbox color="primary" name="jobType" [(ngModel)]="val.checked" #jobType="ngModel"
                    labelPosition="before" value="val.checked">
                    {{val.name}}
                </mat-checkbox>
            </ng-container>
        </div> -->
        <div fxLayout="column" fxFlex class="pl20">
            <div  fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <ng-container *ngFor="let val of data.jobType;let j=index">
                    <mat-checkbox color="primary" name="jobType" [(ngModel)]="val.checked" #jobType="ngModel"
                        labelPosition="before" value="val.checked">
                        {{val.name}}
                    </mat-checkbox>
                </ng-container>
            </div>
        </div>
        <div fxFlex class="pl20">
            <span class="heading">Vehicle Type:</span>
        </div>
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
            <ng-container *ngFor="let val of data.vehicleType;let j=index">
                <mat-checkbox color="primary" name="vehicleType" [(ngModel)]="val.checked" #vehicleType="ngModel"
                    labelPosition="before" value="val.checked">
                    {{val.name}}
                </mat-checkbox>
            </ng-container>
        </div>
        <div fxFlex class="pl20">
            <span class="heading">Fare Type:</span>
        </div>
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
            <ng-container *ngFor="let val of data.fareType;let j=index">
                <mat-checkbox color="primary" name="fareType" [(ngModel)]="val.checked" #fareType="ngModel"
                    labelPosition="before" value="val.checked">
                    {{val.name}}
                </mat-checkbox>
            </ng-container>
        </div>
    </div>
</div>