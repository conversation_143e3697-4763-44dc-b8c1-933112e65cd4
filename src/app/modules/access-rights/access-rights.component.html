<div fxLayout="column" fxLayoutGap="10px" class="main-content" style="height: inherit;">
	<!-- <div fxLayout="row" fxHide.lt-md> -->
	<div fxLayout="row">
		<div fxFlex fxLayout="row" fxLayoutGap="15px">
			<div class="cursor-pointer">
				<mat-icon (click)="goToPrevPage()">arrow_back</mat-icon>
			</div>
			<div>
				<span class="title">Customize Access Rights</span>
			</div>
		</div>
		<div fxFlex fxLayout="row" fxLayout.lt-md="column" fxLayoutGap="10px" fxLayoutAlign="end center" fxHide.lt-md>
			<div *ngIf="cdgService.showRightsMatrix">
				<button mat-raised-button class="btn-apply" (click)="apply()">APPLY</button>
				<!-- [disabled]="isApplyDisable" -->
			</div>
		</div>
	</div>
	<div>
		<span>Customize access rights for certain IDs</span>
	</div>
	<div>
		<form [formGroup]="accessform">
			<mat-form-field appearance="outline" class="inp-width">

				<input id="accessId" matInput formControlName="accessId" placeholder="Enter Email" required>
				<mat-icon matSuffix (click)="onSubmit()" class="cursor-pointer">arrow_forward</mat-icon>
				<!-- style="background-color:white;color:black" formControlName="username" type="email"  -->
				<mat-error *ngIf="accessform.controls.accessId.hasError('required')">
					Please Enter Email
				</mat-error>
			</mat-form-field>
		</form>
	</div>

	<div *ngIf="cdgService.showRightsMatrix">
		<div class="mb20" fxLayout="row" fxLayout.xs="column" fxLayoutGap="10px">
			<div fxFlex fxLayoutGap="10px" fxLayout="row" fxLayoutAlign="start center" fxFlexOrder.gt-xs="1" fxFlexOrder.xs="2">
				<span class="id-style">{{this.cdgService.rightsId}}</span>
				<mat-chip-grid>
					<mat-chip>{{status}}</mat-chip>
				</mat-chip-grid>
			</div>
			<div fxFlex fxLayoutAlign="end center" fxHide.gt-md fxHide.md fxFlexOrder.gt-xs="2" fxFlexOrder.xs="1">
					<button mat-raised-button class="btn-apply" (click)="apply()">APPLY</button>
			</div>


		</div>
		<!-- <div fxLayout="row" fxHide.gt-md fxHide.md>
			<mat-form-field appearance="fill" class="mat">
				<mat-select [(ngModel)]="selectedAcl" name="aclSelect" (ngModelChange)="getDisplayColumn()">
					<mat-option *ngFor="let acl of aclSelect" [value]="acl.value">
						{{acl.viewValue}}
					</mat-option>
				</mat-select>
			</mat-form-field>
		</div> -->
		<div style="overflow-x: auto;">
			<table>
				<tr>
					<th scope="col">FEATURE</th>
					<ng-container *ngFor="let column of dataSource;let i=index">
						<th scope="col">{{column.role.roleDesc}}</th>
					</ng-container>
				</tr>
				<ng-container *ngFor="let header of displayRowHeader;let j=index">
					<tr>
						<td scope="col">{{header}}</td>
						<ng-container *ngFor="let column of dataSource;let l=index">
	
							<td scope="col"> 
								<mat-checkbox name="master" color="primary" [checked]="getCellDetails(column.role,header).isMenuActive" (change)="getCellDetails(column.role,header,$event)"></mat-checkbox>	
							</td>
						</ng-container>
					</tr>
				</ng-container>
			</table>
		</div>
	</div>
</div>