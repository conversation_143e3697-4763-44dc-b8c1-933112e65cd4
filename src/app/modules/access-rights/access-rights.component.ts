import { Component, OnInit, HostListener } from '@angular/core';
import { Location } from '@angular/common'
import { NgForm, FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms'
import { NotificationService } from './../../shared/services/notification.service'
import { CdgSharedService } from './../../shared/services/cdg-shared.service'
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { Router } from '@angular/router';

@Component({
  selector: 'app-access-rights',
  templateUrl: './access-rights.component.html',
  styleUrls: ['./access-rights.component.scss']
})
export class AccessRightsComponent implements OnInit {
  showMatrix: boolean = false;
  dataSource: any[] = [];
  status: any = '';
  accessform:FormGroup;
  public id: any;
  public isWebLayout = false;
  public isMobileLayout = false;
  public innerWidth: any;
  public isAllValTureList: any;
  displayedColumns: string[] = [];
  selectedAcl = 'master';
  selectAll: boolean = false;
  isApplyDisable: boolean = false;
  displayRowHeader: any[] = [];
  userDetails: any;
  constructor(private router: Router, private location: Location, public cdgService: CdgSharedService, private fb: FormBuilder, private notifyService: NotificationService, private apiService: ApiServiceService) {
    this.accessform= this.fb.group({
      accessId: ['', [Validators.required]],
    });
   }

  ngOnInit(): void {
    this.cdgService.showRightsMatrix = false;
    // this.cdgService.showMatrix = true;
    // this.cdgService.showAccessIdDetails = true;
    console.log(this.cdgService.rightsId, this.cdgService.isDataFromAccessId)
    if (this.cdgService.rightsId) {
      this.accessform.get('accessId')?.setValue(this.cdgService.rightsId)
      this.onSubmit();
    }

    // this.innerWidth = window.innerWidth;
    // if (this.cdgService.rightsDataSource && this.cdgService.rightsId) {
    //   this.innerWidth = window.innerWidth;
    //     if (this.innerWidth < 959) {
    //       this.isWebLayout = false;
    //       this.isMobileLayout = true;
    //       if (this.selectedAcl === 'master') {
    //         this.displayedColumns = ['feature', 'master'];
    //         this.isAllValTureList =  this.maxtrixList.find(obj => obj.accessId === this.accessform.value.accessId)?.accesses.filter(x=>x.master === false)
    //         if (this.isAllValTureList.length > 0) {
    //           this.selectAll = false;
    //         }
    //         else {
    //           this.selectAll = true;
    //         }
    //       }
    //     }
    //     else {
    //       this.isWebLayout = true;
    //       this.isMobileLayout = false;
    //       this.displayedColumns = ['feature', 'master', 'individual', 'admin', 'personal', 'subapp']
    //     }

    // // this.cdgService.rightsId=this.accessform.value.accessId;
    //   // this.cdgService.showRightsMatrix = true;
    // }
    // else {
    //   this.cdgService.showRightsMatrix = false;
    // }
  }
  // @HostListener('window:resize', ['$event'])
  // onResize(event: any) {
  //   this.innerWidth = window.innerWidth;
  //   if (this.innerWidth < 959) { // 768px portrait
  //     this.isWebLayout = false;
  //     this.isMobileLayout = true;
  //     if (this.selectedAcl === 'master') {
  //       this.displayedColumns = ['feature', 'master'];
  //       this.isAllValTureList = this.maxtrixList.find(obj => obj.accessId === this.accessform.value.accessId)?.accesses.filter(obj => obj['master'] === false);
  //       if (this.isAllValTureList.length > 0) {
  //         this.selectAll = false;
  //       }
  //       else {
  //         this.selectAll = true;
  //       }
  //     }
  //   }
  //   else {
  //     this.isWebLayout = true;
  //     this.isMobileLayout = false;
  //     this.displayedColumns = ['feature', 'master', 'individual', 'admin', 'personal', 'subapp']
  //   }

  // }
  // toggle() {
  //   this.maxtrixList.find(obj => obj.accessId === this.accessform.value.accessId)?.accesses.forEach(y => {
  //     this.getValForSelection('toggle', y)
  //   })
  // }
  getDisplayColumn() {
    if (this.isAllValTureList.length > 0) {
      this.selectAll = false;
    }
    else {
      this.selectAll = true;
    }
  }
  getCellDetails(data: any, header: any, menuActive?: any) {
    let selectedVal: any
    this.dataSource.forEach((element: any) => {
      if (data.roleDesc === element.role.roleDesc) {
        element.featureList.forEach((value: any) => {
          if (value.menuName.includes(header)) {
            if (menuActive) {
              value.isMenuActive = menuActive.checked;
            }
            else {
              selectedVal = value;
            }
          }
        });
      }
    });
    if (selectedVal) {
      return selectedVal
    }
  }
  onSubmit() {
    this.cdgService.showRightsMatrix = false;
    if (this.accessform.invalid) {
      return;
    }
    else {
      this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_CUSTOM_ACL + this.accessform.value.accessId).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body.customAclList;
          console.log(opt)
          this.dataSource=[]
          this.displayedColumns=[]
          this.displayRowHeader=[]
          this.cdgService.rightsId = this.accessform.value.accessId;
          this.displayedColumns.push('Features')
          opt.forEach((val: any, i: number) => {
            this.displayedColumns.push(val.role.roleDesc)
          });
          let data: any[] = []
          opt[0].featureList.forEach((element: any) => {
            data.push(element.mainMenuDetails);
          });
          let mainMenu = data.reduce((a, c) => {
            if (!a.some((item: any) => item.menuId === c.menuId)) {
              a.push(c)
            }
            return a;
          }, []);
          mainMenu.sort((a: any, b: any) => {
            return a.menuDisplayOrder - b.menuDisplayOrder
          })
          mainMenu.forEach((element: any) => {
            const datas = opt[0].featureList.filter((o: any) => o.mainMenuDetails.menuId === element.menuId)
            datas.sort((a: any, b: any) => {
              return a.menuDisplayOrder - b.menuDisplayOrder
            })
            if (datas.length > 0) {
              datas.forEach((subMenu: any) => {
                this.displayRowHeader.push(subMenu.menuName)
              });
            }
          });
          this.dataSource = opt;
          this.userDetails = response.body.user
          this.status = this.userDetails.isActive ? 'Active' : 'Deactive';
          this.cdgService.showRightsMatrix = true;
        }
      },
        e => {
          // console.log(e)
          this.notifyService.showError(e.error.message, "Error")
        })
    }
    // this.cdgService.rightsDataSource = this.maxtrixList.find(obj => obj.accessId === this.accessform.value.accessId)?.accesses
    // if (this.cdgService.rightsDataSource) {
    //   this.innerWidth = window.innerWidth;
    //     if (this.innerWidth < 959) {
    //       this.isWebLayout = false;
    //       this.isMobileLayout = true;
    //       if (this.selectedAcl === 'master') {
    //         this.displayedColumns = ['feature', 'master'];
    //         this.isAllValTureList =  this.maxtrixList.find(obj => obj.accessId === this.accessform.value.accessId)?.accesses.filter(x=>x.master === false)
    //         if (this.isAllValTureList.length > 0) {
    //           this.selectAll = false;
    //         }
    //         else {
    //           this.selectAll = true;
    //         }
    //       }
    //     }
    //     else {
    //       this.isWebLayout = true;
    //       this.isMobileLayout = false;
    //       this.displayedColumns = ['feature', 'master', 'individual', 'admin', 'personal', 'subapp']
    //     }

    // this.cdgService.rightsId=this.accessform.value.accessId;
    //   this.cdgService.showRightsMatrix = true;
    // }
    // else {
    // }
  }
  goToPrevPage() {
    // this.location.back();
    if (this.cdgService.isDataFromAccessId) {
      this.router.navigateByUrl('/layout/Accessid');
    }
    else {
      this.router.navigateByUrl('/layout/accesscontrol');
    }

  }
  apply() {
    this.isApplyDisable = true;
    let obj = {
      "customAclList": this.dataSource,
      "user": this.userDetails
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.UPDATE_CUSTOM_ACL, obj).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body;
        if (opt.message) {
          this.notifyService.showSuccess(opt.message, 'Success');
        }
      }
    },
      e => {
        console.log(e)
      })

  }
}
