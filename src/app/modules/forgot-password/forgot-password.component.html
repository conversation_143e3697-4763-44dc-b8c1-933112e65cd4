<div fxLayout="column" class="container frt-container bg">
	<div *ngIf="showMsgDiv" class="box" [ngStyle]="{'background-color': popupObj.title == 'Success' ? '#28a745' :  '#ff0000'}">
		<div  fxLayout="column" class="p5">
			<p class="head">{{popupObj.title}}</p>
			<p class="msg">{{popupObj.msg}}</p>
			<div fxFlex fxLayoutAlign="end center" *ngIf="popupObj.title == 'Success'">

				<button mat-raised-button class="ok-btn" [ngStyle]="{'background-color': popupObj.title == 'Success' ? '#DA6C2A' :  ' #808080'}" (click)="routeToLogin()">OK</button>
			</div>
			<div fxFlex fxLayoutAlign="end center" *ngIf="popupObj.title != 'Success'">
				
				<button mat-raised-button class="ok-btn" [ngStyle]="{'background-color': popupObj.title == 'Success' ? '#DA6C2A' :  ' #808080'}" (click)="routeNotToLogin()">OK</button>
			</div>
		</div>
	</div>
	<div fxFlex fxLayoutAlign="center center" >
		<div class="login-div" fxLayout="column" fxLayoutGap="20px">
			<!-- <div fxLayout="row" fxLayoutGap="14px" fxLayoutAlign="center center">
				<img src="/assets/images/Cabcharge_logo_new.png" width="150px" alt=""> -->
				<!-- <hr class="dividerd">
				<p class="heading">Sign In</p> -->
			<!-- </div> -->
			<div fxLayout="row" fxLayoutGap="14px" fxLayoutAlign="start center" >
				<img src="/assets/images/Cabcharge_white.png"  width="84px" alt="">
				<hr class="dividerd">
				<p class="heading">Sign In</p>
			</div>

			<div class="form-div">
				<form [formGroup]="forgotPwdForm" class="form">
					<div fxFlex style="margin-bottom: 20px;">
						<span class="header pb5">Forgot Password?</span>
					</div>
					<div fxFlex style="margin-bottom: 20px;">
						<p class="text-cls">Enter your email address below to get a reset link</p>
					</div>
					<div fxFlex>
						<!-- <mat-label fxFlex class="text-cls font-16"> Email </mat-label> -->
						<mat-form-field appearance="outline" class="inp">

							<input matInput required formControlName="email" style="background-color:white;color:black" placeholder="Email Address">
							<mat-error *ngIf="submitted && forgotPwdForm.controls.email.hasError('required')">
								Email Is Required
							</mat-error>
							<mat-error *ngIf="submitted && forgotPwdForm.controls.email.hasError('email')">
								Email Must Be A Valid Email Address
							</mat-error>
						</mat-form-field>
					</div>
				
					<div fxFlex fxLayoutAlign="center center">
						<button mat-raised-button class="pwd-submit-btn" (click)="onSubmit()">SUBMIT</button>
					</div>
					<div fxLayout="column" fxLayoutAlign="center center" (click)="goToLogin()" class="pb10"><u
                        class="cursor-pointer res">Go Back To Login?</u></div>
				</form>
			</div>
		</div>
	</div>


</div>