import { Component, OnInit, HostListener } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { throwError } from 'rxjs';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss']
})
export class ForgotPasswordComponent implements OnInit {
  public isWebLayout = false;
  public isTabletLayout = false;
  public innerWidth: any;
  submitted = false;
  popupObj = {
    title: "",
    msg: ""
  }
  forgotPwdForm:FormGroup
  showMsgDiv: boolean = false;
  constructor(public cdgService : CdgSharedService,public localStorage: LocalStorageService,private formBuilder: FormBuilder, private apiService: ApiServiceService, private router: Router, private notifyService: NotificationService) { 
    this.forgotPwdForm= this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  ngOnInit(): void {
    this.localStorage.localStorageSet("isLoginSuccessfull",false);
    this.innerWidth = window.innerWidth;
    if (this.innerWidth < 1280) { // 768px portrait
      this.isWebLayout = false;
      this.isTabletLayout = true;
    }
    else {
      this.isWebLayout = true;
      this.isTabletLayout = false;

    }
  }
  get f() {
    return this.forgotPwdForm.controls;
  }
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth < 1280) { // 768px portrait
      this.isWebLayout = false;
      this.isTabletLayout = true;
    }
    else {
      this.isWebLayout = true;
      this.isTabletLayout = false;
    }

  }
  routeToLogin() {
    this.showMsgDiv = false;
    this.router.navigate(['/login'])
  }
  routeNotToLogin(){
    this.showMsgDiv = false;
  }
  goToLogin(){
    this.router.navigate(['/login'])
  }
  onSubmit() {
    this.submitted = true;
    if (this.forgotPwdForm.invalid) {
      return;
    }
    else {
      this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2Apikey + UrlConstants.FORGOT_PASSWORD + this.forgotPwdForm.value.email).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body;
          if (opt.message) {
            this.popupObj.title = "Success";
            this.popupObj.msg = opt.message;
            this.showMsgDiv = true
          }

        }
      },
        e => {
          if (e.error.message) {
            // this.popupObj.title = "error";
            //VAPT: show error in green 
            this.popupObj.title = "Success";
            this.popupObj.msg = e.error.message;
            this.showMsgDiv = true
          }
        })
    }

  }

}
