<div fxLayout="column" fxLayoutGap="25px">
    <div fxLayout="column">
        <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
            <div fxFlex="5%">
                <mat-icon class="material-icons-outlined main-icon">assignment_turned_in</mat-icon>
            </div>
            <div fxFlex fxLayoutAlign="start center">
                <span class="header pb5">Top-up History</span>
            </div>
        </div>
      
    </div>
    <form [formGroup]="topUpForm" class="mb-0">
        <div fxLayout="column" fxLayoutGap="20px" class="custom-padding-left">
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px"  >
                <div fxFlex="12%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
                    <span class="date-title">PRODUCT TYPE <span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxFlex="88%" fxFlex.lt-md="65%" fxFlex.md="75%">
                    <mat-form-field appearance="outline" class="inp">
                        <mat-select  name="productType" formControlName="productType" (selectionChange)="getDetails($event)" placeholder="Select Product Type"
                            [disabled]="isProductDisable" required>
                           
                            <mat-option *ngFor="let acl of productArr" [value]="acl.productId">
                                {{acl.productName}}
                            </mat-option>
                        </mat-select>
                        <mat-error *ngIf="submitted && topUpForm.controls.productType.hasError('required')" class="pt15">
                            Product Type Cannot Be Blank
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
            <div fxLayout="column" fxLayoutGap="30px" *ngIf="isCardNoShow">
                <mat-radio-group name="radioValue" formControlName="radioValue" (change)="radiocard($event)" fxLayoutGap="30px" >
                    <mat-radio-button *ngFor="let val of radioArr" color="primary" [value]="val.value"
                        [disabled]="val.isDisable" labelPosition="before">
                        {{val.viewValue}}
                    </mat-radio-button>
                    <mat-error *ngIf="this.carderrorrad" class="pt15 si">
                        Please select any option
                      </mat-error>
                </mat-radio-group>
                
                
                <div *ngIf="cardshow" >
                   
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px">
                        <div fxFlex="12%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
                            <span class="date-title">CARD NO.<span class="asterisk"><sup>*</sup></span>:</span>
                        </div>
        
        
        
                        <div fxFlex="88%" fxFlex.lt-md="65%" fxFlex.md="75%" fxLayout="row" fxLayoutAlign="start center" *ngIf="!showerror"> 
                            <span class="pb10" *ngIf="!hideCardField">{{this.displaycard}} </span>
                          
                           
                        
                            <mat-form-field appearance="outline" class="inp" *ngIf="!showerror">      
                                <mat-select [formControl]="cardNoSelectedList"  name="cardNoSelectedList"  required  (selectionChange)="carderror($event)" multiple>
                                    <mat-option *ngFor="let val of lastdigit"  [value]="val">{{val}}</mat-option>
                                </mat-select> 
                                <mat-error *ngIf="submitted && topUpForm.controls.cardNoSelectedList.hasError('required')" class="pt15">
                                    Card No Cannot Be Blank
                                </mat-error> 
                            </mat-form-field>
                                <!-- <input matInput type="text" pattern="[0-9]*"
                                    [(ngModel)]="data.cardNo"  name="cardNo" formControlName="cardNo" (click)="fuzzysearch()" (ngModelChange)="keyPress($event)" onkeypress="return event.charCode >= 48 && event.charCode <= 57" [matAutocomplete]="auto"
                                    required />
                                <mat-autocomplete #auto="matAutocomplete">
                                    <mat-option *ngFor="let acl of cardArr" [value]="acl">
                                        {{acl}}
                                    </mat-option>
                                </mat-autocomplete> -->
                             
                                <!-- <div fxLayout="row">
                                    <div fxFlex="30%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                                       
                                    </div>
                                    <div fxFlex="70%" fxFlex.lt-md="65%" fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="start center">
                                        <mat-error class="error" *ngIf="showcarderror && !showerror" fxLayoutAlign="start start">
                                            Card No cannot be blank
                                        </mat-error>
                                      
                                    </div>
                                </div> -->
                               
                            
                        </div>
                        <mat-error *ngIf="showerror" class="pt15">
                            No Card Details Found
                          </mat-error>
                    </div>
                   
                </div>
               
                <!-- <div *ngIf="topupForm.radioValue == 'displayAll'">
                    displayAll
                </div> -->
                   
               
            </div>
            
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px">
                <div fxFlex="12%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
                    <span class="date-title">DOWNLOAD BY:</span>
                </div>
                <div fxFlex="88%" fxFlex.lt-md="65%" fxFlex.md="75%">
                    <mat-form-field appearance="outline" class="inp">
                        <mat-select placeholder="PLEASE SELECT ONE" formControlName="salutation" (selectionChange)="getdownloadby($event)">
                          <mat-option *ngFor = "let item of salutation;" [value]="item.viewValue" >{{item.viewValue}}</mat-option>
                        </mat-select>
                        <!-- <mat-error *ngIf="submitted && enquiryForm.controls.salutation.hasError('required')">
                            Please Select A Salutation
                        </mat-error> -->
                      </mat-form-field>
                     
                </div>
            </div>
            <div fxLayout="column" >
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px">
                    <!-- <div fxFlex fxLayout="row"> -->
                        <div fxFlex="12%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
                          
                            <span class="date-title">REQUEST DATE <span class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxLayout="row" fxLayout.lt-md="column" fxFlex="69%" fxFlex.lt-md="65%" fxFlex.md="75%" fxLayoutGap="10px">
                            <mat-form-field  appearance="outline" class="inp"  >
                                <input matInput [matDatepicker]="picker1" name="startDate" formControlName="startDate"
                                    (dateChange)="daysDifference()">
                                <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                                <mat-datepicker #picker1></mat-datepicker>
                                <mat-error *ngIf="submitted && topUpForm.controls.startDate.hasError('required')" class="pt15">
                                 Start Date Cannot Be Blank
                                </mat-error>
                            </mat-form-field>
                            <span class="date-title pt10">TO<span class="asterisk"><sup>*</sup></span>:</span>
                            <mat-form-field appearance="outline" class="inp">
                                <input matInput [matDatepicker]="picker2" name="endDate" formControlName="endDate"
                                    (dateChange)="daysDifference()">
                                <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                                <mat-datepicker #picker2></mat-datepicker>
                                <mat-error *ngIf="submitted && topUpForm.controls.endDate.hasError('required')"
                                    class="pt15">
                                     End Date Cannot Be Blank
                                </mat-error>
                            </mat-form-field>
                            
                            <div *ngIf="showDateError" class="error">
                                Date Range Must Not Be More Than 31 Days
                            </div>
                            <div *ngIf="showDateRangeError && !showDateError"  class="error">
                                End Date should be greater than start Date
                            </div>
                        </div>
                    <!-- </div> -->
                    <!-- <div fxFlex fxLayout="row">
                        
                            <div fxFlex="31%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
                               
                            </div>
                            <div fxFlex="69%" fxFlex.lt-md="65%" fxFlex.md="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput [matDatepicker]="picker2" name="endDate" formControlName="endDate"
                                        (dateChange)="daysDifference()">
                                    <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                                    <mat-datepicker #picker2></mat-datepicker>
                                    <mat-error *ngIf="submitted && topUpForm.controls.endDate.hasError('required')"
                                        class="pt15">
                                         End Date Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        

                    </div> -->

                </div>
               
            </div>
            <div fxFlex fxLayoutGap="30px">
                <button mat-raised-button class="search-btn" [disabled]="disablesubmit" (click)="onSearch()">SUBMIT</button>
                <button mat-raised-button class="reset-btn" (click)="onReset()">RESET</button>
                <button mat-raised-button class="download-btn" [disabled]="disablesubmit" *ngIf="pdfboolean"  (click)="download()">DOWNLOAD PDF</button>
                <button mat-raised-button class="download-btn" [disabled]="disablesubmit" *ngIf="excelboolean"  (click)="downloadexcel()">DOWNLOAD EXCEL</button>
            </div>
        </div>
    </form>

    <!-- Desktop view like other components -->
    <div class="tbl-container pt15 pl20 pr20" *ngIf="showTable" >
        <mat-table #table class="mat-table mat-cdk-table" [dataSource]="dataSource" matSort>


             <!-- Serial No. Column  -->
            <!-- <ng-container matColumnDef="serialNo" class="pr15">
                <mat-header-cell *matHeaderCellDef class="w20 pr"> S No. </mat-header-cell>
                <mat-cell *matCellDef="let element, let i = index;" class="sno" fxLayoutAlign="start center">
                 <div class="fwb font-11 pr14 fo" > {{i + 1}} </div>   
                </mat-cell>
            </ng-container>  -->
            <ng-container matColumnDef="serialNo">
                <mat-header-cell *matHeaderCellDef> S No. </mat-header-cell>
                <mat-cell *matCellDef="let element;let i=index"> {{i+1}} </mat-cell>
                <mat-footer-cell *matFooterCellDef class="fwb font-11 pr14 fo" > </mat-footer-cell>
            </ng-container>

            <!-- Card No. Column -->
            
            <ng-container matColumnDef="cardNumber">
                <mat-header-cell *matHeaderCellDef> Card No </mat-header-cell>
                <mat-cell *matCellDef="let element" class="fwb font-11 pr14 foo" >  {{element.cardNumber }} </mat-cell>
                <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
            </ng-container>


            <!-- Name on Card Column -->
            <!-- <ng-container matColumnDef="nameOnCard" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Name on Card</mat-header-cell>
                <mat-cell *matCellDef="let element"> 
                    <span class="mobile-label"> </span>
                    {{element.nameOnCard}}
                </mat-cell>
            </ng-container> -->
            <ng-container matColumnDef="nameOnCard">
                <mat-header-cell *matHeaderCellDef> Name on Card </mat-header-cell>
                <mat-cell *matCellDef="let element" class="fwb font-11 pr14 foo" >  {{element.nameOnCard }} </mat-cell>
                <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
            </ng-container>

            <!-- Product Name Column -->
            <!-- <ng-container matColumnDef="productName" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Product Name</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label">Product Name</span>
                     {{element.productName}}
                </mat-cell>
            </ng-container> -->
            <ng-container matColumnDef="productName">
                <mat-header-cell *matHeaderCellDef> Product Name </mat-header-cell>
                <mat-cell *matCellDef="let element" class="fwb font-11 pr14 fo" >  {{element.productName }} </mat-cell>
                <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
            </ng-container>

            <!--  Request Date Column -->
             <!-- <ng-container matColumnDef="requestDate" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Request Date</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label">Request Date</span>
                   
                     <div class="word-break width80">
                        {{element.requestDate}}
                      </div>
                </mat-cell>
            </ng-container> -->
            <ng-container matColumnDef="requestDate">
                <mat-header-cell *matHeaderCellDef> Request Date </mat-header-cell>
                <mat-cell *matCellDef="let element" class="fwb font-11 pr14 fo" >  {{element.requestDate }} </mat-cell>
                <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
            </ng-container>
            <!-- <ng-container matColumnDef="requestor" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16 pr15" mat-sort-header>Requester</mat-header-cell>
                <mat-cell *matCellDef="let element" >
                    <span class="mobile-label">Requester</span>
                    <div class="word-break width80">
                        {{element.requestor}}
                      </div>
                    
                </mat-cell>
            </ng-container> -->
            <ng-container matColumnDef="requestor">
                <mat-header-cell *matHeaderCellDef>Requester</mat-header-cell>
                <mat-cell *matCellDef="let element" class="fwb font-11 pr14 fo" >  {{element.requestor }} </mat-cell>
                <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
            </ng-container>

            <!-- Channel -->
            <!-- <ng-container matColumnDef="channel" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Channel</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label ">Channel</span>
                     <div class="word-break width80">
                        {{element.channel}}
                      </div>
                </mat-cell>
            </ng-container> -->
            <ng-container matColumnDef="channel">
                <mat-header-cell *matHeaderCellDef>Channel</mat-header-cell>
                <mat-cell *matCellDef="let element" class="fwb font-11 pr14 fo" >  {{element.channel }} </mat-cell>
                <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
            </ng-container>

             <!-- Top up Amount -->
            <!-- <ng-container matColumnDef="topupAmount" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Top up Amount</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label">Top up Amount</span>
                     <div class="word-break width80">
                        {{element.topupAmount}}
                      </div>
                </mat-cell>
            </ng-container> -->
            <ng-container matColumnDef="topupAmount">
                <mat-header-cell *matHeaderCellDef>Top up Amount</mat-header-cell>
                <mat-cell *matCellDef="let element" class="fwb font-11 pr14 fo" >  {{element.topupAmount }} </mat-cell>
                <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
            </ng-container>

            <mat-header-row *matHeaderRowDef="displayedColumns" ></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns;" [ngStyle]= "{'background-color': row.colorHex ? row.colorHex : '#cccccc'}"></mat-row>
          
        </mat-table>
        <tr class="mat-row"  *ngIf="dataSource.data.length == 0" fxLayoutAlign="center center">
            <td class="mat-cell" colspan="5" >No Records are  Found</td>
          </tr>
        <mat-paginator class="paginator" [pageSizeOptions]="[5, 10, 20, 50, 100]"
            showFirstLastButtons></mat-paginator>
    </div> 



   

</div>



