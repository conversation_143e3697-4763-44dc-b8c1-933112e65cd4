import { Component, OnInit, ViewChild } from '@angular/core';
import { Validators, FormBuilder,FormControl, FormGroup } from '@angular/forms';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as _moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { throwError } from 'rxjs';
import { ExportxlsxService } from 'src/app/shared/services/exportxlsx.service';
import { DatePipe } from '@angular/common';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { Router } from '@angular/router';

import { Observable } from 'rxjs';
import { RoleConstants } from 'src/assets/url-constants/url-constants';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { FormGroupDirective } from '@angular/forms';
import { saveAs } from 'file-saver'

const moment = _moment;

export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },

  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
}

export interface TopUpData {
  cardNo: number,
  jobNo: number,
  taxiNo: string,
  pickUpAddress: string,
  destination: string,
  tripStartDate: string,
  tripEndDate: string,
  fareAmt: number,
  txnStatus: string
}
const DATA: TopUpData[] = [];
@Component({
  selector: 'app-top-up-history',
  templateUrl: './top-up-history.component.html',
  styleUrls: ['./top-up-history.component.scss'],
  providers: [DatePipe, { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] }, { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]
})
export class TopUpHistoryComponent implements OnInit {

 
  public targetElement: HTMLElement;
  public menuIconName = 'view_headline';
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(FormGroupDirective) formGroupDirective: FormGroupDirective;
  @ViewChild(MatSort) sort: MatSort;
  data = {

    cardNo: ""
  }
  displayedColumns: string[] = ['serialNo', 'cardNumber', 'nameOnCard', 'productName', 'requestDate', 'requestor', 'channel', 'topupAmount'];
  dataSource = new MatTableDataSource(DATA);
  dataSourceCell: Observable<any>
  showTable: boolean = false;
  showcarderror: boolean = false;
  isCardNoShow = false;
  isShowAddBtn =false;
  cardarray:any;
  showPaginator: boolean = false;
  carderrorrad:boolean =false;
  cardshow:boolean =false;
  showerror:boolean=false;
  isProductDisable: boolean = false;
  excelboolean:boolean=false;
pdfboolean:boolean =false;
  disablesubmit: boolean = false;
  innerWidth: any;
  public isMobileLayout = false
  // productArr: any = [];
  submitted: boolean = false;
  showDateRangeError: boolean = false;
  lastfuzz: any = [];
  
  topUpForm:FormGroup
  
  showDateError: boolean = false;
  salutation = [
    { viewValue: 'DOWNLOAD BY EXCEL' },
    { viewValue: 'DOWNLOAD BY PDF' }
    
  ]
  // cardArr: any = [];
  radioArr = [
    { value: "cardNo", viewValue: "CARD NUMBER", isDisable: false },
    { value: "displayAll", viewValue: "DISPLAY ALL", isDisable: false }
  
  ]
  cardNoSelectedList = new FormControl();

  isShowError: boolean = false;
  firstdigit: any;
  lastdigit: any = [];
  fullcardno:any[]=[];
  productArr:any[]=[];
  cardArr : any;
  list: any;
  value: any;
  displaycard: any;
  firstProductSelected: any;
  accountnumber: any;
  hideCardField: boolean;
  showErr: boolean = false;
  customerName: any;
  customernumber: any;
  accountName: any;
  productname: any;
  productId: any;
  fullcardnoo: any[];
  constructor(public localStorage: LocalStorageService, private datePipe: DatePipe, private excelService: ExportxlsxService, private fb: FormBuilder, public cdgService: CdgSharedService, private notifyService: NotificationService, private router: Router, private apiService: ApiServiceService) {
    this.topUpForm = this.fb.group({
      productType: ['', [Validators.required]],
      cardNoSelectedList: ['', [Validators.required]],
      salutation:[''],
      radioValue: [''],
      startDate: [new Date(), [Validators.required]],
      endDate: [new Date(), [Validators.required]]
    })
  }

  ngOnInit(): void {
    this.topUpForm.reset()
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        if(this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0]){
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
        this.customerName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name
        }
      }
      if (this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails) {
        this.customernumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number;
        this.customerName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name
  
      }
      else {
        console.log(this.localStorage.localStorageGet('customerviewvalue'), this.localStorage.localStorageGet('viewvalueonload'));
  
        if (this.localStorage.localStorageGet('customerviewvalue') === null) {
          this.customernumber = this.localStorage.localStorageGet('viewvalueonload').substr(0, 6);
          this.customerName = this.localStorage.localStorageGet('viewvalueonload').substr(9)
        }
        else {
          this.customernumber = this.localStorage.localStorageGet('customerviewvalue').substr(0, 6);
          this.customerName = this.localStorage.localStorageGet('customerviewvalue').substr(9)
        }
      }
    }
    this.accountName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails ? this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name : this.customerName;

    let obj={
      
      "accessId":  this.localStorage.localStorageGet("loginUserDetails").accessId,
      "accountNo": this.accountnumber,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
     
      
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/topupProductType', obj).subscribe((response: any) => {
      if (response.status === 200) {
        console.log(response)
        let opt = response.body;
        this.productArr=opt;
      }
       

    });

  }
  getdownloadby(val:any){
console.log(val)
if(val.value==='DOWNLOAD BY EXCEL'){
this.excelboolean=true;
this.pdfboolean=false;
}
else{
  this.excelboolean=false;
  this.pdfboolean=true;
}
  }
  getDetails(val: any) {
   
    this.displaycard="";
    this.lastdigit=[];
    this.cardArr="";
    this.isCardNoShow = true
    this.isShowAddBtn = true
    this.topUpForm.value.radioValue="cardNo"
   
    let obj={
      "accessId":  this.localStorage.localStorageGet("loginUserDetails").accessId,
      "accountNo": this.accountnumber,
      "productType": this.topUpForm.value.productType,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/getActiveCardList', obj).subscribe
    ((response: any) => {
      console.log(response)
      if (response.status === 200) {
         this.data = response.body;
         if(this.data.cardNo.length>0){
        this.cardArr=this.data.cardNo;
        this.cardarray=this.cardArr;
    
         }
         else{
           this.cardArr=[];
         }
        if(this.cardArr.length===0){
     
          this.showerror=true;
        
      }
        else{
          this.showerror=false;
          if(this.cardArr.length>0){
            this.hideCardField = false;
            this.firstdigit = String(this.cardArr[0]).substr(0, 10);
            this.displaycard = this.firstdigit.substr(0, 6) + "-" + this.firstdigit.substr(6, 4);
           
        
            this.cardArr.forEach((element: any) => {
              this.lastdigit.push((String(element).substr(-6)));
            });
          }
         
        }
      }
    
    });
  
  }
  radiocard(val:any){
    this.dataSource.data=[];
    this.showTable=false;
    this.carderrorrad=false;
 if(val.value==="cardNo"){
   this.cardshow=true;
 }
 else{
  this.cardshow=false; 
 }
  }
  onSelect(item: any) {
    this.displaycard = "";
    this.showcarderror = false;
    this.disablesubmit = false;
    this.topUpForm.value.cardNo=""
    this.data.cardNo=""
    if (item.value) {
    
      let cardnumberobj = {

        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
        "productType": item.value,
        "accountNo": this.accountnumber


      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_CARDLIST, cardnumberobj).subscribe((response: any) => {
        if (response.status === 200) {
          console.log(response)
          let opt = response.body.cardNo;
          if (response.body.cardNo.length >= 1) {
            if (item.value === 'OV') {
              this.hideCardField = true;
              this.list = response.body.cardNo;
              console.log(this.list)
            }
            else {
              this.hideCardField = false;
              this.list = response.body.cardNo;
              this.lastfuzz = [];
              this.list.forEach((element: any) => {
                this.lastfuzz.push((String(element).substr(-6)));
              });
              this.firstdigit = String(opt[0]).substr(0, 10);
              this.displaycard = this.firstdigit.substr(0, 6) + "-" + this.firstdigit.substr(6, 4);
            }
          }
          else {
            this.showcarderror = true;
            this.disablesubmit = true;
          }
          
        }
      });

    }
    else {
 
    }
  }
  fuzzysearch() {
    this.cardArr = [];
    if (this.topUpForm.value.productType !== "OV") {
      this.cardArr = this.lastfuzz;
    }
  }
  daysDifference() {
    this.showDateError = false;
    this.showTable=false;
    if (this.topUpForm.value.startDate !== null && this.topUpForm.value.endDate !== null) {
      let sDate: any = new Date(this.topUpForm.value.startDate);
      let eDate: any = new Date(this.topUpForm.value.endDate)
      console.log(this.topUpForm.value.startDate, this.topUpForm.value.endDate)
      const oneDay = 24 * 60 * 60 * 1000;
      const differenceDays = Math.round(Math.abs((sDate - eDate) / oneDay));
      if (differenceDays >= 31) {
        this.showDateError = true;
      }
    }
    else {
      this.showDateError = false;
    }
  }
  keyPress(event: any) {
    this.showTable=false;
    if(this.topUpForm.value.productType !== "OV"){

    this.cardArr = [];
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      "accountNo": this.accountnumber,
      "cardNoList": this.list,
      "cardNoLike": this.topUpForm.value.productType !== "OV" ? this.firstdigit + this.data.cardNo : this.topUpForm.value.cardNo
     
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.SEARCH_CARDLIST, obj).subscribe((response: any) => {
      if (response.status === 200) {
        console.log(response)
        let opt = response.body.cardNo;
        this.cardArr = [];
        opt.forEach((element: any) => {
         
            this.lastdigit.push((String(element).substr(-6)));
            this.cardArr = this.lastdigit;
         
        });
        this.lastdigit = [];
      }
    });
    }
  else{
    this.cardArr=[];
    this.cardArr = this.list.filter((o: any) => {
      if (o.toString().startsWith(this.topUpForm.value.cardNo.toString())) {
        return o;
      }
    });
  }
  }
  carderror(val:any){
    if(val){
    this.showcarderror=false;
    }
  }
  onSearch() {
    this.submitted = true;
    if(this.topUpForm.value.radioValue === null){
     this.carderrorrad=true;
    }
    else{
      this.carderrorrad=false;
    }
    // if(!this.cardNoSelectedList.value){
    //   this.showcarderror=true;
      
    //    }
    //    else{
    //      this.showcarderror=false; 
    //    }
  //   if (this.topUpForm.invalid) {
  //     this.notifyService.showWarning('Please fill Mandatory fields','Top up History')
  //     return;
  // }
  if (this.topUpForm.value.radioValue === 'displayAll'){
  if(this.cardArr.length===0){
    this.notifyService.showError('Card Details not Found','Error');
  }
}
   
    this.fullcardno=[];
    if(this.topUpForm.value.startDate && this.topUpForm.value.endDate){
    if ((this.topUpForm.value.startDate) > (this.topUpForm.value.endDate)) {
      this.showDateRangeError = true;
    }
  
    else {
      this.showDateRangeError = false;
      this.productArr.forEach((element:any)=>{
        if(this.topUpForm.value.productType===element.productId){
        this.productname=element.productName;
        this.productId= element.productId;
        }
            });
           
     // let listcard = this.cardArr.filter((o: any) => o === this.topUpForm.value.cardNo) this.cardNoSelectedList
    if(this.topUpForm.value.radioValue ==="cardNo"){
     if(this.cardNoSelectedList.value){
     this.cardNoSelectedList.value.forEach((element:any) => {
      this.fullcardno.push( this.firstdigit + element);
      });
    }
    }

     // if (listcard.length > 0) {
      let obj =   {
      //   "accessId":  this.localStorage.localStorageGet("loginUserDetails").accessId,
      //  "accountNo":  this.accountnumber,
      //  "accountName" :  "TATA 33FB6CF5FF CONSUAD052B6C",
      //  "customerNumber" :  this.customernumber,
      //  "productTypeId" : "C5",
      //  "role":  this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      //  "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      //    "cardNumber":  this.fullcardno,
      //   // "productTypeId": this.topUpForm.value.productId,
      //    "productTypeName":  this.productname,
      //    "requestEndDate": this.datePipe.transform(this.topUpForm.value.startDate,'dd/MM/yyyy'),
      //    "requestStartDate":this.datePipe.transform(this.topUpForm.value.endDate,'dd/MM/yyyy')
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
  "accountNo": this.accountnumber,
  "accountName" : "",
  "customerNumber" : this.customernumber,
  "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
  "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
    "cardNumber": this.topUpForm.value.radioValue ==="cardNo" ? this.fullcardno :this.data.cardNo,
    "productTypeId":  this.productId,
    "productTypeName":  this.productname,
    "requestEndDate": this.datePipe.transform(this.topUpForm.value.endDate,'dd/MM/yyyy'),
    "requestStartDate": this.datePipe.transform(this.topUpForm.value.startDate,'dd/MM/yyyy')
       
    
     }
     if( obj.cardNumber.length>0 && obj.productTypeId && obj.requestEndDate && obj.requestStartDate && this.topUpForm.value.radioValue){
      if (!this.showDateError) {
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS +'/cabcharge/onlineapp/retrieveTopupHistory', obj).subscribe((response: any) => {
          if (response.status === 200) {
            if(response.body.errorMessage!==null){
              this.notifyService.showInfo(response.body.errorMessage,'Top-up history')
            }
            else{
            console.log(response)
            let opt = response.body;
            this.dataSource.data= response.body.topupHistoryResponseList;
            this.showTable = true;
            console.log(DATA);
            this.showcarderror = false;
            }
          }
        });
      }
    }
      this.isShowError = true;
      this.submitted = true;
      setTimeout(() => this.dataSource.paginator = this.paginator);
      setTimeout(() => this.dataSource.sort = this.sort);
     this.dataSourceCell = this.dataSource.connect();
    
   

      
   // }
   
    }
  }
  }
 
  onReset() {
this.showDateError=false;
this.showDateRangeError=false;
    this.submitted = false;
    this.dataSource.data=[];
    this.pdfboolean=false;
    this.excelboolean=false;
    this.showTable = false;
    this.showcarderror = false;
    this.disablesubmit = false;
    this.isShowError = false;
    this.showErr = false;
    this.hideCardField = true;
    this.showDateError =false;
   this.cardshow=false;
   this.isCardNoShow=false;
    this.topUpForm.reset();
  this.cardNoSelectedList.reset();

  }

  download() {
    this.showcarderror = false;
    this.submitted = true;
    if(this.topUpForm.value.radioValue === null){
      this.carderrorrad=true;
     }
     else{
       this.carderrorrad=false;
     }
     if(this.topUpForm.value.startDate && this.topUpForm.value.endDate){
    if ((this.topUpForm.value.startDate) > (this.topUpForm.value.endDate)) {
      this.showDateRangeError = true;
    }
  }
    this.showErr = false;
    this.isShowError = true
   
          this.showDateRangeError = false;
      this.fullcardno=[];
   // let listcard = this.cardArr.filter((o: any) => o === this.topUpForm.value.cardNo)
   // if (listcard.length > 0) {
    this.productArr.forEach((element:any)=>{
if(this.topUpForm.value.productType===element.productId){
this.productname=element.productName;
this.productId= element.productId;
}
    });
    if(this.topUpForm.value.radioValue ==="cardNo"){
    if( this.cardNoSelectedList.value){
      this.cardNoSelectedList.value.forEach((element:any) => {
      this.fullcardno.push( this.firstdigit + element);
      });
    }
  }
  
      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
       "accountNo": this.accountnumber,
       "accountName" : this.accountName,
       "customerNumber" : this.customernumber,
       "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
       "roleId":this.localStorage.localStorageGet("loginUserDetails").roles.roleId ,
         "cardNumber": 
         this.topUpForm.value.radioValue ==="cardNo" ? this.fullcardno :this.data.cardNo,
         
         "productTypeId": this.productId,
         "productTypeName": this.productname,
         "requestEndDate": this.datePipe.transform(this.topUpForm.value.endDate,'dd/MM/yyyy'),
         "requestStartDate":this.datePipe.transform(this.topUpForm.value.startDate,'dd/MM/yyyy')
       
     }
      let mediaType = "application/pdf"
      if(obj.cardNumber && obj.productTypeId && obj.requestEndDate && obj.requestStartDate && this.topUpForm.value.radioValue){
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/retrieveTopupHistoryPDF', obj, { responseType: 'blob' }).subscribe(response => {
        let blob = new Blob([response], { type: mediaType });
        saveAs(blob, 'Top up History' + this.fullcardno)
      },
        e => { throwError(e) })
    }
     
 // }
  // else{
  //   this.notifyService.showWarning('Please enter valid CardNo', 'Warning')
  // }
    this.showcarderror = false;
  

  }

  downloadexcel() {
    this.showcarderror = false;
    this.submitted = true;
    if(this.topUpForm.value.radioValue === null){
      this.carderrorrad=true;
     }
     else{
       this.carderrorrad=false;
     }
     if(this.topUpForm.value.startDate && this.topUpForm.value.endDate){
    if ((this.topUpForm.value.startDate) > (this.topUpForm.value.endDate)) {
      this.showDateRangeError = true;
    }
  }
    this.showErr = false;
    this.isShowError = true
   
          this.showDateRangeError = false;
      this.fullcardno=[];
   // let listcard = this
    
    this.productArr.forEach((element:any)=>{
      if(this.topUpForm.value.productType===element.productId){
      this.productname=element.productName;
      this.productId= element.productId;
      }
          });
          if(this.topUpForm.value.radioValue ==="cardNo"){
          if( this.cardNoSelectedList.value){
            this.cardNoSelectedList.value.forEach((element:any) => {
            this.fullcardno.push( this.firstdigit + element);
            });
          }}
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
     "accountNo": this.accountnumber,
     "accountName" : this.accountName,
     "customerNumber" : this.customernumber,
     "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
     "roleId":this.localStorage.localStorageGet("loginUserDetails").roles.roleId ,
       "cardNumber": 
       this.topUpForm.value.radioValue ==="cardNo" ? this.fullcardno :this.data.cardNo,
       "productTypeId":  this.productId,
       "productTypeName":  this.productname,
       "requestEndDate": this.datePipe.transform(this.topUpForm.value.endDate,'dd/MM/yyyy'),
         "requestStartDate":this.datePipe.transform(this.topUpForm.value.startDate,'dd/MM/yyyy')
     
   }
   let mediaType = "application/vnd.ms-excel"
   if(obj.cardNumber && obj.productTypeId && obj.requestEndDate && obj.requestStartDate && this.topUpForm.value.radioValue){
   this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/retrieveTopupHistoryExcel', obj, { responseType: 'blob' }).subscribe(response => {
     let blob = new Blob([response], { type: mediaType });
     saveAs(blob, 'Top up History' + '.xlsx')
   },
     e => {
       throwError(e)
       this.notifyService.showError("Sorry, its a bad request ! Make sure input fields are not empty", 'Error');
     })
    }
  }
  
  
}
