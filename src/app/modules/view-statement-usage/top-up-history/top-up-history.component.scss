@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
.inp{
    width: 260px !important;
}
.tx{
    width: 240px !important;
}
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
::ng-deep .main-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
    height: 40px !important;
    width: 40px !important;
}
.update-btn{
    width:205px !important;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
}
.login-type{
    font-size: $cdgsz-font-size-prime;
    font-weight: $cdgsz-font-weight-normal;
    margin-bottom: 0px !important;
    // padding-top: 10px;
}
.error {
    color: $red;
    font-weight: $cdgsz-font-weight-bold;
    font-size: 12px;
    height: 12px;
    padding-left: 10px;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
 }
 /* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
 :host ::ng-deep .mat-wrap .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
 }
 .info{
     color: $cdgc-font-prime;
 }
//  :host ::ng-deep .mat-checkbox-inner-container {
//      border: 1px solid $lightGrey;
// }
.tbl-container{
    display: flex;
    flex-direction: column;
    // background-color: transparent !important;
    // max-width: 300px;
    // max-height: 500px;
    // max-height: 600px; commented for testing on 1/12/2022 neevika
}
.mat-mdc-header-row{
    border-radius: 10px;
    background-color:$cdgc-bg-prime !important;
    color: $cdgc-font-accent !important;
}
.mat-mdc-header-cell {
    color: $cdgc-font-accent !important;
    
}
.mat-mdc-row{
    border-radius: 12px;
    color: $lightGrey !important;

}
.sno{
    display: block;
}
.mobile-label{
    display: none;
}
 :host input[type=number]::-webkit-outer-spin-button{
    opacity: 0;
}
:host input[type=number]::-webkit-inner-spin-button{
    opacity: 0;
}
 .tbl-inp{
    width: 90px !important;
}
@media screen and (max-width: 600px) {
    .inp{
        width: 183px !important;
    }
    .mobile-label{
        display: inline-block;
        width: 165px;
        font-weight: $cdgsz-font-weight-bold;
    }
    // .mat-header-row{
    //     display: none;
    // }
    .mat-mdc-row{
        flex-direction: column;
        align-items: start;
        padding: 8px 24px;
        border-radius: 12px !important;
        border: 1px solid $cdgc-border-prime;
        min-height: 28px !important;
        // margin-bottom: 10px !important;
    }
    .mat-mdc-header-row{
        border-radius: 10px;
        background-color:$cdgc-bg-prime !important;
        color: $cdgc-font-accent !important;
        justify-content: center;  
    }
    .mat-mdc-cell:first-of-type {
        padding-left: 0px !important; 
    }
    .sno{
        display: none !important;
    }
 }
 .si{
    font-size: 13px;   
 }
 .fo{
    font-size: 14px; 
   // text-align: center;
   // justify-content: center;    
}
.foo{
    font-size: 14px; 
   // text-align: center;
//justify-content: center;  
}
div span.show-on-hover{
visibility: visible;
background-color: $lightGrey;
    color: $cdgc-font-accent;
    height: 50px!important;
    width:  150px!important;
}
div:hover span.show-on-hover{
    visibility: visible;
    background-color: $lightGrey;
    color: $cdgc-font-accent;
    height: 50px!important;
    width:  150px!important;
    // text-align: center;
    }
 @media screen and (max-width: 600px) {
  
 }