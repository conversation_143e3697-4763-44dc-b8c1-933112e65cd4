<div fxLayout="column" fxLayoutGap="50px">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">price_change</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">Download Trip Report</span>
        </div>
    </div>
    <form #f="ngForm" name="form">
        <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
      
            <div>
                <span class="sub-header pb5">DOWNLOAD BY<span class="asterisk"><sup>*</sup></span>:</span>
            </div>
            <div>
                <mat-radio-group name="radioValue" [(ngModel)]="tripData.radioValue" fxLayoutGap="30px" >
                    <mat-radio-button *ngFor="let val of radioArr" [value]="val.value" [disabled]="val.isDisble" labelPosition="before" color="primary">
                        {{val.viewValue}}
                    </mat-radio-button>
                </mat-radio-group>
                <!-- <mat-error *ngIf="f.submitted && radioValue.invalid" class="pt15">
                    <div *ngIf="radioValue.hasError('required')">
                        Download by is required
                    </div>
                </mat-error> -->
            </div>
          
            <div *ngIf="tripData.radioValue == 'invoiceno'">
                <div fxLayout="column" fxLayoutGap="15px">
                    <div fxLayout="row" fxLayoutGap="30px">
                        <span class="date-title pt10">INVOICE NO.<span class="asterisk"><sup>*</sup></span>:</span>
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput type="number" name="invoiceno" [(ngModel)]="tripData.invoiceno"  #invoiceno="ngModel" [required]="tripData.radioValue == 'invoiceno'">
                            <!-- <mat-error *ngIf="invoiceno.invalid" class="pt15">
                                <div *ngIf="invoiceno.hasError('required')">
                                    Invoice No. Is Required
                                </div>
                            </mat-error> -->
                            <mat-error *ngIf="isInvoiceNoNull" class="pt15">
                                <!-- <div *ngIf="invoiceno.hasError('required')"> -->
                                    Invoice No. Is Required
                                <!-- </div> -->
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
            </div>
            <ng-container *ngIf="tripData.radioValue == 'traveldate'">
                <div fxLayout="column" fxLayoutGap="20px">
                    <div fxLayout="row" fxLayoutGap="40px" *ngIf="isShowCICHDropdown">
                        <!-- <span class="date-title pt10">Department :</span> -->
                        <mat-form-field appearance="outline" class="inp">
                            <mat-select [(ngModel)]="selectedData" 
                            [ngModelOptions]="{standalone: true}" name="cichDrodown" (ngModelChange)="getProductData()">
                              <mat-option *ngFor = "let val of cichDropdown;" [value]="val">
                                  {{val.accountName}} ({{val.customerNo}})
                             </mat-option>
                            </mat-select>
                          </mat-form-field>  
                    </div>
                    <div fxLayout="column">
                        <div fxLayout="row" fxLayout.lt-md="column" fxLayoutGap="80px" fxLayoutGap.lt-md="20px">
                            
                            <div fxLayout="row" fxLayoutGap="50px" fxLayoutGap.lt-md="50px" fxLayoutGap.lt-sm="50px">
                                <span class="date-title pt10">START DATE<span
                                        class="asterisk"><sup>*</sup></span>:</span>
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput [matDatepicker]="picker1" name="selectedStartDate" [(ngModel)]="tripData.selectedStartDate"
                                    #selectedStartDate="ngModel"  (ngModelChange)="daysDifference()" [required]="tripData.radioValue == 'traveldate'" onkeydown="return false" autocomplete="off">
                                    <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                                    <mat-datepicker #picker1></mat-datepicker>
                                 
                                    <!-- <mat-error *ngIf="selectedStartDate.invalid" class="pt15">
                                        
                                        <div *ngIf="selectedStartDate.hasError('required')">
                                            Start Date Is Required
                                        </div>
                                    </mat-error> -->
                                    <mat-error *ngIf="isTripDataStartDate" class="pt15">
                                        
                                        <!-- <div *ngIf="selectedStartDate.hasError('required')"> -->
                                            Start Date Is Required
                                        <!-- </div> -->
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <div fxLayout="row" fxLayoutGap="50px" fxLayoutGap.lt-md="110px" fxLayoutGap.lt-sm="100px">
                                <!-- fxLayoutGap.lt-md="90px" -->
                                <span class="date-title pt10">TO<span class="asterisk"><sup>*</sup></span>:</span>
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput [matDatepicker]="picker2" name="selectedEndtDate" [(ngModel)]="tripData.selectedEndtDate"
                                    #selectedEndtDate="ngModel"  (ngModelChange)="daysDifference()" [required]="tripData.radioValue == 'traveldate'" onkeydown="return false" autocomplete="off">
                                    <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                                    <mat-datepicker #picker2></mat-datepicker>
                                    <!-- <mat-error *ngIf="selectedEndtDate.invalid" class="pt15">
                                        <div *ngIf="selectedEndtDate.hasError('required')">
                                            To Date Is Required
                                        </div>
                                    </mat-error> -->
                                    <mat-error *ngIf="isTripDataEndDate" class="pt15">
                                        <!-- <div *ngIf="selectedEndtDate.hasError('required')"> -->
                                            To Date Is Required
                                        <!-- </div> -->
                                    </mat-error>
                                </mat-form-field>
                            </div>

                        </div>
                        <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="140px" fxLayoutGap.md="140px" fxLayoutGap.sm="140px" fxLayoutGap.xs="0px">
                            <div></div>
                            <!-- <div fxFlex="86%"> -->
                            <div *ngIf="showDateError" class="error">
                                Date Range Must Not Be More Than 31 Days
                            </div>
                            <div *ngIf="showRangeDateError" class="error">
                                End Date should be greater than startDate
                            </div>
                        <!-- </div> -->
                        </div>
                    </div>
                   
                    <div fxLayout="row" fxLayoutGap="73px" *ngIf="isShowDivision">
                        <span class="date-title pt10">DIVISION :</span>
                        <!-- <mat-form-field appearance="outline" class="inp">
                            <mat-select [(ngModel)]="tripData.divisionSelect" name="divisionSelect">
                                <mat-option *ngFor="let acl of divitionArr" [value]="acl.viewValue">
                                    {{acl.viewValue}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field> -->

                        <!-- //neevika -->
                          <mat-form-field appearance="outline" class="inp">
                            <mat-select  [(ngModel)]="frstDivSelection" name="divisionSelect" [ngModelOptions]="{standalone: true}" (ngModelChange)="selectDept(frstDivSelection)">
                              <mat-option *ngFor = "let division of divisions;" [value]="division">
                                  {{division.nameToDisplayOnDropdown}}
                             </mat-option>
                            </mat-select>
                          </mat-form-field>
                           <!-- tripData.divisionSelect -->
                           <!-- (ngModelChange)="showTable(divisionSelected)"  [(ngModel)]="divisionSelected"-->
                          <!-- //neevika -->
                    </div>

                    <div fxLayout="row" fxLayoutGap="40px" *ngIf="isShowDepartment">
                        <span class="date-title pt10">DEPARTMENT :</span>
                        <mat-form-field appearance="outline" class="inp">
                            <mat-select [(ngModel)]="tripData.departmentSelect" 
                            [ngModelOptions]="{standalone: true}" name="departmentSelect">
                          
                            <mat-option *ngIf="departments.length > 0" [value]="deptValue">ALL</mat-option>
                              <mat-option *ngFor = "let dep of departments;" [value]="dep">
                                  {{dep?.nameToDisplayOnDropdown}}
                             </mat-option>
                            </mat-select>
                          </mat-form-field>  
                    </div>




                    <div fxLayout="row" fxLayoutGap="65px">
                        <span class="date-title pt10">PRODUCT :</span>
                        <!-- <mat-form-field appearance="outline" class="inp">
                            <mat-select [(ngModel)]="tripData.productSelect" name="productSelect" [disabled]="isProductDisable">
                                <mat-option *ngFor="let acl of productArr" [value]="acl.viewValue">
                                    {{acl.viewValue}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field> -->

                        <!-- //neevika -->
                        <mat-form-field appearance="outline" class="inp">
                            <mat-select [(ngModel)]="frstProductSelection" 
                            
                            [ngModelOptions]="{standalone: true}" name="productSelect" [disabled]="isProductDisable">
                            <!--  -->
                            <!-- "tripData.productSelect"  -->
                           <!-- (ngModelChange)="showTable(divisionSelected)"  [(ngModel)]="divisionSelected"-->
                           <!-- <mat-option *ngIf="productArr.length > 1" [value]="productValue">ALL</mat-option> -->
                           <mat-option *ngFor = "let product of productArr;" [value]="product">
                                  {{product.productName}}
                             </mat-option>
                            </mat-select>
                          </mat-form-field>  
                        <!-- //neevika -->
                    </div>
                   
                </div>
            </ng-container>

            
            <div fxFlex>
                <!-- ,f.form.valid -->
                <button mat-raised-button class="download-btn" *ngIf="!canShowLoaderbtn" (click)="download(tripData.radioValue)">DOWNLOAD</button>
                <button mat-raised-button class="download-btn" *ngIf="canShowLoaderbtn">Processing...</button>
            </div>
        </div>
    </form>
</div>