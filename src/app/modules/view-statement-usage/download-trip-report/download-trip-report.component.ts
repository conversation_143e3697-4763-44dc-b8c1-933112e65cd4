import { Component, OnInit } from '@angular/core';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as _moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core'
import { ExportxlsxService } from 'src/app/shared/services/exportxlsx.service';
import { DatePipe } from '@angular/common';
import { NgForm, FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms'
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';
import { Router } from '@angular/router';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { HttpClient } from '@angular/common/http';
import * as FileSaver from 'file-saver';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { saveAs } from 'file-saver'
import { ThemePalette } from '@angular/material/core';

export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
}
@Component({
  selector: 'app-download-trip-report',
  templateUrl: './download-trip-report.component.html',
  styleUrls: ['./download-trip-report.component.scss'],
  providers: [DatePipe, { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] }, { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]

})
// const obj;
export class DownloadTripReportComponent implements OnInit {

  tripData: any = {
    radioValue: "invoiceno",
    selectedStartDate: "",
    selectedEndtDate: "",
    divisionSelect: {},   //ALL
    productSelect: {}, //ALL
    departmentSelect: {},
    invoiceno: ""
  }

  radioArr = [
    { value: "invoiceno", viewValue: "INVOICE NO.", isDisble: false },
    { value: "traveldate", viewValue: "TRAVEL DATE", isDisble: false }
  ]

  // divitionArr = [
  //   { value: 'all', viewValue: 'ALL' },
  //   { value: 'cabincrew', viewValue: 'CABIN CREW (1003)' },
  //   { value: 'corporateoffice', viewValue: 'CORPORATE OFFICE (1005)' },
  //   { value: 'csm1', viewValue: 'CSM1 (1004)' }
  // ];
  // productArr = [
  //   { value: 'all', viewValue: 'ALL' },
  //   { value: 'contactlesscorprate', viewValue: 'CONTACTLESS CORPORATE CARD' },
  //   { value: 'contactlessprepaid', viewValue: 'CONTACTLESS PREPAID CARD' },
  //   { value: 'corporatecard', viewValue: 'CORPORATE CARD' },
  //   { value: 'evoucher', viewValue: 'OPEN VALUE E-VOUCHER' },
  //   { value: 'virtualcrop', viewValue: 'VIRTUAL CORP 2 TEST' },
  //   { value: 'vitualcorporate', viewValue: 'VIRTUAL CORPORATE' },
  //   { value: 'virtualprepaid', viewValue: 'VIRTUAL PREPAID' }
  // ];

  divisions: any[] = [];
  productArr: any[] = [];
  departments: any[] = [];
  isShowDeptList: boolean = false;
  divisionSelected: any;
  frstDivSelection: any;
  frstProductSelection: any;
  productSelected: any;
  fetchDept: any[] = [];
  isInvoiceNoNull: boolean = false;
  isTripDataEndDate: boolean = false;
  isTripDataStartDate: boolean = false;
  canShowLoaderbtn: boolean = false;

  showDateError: boolean = false;
  isShowDivision: boolean = true;
  isProductDisable: boolean = false;
  isShowDepartment: boolean = false;
  showRangeDateError: boolean = false;
  invoiceSubmitted: boolean = false;
  travelDateSubmitted: boolean = false;
  masterAccNo: any;
  accountnumber: any;
  deptValue: any;
  roleList: any;
  isShowCICHDropdown: boolean = false;
  selectedData: any;
  cichDropdown: any[] = []
  productValue: any;
  constructor(public localStorage: LocalStorageService, private http: HttpClient, private excelService: ExportxlsxService, private fb: FormBuilder, private router: Router, private datePipe: DatePipe, private notifyService: NotificationService, public cdgService: CdgSharedService, private apiService: ApiServiceService) { }

  ngOnInit(): void {

    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }

    }
    this.tripData.radioValue = "invoiceno"
    this.radioArr[0].isDisble = false;
    let obj = {
      "accountNo": 0,
      "accountName": "ALL",
      "accountCategory": "DEPT",
      "accountCode": null,
      "nameToDisplayOnDropdown": "ALL"
    }
    this.deptValue = obj;
    this.tripData.departmentSelect = this.deptValue;
    let prodobj = {
      productId: "",
      productName: "ALL"
    }
    this.productValue = prodobj;
    this.frstProductSelection = this.productValue;

    this.isShowCICHDropdown = false;
    // this.selectedStartDate = new Date();
    // this.selectedEndtDate = new Date();
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER) {
      this.isShowDivision = false;
      this.isProductDisable = true;
      // this.productArr.push({ productId: 'PC', productName: 'PERSONAL CARD' })
      this.tripData.productSelect = 'PERSONAL CARD'
      //  this.productArr.push({ value: 'personalCard', viewValue: 'PERSONAL CARD' })  
    }
    else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
      this.isShowDivision = false;
      this.isShowCICHDropdown = true;
      this.tripData.radioValue = "traveldate"
      this.radioArr[0].isDisble = true;
      this.getCICHDropdownValue();
      // this.isProductDisable = true;
    }
    else if (this.localStorage.localStorageGet("loggedInRole") === 'Personal Card Sub') {
      this.isShowDivision = false;
    }
    if (this.localStorage.localStorageGet("loggedInRole") !== RoleConstants.ROLE_CORPCARDHOLDER) {
      this.getProductData();
    }
    this.getDivisionData();

  }
  getCICHDropdownValue() {
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.CICH_CUSTOMER_DROPDOWN, obj).subscribe
      ((response: any) => {
        if (response.status === 200) {
          console.log(response)
          let opt = response.body.dropdownList;
          this.cichDropdown = Object.values(opt);
          if (this.cichDropdown.length > 0) {
            this.selectedData = this.cichDropdown[0];
          }
          this.getProductData();
        }
      }, e => {
        this.notifyService.showError(e.error.errorMessage, "Error")

      })
  }
  getDivisionData() {
    this.divisions = [];
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.accountnumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
      "masterAccountNo": this.accountnumber
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_DIV_DEPTLIST, obj).subscribe
      ((response: any) => {
        if (response.status === 200) {
          let data = response.body;
          this.divisions = [];
          if (data.corpAccountDto == null) {
            if (data.divAccountList.length > 1) {
              var obj = {
                "accountCategory": "",
                "accountCode": "",
                "accountName": "ALL",
                "accountNo": '',
                "nameToDisplayOnDropdown": "ALL",
                "divAccountList": [
                  {
                    "deptList": [],
                    "divAccount": {
                      "accountCategory": "",
                      "accountCode": "",
                      "accountName": "ALL",
                      "accountNo": '',
                      "nameToDisplayOnDropdown": "ALL"
                    }

                  }
                ]

              }
              this.divisions.push(obj);
              //   data.divAccountList.forEach((element: any) => {
              //     this.divisions.push(element.divAccount);
              //     // this.frstDivSelection = this.divisions[0];
              //   });
              //   // if(data.divAccountList.length > 0)
              //   this.frstDivSelection = this.divisions[0];
            }
          }

          if (data.corpAccountDto) {

            if (Object.keys(data.corpAccountDto).length > 0) {
              this.divisions.push(data.corpAccountDto);
              // this.fetchDept.push(data.corpAccountDto);
            }

            // else{

            // } 
          }
          // if(data.corpAccountDto == null && data.divAccountList.length > 0){
          //   var obj = {
          //     "accountCategory": "DIV",
          //     "accountCode": "1003",
          //     "accountName": "ALL",
          //     "accountNo": 9472,
          //     "nameToDisplayOnDropdown": "ALL"
          //   }
          //   this.divisions[0].push(obj);
          // }

          if (data.divAccountList) {
            if (data.divAccountList.length > 0) {
              console.log(data.divAccountList)
              data.divAccountList.forEach((element: any) => {
                this.fetchDept.push(element);
                console.log(element.divAccount)
                this.divisions.push(element.divAccount);
                console.log(this.divisions)

                this.frstDivSelection = this.divisions[0];
                this.selectDept(this.divisions[0]);

                // if(element.deptList.length > 0)  {
                //   element.deptList.forEach((el:any)=>{
                //     this.departments.push(el.deptList);
                //     this.isShowDepartment = true;
                //   })

                // }

              });
            }
          }

        }
        console.log(this.divisions)
      })
  }
  selectDept(value: any) {
    this.isShowDepartment = false;
    console.log(this.tripData.departmentSelect)
    // this.departments = [];
    if (value.nameToDisplayOnDropdown !== "ALL") {
      if (this.tripData.departmentSelect.nameToDisplayOnDropdown !== "ALL") {
        this.tripData.departmentSelect = ""
      }
      // x.divAccount.nameToDisplayOnDropdown
      // x.divAccount.nameToDisplayOnDropdown === value.nameToDisplayOnDropdown
      const obj = this.fetchDept.find(x => x.divAccount.nameToDisplayOnDropdown === value.nameToDisplayOnDropdown)
      if (obj.nameToDisplayOnDropdown !== "ALL" && obj.deptList.length > 0) {

        this.isShowDepartment = true;
        this.departments = obj.deptList;
        // this.tripData.departmentSelect = this.departments;
      }
    }
    // const obj1 = this.fetchDept.find(x => x.nameToDisplayOnDropdown ? x.nameToDisplayOnDropdown === value.nameToDisplayOnDropdown : (x.divAccount.nameToDisplayOnDropdown ? x.divAccount.nameToDisplayOnDropdown === value.nameToDisplayOnDropdown))

  }
  getProductData() {
    let obj
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
      obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.accountnumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
        "masterAccountNo": this.accountnumber,
        "cardNumberList": this.selectedData.cardNumber,
      }
    }
    else {
      obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.accountnumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
        "masterAccountNo": this.accountnumber,
      }
    }
    this.productArr = []
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_TRIP_REPORT_PRODUCTS, obj).subscribe
      ((response: any) => {
        if (response.status === 200) {
          let data = response.body;
          if (data.length > 0) {
            if (data.length === 1) {
              this.isProductDisable = true
            }
            else {
              this.isProductDisable = false
            }
            if (data.length > 1) {
              let obj = {
                productId: "ALL",
                productName: "ALL"
              }
              this.productArr.push(obj)
            }
            data.forEach((element: any, i: number) => {

              // this.productArr.push(obj);
              // this.productArr.push(element.divAccount);
              this.productArr.push(element);
              // if (data.length === 1) {
              this.frstProductSelection = this.productArr[0];
              // }
            });
            console.log(this.productArr)

          }
        }
      })
  }

  daysDifference() {
    if (this.tripData.selectedStartDate && this.tripData.selectedEndtDate) {
      this.showDateError = false;
      this.showRangeDateError=false;
      if ((this.tripData.selectedStartDate) > (this.tripData.selectedEndtDate)) {
        this.showRangeDateError=true;
      }
      else {
        const oneDay = 24 * 60 * 60 * 1000;
        const differenceDays = Math.round(Math.abs((Number(this.tripData.selectedStartDate) - Number(this.tripData.selectedEndtDate)) / oneDay));
        if (differenceDays >= 31) {
          this.showDateError = true;
        }
        else {
          this.showDateError = false;
        }
      }
    }
  }
  // , valid: boolean

  download(radioValue: any) {
    this.canShowLoaderbtn = true;
    if (radioValue === 'invoiceno') {
      // && valid
      if (this.tripData.invoiceno) {
        this.isInvoiceNoNull = false;
        this.invoiceSubmitted = true;
        let accNoConversion = Number(this.accountnumber);
        let reqObj = {
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.accountnumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
          "invoiceNo": this.tripData.invoiceno,
          "masterAccountNo": accNoConversion
        }
        let mediaType = "text/csv"
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.DOWNLOAD_TRIP_REPORT_BY_INVOICE, reqObj, { responseType: 'text' }).subscribe((response: any) => {
          this.canShowLoaderbtn = false;
          let blob = new Blob([response], { type: mediaType });
          saveAs(blob, 'TripReport_' + this.tripData.invoiceno + '.csv')
        },
          e => {
            this.canShowLoaderbtn = false;
            this.notifyService.showError(JSON.parse(e.error).message, "Error")
          })
      }
      else {
        this.isInvoiceNoNull = true;
      }

    }
    else if (radioValue === 'traveldate') {

      // && valid
      if (this.tripData.selectedStartDate && this.tripData.selectedEndtDate) {
        this.isTripDataStartDate = false;
        this.isTripDataEndDate = false;
        if ( !this.showRangeDateError && !this.showDateError) {
        //   if (((this.tripData.selectedStartDate) > (this.tripData.selectedEndtDate))) {
        //     this.showRangeDateError = true;
        //   }
        // }

        // else {
          this.showRangeDateError = false;
          this.showDateError = false;
          let accNoConversion = Number(this.accountnumber);
          console.log(accNoConversion);
          let obj;
          // if(this.tripData.departmentSelect?.accountCode){
          if (this.localStorage.localStorageGet("loggedInRole") !== RoleConstants.ROLE_CORPCARDHOLDER) {
            obj = {
              "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
              "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
              "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.accountnumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
              "tripStartDt": _moment(this.tripData.selectedStartDate).format("DD-MMM-YY"),
              "tripEndDt": _moment(this.tripData.selectedEndtDate).format("DD-MMM-YY"),
              "divisionCode": this.frstDivSelection?.accountCode,
              "departmentCode": this.tripData.departmentSelect ? this.tripData.departmentSelect.accountCode : "",
              "masterAccountNo": accNoConversion,
              "productTypeId": this.frstProductSelection?.productId
            }
          }
          else {
            obj = {
              "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
              "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
              "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.accountnumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
              "tripStartDt": _moment(this.tripData.selectedStartDate).format("DD-MMM-YY"),
              "tripEndDt": _moment(this.tripData.selectedEndtDate).format("DD-MMM-YY"),
              "divisionCode": this.frstDivSelection?.accountCode,
              "departmentCode": this.tripData.departmentSelect ? this.tripData.departmentSelect.accountCode : "",
              "masterAccountNo": accNoConversion,
              "productTypeId": this.frstProductSelection?.productId,
              "cardNumberList": this.selectedData.cardNumber,
              "accountName": this.selectedData.accountName,
              "customerNo": this.selectedData.customerNo
            }
          }
          let mediaType = "text/csv"
          this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.DOWNLOAD_TRIP_REPORT_BY_TRIPDATE, obj, { responseType: 'text' }).subscribe((response: any) => {
            this.canShowLoaderbtn = false;
            let blob = new Blob([response], { type: mediaType });
            saveAs(blob, 'TripReport_' +
            _moment(this.tripData.selectedStartDate).format("DD-MMM-YY") + '_to_' + _moment(this.tripData.selectedEndtDate).format("DD-MMM-YY")
              + '.csv')
            // this.tripData.selectedStartDate
          },
            e => {
              this.canShowLoaderbtn = false;
              this.notifyService.showError(JSON.parse(e.error).message, "Error")
            })


          // }
          // else {
          //   let obj = {
          //     accessId:this.localStorage.localStorageGet("loginUserDetails").accessId,
          //     "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          //     tripStartDt:this.datePipe.transform(this.tripData.selectedStartDate, 'dd-MMM-YY'),
          //     "tripEndDt":this.datePipe.transform(this.tripData.selectedEndtDate, 'dd-MMM-YY'),
          //     divisionCode:this.frstDivSelection?.accountCode,
          //     "departmentCode":'',
          //     "masterAccountNo": this.accountnumber,
          //     productTypeId:this.frstProductSelection?.productId
          //   }
          //   let mediaType = "text/csv"
          //   this.apiService.post(this.cdgService.localhostUrl + UrlConstants.DOWNLOAD_TRIP_REPORT_BY_TRIPDATE,obj, { responseType: 'text' }).subscribe((response: any) =>{
          //     let blob = new Blob([response], { type: mediaType });
          //     saveAs(blob, 'TripReport_'+
          //     this.datePipe.transform(this.tripData.selectedStartDate, 'ddMMMYYYY') + '_to_' + this.datePipe.transform(this.tripData.selectedEndtDate, 'ddMMMYYYY')
          //   +'.csv')
          //     console.log(response.body)
          //   },
          //   e => {
          //     this.notifyService.showError(JSON.parse(e.error).message, "Error")
          //   })

          // }
          // this.travelDateSubmitted  = false;
          // this.travelDateSubmitted = false;



        }

      }
      else {
        if (this.tripData.selectedStartDate === "") {
          this.isTripDataStartDate = true

        }
        if (this.tripData.selectedEndtDate === "") {
          this.isTripDataEndDate = true
        }
      }

      // this.excelService.exportAsExcel([obj], 'TripReport' + this.datePipe.transform(this.tripData.selectedStartDate, 'ddMMMYYYY') + '_to_' + this.datePipe.transform(this.tripData.selectedEndtDate, 'ddMMMYYYY'))
    }
  }
}
