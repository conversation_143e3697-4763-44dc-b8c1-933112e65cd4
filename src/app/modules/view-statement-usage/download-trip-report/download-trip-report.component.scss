@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
.mat-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
    height: 40px !important;
    width: 40px !important;
}
.sub-header{
    font-size: 19px !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
::ng-deep .mat-mdc-radio-button.mat-color-accent .mat-mdc-radio-outer-circle .mat-radio-checked {
    border-color: rgba(0, 0, 0, 0.54) !important;

}
// /* Change_by_navani (mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
::ng-deep .mat-mdc-radio-button.mat-color-accent.mat-mdc-radio-checked .mat-radio-inner-circle{
    background-color: #000000 !important;
}
.inp{
    width: 270px !important;
}
@media screen and (max-width: 600px) {
    .inp{
        width: 156px !important;
    }
}
:host ::ng-deep .download-btn{
    width:191px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
}
.error{
    font-size: $cdgsz-font-size-sm !important;
    color: $cdgc-font-warn !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
}
.pl{
    padding-left: 110px !important;
}
:host input[type=number]::-webkit-outer-spin-button{
    opacity: 0;
}
:host input[type=number]::-webkit-inner-spin-button{
    opacity: 0;
}

/* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version. */
::ng-deep .mat-mdc-select-arrow-wrapper {
    vertical-align: bottom !important;
  }
  ::ng-deep .mat-mdc-select-panel{
    min-width: fit-content !important;
  }