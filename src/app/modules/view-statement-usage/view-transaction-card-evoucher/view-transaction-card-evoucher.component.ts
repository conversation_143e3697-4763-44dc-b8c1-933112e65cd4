import { Component, OnInit, ViewChild } from '@angular/core';
import { Validators, FormBuilder, FormGroup } from '@angular/forms';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as _moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { throwError } from 'rxjs';
import { ExportxlsxService } from 'src/app/shared/services/exportxlsx.service';
import { DatePipe } from '@angular/common';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { RoleConstants } from 'src/assets/url-constants/url-constants';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { FormGroupDirective } from '@angular/forms';
import { saveAs } from 'file-saver'

export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },

  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
}

export interface TransactionData {
  cardNo: number,
  jobNo: number,
  taxiNo: string,
  pickUpAddress: string,
  destination: string,
  tripStartDate: string,
  tripEndDate: string,
  fareAmt: number,
  txnStatus: string
}
const DATA: TransactionData[] = [];


// const DATA: TransactionData[] = [
//   { cardNo: 6010896501570711, jobNo: 9000005651, taxiNo:'TAXI1333', pickupDest: 'MARRYMOUNT MRT - BSHAN MRT', travelPeriod: '08/01/21 00:00 - 08/01/21 00:00 ',taxiFares: 20, status: 'BILLED' },
//   { cardNo: 6010896501570712, jobNo: 9000005654, taxiNo:'TAXI1333', pickupDest: 'MARRYMOUNT MRT - BSHAN MRT', travelPeriod: '08/01/21 00:00 - 08/01/21 00:00 ',taxiFares: 20, status: 'BILLED' },
//   { cardNo: 6010896501570713, jobNo: 9000005652, taxiNo:'TAXI1333', pickupDest: 'MARRYMOUNT MRT - BSHAN MRT', travelPeriod: '08/01/21 00:00 - 08/01/21 00:00 ',taxiFares: 20, status: 'BILLED' },
//   { cardNo: 6010896501570714, jobNo: 9000005653, taxiNo:'TAXI1334', pickupDest: 'MARRYMOUNT MRT - BSHAN MRT', travelPeriod: '08/01/21 00:00 - 08/01/21 00:00 ',taxiFares: 20, status: 'BILLED' },
//   { cardNo: 6010896501570715, jobNo: 9000005658, taxiNo:'TAXI1333', pickupDest: 'MARRYMOUNT MRT - BSHAN MRT', travelPeriod: '08/01/21 00:00 - 08/01/21 00:00 ',taxiFares: 20, status: 'BILLED' },
//   { cardNo: 6010896501570716, jobNo: 9000005658, taxiNo:'TAXI1331', pickupDest: 'MARRYMOUNT MRT - BSHAN MRT', travelPeriod: '08/01/21 00:00 - 08/01/21 00:00 ',taxiFares: 20, status: 'BILLED' },
//   { cardNo: 6010896501570717, jobNo: 9000005658, taxiNo:'TAXI1333', pickupDest: 'MARRYMOUNT MRT - BSHAN MRT', travelPeriod: '08/01/21 00:00 - 08/01/21 00:00 ',taxiFares: 20, status: 'BILLED' },
//   { cardNo: 6010896501570718, jobNo: 9000005658, taxiNo:'TAXI1333', pickupDest: 'MARRYMOUNT MRT - BSHAN MRT', travelPeriod: '08/01/21 00:00 - 08/01/21 00:00 ',taxiFares: 20, status: 'BILLED' },
//   { cardNo: 6010896501570719, jobNo: 9000005658, taxiNo:'TAXI1333', pickupDest: 'MARRYMOUNT MRT - BSHAN MRT', travelPeriod: '08/01/21 00:00 - 08/01/21 00:00 ',taxiFares: 20, status: 'BILLED' },
//   { cardNo: 6010896501570720, jobNo: **********, taxiNo:'TAXI1332', pickupDest: 'MARRYMOUNT MRT - BSHAN MRT', travelPeriod: '08/01/21 00:00 - 08/01/21 00:00 ',taxiFares: 20, status: 'BILLED' },

//  // { month: new Date('November 2021'), totalTrips: 5, avgSpend: 10, totalAmt: 50 }
// ]
@Component({
  selector: 'app-view-transaction-card-evoucher',
  templateUrl: './view-transaction-card-evoucher.component.html',
  styleUrls: ['./view-transaction-card-evoucher.component.scss'],
  providers: [DatePipe, { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] }, { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]

})
export class ViewTransactionCardEvoucherComponent implements OnInit {
  public targetElement: HTMLElement;
  public menuIconName = 'view_headline';
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(FormGroupDirective) formGroupDirective: FormGroupDirective;
  @ViewChild(MatSort) sort: MatSort;
  data = {

    cardNo: ""
  }
  displayedColumns: string[] = ['serialNo', 'cardNo', 'jobNo', 'taxiNo', 'pickUpAddress', 'destination', 'tripStartDate', 'tripEndDate', 'fareAmt', 'txnStatus', 'downloadAction'];
  dataSource = new MatTableDataSource(DATA);
  dataSourceCell: Observable<any>
  //dataSourceCell = this.dataSource.data
  //dataSourceCell = DATA;
  showTable: boolean = false;
  showcarderror: boolean = false;
  showPaginator: boolean = false;
  isProductDisable: boolean = false;
  disablesubmit: boolean = false;
  innerWidth: any;
  public isMobileLayout = false
  productArr: any = [];
  submitted: boolean = false;
  showDateRangeError: boolean = false;
  lastfuzz: any = [];
  // productArr = [
  //   { value: 'contactlesscorprate', viewValue: 'CONTACTLESS CORPORATE CARD' },
  //   { value: 'contactlessprepaid', viewValue: 'CONTACTLESS PREPAID CARD' },
  //   { value: 'corporatecard', viewValue: 'CORPORATE CARD' },
  //   { value: 'evoucher', viewValue: 'OPEN VALUE E-VOUCHER' },
  //   { value: 'virtualcrop', viewValue: 'VIRTUAL CORP 2 TEST' },
  //   { value: 'vitualcorporate', viewValue: 'VIRTUAL CORPORATE' },
  //   { value: 'virtualprepaid', viewValue: 'VIRTUAL PREPAID' }
  // ];
  transactionForm:FormGroup
  showDateError: boolean = false;
  //cardArr = ['601089-6511-1239', '601089-6511-1237', '601089-6511-1235', '601089-6511-1233', '601089-6511-1231', '601089-6511-1230'];
  cardArr: any = [];
  // ['123901', '123712', '123513', '123311', '123123', '123023'];
  isShowError: boolean = false;
  firstdigit: any;
  lastdigit: any = [];
  list: any;
  value: any;
  displaycard: any;
  firstProductSelected: any;
  accountnumber: any;
  hideCardField: boolean;
  showErr: boolean = false;
  constructor(public localStorage: LocalStorageService, private datePipe: DatePipe, private excelService: ExportxlsxService, private fb: FormBuilder, public cdgService: CdgSharedService, private notifyService: NotificationService, private router: Router, private apiService: ApiServiceService) {
    this.transactionForm= this.fb.group({
      productType: ['', [Validators.required]],
      cardNo: ['', [Validators.required]],
      startDate: [new Date(), [Validators.required]],
      endDate: [new Date(), [Validators.required]]
    })

  }

  ngOnInit(): void {
    this.transactionForm.reset()
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }
    }
    let prodtypeobj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accountNo": this.accountnumber,
      "masterAccountNo": Number(this.accountnumber)
    }

    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_PRODUCTTYPE, prodtypeobj).subscribe((response: any) => {
      if (response.status === 200) {
        //  console.log(response)
        let opt = response.body.productType;
        // if(opt.length > 1){
        //   //  console.log("I am in legth 1")
        //   this.isProductDisable = false
        // }
        // else{
        //   this.isProductDisable = true
        //   opt.forEach((element: any) => {
        //     this.productArr.push(element);
        //     //  console.log(this.productArr);
        //   });
        //   this.firstProductSelected = this.productArr[0];
        //   //  console.log(this.firstProductSelected);
        // }
        opt.forEach((element: any) => {
          this.productArr.push(element);
          //  console.log(this.productArr);
        });




        //  opt.filter((element:any )=> {
        //           this.productArr =[element];
        //         });




      }

    });

    // if(this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
    //     this.isProductDisable = false;

    //   }

    // if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER) {
    //   // this.isShowDivision = false;
    //   this.isProductDisable = true;
    //   this.productArr.push({ value: 'personalCard', viewValue: 'PERSONAL CARD' })
    //   this.transactionForm.get('productType')?.setValue('personalCard')
    //   this.transactionForm.get('cardNo')?.setValue('601089-6511-1239')

    //   // this.tripData.productSelect = 'PERSONAL CARD'
    // }
    // else if(this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
    //   this.isProductDisable = true;
    //   this.transactionForm.get('productType')?.setValue('corporatecard')
    //   this.transactionForm.get('cardNo')?.setValue('601089-6511-1237')
    // }
    // else if(this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUBAPPLICANT){
    //     this.productArr.splice(0,this.productArr.length);
    //     this.productArr.push({value: 'personalCard', viewValue: 'PERSONAL CARD'});
    //     this.productArr.push({value: 'priorityCard', viewValue: 'PRIORITY CARD'});
    // }

  }
  // ngAfterViewInit():void{
  //   if(!this.isMobileLayout){
  //   this.innerWidth = window.innerWidth;
  //   document.onclick=(args:any):void=>{
  //     if(args.target.tagName==='DIV'){
  //       if (this.innerWidth <= 990){
  //         this.cdgService.showMenu = !this.cdgService.showMenu;
  //         // this.showCloseIcon = !iconVal;
  //         if (!this.cdgService.showMenu) {
  //           this.menuIconName = 'view_headline';
  //         }
  //         else {
  //           this.menuIconName = 'close';

  //         }
  //       }
  //       else{
  //         this.cdgService.expSidebar = !this.cdgService.expSidebar;
  //         if (!this.cdgService.expSidebar) {
  //           this.menuIconName = 'view_headline';
  //         }
  //         else {
  //           this.menuIconName = 'close';

  //         }
  //       }

  //     //  console.log("clsoe")
  //     }
  //   }
  // }
  // }

  // ngAfterViewInit() {

  // this.dataSource.sort = this.sort;
  // this.dataSource.paginator = this.paginator;
  //}
  onSelect(item: any) {
    this.displaycard = "";
    this.showcarderror = false;
    this.disablesubmit = false;
    this.transactionForm.controls['cardNo'].reset()
    this.data.cardNo=""
    if (item.value) {
    
      let cardnumberobj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
        "productType": item.value,
        "accountNo": this.accountnumber
      }
      
      if (item.value === 'OV') {
        this.hideCardField = true;
        return;
      }

      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_CARDLIST, cardnumberobj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body.cardNo;
          if (response.body.cardNo.length >= 1) {
              this.hideCardField = false;
              this.list = response.body.cardNo;
              this.lastfuzz = [];
              this.list.forEach((element: any) => {
                this.lastfuzz.push((String(element).substr(-6)));
              });
              this.firstdigit = String(opt[0]).substr(0, 10);
              this.displaycard = this.firstdigit.substr(0, 6) + "-" + this.firstdigit.substr(6, 4);
          }
          else {
            this.showcarderror = true;
            this.disablesubmit = true;
          }
          // opt.forEach((element:any) => {
          //   this.lastdigit=(String(element).substr(-6));
          //   this.cardArr=this.lastdigit;
          // });

        }
      });

    }
    else {
      //this.isDisableRadioButton = false;
    }
  }
  daysDifference() {
    this.showDateError = false;
    this.showTable=false;
    if (this.transactionForm.value.startDate !== null && this.transactionForm.value.endDate !== null) {
      let sDate: any = new Date(this.transactionForm.value.startDate);
      let eDate: any = new Date(this.transactionForm.value.endDate)
      //  console.log(this.transactionForm.value.startDate, this.transactionForm.value.endDate)
      const oneDay = 24 * 60 * 60 * 1000;
      const differenceDays = Math.round(Math.abs((sDate - eDate) / oneDay));
      if (differenceDays >= 31) {
        this.showDateError = true;
      }
    }
    else {
      this.showDateError = false;
    }
  }

  checkValidCard(value : any){
    if(!value){ //checking here "" & null
      return false;
    }else{
      return true;
    }
  }

  onSearch() {
    this.showcarderror = false;
    if ((this.transactionForm.value.startDate) > (this.transactionForm.value.endDate)) {
      this.showDateRangeError = true;
    }
    else {
      this.showDateRangeError = false;
      // let listcard = this.cardArr.filter((o: any) => o === this.transactionForm.value.cardNo)
      if (this.checkValidCard(this.transactionForm.value.cardNo)) { //checking cardNo is not "" & null
      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
        "accountNo": this.accountnumber,
        "productType": this.transactionForm.value.productType,
        "cardNo": this.transactionForm.value.productType === "OV" ? this.transactionForm.value.cardNo : this.firstdigit + this.transactionForm.value.cardNo,
        "travelDateFrom": this.datePipe.transform(this.transactionForm.value.startDate, 'dd-MMM-yyyy'), // date error solution
        "travelDateTo": this.datePipe.transform(this.transactionForm.value.endDate, 'dd-MMM-yyyy')
      }

      //  console.log("OBJ: ", obj)
      if (!this.showDateError) {
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_VIEWTRANS_ECARD, obj).subscribe((response: any) => {
          if (response.status === 200) {
            //  console.log(response)
            let opt = response.body;
            this.dataSource.data = (response.body.cardTxnDtoList) ? response.body.cardTxnDtoList : [];
            //  console.log(DATA);
            this.showcarderror = false;

          }
        });
      }
      this.isShowError = true;
      this.submitted = true;
      setTimeout(() => this.dataSource.paginator = this.paginator);
      setTimeout(() => this.dataSource.sort = this.sort);
      this.dataSourceCell = this.dataSource.connect();
      if (this.transactionForm.valid) {
        this.showTable = true;
      }
    }
    else{
      this.notifyService.showWarning('Please enter valid CardNo', 'Warning')
    }
    }
  }

  onReset() {

    // if(this.transactionForm.invalid){
    //   this.formGroupDirective.resetForm();
    // }
    this.submitted = false;
    this.showcarderror = false;
    this.disablesubmit = false;
    this.isShowError = false;
    this.showErr = false;
    this.hideCardField = true;
    this.showDateError =false;
    this.transactionForm.reset();

  }

  download() {
    this.showcarderror = false;

    if ((this.transactionForm.value.startDate) > (this.transactionForm.value.endDate)) {
      this.showDateRangeError = true;
    }
    this.showErr = false;
    this.isShowError = true
    if (this.transactionForm.invalid) {
      this.notifyService.showWarning('Please enter valid CardNo', 'Warning')
      return;
    }
    else {
      this.showDateRangeError = false;
      
    if (this.checkValidCard(this.transactionForm.value.cardNo)) { //checking cardNo is not "" & null  
      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
        "accountNo": this.accountnumber,
        "productType": this.transactionForm.value.productType,
        "cardNo": this.transactionForm.value.productType === "OV" ? this.transactionForm.value.cardNo : this.firstdigit + this.transactionForm.value.cardNo,
        "travelDateFrom": this.datePipe.transform(this.transactionForm.value.startDate, 'dd-MMM-yyyy'),
        "travelDateTo": this.datePipe.transform(this.transactionForm.value.endDate, 'dd-MMM-yyyy')
      }
      let mediaType = "application/pdf"
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_DOWNLOAD, obj, { responseType: 'blob' }).subscribe(response => {
        let blob = new Blob([response], { type: mediaType });
        saveAs(blob, 'Transaction By Card/E-Voucher' + this.transactionForm.value.cardNo)
      },
        e => { throwError(e) })
      //   this.apiService.post(this.cdgService.localhostUrl + UrlConstants.GET_DOWNLOAD,obj).subscribe((response: any) => {
      //     if (response.status === 200) {
      //       //  console.log(response)
      //       let opt = response.body;
      //       if(response){
      //         this.excelService.exportAsExcel([response.body], this.transactionForm.value.cardNo+ '_invoice')
      //       }

      //     }
      // });

      //this.excelService.exportAsExcel([obj], 'Transaction' + this.datePipe.transform(this.transactionForm.value.startDate, 'ddMMMYYYY') + '_to_' + this.datePipe.transform(this.transactionForm.value.endDate, 'ddMMMYYYY'));
  }
  else{
    this.notifyService.showWarning('Please enter valid CardNo', 'Warning')
  }
    this.showcarderror = false;
  }
}


  downloadPdf(data: any) {
    this.showcarderror = false;
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "jobNo": data.jobNo,
      "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      "accountNo": this.accountnumber
    }
    let mediaType = "application/pdf"
    // 9090
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_ERECIEPT, obj, { responseType: 'blob' }).subscribe(response => {
      let blob = new Blob([response], { type: mediaType });
      saveAs(blob, 'E-Receipt' + this.transactionForm.value.cardNo)
    },
      e => { throwError(e) })
    //    this.apiService.post(this.cdgService.localhostUrl + UrlConstants.GET_ERECIEPT,obj).subscribe((response: any) => {
    //     if (response.status === 200) {
    //       //  console.log(response)
    //       let opt = response.body;
    //       if(response){
    //         this.excelService.exportAsExcel([response.body], this.transactionForm.value.cardNo+ '_invoice')
    //       }

    //     }
    // });
    // this.excelService.exportAsExcel([data], data.cardNo + '_invoice')
  }
  gotMySettings() {
    this.router.navigate(['/layout/view_transaction_by_card_setting']);
  }
}


