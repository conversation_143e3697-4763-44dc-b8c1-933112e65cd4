<div fxLayout="column" fxLayoutGap="25px">
    <div fxLayout="column">
        <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
            <div fxFlex="5%">
                <img src="/assets/images/recent transactions.svg" alt="" width="40px">
            </div>
            <div fxFlex fxLayoutAlign="start center">
                <span class="header pb5">View Transaction By Card/E-Voucher</span>
            </div>
        </div>
        <div fxFlex fxLayout="row" fxLayoutAlign="end center">
            <p (click)="gotMySettings()" fxLayoutAlign="center center" fxLayoutGap="5px" class="cursor-pointer">
               <mat-icon color="primary" class="construction-icon">construction</mat-icon> <u class="cursor-pointer setting-font">My Settings</u>
            </p>
        </div>
    </div>
    <form [formGroup]="transactionForm" class="mb-0">
        <div fxLayout="column" fxLayoutGap="20px" class="custom-padding-left">
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px">
                <div fxFlex="12%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center" class="title-head">
                    <span class="date-title">PRODUCT TYPE <span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxFlex="88%" fxFlex.lt-md="65%" fxFlex.md="75%">
                    <mat-form-field appearance="outline" class="inp">
                        <mat-select  name="productType" formControlName="productType" (selectionChange)="onSelect($event)" placeholder="Select Product Type"
                            [disabled]="isProductDisable">
                            <mat-option *ngFor="let acl of productArr" [value]="acl.productTypeId">
                                {{acl.name}}
                            </mat-option>
                        </mat-select>
                        <mat-error *ngIf="submitted && transactionForm.controls.productType.hasError('required')" class="pt15">
                            Product Type Cannot Be Blank
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px">
                <div fxFlex="12%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center" class="title-head">
                    <span class="date-title">CARD NO.<span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxFlex="88%" fxFlex.lt-md="65%" fxFlex.md="75%" fxLayout="row" fxLayoutAlign="start center"> 
                    <span class="pb10" *ngIf="!hideCardField" >{{this.displaycard}} </span>
                    <mat-error *ngIf="showcarderror" class="pt15">
                      No Card Details Found
                    </mat-error>
                    <!-- <mat-form-field appearance="outline" class="inp-cardno" *ngIf="!showcarderror">
						<input matInput type="text" pattern="[0-9]*"
							[(ngModel)]="data.cardNo"  name="cardNo" formControlName="cardNo" (click)="fuzzysearch()" (ngModelChange)="keyPress($event)" onkeypress="return event.charCode >= 48 && event.charCode <= 57" [matAutocomplete]="auto"
							required />
						<mat-autocomplete #auto="matAutocomplete">
							<mat-option *ngFor="let acl of cardArr" [value]="acl">
								{{acl}}
							</mat-option>
                        </mat-autocomplete>
                        <mat-error *ngIf="submitted && transactionForm.controls.cardNo.hasError('required')" class="pt15">
                            Card No. Cannot Be Blank
                        </mat-error>
                    </mat-form-field> -->
                    
                    <mat-form-field appearance="outline" class="inp-cardno" *ngIf="!showcarderror">
						<input matInput type="text" pattern="[0-9]*" name="cardNo" formControlName="cardNo" required />
                        <mat-error *ngIf="submitted && transactionForm.controls.cardNo.hasError('required')" class="pt15">
                            Card No. Cannot Be Blank
                        </mat-error>
                        <mat-error *ngIf="transactionForm.controls.cardNo.hasError('pattern')" class="pt15">
                            Please enter a number
                        </mat-error>
					</mat-form-field>
                </div>
            </div>
            <div fxLayout="column" [ngClass]="{'mt20':(transactionForm.controls.cardNo.hasError('required') && isShowError && submitted) || 
            (transactionForm.controls.cardNo.hasError('required') && 
            transactionForm.controls.cardNo.touched) && submitted}">
                <div fxLayout="row" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutGap="20px">
                    <div fxFlex fxLayout="row">
                        <div fxFlex="31%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center" class="title-head">
                          
                            <span class="date-title">START DATE <span class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxLayout="column" fxFlex="69%" fxFlex.lt-md="65%" fxFlex.md="75%">
                            <mat-form-field  appearance="outline" class="inp"  >
                                <input matInput [matDatepicker]="picker1" name="startDate" formControlName="startDate"
                                    (dateChange)="daysDifference()">
                                <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                                <mat-datepicker #picker1></mat-datepicker>
                                <mat-error *ngIf="submitted && transactionForm.controls.startDate.hasError('required')" class="pt15">
                                 Start Date Cannot Be Blank
                                </mat-error>
                            </mat-form-field>
                            <div *ngIf="showDateError" class="error">
                                Date Range Must Not Be More Than 31 Days
                            </div>
                            <div *ngIf="showDateRangeError"  class="error">
                                End Date should be greater than start Date
                            </div>
                        </div>
                    </div>
                    <div fxFlex fxLayout="row">
                        
                            <div fxFlex="31%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center" class="title-head">
                                <span class="date-title">END DATE <span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="69%" fxFlex.lt-md="65%" fxFlex.md="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput [matDatepicker]="picker2" name="endDate" formControlName="endDate"
                                        (dateChange)="daysDifference()">
                                    <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                                    <mat-datepicker #picker2></mat-datepicker>
                                    <mat-error *ngIf="submitted && transactionForm.controls.endDate.hasError('required')"
                                        class="pt15">
                                         End Date Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        

                    </div>

                </div>
                <!-- <div *ngIf="showDateError" class="error pl">
                    Date Range Must Not Be More Than 31 Days
                </div>
                <div *ngIf="showDateRangeError"  class="error pl">
                    End Date should be greater than start Date
                </div> -->
            </div>
            <div fxFlex fxLayoutGap="30px">
                <button mat-raised-button class="search-btn" [disabled]="disablesubmit" (click)="onSearch()">SEARCH</button>
                <button mat-raised-button class="reset-btn" (click)="onReset()">RESET</button>
                <button mat-raised-button class="download-btn" [disabled]="disablesubmit" (click)="download()">DOWNLOAD PDF</button>
            </div>
            <div fxFlex fxLayoutGap="30px">
                <h3>**Unbilled trips are subjected to adjustments by Cabcharge Asia.</h3>
            </div>
        </div>
    </form>

    <!-- Mobile, tablet view -->
    <!-- <div fxFlex="grow" *ngIf="showTable && transactionForm.valid">
        <mat-card fxHide.gt-md *ngFor="let i of dataSourceCell | async,let ind=index " class="card-table" fxFlex="grow"
            fxLayout="column">
            <div fxLayout="row">
                <p class="label" fxFlex="40%">Card No.</p>
                <p fxFlex="60%">{{i.cardNo}}</p>
            </div>
            <div fxLayout="row">
                <p class="label" fxFlex="40%">Job No.</p>
                <p fxFlex="60%">{{i.jobNo}}</p>
            </div>
            <div fxLayout="row">
                <p class="label" fxFlex="40%">Taxi No.</p>
                <p fxFlex="60%">{{i.taxiNo}}</p>
            </div>
            <div fxLayout="row">
                <p class="label" fxFlex="40%">Pickup-Destination</p>
                <p fxFlex="60%">{{i.pickupDest}}</p>
            </div>
            <div fxLayout="row">
                <p class="label" fxFlex="40%">Travel Period</p>
                <p fxFlex="60%">{{i.travelPeriod}}</p>
            </div>
            <div fxLayout="row">
                <p class="label" fxFlex="40%">Taxi Fares ($)</p>
                <p fxFlex="60%">{{i.taxiFares}}</p>
            </div>
            <div fxLayout="row">
                <p class="label" fxFlex="40%">Status</p>
                <p fxFlex="60%">{{i.status}}</p>
            </div>
            <div fxLayout="row">
                <p class="label" fxFlex="40%">E-Receipt</p>
                <mat-icon class="download-icon" (click)="downloadPdf(i)">get_app</mat-icon>
            </div>
        </mat-card>
    </div>  -->
       <!-- Desktop view -->
       <!-- <div class="mat-elevation-z0" *ngIf="showTable && transactionForm.valid" fxHide.md fxhide.lt-md>
        <table mat-table [dataSource]="dataSource" matSort> -->

            <!-- Serial No. Column -->
            <!-- <ng-container matColumnDef="serialNo">
                <th mat-header-cell *matHeaderCellDef> S No. </th>
                <td mat-cell *matCellDef="let element, let i = index;"> {{i + 1}} </td>
            </ng-container> -->

            <!-- Card No. Column -->
            <!-- <ng-container matColumnDef="cardNo">
                <th class="text-right" mat-header-cell *matHeaderCellDef mat-sort-header>Card No. </th>
                <td mat-cell *matCellDef="let element"> {{element.cardNo }} </td>
            </ng-container> -->

            <!-- Job No. Column -->
            <!-- <ng-container matColumnDef="jobNo">
                <th class="text-right" mat-header-cell *matHeaderCellDef mat-sort-header> Job No. </th>
                <td mat-cell *matCellDef="let element"> {{element.jobNo}} </td>
            </ng-container> -->

            <!-- Taxi No. Column -->
            <!-- <ng-container matColumnDef="taxiNo">
                <th class="text-right" mat-header-cell *matHeaderCellDef mat-sort-header> Taxi No. </th>
                <td mat-cell *matCellDef="let element"> {{element.taxiNo}} </td>
            </ng-container> -->

            <!--  Pickup-Destination Column -->
            <!-- <ng-container matColumnDef="pickupDest">
                <th mat-header-cell *matHeaderCellDef> Pickup-Destination </th>
                <td mat-cell *matCellDef="let element"> {{element.pickupDest}} </td>
            </ng-container> -->

            <!-- Travel Period Column -->
            <!-- <ng-container matColumnDef="travelPeriod">
                <th mat-header-cell *matHeaderCellDef> Travel Period </th>
                <td mat-cell *matCellDef="let element"> {{element.travelPeriod}} </td>
            </ng-container> -->

            <!-- Taxi Fares($) Column -->
            <!-- <ng-container matColumnDef="taxiFares">
                <th mat-header-cell *matHeaderCellDef> Taxi Fares ($) </th>
                <td mat-cell *matCellDef="let element"> {{element.taxiFares | currency}} </td>
            </ng-container> -->

            <!-- Status Column -->
            <!-- <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef> Status </th>
                <td mat-cell *matCellDef="let element"> {{element.status}} </td>
            </ng-container> -->

            <!-- E-Receipt Column -->
            <!-- <ng-container matColumnDef="downloadAction">
                <th mat-header-cell *matHeaderCellDef> E-Receipt </th>
                <td mat-cell *matCellDef="let element">

                    <mat-icon class="download-icon" (click)="downloadPdf(element)">get_app</mat-icon>

                </td>
            </ng-container> -->

<!-- 
            <tr mat-header-row *matHeaderRowDef="displayedColumns" fxHide.lt-md fxHide.md></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;" fxHide.lt-md fxHide.md></tr>
        </table>
        <mat-paginator class="paginator" [pageSizeOptions]="[5, 10, 20, 50, 100]"
            showFirstLastButtons></mat-paginator>
    </div> -->


    <!-- Desktop view like other components -->
    <div class="tbl-container pt15 pl20 pr20" *ngIf="showTable && transactionForm.valid" >
        <mat-table #table class="mat-table mat-cdk-table" [dataSource]="dataSource" matSort>


             <!-- Serial No. Column  -->
            <ng-container matColumnDef="serialNo" class="pr15">
                <mat-header-cell *matHeaderCellDef class="w20 padRight"> S No. </mat-header-cell>
                <mat-cell *matCellDef="let element, let i = index;" class="sno" fxLayoutAlign="start center">
                 <div class="word-break w20"> {{i + 1}} </div>   
                </mat-cell>
            </ng-container> -->

            <!-- Card No. Column -->
            <ng-container matColumnDef="cardNo" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Destination</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label"> Card No.:</span>
                    <div class="word-break width80">
                        {{element.cardNo }}
                      </div>
                    
                </mat-cell>
            </ng-container>

            <!-- Job No. Column -->
            <ng-container matColumnDef="jobNo" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Job No.</mat-header-cell>
                <mat-cell *matCellDef="let element"> 
                    <span class="mobile-label"> Job No.:</span>
                    {{element.jobNo}}
                </mat-cell>
            </ng-container>

            <!-- Taxi No. Column -->
            <ng-container matColumnDef="taxiNo" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Taxi No.</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label">Taxi No.:</span>
                     {{element.taxiNo}}
                </mat-cell>
            </ng-container>

            <!--  Pickup-Destination Column -->
             <ng-container matColumnDef="pickUpAddress" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Pick up</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label">Pickup:</span>
                     <!-- {{element.pickUpAddress}} -->
                     <div class="word-break width80">
                        {{element.pickUpAddress}}
                      </div>
                </mat-cell>
            </ng-container>

            <ng-container matColumnDef="destination" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Destination</mat-header-cell>
                <mat-cell *matCellDef="let element" >
                    <span class="mobile-label">Destination:</span>
                    <div class="word-break width80">
                        {{element.destination}}
                      </div>
                    
                </mat-cell>
            </ng-container>

            <!-- Travel Period Column -->
            <ng-container matColumnDef="tripStartDate" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Trip Start Date</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label ">Trip StartDate:</span>
                     <div class="word-break width80">
                        {{element.tripStartDate}}
                      </div>
                </mat-cell>
            </ng-container>
            <ng-container matColumnDef="tripEndDate" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Trip End Date</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label">Trip EndDate:</span>
                     <div class="word-break width80">
                        {{element.tripEndDate}}
                      </div>
                </mat-cell>
            </ng-container>

            <!-- Taxi Fares($) Column -->
            <ng-container matColumnDef="fareAmt" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Taxi Fares ($)</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label">Taxi Fares ($):</span>
                     {{element.fareAmt | currency}}
                </mat-cell>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="txnStatus" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Status</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label">Status:</span>
                     {{element.txnStatus}}
                </mat-cell>
            </ng-container>

            <!-- E-Receipt Column -->
            <ng-container matColumnDef="downloadAction" class="pr15">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>E-Receipt</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label">E-Receipt:</span>
                    <mat-icon class="download-icon" (click)="downloadPdf(element)">get_app</mat-icon>
                </mat-cell>
            </ng-container>

            <mat-header-row *matHeaderRowDef="displayedColumns" ></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns;" [ngStyle]= "{'background-color': row.colorHex ? row.colorHex : '#cccccc'}"></mat-row>
            <!-- <tr class="mat-row" *matNoDataRow fxLayoutAlign="center center">
                <td class="mat-cell" colspan="4">No Data Present</td>
              </tr> -->
        </mat-table>
        <tr class="mat-row"  *ngIf="dataSource.data.length == 0" fxLayoutAlign="center center">
            <td class="mat-cell" colspan="5" >No Records are  Found</td>
          </tr>
        <mat-paginator class="paginator" [pageSizeOptions]="[5, 10, 20, 50, 100]"
            showFirstLastButtons></mat-paginator>
    </div> 



   

</div>