@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';

::ng-deep th.mat-mdc-header-cell{
    height: 36px !important;
     padding-top: 0 !important;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button{
    -webkit-appearance: none;
    margin: 0;
  }

.mat-mdc-header-row{
    height: auto !important;
  }

  .width80{
    width:80% 
  }
  .w20{
    width:20% 
  }
  // .mat-column-serialNo {
  //   flex: 0 0 5%;
  //  } 
  //  .mat-column-taxiNo{
  //   flex: 0 0 12%;
  //  }
.mat-column-cardNo {
  flex: 0 0 12%;
}
// .mat-column-destination{
//   flex: 0 0 13%;
// }
// .mat-column-pickUpAddress{
//   flex: 0 0 13%;
// }
.mat-column-tripEndDate{
  flex: 0 0 11%;
}
.mat-column-tripStartDate{
  flex: 0 0 11%;
}
.mat-column-fareAmt{
  flex: 0 0 8%;
}
// ::ng-deep .mat-column-cardNo{
//   width: 80% !important;
 
// }
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
::ng-deep .main-icon{
    color: $lightGrey !important;
    // font-size: $cdgsz-font-size-xxl !important;
    height: 40px !important;
    width: 40px !important;
}
.update-btn{
    width:205px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
}
.inp{
    width: 320px !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
 }
 .error{
    font-size: $cdgsz-font-size-sm;
    color: $cdgc-font-warn;
}
.pl{
    padding-left: 252px !important;
}
.search-btn{
    width:205px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 5px !important;
    margin-bottom: 10px;
}
.reset-btn{
  width:205px;
  background-color:$cdgc-warn !important;
  color: $cdgc-font-accent !important;
  border-radius: 5px !important;
  margin-bottom: 10px !important;
}
.download-btn{
    width:205px;
    background-color:$cdgc-font-accent !important;
    color: $cdgc-bg-hue !important;
    border: 1px solid $cdgc-bg-hue;
    border-radius: 5px !important; 
    margin-bottom: 10px;
}

table {
    width: 100%;
    font-family: Roboto;
    font-style: normal;
    text-align: center;
  
  }

  .mat-mdc-header-cell{
    font-weight: $cdgsz-font-weight-normal !important;
    font-size: $cdgsz-font-size-sm !important;
    background-color: $cdgc-bg-prime !important;
    color: $cdgc-font-accent ;
    height: 36px !important;
    text-align: center;
    // border-radius: 12px !important;
    // padding-right:5px ;
    // word-break: break-all !important;
  }
  :host ::ng-deep .mat-sort-header-arrow {
    margin: 0 0 0 6px !important;
    right: 5px !important;
    transform: none !important;
    opacity: 1 !important;
    color: $cdgc-bg-accent;
}
  .mat-mdc-header-cell:first-child{
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
  }

  .mat-mdc-header-cell:last-child{
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
  }

  .mat-mdc-cell{
    font-weight: $cdgsz-font-weight-normal !important;
    // font-size: $cdgsz-font-size-prime;
    // line-height: 18px;
    font-size: 14px;
    line-height: 16px;
    // text-align: center !important;
 }

 .mat-column-pickupDest{
    width:14% !important;
    padding: 0px 4px 0px 6px !important; 
  }
 

  .mat-column-travelPeriod{
    width:15% !important;
    padding: 0 0px 0 10px;
  }
 

  .download-icon{
    color: $cdgc-font-prime;
    cursor: pointer;
}

// ::ng-deep .mat-sort-header-arrow{
//     transform: none !important;
//     opacity: 1 !important;
//     color: $cdgc-bg-accent;
//   }

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
 ::ng-deep .paginator .mat-mdc-form-field-appearance-legacy .mat-mdc-form-field-text-infix{
    padding: 1.5em 0 !important;
}


/* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
 ::ng-deep .paginator .mat-mdc-select-arrow{
    
    margin-top:0px !important;
    

}
  .card-table{
    border-radius: 12px !important;
    border: 1px solid $cdgc-border-prime;
    margin-bottom: 10px !important;
  }

  .label{
    font-weight: $cdgsz-font-weight-bold;
  }

  .mb-0{
    margin-bottom: 0 !important;
  }

  .cell-inp{
    width: 100%;
  }
  .construction-icon{
    font-size: 25px !important;
  }
  .setting-font{
    color: $cdgc-font-prime;
  }
  // .inp-cardno{
  //   width: 150px !important;
  // }
  @media screen and (max-width: 500px) {
    // .inp-cardno{
    //     width: 95px !important;
    // }
    .inp{
      width: 100% !important;
    }
 }

 .mt20{
   margin-top: 20px !important;
 }

 .sno{
  display: block;
}
.mobile-label{
  display: none;
}
@media screen and (max-width: 600px) {
  .mobile-label{
      display: inline-block;
      width: 72px !important;
      font-weight: $cdgsz-font-weight-bold !important;
        // word-break: break-all !important;
  }
  .mat-mdc-header-row{
      display: none;
  }
  .mat-mdc-row{
      flex-direction: column;
      align-items: start;
      padding: 8px 24px;
      border-radius: 12px !important;
      border: 1px solid $cdgc-border-prime;
      min-height: 28px !important;
      margin-bottom: 10px;
  }
  .mat-column-pickupDest{
    width:100% !important;
    padding: 8px 24px  8px 0px!important;  
  }

  .mat-column-travelPeriod{
    width:100% !important;
    padding: 8px 24px 8px 0px!important;
  }
 
  .mat-mdc-footer-row{
    flex-direction: column;
    align-items: start;
    padding: 8px 24px;
    border-radius: 12px !important;
    border: 1px solid $cdgc-border-prime;
    min-height: 28px !important;
    margin-bottom: 10px;
}
  :host ::ng-deep  mat-cell:first-of-type, mat-header-cell:first-of-type, mat-footer-cell:first-of-type {
   padding-left: 0px !important;
  }
  .sno{
      display: none !important;
  }
}

//chnage by navani
::ng-deep .title-head{
  max-height: 55px !important;
}
::ng-deep .padRight{
  padding-right:15px !important
}

:host ::ng-deep .mdc-data-table__cell, .mdc-data-table__header-cell { //for table header - temp
  padding: -1px 0px 0px 0px;
}