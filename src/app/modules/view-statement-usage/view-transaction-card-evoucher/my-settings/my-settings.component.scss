@import '@angular/material/theming';
@import './../../../../../styles/colors.scss';
@import './../../../../../styles/sizing.scss';
@import './../../../../../styles/main.scss';
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
:host .mat-icon{
    color: $cdgc-font-prime;
}
.card-icon{
    color: $lightGrey !important;
    font-size: 40px !important;
    height: 40px !important;
    width: 40px !important;
}
.num-inp{
    width: 70px;
}
:host input[type=number]::-webkit-outer-spin-button{
    opacity: 1;
}
:host input[type=number]::-webkit-inner-spin-button{
    opacity: 1;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
}
.circle{
        // display: block;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        box-sizing: border-box;
        border:1px solid #dadada;
        box-shadow: 0 2px 4px 0 rgba(0,0,0,0.14);
}
.circle-tbl{
    // display: block;
    width: 40px;
    height: 40px !important;
    border-radius: 50%;
    box-sizing: border-box;
    border:1px solid #dadada;
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.14);
}
.active{
    border: 3px double rgb(10, 10, 10) ;

    // border: 3px double rgb(143, 137, 137);
    // margin: 10px;
}
.add-btn{
    width:100px;
    background-color:$cdgc-bg-prime !important;
    color: $cdgc-font-accent;
    border-radius: 10px !important;
}
.error {
    color: $cdgc-font-warn;
    font-weight: 600;
    font-size: $cdgsz-font-size-xs;
}
.tbl-container{
    display: flex;
    flex-direction: column;
    // background-color: transparent !important;
    // max-width: 300px;
    max-height: 500px;
}
.mat-mdc-header-row{
    border-radius: 10px;
    background-color:$cdgc-bg-prime !important;
    color: $cdgc-font-accent !important;
}
.mat-mdc-header-cell {
    color: $cdgc-font-accent !important;
    
}
.mat-mdc-row{
    border-radius: 12px;
    color: $lightGrey !important;

}
.sno{
    display: block;
}
.mobile-label{
    display: none;
}
@media screen and (max-width: 600px) {
    .mobile-label{
        display: inline-block;
        width: 145px;
        font-weight: $cdgsz-font-weight-bold;
    }
    .mat-mdc-header-row{
        display: none;
    }
    .mat-mdc-row{
        flex-direction: column;
        align-items: start;
        padding: 8px 24px;
        border-radius: 12px !important;
        border: 1px solid $cdgc-border-prime;
        min-height: 28px !important;
        // margin-bottom: 10px !important;
    }
    .sno{
        display: none !important;
    }
    .inp{
        width: 178px !important;
    }
    :host input[type=number]::-webkit-outer-spin-button{
        opacity: 1;
    }
    :host input[type=number]::-webkit-inner-spin-button{
        opacity: 1;
    }
    .circle{
        // display: block;
        width: 40px;
        height: 22px !important;
        border-radius: 50%;
        box-sizing: border-box;
        border:1px solid #dadada;
        box-shadow: 0 2px 4px 0 rgba(0,0,0,0.14);
}
.circle-tbl{
    // display: block;
    width: 25px;
    height: 25px !important;
    border-radius: 50%;
    box-sizing: border-box;
    border:1px solid #dadada;
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.14);
}
.active{
    border: 3px double rgb(10, 10, 10) ;

    // border: 3px double rgb(143, 137, 137);
    // margin: 10px;
}
}