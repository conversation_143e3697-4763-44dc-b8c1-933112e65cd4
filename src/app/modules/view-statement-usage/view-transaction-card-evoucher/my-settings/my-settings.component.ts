import { Component, HostListener, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { Router } from '@angular/router';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { Location } from '@angular/common';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { CommonModalComponent } from 'src/app/shared/shared-modules/common-modal/common-modal.component';
import { element } from 'protractor';
import { MatTableDataSource } from '@angular/material/table';
import { DatePipe } from '@angular/common';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as _moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core'

// import { cosh } from 'core-js/fn/number';

export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
}
@Component({
  selector: 'app-my-settings',
  templateUrl: './my-settings.component.html',
  styleUrls: ['./my-settings.component.scss'],
  providers: [DatePipe, { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] }, { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]
})
export class MySettingsComponent implements OnInit {
  mySettingsObj =
    {
      dayList: [
        { name: 'Mon', value: 'Monday', checked: false },
        { name: 'Tue', value: 'Tuesday', checked: false },
        { name: 'Wed', value: 'Wednesday', checked: false },
        { name: 'Thr', value: 'Thursday', checked: false },
        { name: 'Fri', value: 'Friday', checked: false },
        { name: 'Sat', value: 'Saturday', checked: false },
        { name: 'Sun', value: 'Sunday', checked: false },
        { name: 'Public Holiday', value: 'Public Holiday', checked: false }
      ],

     
      startTime: {
        hr:'00',
        min: '00'
      },
      endTime: {
        hr: '23',
        min: '59'
      },
      colourCode: ['#9400D3', '#4B0082', '#0000FF', '#00FF00', '#FFFF00', '#FF7F00', '#FF0000', '#4eaa25']
      // colourCode: ['#2f0502', '#4a34ae', '#0030ae', '#44afb1', '#5e8639', '#990000', '#a72e5a', '#4eaa25']
    }
  settingTbl: any = [
    // {
    //   dayList: [''],
    //   time: '',
    //   colourCode: ''
    //   settingId :''
    // }
  ]
   hrList = [
     {viewValue:'00'}, {viewValue:'01'},{viewValue:'02'},
     {viewValue:'03'},{viewValue:'04'},{viewValue:'05'},
     {viewValue:'06'}, {viewValue:'07'}, {viewValue:'08'},
     {viewValue:'09'}, {viewValue:'10'}, {viewValue:'11'},
     {viewValue:'12'},{viewValue:'13'}, {viewValue:'14'},
     {viewValue:'15'}, {viewValue:'16'}, {viewValue:'17'},
      {viewValue:'18'},{viewValue:'19'},{viewValue:'20'}, {viewValue:'21'}, {viewValue:'22'},
     {viewValue:'23'}
   ]
   minList = [
    {viewValue:'00'}, {viewValue:'01'}, {viewValue:'02'},  {viewValue:'03'},{viewValue:'04'},
    {viewValue:'05'}, {viewValue:'06'}, {viewValue:'07'},{viewValue:'08'},{viewValue:'09'},
    {viewValue:'10'}, {viewValue:'11'},{viewValue:'12'},{viewValue:'13'},{viewValue:'14'},
    {viewValue:'15'}, {viewValue:'16'},{viewValue:'17'},{viewValue:'18'}, {viewValue:'19'},
    {viewValue:'20'},{viewValue:'21'},{viewValue:'22'},{viewValue:'23'},{viewValue:'24'},
    {viewValue:'25'},{viewValue:'26'},{viewValue:'27'},{viewValue:'28'},{viewValue:'29'},
     {viewValue:'30'}, {viewValue:'31'}, {viewValue:'32'}, {viewValue:'33'}, {viewValue:'34'},
     {viewValue:'35'}, {viewValue:'36'}, {viewValue:'37'}, {viewValue:'38'},
    {viewValue:'39'},  {viewValue:'40'},  {viewValue:'41'}, {viewValue:'42'}, 
    {viewValue:'43'}, {viewValue:'44'}, {viewValue:'45'},
    {viewValue:'46'}, {viewValue:'47'},{viewValue:'48'},{viewValue:'49'},
    {viewValue:'50'},  {viewValue:'51'}, {viewValue:'52'},{viewValue:'53'},{viewValue:'54'},
    {viewValue:'55'}, {viewValue:'56'}, {viewValue:'57'}, {viewValue:'58'},  {viewValue:'59'}  
  ]
 
  // settingTbl = [];
  selected: any = null;
  isShowDaysError: boolean = false;
  isShowTimeError: boolean = false;
  isShowColorEroor: boolean = false;
  displayedColumns = ['slno', 'description', 'time', 'colourCode', 'action'];
  // dataSource=this.settingTbl;
  // dataSource:any;
  dataSourceData: any = [];

  dataSource = new MatTableDataSource<any>(this.settingTbl);
  settingsId: any;
  startFormatTime: any;
  endFormatTime: string;
  startFormatedTime: string;
  endFormatedTime: string;
  input: string;
  innerWidth: number;
  isMobileLayout: boolean=false;
  constructor(public localStorage: LocalStorageService, private location: Location, private router: Router, public cdgService: CdgSharedService, private fb: FormBuilder,
    private notifyService: NotificationService, private apiService: ApiServiceService, public dialog: MatDialog, private datePipe: DatePipe) {
    // this.dataSource= this.settingTbl
  }

  ngOnInit(): void {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 600) { // 768px portrait
      this.isMobileLayout = true;
    }
    else{
      this.isMobileLayout = false;
    }
    this.getListofData();
    //  console.log(this.mySettingsObj.startTime.hr)
    //  console.log(this.mySettingsObj.startTime.min)
    //  console.log(this.mySettingsObj.endTime.min)
    //  console.log(this.mySettingsObj.endTime.hr)
    // //  console.log(obj);
    // for (var i = 0; i < this.settingTbl.length; i++) {
    //   if (this.mySettingsObj.colourCode.indexOf(this.settingTbl[i].colourCode) > -1) {
    //     this.mySettingsObj.colourCode.splice(this.mySettingsObj.colourCode.indexOf(this.settingTbl[i].colourCode), 1)
    //   }
    // }
  }
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 600) { // 768px portrait
      this.isMobileLayout = true;
    }
    else{
      this.isMobileLayout = false;
    }
  }
  getListofData() {
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "settingId": null,
      "dayList": null,
      "startTime": null,
      "endTime": null,
      "colourCode": null
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.LOAD_MY_SETTINGS, obj).subscribe((response: any) => {
      if (response.status === 200) {
        //  console.log(response.body);
        let opt = response.body;
        //  console.log(opt);
        this.settingTbl = [];

        // let starttime = this.datePipe.transform(opt.startTime, "HH:mm")
        // let endtime = this.datePipe.transform(opt.endTime, "HH:mm")

        // let new = opt.startTime | date:'HH:mm'
        // let settingObj = opt;
        if (opt.length > 0) {
          for (var i = 0; i < opt.length; i++) {
            let starttime = opt[i].startTime.toString().split(':')
            let startHr = starttime[0].length;
            let startMin = starttime[1].length;
            let endtime = opt[i].endTime.toString().split(':')
            let endHr = endtime[0].length;
            let endMin = endtime[1].length;
            //  console.log(starttime, startHr, startMin);
            //  console.log(opt[i]);
            if (startHr == 1) {
              starttime[0] = '0' + startHr;
            }
            if (startMin == 1) {
              starttime[1] = '0' + startMin;
            }
            if (endHr == 1) {
              endtime[0] = '0' + endHr;
            }
            if (endMin == 1) {
              endtime[1] = '0' + endMin;
            }
            let startTimeArr = [starttime[0], starttime[1]]
            this.startFormatTime = startTimeArr.join(':')
            //  console.log(this.startFormatTime);

            let endTimeArr = [endtime[0], endtime[1]]
            this.endFormatTime = endTimeArr.join(':')
            //  console.log(this.endFormatTime);
            this.settingTbl.push({
              "dayList": opt[i].dayList,
              'time': opt[i].startTime + 'HRS -' + opt[i].endTime + 'HRS',
              // time:
              "colourCode": opt[i].colourCode,
              "settingId": opt[i].settingId
            })

            // let starttime = opt[i].startTime.split(':');
            // let endtime = opt[i].endtime.split(':')

            // let starttime = this.datePipe.transform(opt[i].startTime, "HH:mm") 
            // let endtime = this.datePipe.transform(opt[i].endTime, "HH:mm") 

            // endtime
            this.dataSource = this.settingTbl;
          }



          // this.dataSource=[];
          //  this.settingTbl.forEach((element:any) => {
          //  this.dataSource.push(element)
          //  this.dataSourceData.push(element)
          //  this.dataSource.push(this.dataSourceData);
          // });
          // //  console.log(this.dataSourceData)
          // //  console.log(this.dataSource)

          // this.dataSource = [...this.dataSource];
          // //  console.log(this.settingTbl, "settingTable");
          // //  console.log(this.dataSource,"datasource");
        }
        for (var i = 0; i < this.settingTbl.length; i++) {
          if (this.mySettingsObj.colourCode.indexOf(this.settingTbl[i].colourCode) > -1) {
            this.mySettingsObj.colourCode.splice(this.mySettingsObj.colourCode.indexOf(this.settingTbl[i].colourCode), 1)
          }
        }
      }

    },
      e => {
        //  console.log(e)
        this.notifyService.showError(e.message, "Error");
      })
  }
  leadingzero(input:any){
   let temp= input.toString();
if(!isNaN(input) && temp.length === 1){
  input ='0' + input;
  this.mySettingsObj.startTime.hr=input;
}
  }
  leadingzero1(input:any){
    let temp= input.toString();
 if(!isNaN(input) && temp.length === 1){
   input ='0' + input;
   this.mySettingsObj.startTime.min=input;
 }
   }
   leadingzero2(input:any){
    let temp= input.toString();
 if(!isNaN(input) && temp.length === 1){
   input ='0' + input;
   this.mySettingsObj.endTime.hr=input;
 }
   }
   leadingzero3(input:any){
    let temp= input.toString();
 if(!isNaN(input) && temp.length === 1){
   input ='0' + input;
   this.mySettingsObj.endTime.min=input;
 }
   }

  add() {
    //  console.log(this.mySettingsObj.startTime.hr)
    //  console.log(this.mySettingsObj.startTime.min)
    //  console.log(this.mySettingsObj.endTime.min)
    //  console.log(this.mySettingsObj.endTime.hr)
    //  console.log(this.settingTbl.length);
    const daysChecked = this.mySettingsObj.dayList.filter(o => o.checked === true);
    if (daysChecked.length === 0 || (this.mySettingsObj.startTime.hr >= this.mySettingsObj.endTime.hr && this.mySettingsObj.startTime.min >= this.mySettingsObj.endTime.min) || this.selected === null) {
      if (daysChecked.length === 0) {
        this.isShowDaysError = true;
      }
      else if (this.mySettingsObj.startTime.hr >= this.mySettingsObj.endTime.hr && this.mySettingsObj.startTime.min >= this.mySettingsObj.endTime.min) {
        this.isShowTimeError = true;
      }
      else if (this.selected === null) {
        this.isShowColorEroor = true;
      }
    }
    // else if(this.settingTbl.length >= 3){
    //   this.mySettingsObj.dayList.forEach(element => {
    //     element.checked = false;
    //    })
    //   this.notifyService.showError("Exceed allowed total 3 setting calendar per person", "Error");
    // }

    else {
      // let found: boolean = false
      let dayArr: any[] = [];
      daysChecked.forEach(element => {
        dayArr.push(element.value)
      });
      // let obj = {
      //   dayList: dayArr,
      //   time: this.mySettingsObj.from.hr + ':' + this.mySettingsObj.from.min + 'HRS - ' + this.mySettingsObj.to.hr + ':' + this.mySettingsObj.to.min + 'HRS',
      //   colourCode: this.mySettingsObj.colourCode[this.selected],

      // }#4a34ae


      //If the api gives 500 check the request body format whether it is matching or not 
      // especially dayList,startTime,endTime
      // Tried using the response json provided by Shruti .It got added.
      //Please check.

      // let obj= {
      //   "settingId" : null,
      //   "accessId" : this.localStorage.localStorageGet("loginUserDetails").accessId,
      //   "dayList" : dayArr,
      //   "startTime" : this.mySettingsObj.startTime?.hr + ':' + this.mySettingsObj.startTime.min ,
      //   "endTime" : this.mySettingsObj.endTime.hr + ':' + this.mySettingsObj.endTime.min,
      //   "colourCode" : this.mySettingsObj.colourCode[this.selected]
      // }
      // //  console.log(obj);
      //   let obj={
      //     "settingId" : null,
      //     "accessId" : "<EMAIL>",
      //     "dayList" : [
      //         "Tuesday"
      //         ],
      //     "startTime" : "00:56",
      //     "endTime" :"12:25",
      //     "colourCode" : "#FF1493"
      // }

      //commented by neevika on aug 19 -------------------code to format time 
      // let startHour = this.mySettingsObj.startTime.hr.toString();
      // let startMinute = this.mySettingsObj.startTime.min.toString();
      // let startHrlen = startHour.length;
      // let startMinlen = startMinute.length;
      // if (startHrlen == 1) {
      //   startHour = '0' + startHour;
      //   //  console.log(startHour);
      // }
      // if (startMinlen == 1) {
      //   startMinute = '0' + startMinute;
      //   //  console.log(startMinute);
      // }
      // let endHour = this.mySettingsObj.endTime.hr.toString();
      // let endMinute = this.mySettingsObj.endTime.min.toString();
      // let endHrlen = endHour.length;
      // let endMinlen = endMinute.length;
      // if (endHrlen == 1) {
      //   endHour = '0' + endHour;
      //   //  console.log(endHour);
      // }
      // if (endMinlen == 1) {
      //   endMinute = '0' + endMinute;
      //   //  console.log(endMinute);
      // }
      // let startTimeArray = [startHour, startMinute]
      // this.startFormatedTime = startTimeArray.join(':')
      // //  console.log(this.startFormatedTime);

      // let endTimeArray = [endHour, endMinute]
      // this.endFormatedTime = endTimeArray.join(':')
      // //  console.log(this.endFormatedTime);

//commented on aug 19 by neevika --------ends here



      // let starttime = this.mySettingsObj.startTime.toString().split(':');
      // //  console.log(starttime,starttime[0],starttime[0]);
      // let startHr = starttime[0].length;
      // let startMin = starttime[1].length;
      // let endtime = this.mySettingsObj.endTime.toString().split(':')
      // let endHr = endtime[0].length;
      // let endMin = endtime[1].length;
      // //  console.log(starttime, startHr, startMin);
      // if(startHr == 1){
      // starttime[0] = '0'+startHr;
      // }
      // if(startMin == 1){
      //   starttime[1] = '0'+startMin;
      // }
      // if(endHr == 1){
      //   endtime[0] = '0'+endHr;
      // }
      // if(endMin == 1){
      //   endtime[1]= '0'+endMin;
      // }
      // let startTimeArr = [starttime[0],starttime[1]]
      // this.startFormatTime = startTimeArr.join(':')
      // //  console.log(this.startFormatTime);

      // let endTimeArr = [endtime[0],endtime[1]]
      // this.endFormatTime = endTimeArr.join(':')
      // //  console.log(this.endFormatTime);




      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "settingId": null,
        dayList: dayArr,
        // startTime: this.startFormatedTime,
        startTime: this.mySettingsObj.startTime.hr + ':' + this.mySettingsObj.startTime.min,
        // endTime: this.mySettingsObj.endTime.hr + ':' + this.mySettingsObj.endTime.min,
        // endTime: this.endFormatedTime,
        endTime: this.mySettingsObj.endTime.hr + ':' + this.mySettingsObj.endTime.min,
        // time: this.mySettingsObj.startTime.hr + ':' + this.mySettingsObj.startTime.min + 'HRS - ' + this.mySettingsObj.endTime.hr + ':' + this.mySettingsObj.endTime.min + 'HRS',
        colourCode: this.mySettingsObj.colourCode[this.selected]
      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.LOAD_MY_SETTINGS, obj).subscribe((response: any) => {
        if (response.status === 200) {
          // let opt = response.body;
          this.getListofData();
          // //  console.log(response.body);
          // this.settingsId = opt.settingId
          // this.settingTbl.push({
          //   "dayList": opt.dayList,
          //   'time': opt.startTime + 'HRS -' + opt.endTime + 'HRS',
          //   "colourCode":opt.colourCode,
          //   "settingId":opt.settingId
          // }) 

          // this.settingTbl.forEach((element : any)=> {
          //   for (var i = 0; i < element.dayList.length; i++) {
          //     for (var j = 0; j < obj.dayList.length; j++) {
          //       if (element.dayList[i] === obj.dayList[j])
          //         found = true

          //     }

          //   }
          // });

          // if (found) {
          //   this.notifyService.showError("There is an overlap setting on selected time", "Error")
          // }
          // else {

          // this.settingTbl.push(obj)
          this.mySettingsObj.dayList.forEach(element => {
            element.checked = false;
          })
          this.mySettingsObj.startTime.hr = '00';
          this.mySettingsObj.startTime.min = '00';
          this.mySettingsObj.endTime.hr = '23';
          this.mySettingsObj.endTime.min = '59';
          this.isShowTimeError = false;
          // this.dataSource = this.settingTbl;
          // this.dataSource=[];

          // this.settingTbl.forEach((element:any) => {
          //   this.dataSource.push(element);
          // this.dataSourceData.push(element);
          // this.dataSource = this.dataSourceData;
          // });
          // this.dataSource = this.dataSourceData;
          for (var i = 0; i < this.settingTbl.length; i++) {
            if (this.mySettingsObj.colourCode.indexOf(this.settingTbl[i].colourCode) > -1) {
              this.mySettingsObj.colourCode.splice(this.mySettingsObj.colourCode.indexOf(this.settingTbl[i].colourCode), 1)

            }
          }
          // }
        }
      },
        e => {
          //  console.log(e)
          this.notifyService.showError(e.error.message, "Error");

        })
      // this.settingTbl.forEach((element:any) => {
      //   for (var i = 0; i < element.dayList.length; i++) {
      //     for (var j = 0; j < obj.dayList.length; j++) {
      //       if (element.dayList[i] === obj.dayList[j])
      //         found = true

      //     }

      //   }
      // });
      // if (found) {
      //   this.notifyService.showError("There is an overlap setting on selected time", "Error")
      // }
      // else {

      //   this.settingTbl.push(obj)
      //   this.mySettingsObj.dayList.forEach(element => {
      //    element.checked = false;
      //   })

      //   this.dataSource=[];

      //   this.settingTbl.forEach((element:any) => {
      //     this.dataSource.push(element);

      //   });
      //   // for (var i = 0; i < this.settingTbl.length; i++) {
      //   if (this.mySettingsObj.colourCode.indexOf(this.settingTbl[i].colourCode) > -1) {
      //     this.mySettingsObj.colourCode.splice(this.mySettingsObj.colourCode.indexOf(this.settingTbl[i].colourCode), 1)

      //   }
      // }
      // }
    }
  }
  showDaysError() {
    this.isShowDaysError = false;
  }
  showColorError(i: number) {
    this.selected = i;
    this.isShowColorEroor = false
  }

  goToPrevPage() {
    this.location.back();
  }

  delete(val: any, i: number) {
    //  console.log(val)
    let timeVar = val.time;
    let splitTime = timeVar.split('-');
    let startTimeVar = splitTime[0];
    //  console.log(startTimeVar)  //00:00HRS
    let endTimeVar = splitTime[1]; //00.90HRS
    //  console.log(endTimeVar)
    let truncatedStartTime = startTimeVar.slice(0, -4)
    //  console.log(truncatedStartTime);
    let truncatedEndTime = endTimeVar.slice(0, -3)
    //  console.log(truncatedEndTime);
    const dialogConfig = new MatDialogConfig()
    dialogConfig.data = {
      title: "Confirmation",
      msg: "Do you really want to delete this setting ?",
      btnClose: "Cancel",
      btnConfirm: "Ok",
      func: 'delete'
    }
    const dialogRef = this.dialog.open(CommonModalComponent, dialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        //  console.log("yes needed here setting")
        // dummydata to be deleted after verification.
        // let obj1 = {
        //   "settingId": 1193,
        //   "accessId": "<EMAIL>",
        //   "dayList": [
        //     "Tuesday",
        //     "Saturday"
        //   ],
        //   "startTime": "13:13",
        //   "endTime": "16:30",
        //   "colourCode": "#FFC0CB"
        // }
        // //  console.log(obj1);
        let obj = {
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "settingId": val.settingId,
          "dayList": val.dayList,
          startTime: truncatedStartTime,
          // + ':' + val.startTime
          endTime: truncatedEndTime,
          // .hr + ':' + val.endTime.min
          // time: this.mySettingsObj.startTime.hr + ':' + this.mySettingsObj.startTime.min + 'HRS - ' + this.mySettingsObj.endTime.hr + ':' + this.mySettingsObj.endTime.min + 'HRS',
          colourCode: val.colourCode
        }
        //  console.log(obj);
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.DELETE_MY_SETTINGS, obj).subscribe
          ((response: any) => {
            if (response.status === 200) {
              //  console.log(this.settingTbl,response.body);
              this.getListofData();
              if(response.body){
                this.settingTbl=[];
                response.body.forEach((opt:any) => {
                  this.settingTbl.push({
                    "dayList": opt.dayList,
                    'time': opt.startTime + 'HRS -' + opt.endTime + 'HRS',
                    "colourCode": opt.colourCode,
                    "settingId": opt.settingId
                  })
                });
                this.dataSource=this.settingTbl;
              }
              //  console.log(this.settingTbl);
              this.mySettingsObj.colourCode.push(val.colourCode);
              //     let opt = response.body;
              // //  console.log(opt);
              // let settingObj = opt;
              // for (var i=0; i < settingObj.length; i++){
              //   //  console.log(settingObj[i]);
              //   this.settingTbl.push({
              //     "dayList": settingObj[i].dayList,
              //     'time': settingObj[i].startTime + 'HRS -' + settingObj[i].endTime + 'HRS',
              //     "colourCode":settingObj[i].colourCode,
              //     "settingId": settingObj[i].settingId
              //   })
              //   this.dataSource=[];
              //   this.settingTbl.forEach((element:any) => {
              //   this.dataSource.push(element)
              // });
              // this.mySettingsObj.colourCode.push(val.colourCode);
              // //  console.log(this.settingTbl, "settingTable");
              // //  console.log(this.dataSource,"datasource");
              // }
            }
          });

        //  this.settingTbl.splice(i, 1)
      }
    });
  }
  numbersValidator(event: any, type: string, timeType: string) {
    //  console.log(event.target.value.toString().length)
    if ((Number(event.target.value) < Number(event.target.min) || event.target.value > Number(event.target.max)) || event.target.value.toString().length >2) {
      if (timeType === 'startTime') {
        if (type === 'hr') {
          event.target.value = Number(event.target.min);
        }
        else {
          event.target.value = Number(event.target.min);
        }
      }
      else {
        if (type === 'hr') {
          event.target.value=Number(event.target.max);
        }
        else {
          event.target.value = Number(event.target.max);
        }
      }
    }
    // else{
    //   event.target.value=0;
    // }
  }
}

