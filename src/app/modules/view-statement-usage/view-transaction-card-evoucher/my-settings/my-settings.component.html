<div fxLayout="column" fxLayoutGap="50px" (window:resize)="onResize($event)">
    <div fxLayout="row">
        <div fxFlex fxLayout="row" fxLayoutGap="15px">
            <div class="cursor-pointer" fxLayoutAlign="start center">
                <mat-icon (click)="goToPrevPage()">arrow_back</mat-icon>
            </div>
            <div fxFlex fxLayout="row" fxLayoutGap="15px" fxLayoutGap.lt-md="30px">
                <div>
                    <mat-icon class="material-icons-outlined card-icon">construction</mat-icon>
                </div>
                <div fxLayoutAlign="start center">
                    <span class="header pb5">My Settings</span>
                </div>
            </div>
        </div>
    </div>
    <form #f="ngForm" name="form">
        <div fxLayout="column" fxLayoutGap="20px" class="custom-padding-left">
            <div fxLayout="row">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
                    <span class="date-title">DAYS <span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxLayout="column" fxFlex="85%" fxFlex.lt-md="65%" fxFlex.md="75%" class="pl20">
                    <div fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <ng-container *ngFor="let val of mySettingsObj.dayList;let i=index">
                            <mat-checkbox color="primary" name="dayList{{i}}" [(ngModel)]="val.checked"
                                #dayList="ngModel" labelPosition="after" value="val.checked"
                                (ngModelChange)="showDaysError()">
                                {{val.name}}
                            </mat-checkbox>
                        </ng-container>
                    </div>
                    <div *ngIf="isShowDaysError" class="error">
                        Days is required
                    </div>
                </div>
            </div>
            <!-- <div fxLayout="row">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
                    <span class="date-title">Time <span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxLayout="column" fxFlex="85%" fxFlex.lt-md="65%" fxFlex.md="75%" fxLayoutGap="5px"
                    fxLayoutGap.lt-md="5px" class="pl20">
                    <div fxLayout="row" fxLayoutGap="10px">
                        <mat-form-field appearance="outline" class="num-inp">
                            <input matInput type="number" max="23" name="fromHr" value="00" min="00" (change)="leadingzero(mySettingsObj.startTime.hr)" (keyup)="leadingzero(mySettingsObj.startTime.hr)"
                                [(ngModel)]="mySettingsObj.startTime.hr" #fromHr="ngModel" (input)="numbersValidator($event,'hr','startTime')">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="num-inp">
                            <input matInput name="fromMin" type="number" min="00" max="59" value="00" (change)="leadingzero1(mySettingsObj.startTime.min)" (keyup)="leadingzero1(mySettingsObj.startTime.min)" 
                                [(ngModel)]="mySettingsObj.startTime.min" #fromMin="ngModel" (input)="numbersValidator($event,'min','startTime')">
                        </mat-form-field>
                    </div>
                    <div fxLayout="row" fxLayoutGap="10px">
                        <mat-form-field appearance="outline" class="num-inp">
                            <input matInput type="number" min="0" max="23" name="toHr" (change)="leadingzero2(mySettingsObj.endTime.hr)" (keyup)="leadingzero2(mySettingsObj.endTime.hr)"
                                [(ngModel)]="mySettingsObj.endTime.hr" #toHr="ngModel" (input)="numbersValidator($event,'hr','endTime')">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="num-inp">
                            <input matInput name="toMin" type="number" min="0" max="59" (change)="leadingzero3(mySettingsObj.endTime.min)" (keyup)="leadingzero3(mySettingsObj.endTime.min)"
                                [(ngModel)]="mySettingsObj.endTime.min" #toMin="ngModel" (input)="numbersValidator($event,'min','endTime')">
                        </mat-form-field>
                    </div>
                    <div *ngIf="isShowTimeError" class="error">
                        Start Time cannot be higher than or similar to End Time
                    </div>
                </div>
            </div> -->
            <div fxLayout="row">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
                    <span class="date-title">TIME <span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxLayout="column" fxFlex="85%" fxFlex.lt-md="65%" fxFlex.md="75%" fxLayoutGap="5px"
                    fxLayoutGap.lt-md="5px" class="pl20">
                    <div fxLayout="row" fxLayoutGap="10px">

                        <mat-form-field appearance="outline" class="num-inp">
                            <!-- <input matInput type="number" max="23" name="fromHr" value="00" min="00" (change)="leadingzero(mySettingsObj.startTime.hr)" (keyup)="leadingzero(mySettingsObj.startTime.hr)"
                                [(ngModel)]="mySettingsObj.startTime.hr" #fromHr="ngModel" > -->
                            <!-- (input)="numbersValidator($event,'hr','startTime')" -->
                            <mat-select [(ngModel)]="mySettingsObj.startTime.hr" #fromHr="ngModel" name="fromHr">
                                <mat-option *ngFor="let acl of hrList" [value]="acl.viewValue">
                                    {{acl.viewValue}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="num-inp">
                            <!-- <input matInput name="fromMin" type="number" min="00" max="59" value="00" (change)="leadingzero1(mySettingsObj.startTime.min)" (keyup)="leadingzero1(mySettingsObj.startTime.min)" 
                                [(ngModel)]="mySettingsObj.startTime.min" #fromMin="ngModel" (input)="numbersValidator($event,'min','startTime')"> -->
                            <mat-select [(ngModel)]="mySettingsObj.startTime.min" #fromMin="ngModel" name="fromMin">
                                <mat-option *ngFor="let acl of minList" [value]="acl.viewValue">
                                    {{acl.viewValue}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div fxLayout="row" fxLayoutGap="10px">
                        <mat-form-field appearance="outline" class="num-inp">
                            <!-- <input matInput type="number" min="0" max="23" name="toHr" (change)="leadingzero2(mySettingsObj.endTime.hr)" (keyup)="leadingzero2(mySettingsObj.endTime.hr)"
                                [(ngModel)]="mySettingsObj.endTime.hr" #toHr="ngModel" (input)="numbersValidator($event,'hr','endTime')"> -->
                            <mat-select [(ngModel)]="mySettingsObj.endTime.hr" #toHr="ngModel" name="toHr">
                                <mat-option *ngFor="let acl of hrList" [value]="acl.viewValue">
                                    {{acl.viewValue}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="num-inp">
                            <!-- <input matInput name="toMin" type="number" min="0" max="59" (change)="leadingzero3(mySettingsObj.endTime.min)" (keyup)="leadingzero3(mySettingsObj.endTime.min)"
                                [(ngModel)]="mySettingsObj.endTime.min" #toMin="ngModel" (input)="numbersValidator($event,'min','endTime')"> -->
                            <mat-select [(ngModel)]="mySettingsObj.endTime.min" #toMin="ngModel" name="toMin">
                                <mat-option *ngFor="let acl of minList" [value]="acl.viewValue">
                                    {{acl.viewValue}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div *ngIf="isShowTimeError" class="error">
                        Start Time cannot be higher than or similar to End Time
                    </div>
                </div>
            </div>
            <div fxLayout="row">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
                    <span class="date-title">COLOR<span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxLayout="column" fxFlex="85%" fxFlex.lt-md="65%" fxFlex.md="75%">
                    <div fxLayout="row" fxLayoutGap="10px" class="pl20">
                        <ng-container *ngFor="let val of mySettingsObj.colourCode;let i=index">
                            <div class="circle cursor-pointer" [ngStyle]="{'background-color':val}"
                                [ngClass]="{'active': (selected === i)}" (click)="showColorError(i)">

                            </div>
                        </ng-container>
                    </div>
                    <div *ngIf="isShowColorEroor" class="error pl20">
                        Please select Color
                    </div>
                </div>
            </div>
            <div fxFlex>
                <button mat-raised-button class="add-btn" (click)="add()">Add</button>
            </div>
            <div class="tbl-container mat-elevation-z8">
                <mat-table #table [dataSource]="dataSource">
                    <ng-container matColumnDef="slno">
                        <mat-header-cell *matHeaderCellDef> S No. </mat-header-cell>
                        <mat-cell *matCellDef="let element;let i=index" class="sno" fxLayoutAlign="start center">
                            {{i+1}}
                        </mat-cell>
                    </ng-container>
                    <ng-container matColumnDef="description">
                        <mat-header-cell *matHeaderCellDef>Description </mat-header-cell>
                        <mat-cell *matCellDef="let element;let i=index">
                            <div fxLayout="row" fxLayoutGap="2px">
                                <div  *ngIf="isMobileLayout">
                                    <span class="mobile-label"> Description :</span>
                                </div>
                                <div >
                                    {{element.dayList.join(', ')}}
                                </div>
                            </div>
                        </mat-cell>
                    </ng-container>
                    <ng-container matColumnDef="time">
                        <mat-header-cell *matHeaderCellDef> Time </mat-header-cell>
                        <mat-cell *matCellDef="let element;let i=index">                     
                            <div fxLayout="row" fxLayoutGap="2px">
                                <div *ngIf="isMobileLayout">
                                    <span class="mobile-label"> Time :</span>
                                </div>
                                <div >
                                    {{element.time}}
                                </div>
                            </div>
                        </mat-cell>
                    </ng-container>
                    <ng-container matColumnDef="colourCode">
                        <mat-header-cell *matHeaderCellDef>Color </mat-header-cell>
                        <mat-cell *matCellDef="let element;let i=index">
                            <span class="mobile-label">Color :</span>
                            <!-- {{element.color}} -->
                            <div class="circle-tbl cursor-pointer ml5" [ngStyle]="{'background-color':element.colourCode}">

                            </div>
                        </mat-cell>
                    </ng-container>
                    <ng-container matColumnDef="action">
                        <mat-header-cell *matHeaderCellDef>Action </mat-header-cell>
                        <mat-cell *matCellDef="let element;let i=index">
                            <span class="mobile-label">Action :</span>
                            <div fxFlex>
                                <button mat-icon-button class="del-btn" (click)="delete(element,i)">
                                    <mat-icon class="icon">delete</mat-icon>
                                </button>
                            </div>
                        </mat-cell>
                    </ng-container>
                    <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                    <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
                    <tr class="mat-row" *matNoDataRow fxLayoutAlign="center center">
                        <td class="mat-cell" colspan="4">No Data Preset</td>
                    </tr>
                </mat-table>
            </div>
        </div>
    </form>
</div>