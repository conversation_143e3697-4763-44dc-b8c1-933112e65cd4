import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ViewStatementUsageComponent } from './view-statement-usage.component';

describe('ViewStatementUsageComponent', () => {
  let component: ViewStatementUsageComponent;
  let fixture: ComponentFixture<ViewStatementUsageComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ViewStatementUsageComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ViewStatementUsageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
