import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ExportxlsxService } from 'src/app/shared/services/exportxlsx.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { throwError } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { NotificationService } from '../../../shared/services/notification.service';
import { FlexAlignDirective } from '@angular/flex-layout';
import { saveAs } from 'file-saver'

export interface DropDownElement {
  division: string;
  department: string;
}



@Component({
  selector: 'app-download-card-holder-list',
  templateUrl: './download-card-holder-list.component.html',
  styleUrls: ['./download-card-holder-list.component.scss']
})
export class DownloadCardHolderListComponent implements OnInit {
  data = {

    cardNo: "",
    cardFrom: "",
    cardTo: ""
  }
  divArray: any[] = [];
  lastfuzz: any = [];
  deptArray: any[] = [];
  isShowDepartment: boolean = false;
  disablesubmit: boolean = false;
  showcarderrorfrom: boolean = false;
  showcarderrorto: boolean = false;
  canShowProcessingBtn: boolean = false;

  typeArray = [
    { value: '', viewValue: '- - Please Select Type - -' },
    { value: 'A', viewValue: 'Enquire By Account' },
    { value: 'C', viewValue: 'Enquire By Card' },

  ]

  // divArray = [
  //   { value: '-CORPORATE INVOICE-', viewValue: '-CORPORATE INVOICE-' }

  // ];

  // deptArray = [
  //   { value: '-DIVISION INVOICE-', viewValue: '-DIVISION INVOICE-' }

  // ];






  // defaultDivision = this.divArray[0].viewValue;
  // defaultDept = this.deptArray[0].viewValue;
  dynamicForm:FormGroup

  enquireByAccForm:FormGroup

  enquireByCardForm: FormGroup

  enquireByCardProductType1 :FormGroup

  enquireByCardProductType2:FormGroup


  isEnquireByAcc: boolean = false;
  isEnquireByCard: boolean;
  cardArr: any = [];
  lastdigit: any = [];
  showcarderror: boolean = false;
  //enquireByCardForm: any ="";
  isProductTypeCard: boolean;
  enquireByCardProductType: any = "";
  isProductType1Card: boolean = false;
  isProductType2Card: boolean = false;
  //enquireByCardProductType2: any = "";
  //enquireByCardProductType1: any ="";
  isCabinCrew: boolean;
  enquireAccCabinCrewDiv: any = "";
  isCorpInvoice: boolean = false;
  isdownload: boolean;
  cardError: boolean;
  isCardError: boolean;
  cardNoFromVal: number;
  cardNoToVal: number;
  cardNoError: boolean;
  isTypeSelect: boolean = false;
  productArr: any = [];
  productAccArr: any = [];
  productCardArr: any = [];
  accountnumber: any;
  list: any;
  firstdigit: string;
  displaycard: string;
  displayCardValue: string;
  customerNumber: any;
  frstDivSelection: any;
  fetchDept: any[] = [];
  deptValue: any;
  defaultDept: any;
  companyName: any;
  listFrom: any;
  listTo: any;
  // cardNoError: boolean = false;
  // cardNoErrorNum: boolean = false;

  // get f() {return this.dynamicForm.controls;}
  // get t() {return this.f.enquireByAcc as FormArray;} 
  constructor(private fb: FormBuilder, private notifyService: NotificationService, public cdgService: CdgSharedService, private apiService: ApiServiceService, private excelService: ExportxlsxService, public localStorage: LocalStorageService) { 
    this.dynamicForm = this.fb.group({
      typeDropdown: ['', Validators.required],
      // enquireByAcc : new FormArray([])
    });

    this.enquireByAccForm = this.fb.group({
      division: [''],
      department: [''],
      companyName: [''],
      productType: ['', Validators.required]
    });

    this.enquireByCardForm = this.fb.group({

      productTypeCard: ['', Validators.required]
    });

    this.enquireByCardProductType1 = this.fb.group({

      cardNo: ['', [Validators.required, Validators.pattern(/^[0-9]{6}$/)]]
      // Validators.pattern(/^-?(0|[1-9]\d*)?$/)
  
    });

    this.enquireByCardProductType2= this.fb.group({

      cardNoFrom: [''],
      // [Validators.required, Validators.pattern(/^[0-9]{6}$/)]
      cardNoTo: [''],
      // , [Validators.required, Validators.pattern(/^[0-9]{6}$/)]
    });

  }



  ngOnInit(): void {
    let cusNo = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails;
    console.log(cusNo)
    if (cusNo) {
      cusNo.forEach((element: any) => {
        this.customerNumber = element.customer_number;
        console.log(this.customerNumber);
      })
    }
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }

    }

    let obj = {
      "accountNo": 0,
      "accountName": "ALL",
      "accountCategory": "DEPT",
      "accountCode": null,
      "nameToDisplayOnDropdown": "ALL"
    }
    this.deptValue = obj;
    this.defaultDept = this.deptValue;

    let prodtypeobj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accountNo": this.accountnumber,
      "masterAccountNo": Number(this.accountnumber)
    }
    this.productArr = [];

    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_PRODUCTTYPE, prodtypeobj).subscribe((response: any) => {
      this.productArr = [];
      if (response.status === 200) {
        console.log(response)
        let opt = response.body.productType;

        opt.forEach((element: any) => {
          this.productArr.push(element);
          console.log(this.productArr);
        });
      }
    });

  }

  onSelect(e: any) {
    this.enquireByCardForm.reset();
    this.enquireByAccForm.reset();
    this.isEnquireByAcc = false;
    this.isEnquireByCard = false;
    this.isProductType1Card = false;
    this.isProductType2Card = false;
    this.isCabinCrew = false;
    // this.isCorpInvoice = true;
    this.enquireByCardForm.controls.productTypeCard.markAsUntouched();
    this.enquireByAccForm.controls.productType.markAsUntouched();
    this.dynamicForm.controls.typeDropdown.markAsUntouched();
    //this.enquireByAccForm.markAllAsTouched();
    if (e.value == "A") {

      let byaccountobj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "masterAccountNo": Number(this.accountnumber)
      }

      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_DOWNLOADBYACCOUNTPRODUCT, byaccountobj).subscribe((response: any) => {
        this.productAccArr = [];
        if (response.status === 200) {
          console.log(response)
          let opts = response.body;

          opts.forEach((element: any) => {
            this.productAccArr.push(element);
            console.log(this.productAccArr);
          });
        }

      });
      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "masterAccountNo": Number(this.accountnumber)
      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_DOWNLOADBYACCDEPT, obj).subscribe((response: any) => {
        this.divArray = [];
        if (response.status === 200) {
          console.log(response);

          let data = response.body;
          this.companyName = data.corpAccountDto.accountName;
          console.log(data);
          if (data.corpAccountDto == null) {
            if (data.divAccountList.length > 1) {
              var obj = {
                // "accountCategory": "",
                // "accountCode": "",
                // "accountName": "ALL",
                // "accountNo":'' ,
                // "nameToDisplayOnDropdown": "ALL",
                // "divAccountList":[
                // {
                "deptList": [],
                "divAccount": {
                  "accountCategory": "",
                  "accountCode": "",
                  "accountName": "ALL",
                  "accountNo": '',
                  "nameToDisplayOnDropdown": "ALL"
                }

                // }             
                //  ]

              }
              this.divArray.push(obj)

              data.divAccountList.forEach((element: any) => {
                console.log(element);
                this.divArray.push(element.divAccount);
                console.log(this.divArray);
              });
              this.frstDivSelection = this.divArray[0];
              console.log(this.divArray)
            }
          }
          if (data.corpAccountDto) {
            if (Object.keys(data.corpAccountDto).length > 0) {
              let obj = {
                accountCategory: data.corpAccountDto.accountCategory,
                accountCode: data.corpAccountDto.accountCode,
                accountName: data.corpAccountDto.accountName,
                accountNo: data.corpAccountDto.accountNo,
                nameToDisplayOnDropdown: data.corpAccountDto.nameToDisplayOnDropdown
              }
              this.divArray.push(obj);
              this.isCorpInvoice = true;
              console.log(this.divArray);
            }

          }

          if (data.divAccountList) {
            if (data.divAccountList.length > 0) {
              data.divAccountList.forEach((element: any) => {
                this.fetchDept.push(element);
                this.divArray.push(element.divAccount);
                console.log(this.divArray);
                this.frstDivSelection = this.divArray[0];
                if (element.deptList.length > 0) {
                  this.selectDept(this.divArray[0]);
                }
                console.log(element);
                // this.selectDept(this.frstDivSelection);              
              });
            }
          }



          // let opts = response.body.divAccountList;
          // opts.forEach((element:any) => {
          //   this.divArray.push(element.divAccount);
          //   if(element.deptList.length>1){
          //   this.deptArray.push(element.deptList);
          //   }
          // });
        }
      });
      this.isEnquireByAcc = true;
    }
    if (e.value == "C") {
      this.isEnquireByCard = true;
    }

  }



  selectDept(value: any) {
    console.log(value);
    this.isShowDepartment = false;
    this.isCorpInvoice = false;
    // this.isCabinCrew = false;
    console.log(this.divArray)
    console.log(value);
    if (value.nameToDisplayOnDropdown === "-CORPORATE INVOICE-") {
      this.isCorpInvoice = true;
    }
    if (value.nameToDisplayOnDropdown !== "ALL" && value.nameToDisplayOnDropdown !== "-CORPORATE INVOICE-") {
      const obj = this.fetchDept.find(x => x.divAccount.nameToDisplayOnDropdown === value.nameToDisplayOnDropdown)
      console.log(obj)
      this.deptArray = [];
      if (obj.deptList.length > 0) {
        this.isShowDepartment = true;
        this.isCabinCrew = true;
        this.deptArray.push({
          accountCategory: "",
          accountCode: "",
          accountName: "",
          accountNo: "",
          nameToDisplayOnDropdown: "-DIVISION INVOICE-"
        })
        obj.deptList.forEach((val: any) => {
          this.deptArray.push(val);
        });
        this.defaultDept = this.deptArray[0];
      }
      console.log(obj);
    }
  }
  // onDivSelect(e: any) {
  //   this.isdownload = false;
  //   this.isCabinCrew = false;
  //   this.isCorpInvoice = true;
  //   if (e.value != "-CORPORATE INVOICE-") {
  //     this.isCorpInvoice = false;
  //   }
  //   if (e.value == "CABIN CREW (1003)") {
  //     this.isCabinCrew = true;
  //   }
  // }

  onProductTypeCard(e: any) {
    this.isdownload = false;
    this.isProductType1Card = false;
    this.isProductType2Card = false;
    // this.enquireByCardProductType2.markAsUntouched();
    this.data.cardNo = ""

    let cardnumberobj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      "productType": e.value,
      "accountNo": this.accountnumber
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_CARDLIST, cardnumberobj).subscribe((response: any) => {
      this.list = '';
      if (response.status === 200) {
        console.log(response)
        let opt = response.body.cardNo;
        console.log(opt);
      
        if (response.body.cardNo.length === 0) {
          this.showcarderror = true;
          this.disablesubmit = true;
        }
        else {
          this.showcarderror = false;
          this.disablesubmit = false;

        }

        if (e.value == "C1" || e.value == "C5" || e.value == "CC" || e.value == "V1" || e.value == "V6" || e.value == "C3" || e.value == "C6" || e.value == "TP" || e.value == "PZ" || e.value == "VC" || e.value == "PR") {

          this.isProductType1Card = true;
          this.list = response.body.cardNo;
          this.lastfuzz = [];
          this.list.forEach((element: any) => {
            this.lastfuzz.push((String(element).substr(-6)));
          });
          this.firstdigit = String(opt[0]).substr(0, 10);
          this.displaycard = this.firstdigit.substr(0, 6) + "-" + this.firstdigit.substr(6, 4);
          this.displayCardValue = this.firstdigit.substr(0, 6) + this.firstdigit.substr(6, 4);
        }
        else {

          this.isProductType2Card = true;
          this.list = response.body.cardNo;
          this.displaycard="";
          if (this.list.length === 0) {
            this.showcarderrorfrom = true;
            this.showcarderrorto = true;
          }
          else{
            this.showcarderrorfrom = false;
            this.showcarderrorto = false;
          }
          
          // this.firstdigit = String(opt[0]).substr(0, 10);
          // this.displaycard = this.firstdigit.substr(0, 6) + "-" + this.firstdigit.substr(6, 4);
          // this.displayCardValue = this.firstdigit.substr(0, 6) + this.firstdigit.substr(6, 4);
        }

        // if(response.body.cardNo.length>1){


        //  }
        // else{
        // this.showcarderror =true;
        // }
        // opt.forEach((element:any) => {
        //   this.lastdigit=(String(element).substr(-6));
        //   this.cardArr=this.lastdigit;
        // });

      }
    });
    // this.enquireByCardProductType1  = this.fb.group({

    //   cardNo : ['', [Validators.required, Validators.pattern(/^-?(0|[1-9]\d*)?$/)]]
    // });



  }

  fuzzysearch() {
    this.cardArr = [];
    if (this.enquireByCardForm.controls.productTypeCard.value !== "OV") {
      this.cardArr = this.lastfuzz;
    }
  }
  validateCardNo() {
    this.isCardError = false;
    this.cardNoFromVal = parseInt(this.enquireByCardProductType2.controls.cardNoFrom.value);
    this.cardNoToVal = parseInt(this.enquireByCardProductType2.controls.cardNoTo.value);
    if (this.cardNoFromVal > this.cardNoToVal) {
      this.isCardError = true;
    }
  }
  keyPress(event: any,fieldName?:any) {
    this.lastdigit = [];
    this.cardArr = [];
    //if (event.length > 2) {
      this.lastdigit = [];
      this.cardArr = [];
      if (this.enquireByCardForm.controls.productTypeCard.value !== "OV") {
        let obj = {
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          "accountNo": this.accountnumber,
          "cardNoList": this.list,
          "cardNoLike": this.firstdigit + this.data.cardNo
          //   "accessId" : "<EMAIL>",
          //   "role" :"ROLE_CORPADMIN",
          // "cardNoList": [   
          //       "************9642",    
          //       "****************",
          //       "****************",
          //       "****************"
          //   ],
          // "cardNoLike" : "************"

        }
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.SEARCH_CARDLIST, obj).subscribe((response: any) => {

          if (response.status === 200) {
            console.log(response)
            let opt = response.body.cardNo;
            this.cardArr = [];
            opt.forEach((element: any) => {
           
              this.lastdigit.push((String(element).substr(-6)));
              this.cardArr = this.lastdigit;
              // this.cardArr [(String(element).substr(-6))];
              // this.cardArr=[this.lastdigit];          
            });

            this.lastdigit = [];
          }

        });
      }
      else{
        this.listFrom=[];
        this.listTo=[];
        if(fieldName=== "cardFrom"){
        this.listFrom = this.list.filter((o: any) => {
          if (o.toString().startsWith(this.data.cardFrom.toString())) {
            return o;
          }
        });
      }
      else{
        this.listTo = this.list.filter((o: any) => {
          if (o.toString().startsWith(this.data.cardTo.toString())) {
            return o;
          }
        });
      }
      }
    //}
  }

  download() {
    this.enquireByCardProductType2.markAllAsTouched();
    this.enquireByCardForm.controls.productTypeCard.markAsTouched();
    this.enquireByAccForm.controls.productType.markAsTouched();
    this.dynamicForm.controls.typeDropdown.markAsTouched();
    this.isdownload = true;
    this.canShowProcessingBtn = true;
    // Enquire By Account
    if (this.dynamicForm.controls.typeDropdown.value == "A" && this.dynamicForm.valid &&
      this.enquireByAccForm.controls.productType.value != "" && this.enquireByAccForm.valid) {
      if (this.defaultDept.accountCode == null) {
        let obj = {
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          "customerNo": this.customerNumber,
          "masterAccountNo": Number(this.accountnumber),
          "accountNo": this.accountnumber,
          "divisionCode": this.frstDivSelection?.accountCode,
          "deptCode": "",
          "productTypeId": this.enquireByAccForm.controls.productType.value,
          "cardNo": this.enquireByCardProductType1.controls.cardNo.value,
          "cardNoFrom": this.enquireByCardProductType2.controls.cardNoFrom.value,
          "cardNoTo": this.enquireByCardProductType2.controls.cardNoTo.value,
          "enquireBy": this.dynamicForm.controls.typeDropdown.value
        }
        let mediaType = "application/vnd.ms-excel"
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.DOWNLOAD_CARD_HOLDERLIST_BYCARD, obj, { responseType: 'blob' }).subscribe(response => {
          this.canShowProcessingBtn = false;
          let blob = new Blob([response], { type: mediaType });
          saveAs(blob, 'CardHolderList_ByAccount' + '.xlsx')
        },
          e => {
            this.canShowProcessingBtn = false;
            throwError(e)
            this.notifyService.showError("Sorry, its a bad request ! Make sure input fields are not empty", 'Error');
          })

      }
      else {
        let obj = {
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          "customerNo": this.customerNumber,
          "masterAccountNo": Number(this.accountnumber),
          "accountNo": this.accountnumber,
          "divisionCode": this.frstDivSelection?.accountCode,
          "deptCode": this.defaultDept?.accountCode,
          "productTypeId": this.enquireByAccForm.controls.productType.value,
          "cardNo": this.enquireByCardProductType1.controls.cardNo.value,
          "cardNoFrom": this.enquireByCardProductType2.controls.cardNoFrom.value,
          "cardNoTo": this.enquireByCardProductType2.controls.cardNoTo.value,
          "enquireBy": this.dynamicForm.controls.typeDropdown.value
        }
        let mediaType = "application/vnd.ms-excel"
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.DOWNLOAD_CARD_HOLDERLIST_BYCARD, obj, { responseType: 'blob' }).subscribe(response => {
          this.canShowProcessingBtn = false;
          let blob = new Blob([response], { type: mediaType });
          saveAs(blob, 'CardHolderList_ByAccount' + '.xlsx')
        },
          e => {
            this.canShowProcessingBtn = false;
            throwError(e)
            this.notifyService.showError("Sorry, its a bad request ! Make sure input fields are not empty", 'Error');
          })
        // this.excelService.exportAsExcel([obj], 'CardHolderList_ByCard');
      }
    }



    else if (this.dynamicForm.controls.typeDropdown.value == "C" && this.enquireByCardProductType1.valid) {

      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "customerNo": this.customerNumber,
        "accountNo": this.accountnumber,
        "masterAccountNo": Number(this.accountnumber),
        "divisionCode": "",
        "deptCode": "",
        productTypeId: this.enquireByCardForm.controls.productTypeCard.value,
        enquireBy: this.dynamicForm.controls.typeDropdown.value,
        // cardNo: (this.enquireByCardProductType1.controls.cardNo.value != undefined) ?this.enquireByCardProductType1.controls.cardNo : "" ,
        // cardFrom: ( this.enquireByCardProductType2.controls.cardNoFrom.value != undefined ) ?  this.enquireByCardProductType2.controls.cardFrom.value: "",
        // cardTo: (this.enquireByCardProductType2.controls.cardNoTo.value != undefined) ? this.enquireByCardProductType2.controls.cardTo.value: "",
        cardNo: this.displayCardValue + this.enquireByCardProductType1.controls.cardNo.value,
        cardNoFrom: this.enquireByCardProductType2.controls.cardNoFrom.value,
        cardNoTo: this.enquireByCardProductType2.controls.cardNoTo.value,

      }
      let mediaType = "application/vnd.ms-excel"
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.DOWNLOAD_CARD_HOLDERLIST_BYCARD, obj, { responseType: 'blob' }).subscribe(response => {
        this.canShowProcessingBtn = false;
        let blob = new Blob([response], { type: mediaType });
        saveAs(blob, 'CardHolderList_ByCard' + '.xlsx')
      },
        e => {
          this.canShowProcessingBtn = false;
          throwError(e)
          this.notifyService.showError("Entered card number is invalid.", 'Error');
        })
      // this.excelService.exportAsExcel([obj], 'CardHolderList_ByCard');



    } else if (this.dynamicForm.controls.typeDropdown.value == "C"  && !this.isCardError) {
      // this.enquireByCardProductType2.valid

      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "customerNo": this.customerNumber,
        "accountNo": this.accountnumber,
        "masterAccountNo": Number(this.accountnumber),
        "divisionCode": "",
        "deptCode": "",
        enquireBy: this.dynamicForm.controls.typeDropdown.value,
        productTypeId: this.enquireByCardForm.controls.productTypeCard.value,
        // cardNo: (this.enquireByCardProductType1.controls.cardNo.value != undefined) ?this.enquireByCardProductType1.controls.cardNo : "" ,
        // cardFrom: ( this.enquireByCardProductType2.controls.cardNoFrom.value != undefined ) ?  this.enquireByCardProductType2.controls.cardFrom.value: "",
        // cardTo: (this.enquireByCardProductType2.controls.cardNoTo.value != undefined) ? this.enquireByCardProductType2.controls.cardTo.value: "",
        cardNo: this.enquireByCardProductType1.controls.cardNo.value,
        // cardNoFrom: this.enquireByCardProductType2.controls.cardNoFrom.value,
        // cardNoTo: this.enquireByCardProductType2.controls.cardNoTo.value,
        cardNoFrom: this.data.cardFrom,
        cardNoTo: this.data.cardTo,

      }
      let mediaType = "application/vnd.ms-excel"
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.DOWNLOAD_CARD_HOLDERLIST_BYCARD, obj, { responseType: 'blob' }).subscribe(response => {
        this.canShowProcessingBtn = false;
        let blob = new Blob([response], { type: mediaType });
        saveAs(blob, 'CardHolderList_ByCard' + '.xlsx')
      },
        e => {
          this.canShowProcessingBtn = false;
          throwError(e)
          this.notifyService.showError("Entered card number is invalid.", 'Error');
        })
      // this.excelService.exportAsExcel([obj], 'CardHolderList_ByCard');

    } else{
      this.canShowProcessingBtn = false;
    }

  }

  reset() {
    // this.isEnquireByAcc = false;
    // this.isEnquireByCard = false;
    // // this.isProductType1Card = false;
    // // this.isProductType2Card = false;
    window.location.reload();
  }

}
