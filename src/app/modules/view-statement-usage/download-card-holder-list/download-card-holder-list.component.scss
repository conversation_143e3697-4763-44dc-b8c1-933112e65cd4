@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';

.cell-label{
  
    margin-bottom: 2px !important;
     margin-top: 2%;
 }

 .header{
  color: $lightGrey !important;
  font-size: 28px;
  font-weight:$cdgsz-font-weight-normal ;
}
 .asterisk{
    color: $red;
  }

  .heading{
    color: $lightGrey !important;
    font-size: 28px !important;
    font-weight:$cdgsz-font-weight-normal !important ;
   margin-bottom: 25px !important;
  }
  
  .card{
    background-color: #E7E7E7;
    border-radius: 8px;
  }

  // :host ::ng-deep .mat-raised-button{
  //     background-color: $cdgc-bg-prime !important;
  //     color: $cdgc-font-accent !important;
  // }

  .download-btn{
  width:191px;
  background-color:$cdgc-bg-hue !important;
  color: $cdgc-font-accent !important;
  border-radius: 10px !important;
  }

  .reset-btn{
    width:130px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
  }

  .content{
    color:$lightGrey !important;
     font-size:$cdgsz-font-size-sm ;
     margin-bottom: 0 !important;
    // font-family: ;
  }

  .company-label{
    margin-bottom: 0 !important;
  }

  .btn-cell{
    width:100%;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent;
    border-radius: 10px !important;
  }

  /* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
  :host ::ng-deep .mat-mdc-select-arrow{
    border-left: 0px solid transparent !important;
    border-top: 0px solid !important;
    border-style: solid !important;
    border-right: 2px solid !important;
    border-width: 0 2px 2px 0 !important;
    content: "";
    display: inline-block !important;
    padding: 3px !important;
    transform: rotate(45deg) !important;
    vertical-align: middle !important;
    // color: red !important;
  }

  :host::ng-deep .mat-mdc-form-field-error{
    font-size: $cdgsz-font-size-sm !important;
  }

  .error{
    font-size: $cdgsz-font-size-sm;
    color: $cdgc-font-warn;
}

.error-container {
  margin: 14px 0 14px 0;
}


/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
  padding-bottom: 0px !important;
}

.label{
  font-weight: $cdgsz-font-weight-intermediate !important;
}

.info{
  margin-bottom: 0 !important;
}