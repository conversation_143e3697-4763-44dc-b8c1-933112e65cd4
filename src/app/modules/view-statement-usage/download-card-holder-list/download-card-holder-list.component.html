<div fxLayout="column" fxLayoutGap="20px">
    <!-- <p class="heading">Download Card Holder List</p> -->
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <img src="/assets/images/recent transactions.svg" alt="" width="40px">
            <!-- <mat-icon class="material-icons-outlined main-icon">price_change</mat-icon> -->
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">Download Card Holder List</span>
        </div>
    </div>
    <div>
        <p class="info">INSTRUCTIONS:</p>
        <p>Items marked with <span class="asterisk">*</span> are mandatory. Please ensure all mandatory fields are
            filled.</p>
    </div>

    <div>
        <form [formGroup]="dynamicForm">
            <mat-card appearance="outlined" class="card" fxLayout="column" fxLayoutGap.gt-md="2%" fxLayoutGap.lt-md="5%" style="padding: 15px !important;">

                <div fxLayout="row" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutAlign.gt-md="start center">
                    <!-- Test for responsive - navani
                    <p class="label" fxFlex="15%" [ngClass.lt-md]="'cell-label'"  >TYPE:<span class="asterisk">*</span>
                        ngClass 
                    </p> 
                     --> 

                    <p class="label" fxFlex="15%" >TYPE:<span class="asterisk">*</span>
                    </p>

                    <mat-form-field fxFlex.gt-md="35%" fxFlex.lt-md="25%" fxFlex.md="50%" style="width: 100%;"
                        appearance='outline'>
                        <!-- <mat-label>Division</mat-label> -->
                        <mat-select formControlName="typeDropdown" (selectionChange)="onSelect($event)"
                            placeholder="- - Please Select Type - -" required>

                            <mat-option *ngFor="let type  of typeArray" [value]="type.value">
                                {{type.viewValue}}
                            </mat-option>
                            <!-- <mat-option>- - Please Select Type - -</mat-option>
                            <mat-option value="Enquire By Account">Enquire By Account</mat-option>
                            <mat-option value="Enquire By Card">Enquire By Card</mat-option> -->
                        </mat-select>
                        <mat-error class="error-container"
                            *ngIf="(dynamicForm.controls.typeDropdown.hasError('required') && isdownload)">
                            Please Select A Type
                        </mat-error>
                    </mat-form-field>
                </div>
                <div *ngIf="isEnquireByAcc" [formGroup]="enquireByAccForm" fxLayout="column" fxLayoutGap="2%">
                    <div fxLayout="row" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutAlign.gt-md="start center">
                        <!-- Test for responsive - navani
                            <p class="label" fxFlex="15%" [ngClass.lt-md]="'cell-label'">DIVISION<span
                                class="asterisk">*</span></p> -->

                                <p class="label" fxFlex="15%" >DIVISION<span
                                    class="asterisk">*</span></p> 

                        <mat-form-field fxFlex.gt-md="35%" fxFlex.lt-md="25%" fxFlex.md="50%" style="width: 100%;"
                            appearance='outline'>
                            <!-- <mat-label>Division</mat-label> -->
                            <mat-select [(ngModel)]="frstDivSelection" formControlName="division"
                                (selectionChange)="selectDept(frstDivSelection)">
                                <!-- (ngModelChange)="selectDept(frstDivSelection)" -->
                                <!-- (selectionChange)="onDivSelect($event)" -->

                                <mat-option *ngFor="let div of divArray" [value]="div">
                                    {{div.nameToDisplayOnDropdown}}
                                </mat-option>
                                <!-- <mat-option value="-CORPORATE INVOICE-">-CORPORATE INVOICE-</mat-option>
                                <mat-option value="CABIN CREW (1003)">CABIN CREW (1003)</mat-option>
                                <mat-option value="CORPORATE OFFICE (1005)">CORPORATE OFFICE (1005)</mat-option>
                                <mat-option value="CSM1 (1004)">CSM1 (1004)</mat-option> -->
                            </mat-select>
                        </mat-form-field>
                    </div>

                    <!-- *ngIf="isCabinCrew" -->
                    <div fxLayout="row" fxLayout.lt-md="column" fxLayout.md="column" *ngIf="isShowDepartment"
                        fxLayoutAlign.gt-md="start center">
                        <!-- Test for responsive - navani 
                            <p class="label" fxFlex="15%" [ngClass.lt-md]="'cell-label'">DEPARTMENT</p> -->

                        <p class="label" fxFlex="15%" >DEPARTMENT</p>

                        <mat-form-field fxFlex.gt-md="35%" fxFlex.lt-md="25%" fxFlex.md="50%" style="width: 100%;"
                            appearance='outline'>
                            <!-- <mat-label>Division</mat-label> -->
                            <mat-select [(ngModel)]="defaultDept" formControlName="department">
                                <!-- <mat-option *ngIf="deptArray.length > 0" [value]="deptValue">ALL</mat-option> -->
                                <mat-option *ngFor="let dept of deptArray" [value]="dept">
                                    {{dept?.nameToDisplayOnDropdown}}
                                </mat-option>
                                <!-- <mat-option value="-DIVISION INVOICE-">-DIVISION INVOICE-</mat-option>
                                <mat-option value="DEPT 1 (001A)">DEPT 1 (001A)</mat-option> -->
                            </mat-select>
                        </mat-form-field>
                    </div>

                    <div fxLayout="row" fxLayout.lt-md="column" fxLayout.md="column" *ngIf="isCorpInvoice"
                        fxLayoutAlign.gt-md="start center">
                        <!-- Test for responsive - navani  <p class="label company-label" fxFlex="15%" [ngClass.lt-md]="'cell-label'">COMPANY NAME:</p> -->
                        <p class="label company-label" fxFlex="15%" >COMPANY NAME:</p>
                        <p class="content">
                            {{companyName}}
                        </p>
                    </div>

                    <div fxLayout="row" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutAlign.gt-md="start center">
                        <!-- Test for responsive - navani  <p class="label" fxFlex="15%" [ngClass.lt-md]="'cell-label'">PRODUCT TYPE:<span -->
                            <p class="label" fxFlex="15%" >PRODUCT TYPE:<span
                                class="asterisk">*</span></p>
                        <mat-form-field fxFlex.gt-md="35%" fxFlex.lt-md="25%" fxFlex.md="50%" style="width: 100%;"
                            appearance='outline'>
                            <!-- <mat-label>Division</mat-label> -->
                            <mat-select formControlName="productType" placeholder="--Select Product Type--" required>

                                <!-- <mat-option *ngFor="let prod of productAccArr" [value]="prod.value" >
                        {{prod.viewValue}}
                    
                    </mat-option> -->

                                <mat-option *ngFor="let prod of productAccArr" [value]="prod.productId">
                                    {{prod.productName}}
                                </mat-option>
                                <!-- <mat-option value="- - Select Product Type - -">- - Select Product Type - -</mat-option>
                                <mat-option value="CONTACTLESS CORPORATE CARD">CONTACTLESS CORPORATE CARD</mat-option>
                                <mat-option value="CONTACTLESS PREPAID CARD">CONTACTLESS PREPAID CARD</mat-option>
                                <mat-option value="CORPORATE CARD">CORPORATE CARD</mat-option>
                                <mat-option value="VIRTUAL CORPORATE">VIRTUAL CORPORATE</mat-option>
                                <mat-option value="VIRTUAL PREPAID">VIRTUAL PREPAID</mat-option> -->
                            </mat-select>
                            <mat-error class="error-container"
                                *ngIf="enquireByAccForm.controls.productType.hasError('required')  && isdownload ">
                                Please Select A Product Type
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>

                <div *ngIf="isEnquireByCard" [formGroup]="enquireByCardForm">
                    <div fxLayout="row" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutAlign.gt-md="start center">
                        <!-- Test for responsive - navani <p class="label" fxFlex="15%" [ngClass.lt-md]="'cell-label'">PRODUCT TYPE:<span -->
                            <p class="label" fxFlex="15%" >PRODUCT TYPE:<span
                                class="asterisk">*</span></p>
                        <mat-form-field fxFlex.gt-md="35%" fxFlex.lt-md="25%" fxFlex.md="50%" style="width: 100%;"
                            appearance='outline'>
                            <!-- <mat-label>Division</mat-label> -->
                            <mat-select formControlName="productTypeCard" (selectionChange)="onProductTypeCard($event)"
                                placeholder="--Select Product Type--" required>

                                <mat-option *ngFor="let prod of productArr " [value]="prod.productTypeId">
                                    {{prod.name}}
                                </mat-option>
                                <!-- <mat-option value="- - Select Product Type - -">- - Select Product Type - -</mat-option>
                                <mat-option value="CONTACTLESS CORPORATE CARD">CONTACTLESS CORPORATE CARD</mat-option>
                                <mat-option value="CONTACTLESS PREPAID CARD">CONTACTLESS PREPAID CARD</mat-option>
                                <mat-option value="CORPORATE CARD">CORPORATE CARD</mat-option>
                                <mat-option value="OPEN VALUE E-VOUCHER">OPEN VALUE E-VOUCHER</mat-option>
                                <mat-option value="VIRTUAL CORP 2 TEST">VIRTUAL CORP 2 TEST</mat-option>
                                <mat-option value="VIRTUAL CORPORATE">VIRTUAL CORPORATE</mat-option>
                                <mat-option value="VIRTUAL PREPAID">VIRTUAL PREPAID</mat-option> -->
                            </mat-select>
                            <mat-error class="error-container"
                                *ngIf="enquireByCardForm.controls.productTypeCard.hasError('required') && isdownload">
                                Please Select A Product Type
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div *ngIf="isProductType1Card" [formGroup]="enquireByCardProductType1" fxLayoutGap="2%">
                    <div fxLayout="row" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutAlign.gt-md="start center">
                        <!-- Test for responsive - navani <p class="label" fxFlex="15%" [ngClass.lt-md]="'cell-label'">CARD NO:<span -->
                            <p class="label" fxFlex="15%" >CARD NO:<span
                                class="asterisk">*</span></p>
                        <div fxLayout="column">
                            <div fxLayout="row" fxLayoutAlign="start start">

                                <span class="mt-10" *ngIf="!showcarderror"> {{this.displaycard}} </span>
                                <mat-error *ngIf="showcarderror" class="pt15">
                                    No Card Details Found
                                </mat-error>
                                <mat-form-field style="width: 55%;" appearance='outline' *ngIf="!showcarderror">
                                    <!-- <input formControlName="cardNo" placeholder="No." matInput required> -->
                                    <input type="text" pattern="[0-9]*" matInput formControlName="cardNo"
                                        [(ngModel)]="data.cardNo" (click)="fuzzysearch()"
                                        (ngModelChange)="keyPress($event)" maxlength="6" [matAutocomplete]="auto"
                                        required>
                                    <mat-autocomplete #auto="matAutocomplete">
                                        <mat-option *ngFor="let acl of cardArr" [value]="acl">
                                            {{acl}}
                                        </mat-option>
                                    </mat-autocomplete>

                                </mat-form-field>

                            </div>


                            <mat-error class="error-container"
                                *ngIf="enquireByCardProductType1.controls.cardNo.hasError('required') && isdownload">
                                Please Enter Card Number
                            </mat-error>
                            <mat-error class="error-container"
                                *ngIf="enquireByCardProductType1.controls.cardNo.hasError('pattern') && isdownload">
                                Please Enter Numeric Value Only
                            </mat-error>

                        </div>

                    </div>
                    <!-- <div *ngIf="cardNoError"  class="error-container error">
                        Please enter Card Number
                    </div> -->


                </div>

                <div *ngIf="isProductType2Card " [formGroup]="enquireByCardProductType2" fxLayout="column"
                    fxLayoutGap="2%">
                    <div fxLayout="row" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutAlign.gt-md="start center">
                        <!-- Test for responsive <p class="label" fxFlex.gt-md="15%" [ngClass.lt-md]="'cell-label'">CARD NO. FROM:<span -->
                            <p class="label" fxFlex.gt-md="15%" >CARD NO. FROM:<span    
                            class="asterisk">*</span></p>
                        <div fxLayout="column">
                            <div fxLayout="row" fxLayoutAlign="start start">

                                <span class="mt-10" *ngIf="!showcarderrorfrom"> {{this.displaycard}} </span>
                                <mat-error *ngIf="showcarderrorfrom" class="pt15">
                                    No Card Details Found
                                </mat-error>
                                <mat-form-field style="width: 110%;" appearance='outline' *ngIf="!showcarderrorfrom">
                                    <!-- <input formControlName="cardNo" placeholder="No." matInput required> -->
                                    <input type="text" pattern="[0-9]*" matInput [(ngModel)]="data.cardFrom"
                                        name="cardFrom" placeholder="Card No. From" #cardFrom="ngModel"
                                        (click)="fuzzysearch()" (ngModelChange)="keyPress($event,'cardFrom')" 
                                        [matAutocomplete]="auto"[ngModelOptions]="{standalone: true}" required>
                                    <mat-autocomplete #auto="matAutocomplete">
                                        <mat-option *ngFor="let acl of listFrom" [value]="acl">
                                            {{acl}}
                                        </mat-option>
                                    </mat-autocomplete>
                                    <!-- <mat-error *ngIf="isdownload && enquireByCardProductType2.controls.cardFrom.invalid" class="pt15"> -->
                                        
                                        <mat-error class="error-container">
                                            <div *ngIf="isdownload && !data.cardFrom.length" style="width: 200px;">
                                                Please Enter Card Number From
                                            </div>
                                        </mat-error>
                                    <!-- </mat-error> -->
                                    <!-- <mat-error class="error-container"
                                        *ngIf="enquireByCardProductType2.controls.cardNoFrom.hasError('required') && isdownload">
                                        Please Enter Card Number From
                                    </mat-error>
                                    <mat-error class="error-container"
                                        *ngIf="enquireByCardProductType2.controls.cardNoFrom.hasError('pattern') && isdownload">
                                        Please Enter Numeric Value Only
                                    </mat-error> -->
                                </mat-form-field>
                               
                            </div>

                        </div>
                    </div>

                    <div fxLayout="row" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutAlign.gt-md="start center">
                        <!--Test for responsive - navani
                             <p class="label" fxFlex.gt-md="15%" [ngClass.lt-md]="'cell-label'">CARD NO. TO:<span -->
                            <p class="label" fxFlex.gt-md="15%" >CARD NO. TO:<span
                                class="asterisk">*</span>
                        </p>
                        <div fxLayout="column">
                            <div fxLayout="row" fxLayoutAlign="start start">

                                <span class="mt-10" *ngIf="!showcarderrorto"> {{this.displaycard}} </span>
                                <mat-error *ngIf="showcarderrorto" class="pt15">
                                    No Card Details Found
                                </mat-error>
                                <mat-form-field style="width: 100%;" appearance='outline' *ngIf="!showcarderrorto">
                                    <!-- <input formControlName="cardNo" placeholder="No." matInput required> -->
                                    <input type="text" pattern="[0-9]*" matInput [(ngModel)]="data.cardTo" name="cardTo"
                                        #cardTo="ngModel" placeholder="Card No. To" [ngModelOptions]="{standalone: true}" (click)="fuzzysearch()"
                                        (ngModelChange)="keyPress($event,'cardTo')"  [matAutocomplete]="auto"
                                        required>
                                    <mat-autocomplete #auto="matAutocomplete">
                                        <mat-option *ngFor="let acl of listTo" [value]="acl">
                                            {{acl}}
                                        </mat-option>
                                    </mat-autocomplete>
                                    <mat-error class="error-container" >
                                        <div *ngIf="isdownload && !data.cardTo.length">
                                            Please Enter Card Number To
                                        </div>
                                    </mat-error>
                                   
                                    <!-- <mat-error class="error-container"
                                        *ngIf="enquireByCardProductType2.controls.cardNoTo.hasError('required') && isdownload">
                                        Please Enter Card Number To
                                    </mat-error>
                                    <mat-error class="error-container"
                                        *ngIf="enquireByCardProductType2.controls.cardNoTo.hasError('pattern') && isdownload">
                                     
                                        Please Enter Numeric Value Only
                                    </mat-error> -->
                                </mat-form-field>

                            </div>

                        </div>
                        <!-- <mat-form-field fxFlex.gt-md="35%" fxFlex.lt-md="25%" fxFlex.md="70%" style="width: 100%;"
                            appearance='outline' *ngIf="!showcarderror">
                            <mat-select [(ngModel)]="data.cardTo" name="cardTo" #cardTo="ngModel"
                                placeholder="Card No. To" required [ngModelOptions]="{standalone: true}">
                                <mat-option *ngFor="let acl of listTo" [value]="acl">
                                    {{acl}}
                                </mat-option>
                            </mat-select>
                            <mat-error class="error-container"
                                *ngIf="enquireByCardProductType2.controls.cardNoTo.hasError('required')">
                                Please Enter Card Number To
                            </mat-error>
                            <mat-error class="error-container"
                                *ngIf="enquireByCardProductType2.controls.cardNoTo.hasError('pattern')">
                                Please Enter Numeric Value Only
                            </mat-error>


                        </mat-form-field> -->
                        <mat-error class="error-container" *ngIf="isCardError">
                            Card No. From Cannot Be Greater Than Card No. To
                        </mat-error>
                        <!-- <mat-error class="error-container" *ngIf="showcarderrorto">
                            No Card Details Found
                        </mat-error> -->

                    </div>
                </div>

                <div fxLayout="row" class="mt-10" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutGap.gt-md="1%"
                    fxLayoutGap.lt-md="4%">
                    <!-- Test for responsive - navani 
                        <button [ngClass.gt-md]="'download-btn'" *ngIf="!canShowProcessingBtn" [disabled]="disablesubmit" [ngClass.lt-md]="'btn-cell'" -->
                        <button  *ngIf="!canShowProcessingBtn" [disabled]="disablesubmit" class="download-btn" 
                        mat-raised-button (click)="download()">

                        Download
                    </button>
                    <!-- Test for responsive - navani
                         <button mat-raised-button [ngClass.gt-md]="'download-btn'" *ngIf="canShowProcessingBtn">Processing...</button> -->
                    <button mat-raised-button class="download-btn" *ngIf="canShowProcessingBtn">Processing...</button>
                    <!-- Test for responsive - navani
                         <button [ngClass.gt-md]="'reset-btn'" [ngClass.lt-md]="'btn-cell'" mat-raised-button -->
                        <button  mat-raised-button class="download-btn"

                        (click)="reset()">
                        Reset
                    </button>
                </div>

            </mat-card>
        </form>
    </div>

</div>