
import { DatePipe } from '@angular/common';
import { AfterViewInit, Component, OnInit, SimpleChange, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
import { MatPaginator } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { element } from 'protractor';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';


// export const MY_FORMATS = {
//   parse: {
//     dateInput: 'LL'
//   },
//   display: {
//     dateInput: 'DD-MM-YYYY',
//     monthYearLabel: 'YYYY',
//     dateA11yLabel: 'LL',
//     monthYearA11yLabel: 'YYYY'
//   }
// }

export const MY_FORMATS = {
  parse: {
    dateInput: 'MMM YYYY'
  },
  display: {
    dateInput: 'MMMM YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMM YYYY'
  }
}

export interface ReportElement {

  [key: string]: any; //for item[property]
  monthYear: string;
  //month : Date
  totalTrips: number;
  totalAmount: number;
  avgAmountPerTrip: number;

}

export interface DropDownElement {
  division: string;
  department: string;
}

// const DATA: ReportElement[] = [
//   { month: 'Jan 2020', totalTrips: 526, avgSpend: 1000, totalAmt: 5000 },
//   { month: 'Oct 2021', totalTrips: 6, avgSpend: 10, totalAmt: 500 },
//   { month: 'Feb 2021', totalTrips: 5, avgSpend: 10, totalAmt: 50 },
//   { month: 'Mar 2021', totalTrips: 6, avgSpend: 10, totalAmt: 50 },
//   { month: 'Nov 2021', totalTrips: 5, avgSpend: 10, totalAmt: 50 }
//   // { month: new Date('November 2021'), totalTrips: 5, avgSpend: 10, totalAmt: 50 }
// ]

const dropdownData: DropDownElement[] = [
  { division: 'Corporate Office (1005)', department: 'ABC' },
  { division: 'Corporate Office (1005) 12345678788899999   sdfgghhhhhhhhhjhj', department: 'XYZ' },
  { division: 'Corporate Office (1005)', department: 'JKL' },
  { division: 'Corporate Office (1005)', department: 'AAA' }
]
// val:string=""
@Component({
  selector: 'app-usagereport',
  templateUrl: './usagereport.component.html',
  styleUrls: ['./usagereport.component.scss'],
  providers: [DatePipe, { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] }, { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]
})
export class UsagereportComponent implements AfterViewInit {

  displayedColumns: string[] = [];
  public useageData: any[] = []
  Paginator: any;
  divisions: any;
  isShowDeptList: boolean = false;
  response: any;
  grandTotal: any;
  accountnumber: any;
  isShowCICHDropdown: boolean = false;
  cichDropdown: any[] = [];
  selectedData: any;
  @ViewChild(MatPaginator, { static: false })
  set paginator(value: MatPaginator) {
    this.Paginator = value;
    this.dataSource.paginator = value;
  }
  dataSource = new MatTableDataSource<any>(this.useageData);
  property: number;
  // @ViewChild(MatSort) sort: MatSort;
  val: any = "Corporate Office (1005)";
  dataSourceCell: any[] = [];
  // dataDropdown = dropdownData;
  dataDropdown: any[] = []
  // selected= this.dataSource[0].avgSpend;
  isShowSearch: boolean = true;
  usageReportForm:FormGroup;
  div: any;
  dept: any;
  startMonth: any;
  endMonth: any;
  divisionSelected: any;
  deptSelected: any;
  deptList: any[] = [];
  startMonthList: any[] = [];
  endMonthList: any[] = [];
  public months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
  lastTwelveMonths: any = [];
  constructor(public localStorage: LocalStorageService, private fb: FormBuilder, private router: Router, private datePipe: DatePipe, private notifyService: NotificationService, public cdgService: CdgSharedService, private apiService: ApiServiceService) {
    this.usageReportForm = this.fb.group({
      division: ['', Validators.required],
      department: [''],
      startMonth: ['', Validators.required],
      endMonth: ['', Validators.required]
    },
     { validator: this.checkDates });
  }
  ngAfterViewInit() {

    // this.dataSource.paginator = this.paginator;

    // this.dataSource.sort = this.sort;

    // this.dataSource.sortingDataAccessor = (item, property) => {
    //   switch (property) {
    //     case 'monthYear': return new Date(item.monthYear);
    //     default: return item[property];
    //   }
    // };
  }
  checkDates(form: FormGroup) {
    if (form.controls.startMonth.value > form.controls.endMonth.value && (form.controls.endMonth.value != "")) {
      return { notValid: true };
    }
    return null;
  }
  ngOnInit(): void {
    this.isShowCICHDropdown = false;
    this.isShowSearch=false;
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }
    }
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
      this.isShowCICHDropdown = true;
      this.getCICHDropdownValue();
    }
    this.cdgService.monthlySummaryObj = {}
    let d;
    const today = new Date();
    for (var i = 0; i < 12; i += 1) {
      d = new Date(today.getFullYear(), today.getMonth() - i, 1);
      this.lastTwelveMonths.push(this.months[d.getMonth()] + ' ' + d.getFullYear());
    }
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPADMIN || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_MASTERUSER) {
      this.isShowSearch = false;
      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "divisionCode": null,
        "deptCode": null,
        "accountNo": this.accountnumber
      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2  + UrlConstants.GET_USEAGE_REPORT, obj).subscribe((response: any) => {
        if (response.status === 200) {
          let data = response.body;
          let opt = response.body.divDeptListResponseDto;
          this.response = opt;
          if (Object.keys(opt.corpAccountDto).length > 0) {
            this.dataDropdown.push(opt.corpAccountDto)
            this.divisionSelected = opt.corpAccountDto;
            this.showTable(this.divisionSelected)
          }
          if (opt.divAccountList.length > 0) {
            opt.divAccountList.forEach((element: any) => {
              this.dataDropdown.push(element.divAccount)
            });
            this.deptList.push({
              "accountNo": 0,
              "accountName": "ALL",
              "accountCategory": "DEPT",
              "accountCode": null,
              "nameToDisplayOnDropdown": "ALL"
            })
            this.usageReportForm.get('department')?.setValue(this.deptList[0]);
            this.startMonthList = this.lastTwelveMonths;
            this.endMonthList = this.lastTwelveMonths;
            this.usageReportForm.get('startMonth')?.setValue(this.startMonthList[this.startMonthList.length - 1]);
            this.usageReportForm.get('endMonth')?.setValue(this.endMonthList[0]);
            if (data.OneYearUsageReport.usageReportByMonthList.length > 0) {
              this.displayedColumns = ['serialNo', 'monthYear', 'totalTrips', 'totalAmount', 'avgAmountPerTrip']
              this.useageData = data.OneYearUsageReport.usageReportByMonthList;
              // this.useageData[0].isHyperlinked=true;
              this.dataSource = new MatTableDataSource<any>(this.useageData);
              this.dataSource.paginator = this.paginator;
              this.dataSourceCell = data.OneYearUsageReport.usageReportByMonthList;
            }
            this.grandTotal = data.OneYearUsageReport.grandTotal;
          }else{
            this.deptList.push({
              "accountNo": 0,
              "accountName": "ALL",
              "accountCategory": "DEPT",
              "accountCode": null,
              "nameToDisplayOnDropdown": "ALL"
            })
            this.usageReportForm.get('department')?.setValue(this.deptList[0]);
            this.startMonthList = this.lastTwelveMonths;
            this.endMonthList = this.lastTwelveMonths;
            this.usageReportForm.get('startMonth')?.setValue(this.startMonthList[this.startMonthList.length - 1]);
            this.usageReportForm.get('endMonth')?.setValue(this.endMonthList[0]);
          }
          if (this.dataDropdown.length > 0) {
            this.isShowSearch = true;
          }
        }
      },
        e => {
          this.notifyService.showError(e.error.statusMessage, "Error")
          // this.isLoading = false;
        })
    }
    else if (this.localStorage.localStorageGet("loggedInRole") !== RoleConstants.ROLE_CORPADMIN && this.localStorage.localStorageGet("loggedInRole") !== RoleConstants.ROLE_MASTERUSER && this.localStorage.localStorageGet("loggedInRole") !== RoleConstants.ROLE_CORPCARDHOLDER) {
      this.isShowSearch = false;
      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "divisionCode": null,
        "deptCode": null,
        "accountNo": this.accountnumber
      }
      this.getStatementOfAcc(obj)
    }
    this.dataSource.paginator = this.paginator;

    // this.dataSource.sort = this.sort;
    // this.val = "Corporate Office (1005)"
  }
  getCICHDropdownValue() {
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.CICH_CUSTOMER_DROPDOWN, obj).subscribe
      ((response: any) => {
        if (response.status === 200) {
          let opt = response.body.dropdownList;
          this.cichDropdown = Object.values(opt);
          if (this.cichDropdown.length > 0) {
            this.selectedData = this.cichDropdown[0];
            this.isShowSearch = false;
            let obj = {
              "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
              "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
              "divisionCode": null,
              "deptCode": null,
              "accountNo": this.accountnumber,
              "cardList": this.selectedData?.cardNumber
              //this.localStorage.localStorageGet("custNo").view
            }
            this.getStatementOfAcc(obj)
          }
        }
      }, e => {
        this.notifyService.showError(e.error.errorMessage, "Error")

      })
  }
  getVal(){
    this.isShowSearch = false;
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "divisionCode": null,
      "deptCode": null,
      "accountNo": this.accountnumber,
      "cardList": this.selectedData?.cardNumber
      //this.localStorage.localStorageGet("custNo").view
    }
    this.getStatementOfAcc(obj)
  }
  sortData(sort: Sort) {
    const data = this.useageData.slice();
    if (!sort.active || sort.direction === '') {
      this.dataSource.data = data;
      return;
    }

    this.dataSource.data = data.sort((x, y) => {
      const isAsc = sort.direction === 'asc';
      switch (sort.active) {
        case 'monthYear': return this.compare(new Date(x.monthYear), new Date(y.monthYear), isAsc);
        default: return 0;
      }
    });
  }
  compare(a: Date | string, b: Date | string, isAsc: boolean) {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1)
  }
  getStatementOfAcc(reqObj: any) {
    // this.cdgService.localhostUrl
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2  + UrlConstants.GET_USEAGE_REPORT, reqObj).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body;

        //if (opt.length > 0) {
        if (this.localStorage.localStorageGet("loggedInRole") !== RoleConstants.ROLE_MASTERUSER) {
          if (opt.OneYearUsageReport.usageReportByMonthList && Object.keys(opt.OneYearUsageReport.usageReportByMonthList).length > 0) {
            this.displayedColumns = ['serialNo', 'monthYear', 'totalTrips', 'totalAmount', 'avgAmountPerTrip']
            this.useageData = opt.OneYearUsageReport.usageReportByMonthList;
            this.grandTotal = opt.OneYearUsageReport.grandTotal
          }

          // this.useageData[0].isHyperlinked=true;
          this.dataSource = new MatTableDataSource<any>(this.useageData);
          this.dataSource.paginator = this.paginator;
          this.dataSourceCell = opt;
        }
        else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_MASTERUSER) {
          if (opt.OneYearUsageReport.usageReportByMonthList && Object.keys(opt.OneYearUsageReport.usageReportByMonthList).length > 0) {
            this.displayedColumns = ['serialNo', 'monthYear', 'totalTrips', 'totalAmount', 'avgAmountPerTrip']
            this.useageData = opt.OneYearUsageReport.usageReportByMonthList;
            this.grandTotal = opt.OneYearUsageReport.grandTotal
          }
          this.dataSource = new MatTableDataSource<any>(this.useageData);
          this.dataSource.paginator = this.paginator;
          this.dataSourceCell = opt;
        }
        else {
          if (this.localStorage.localStorageGet("loggedInRole") !== RoleConstants.ROLE_CORPADMIN || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_MASTERUSER)
            this.notifyService.showInfo("No Records Found", 'Info')
        }
      }
    },
      e => {
        this.notifyService.showError(e.error.message, "Error")
        // this.isLoading = false;
      })
  }
  getTotalTrips() {
    return this.dataSourceCell.map(t => t.totalTrips).reduce((acc, value) => acc + value, 0)
  }

  getTotalAmt() {
    return this.dataSourceCell.map(t => t.totalAmount).reduce((acc, value) => acc + value, 0)
  }

  getTotalAvgSpend() {
    return this.dataSourceCell.map(t => t.avgAmountPerTrip).reduce((acc, value) => acc + value, 0)
  }
  showTable(selectedDivision: any) {
    this.isShowDeptList = false;
    let obj: any;
    if (selectedDivision.nameToDisplayOnDropdown === 'ALL') {
      obj = {
        accessId: this.localStorage.localStorageGet("loginUserDetails").accessId,
        role: this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        divisionCode: null,
        deptCode: null,
        accountNo: selectedDivision.accountNo
      }
      this.getStatementOfAcc(obj);
    }
    else {
      const dataSeleted = this.response.divAccountList.find((o: any) => o.divAccount.nameToDisplayOnDropdown === selectedDivision.nameToDisplayOnDropdown)
      if (dataSeleted) {
        if (dataSeleted.deptList.length > 0) {
          this.deptList = []
          this.deptList.push({
            "accountNo": 0,
            "accountName": "ALL",
            "accountCategory": "DEPT",
            "accountCode": null,
            "nameToDisplayOnDropdown": "ALL"
          })
          dataSeleted.deptList.forEach((element: any) => {
            this.deptList.push(element);
          });
          this.usageReportForm.get('department')?.setValue(this.deptList[0]);
          this.isShowDeptList = true;
        }
        else {
          this.deptList = []
          this.deptList.push({
            "accountNo": 0,
            "accountName": "ALL",
            "accountCategory": "DEPT",
            "accountCode": null,
            "nameToDisplayOnDropdown": "ALL"
          })
          this.usageReportForm.get('department')?.setValue(this.deptList[0]);
        }
      }
    }
  }
  goToHyperlinkPage(data: any) {
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPADMIN || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_MASTERUSER) {
      data['division'] = this.divisionSelected;
      this.cdgService.monthlySummaryObj = data;
    }
    else {
      this.cdgService.monthlySummaryObj = data;
    }
    this.router.navigateByUrl('/layout/usageReport/monthlySummary');
  }
  applyFilter() {
    const startDate = this.usageReportForm.value.startMonth.split(" ")
    const endDate = this.usageReportForm.value.endMonth.split(" ")
    let reqObj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accountNo": this.accountnumber,
      "divisionCode": this.usageReportForm.value.division.accountCode,
      "deptCode": this.usageReportForm.value.department.accountCode,
      "startmonth": this.months.indexOf(startDate[0]) + 1 < 10 ? '0' + (this.months.indexOf(startDate[0]) + 1) : this.months.indexOf(startDate[0]) + 1,
      "startyear": startDate[1],
      "endmonth": this.months.indexOf(endDate[0]) + 1 < 10 ? '0' + (this.months.indexOf(endDate[0]) + 1) : this.months.indexOf(endDate[0]) + 1,
      "endyear": endDate[1]
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2  + UrlConstants.USEAGE_REPORT_SEARCH, reqObj).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body;
        if (opt.usageReportByMonthList.length > 0) {
          this.displayedColumns = ['serialNo', 'monthYear', 'totalTrips', 'totalAmount', 'avgAmountPerTrip']
          this.useageData = opt.usageReportByMonthList;
          this.dataSource = new MatTableDataSource<any>(this.useageData);
          this.dataSource.paginator = this.paginator;
          this.dataSourceCell = opt.usageReportByMonthList;
        }
        this.grandTotal = opt.grandTotal;
      }
    },
      e => {
        this.notifyService.showError(e.error.statusMessage, "Error")
      })
  }
}
