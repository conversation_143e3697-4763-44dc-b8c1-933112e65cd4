import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort, Sort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';


export interface ReportElement {
  div: string;
  totalTrips: number;
  totalAmount: number;
  avgAmountPerTrip: number;
}

const DATA: ReportElement[] = [
  { div: 'DIV 1', totalTrips: 3, totalAmount: 95.90, avgAmountPerTrip: 31.97 },
  { div: 'DIV 2', totalTrips: 4, totalAmount: 95.90, avgAmountPerTrip: 31.97 },
  { div: 'DIV 3', totalTrips: 5, totalAmount: 95.90, avgAmountPerTrip: 31.97 },
  { div: 'DIV 4', totalTrips: 3, totalAmount: 95.90, avgAmountPerTrip: 31.97 },


  // { month: new Date('November 2021'), totalTrips: 5, avgSpend: 10, totalAmt: 50 }
]

@Component({
  selector: 'app-monthly-summary',
  templateUrl: './monthly-summary.component.html',
  styleUrls: ['./monthly-summary.component.scss'],
  providers: [DatePipe]
})


export class MonthlySummaryComponent implements OnInit {

  // Static Data used
  displayedColumns: string[] = ['serialNo', 'div', 'totalTrips', 'totalAmount', 'avgAmountPerTrip'];
  dataSourceDiv = new MatTableDataSource(DATA);

  public months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
  isShowPersonalCardHolderTbl: boolean = false;
  displayedColumnsPersonalCard: any[] = [];
  displayedColumnsCorpAdminCard: any[] = [];
  useageData: any[] = [];
  usageCorpAdminData:any[]=[];
  grandTotalObj: any;
  dataSource = new MatTableDataSource<any>(this.useageData);
  dataSourceCorp = new MatTableDataSource<any>(this.usageCorpAdminData);

  @ViewChild(MatSort) sort: MatSort
  Paginator: any;
  divisionCodeDetail: any;
  accountnumber: any;
  isData: boolean=false;
  @ViewChild(MatPaginator, { static: false })
  set paginator(value: MatPaginator) {
    this.Paginator = value;
    this.dataSource.paginator = value;
  }
  constructor(public localStorage: LocalStorageService,private fb: FormBuilder, private router: Router, private datePipe: DatePipe, private notifyService: NotificationService, public cdgService: CdgSharedService, private apiService: ApiServiceService) { }

  ngOnInit(): void {
    if(this.localStorage.localStorageGet("customerNoSelected")!=null){
      this.accountnumber=this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if(this.accountnumber=this.localStorage.localStorageGet("custNo")!=null){
      this.accountnumber=this.localStorage.localStorageGet("custNo").view;
    }
    else {
      this.accountnumber= this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
    }
    }
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPADMIN || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_MASTERUSER) {
      console.log("click me")
      this.isShowPersonalCardHolderTbl = false;
      if (this.cdgService.monthlySummaryObj) {
        console.log(this.cdgService.monthlySummaryObj)
        const data = this.cdgService.monthlySummaryObj.monthYear.split(" ");
        const mon=this.months.indexOf(data[0])+1
        let obj = {
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "accountNo" : this.accountnumber,
          "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          "accountLevel": "DIV",
          "divisionCode": this.cdgService.monthlySummaryObj.division?.accountCode,
          "deptCode": null,
          "month":mon <10 ? '0'+mon : mon,
          "year": data[1]
        }
        this.getUsageMonthlySummary(obj);
      }
    }
    else {
      this.isShowPersonalCardHolderTbl = true;
      if (this.cdgService.monthlySummaryObj) {
        const data = this.cdgService.monthlySummaryObj.monthYear.split(" ");
        let obj = {
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "month": this.months.findIndex(x => x === data[0]) + 1,
          "year": data[1]
        }
        this.getUsageMonthlySummary(obj);
      }
    }
    this.dataSource.sort = this.sort;

  }
  ngAfterViewInit() {

    // this.dataSource.paginator = this.paginator;

    this.dataSource.sort = this.sort;
  }
  getTotalTrips() {
    return this.usageCorpAdminData.map(t => t.totalTrips).reduce((acc, value) => acc + value, 0)
  }

  getTotalAmt() {
    return this.usageCorpAdminData.map(t => t.totalAmount).reduce((acc, value) => acc + value, 0)
  }

  getTotalAvgSpend() {
    return this.usageCorpAdminData.map(t => t.avgAmountPerTrip).reduce((acc, value) => acc + value, 0)
  }
  getUsageMonthlySummary(reqObj: any) {
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPADMIN || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_MASTERUSER) {
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.USEAGE_REPORT_MONTH_DIV_DEPT, reqObj).subscribe((response: any) => {
       console.log(response)
        if (response.status === 200) {
          let opt = response.body;
          if (opt.length > 0) {
            this.isData=false;
            this.displayedColumnsCorpAdminCard = ['serialNo', 'division', 'totalTrips', 'totalAmount', 'avgAmountPerTrip']
            this.usageCorpAdminData = opt;
            console.log(opt);
            // for(var i= 0 ; i > this.usageCorpAdminData.length;i++)
            // {
            //   if(this.usageCorpAdminData[i].isHyperlinked == true){
            //      let divicode1 =this.usageCorpAdminData.find(x => x.isHyperlinked === true)
            //        console.log(divicode1.divCode);
            //       this.divisionCodeDetail = divicode1.divCode;
            //       let diviscode = this.usageCorpAdminData[i].divCode;
            //       console.log(diviscode,"diviscode new")
            //   }
            // }


           
            // let divicode1 =this.usageCorpAdminData.find(x => x.isHyperlinked === true)
            // console.log(divicode1.divCode);
            //  this.divisionCodeDetail = divicode1.divCode;
            // console.log(divicode1)
            
           
            // this.usageCorpAdminData[0].isHyperlinked=true;
            this.dataSourceCorp = new MatTableDataSource<any>(this.usageCorpAdminData);
            this.dataSourceCorp.sort = this.sort;
            this.dataSourceCorp.paginator = this.paginator;
          }
          else{
            this.isData=true;
          }
        }
      },
        e => {
          this.notifyService.showError(e.error.statusMessage, "Error")
          // this.isLoading = false;
        })
    }
    else{
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_USEAGE_REPORT_MONTHLY, reqObj).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body;
        if (opt.usageReportByMonthList.length > 0) {
          this.isData=false;
          this.displayedColumnsPersonalCard = ['serialNo', 'cardNumber', 'cardHolderName', 'totalTrips', 'totalAmount', 'avgAmountPerTrip']
          this.useageData = opt.usageReportByMonthList;
          this.grandTotalObj = opt.grandTotal;
          this.dataSource = new MatTableDataSource<any>(this.useageData);
          this.dataSource.sort = this.sort;
          this.dataSource.paginator = this.paginator;
          // this.dataSourceCell=opt;
        }
        else{
          this.isData=true;
        }
      }
    },
      e => {
        this.notifyService.showError(e.error.statusMessage, "Error")
        // this.isLoading = false;
      })
    }
  }
  goToPrevPage() {
    
    this.router.navigateByUrl('/layout/usageReport');
  }
  goToHyperlinkPage(data:any){
    data['division']=this.cdgService.monthlySummaryObj.division
    data['monthYear']=this.cdgService.monthlySummaryObj.monthYear
    data['divisionCode'] = this.divisionCodeDetail;
    console.log(data['divisionCode']);
    this.cdgService.monthlySummaryDivObj=data;
    console.log(data);
    this.router.navigateByUrl('/layout/usageReport/divMonthlySummary');
  }
  sortData(sort: Sort) {
    const data = this.usageCorpAdminData.slice();
    if (!sort.active || sort.direction === '') {
      this.dataSourceCorp.data = data;
      return;
    }

    this.dataSourceCorp.data = data.sort((x, y) => {
      const isAsc = sort.direction === 'asc';
      console.log(typeof x.division,x.division)
      switch (sort.active) {
        case 'division': return this.compareString(x.divName, y.divName, isAsc);
        case 'totalTrips': return this.compare(x.totalTrips, y.totalTrips, isAsc);
        case 'totalAmount': return this.compare(x.totalAmount, y.totalAmount, isAsc);
        case 'totalTrips': return this.compare(x.avgAmountPerTrip, y.avgAmountPerTrip, isAsc);
        default: return 0;
      }
    });
  }
  compare(a: Date | string | number, b: Date | string | number, isAsc: boolean) {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1)
  }
  compareString(a:string, b:string, isAsc: boolean) {
    return (a.localeCompare(b)) * (isAsc ? 1 : -1)
  }
}
