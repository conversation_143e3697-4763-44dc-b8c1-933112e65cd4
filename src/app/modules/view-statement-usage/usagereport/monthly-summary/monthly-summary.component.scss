@import '@angular/material/theming';
@import './../../../../../styles/colors.scss';
@import './../../../../../styles/sizing.scss';
@import './../../../../../styles/main.scss';
.heading{
    // font-family: Roboto;
    // font-family:".SF NS Display";
    // font-style: normal;
    //font-weight: $cdgsz-font-weight-intermediate;
    // font-size: 24px !important; 
    // line-height: 14px !important;
    // padding-left: 2%;
    color: $lightGrey !important;
    font-size: 28px !important; 
    font-weight:$cdgsz-font-weight-normal !important ;
    
    }
    .icon{
        color: $cdgc-font-prime !important;
    }
    .sub-header{
        padding-left:40px;
        color: $cdgc-font-prime !important;
    }
    .noData{
        padding-left:40px;
        color: $cdgc-font-warn !important;
        font-weight: $cdgsz-font-weight-bold !important ;
    }
    .tbl-container{
        display: flex;
        flex-direction: column;
        // background-color: transparent !important;
        // max-width: 300px;
        max-height: 500px;
    }
    .mat-mdc-header-row{
        border-radius: 10px;
        background-color:$cdgc-bg-prime !important;
        color: $cdgc-font-accent !important;
    }
    .mat-mdc-header-cell {
        color: $cdgc-font-accent !important;
        
    }
    .mat-mdc-row{
        border-radius: 12px;
        color: $lightGrey !important;
    
    }
    .mat-mdc-footer-cell{
        font-weight: $cdgsz-font-weight-bold !important;
    }
    .sno{
        display: block;
    }
    .mobile-label{
        display: none;
    }
    .mat-mdc-footer-row{
        font-weight: $cdgsz-font-weight-bold !important;
    }
      @media screen and (max-width: 600px) {
        .mobile-label{
            display: inline-block;
            width: 165px;
            font-weight: $cdgsz-font-weight-bold;
        }
        .mat-mdc-header-row{
            display: none;
        }
        .mat-mdc-row{
            flex-direction: column;
            align-items: start;
            padding: 8px 24px;
            border-radius: 12px !important;
            border: 1px solid $cdgc-border-prime;
            min-height: 28px !important;
            margin-bottom: 10px;
        }
        .mat-mdc-footer-row{
          flex-direction: column;
          align-items: start;
          padding: 8px 24px;
          border-radius: 12px !important;
          border: 1px solid $cdgc-border-prime;
          min-height: 28px !important;
          margin-bottom: 10px;
      }
        :host ::ng-deep  mat-cell:first-of-type, mat-header-cell:first-of-type, mat-footer-cell:first-of-type {
         padding-left: 0px !important;
        }
        .sno{
            display: none !important;
        }
      }
      .hyperlink{
        color: $cdgc-font-prime !important;
      }