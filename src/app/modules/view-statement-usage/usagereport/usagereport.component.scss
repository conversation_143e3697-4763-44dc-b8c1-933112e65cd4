@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';


::ng-deep th.mat-mdc-header-cell{
  height: 36px !important;
   padding-top: 0 !important;
}
.inp{
  width: 270px !important;
}
.sub-header{
  font-size: 19px !important;
}
.card-table{
width: 95%;
height: 96px;
// background-color: #CAEBFF;
//background-color:$cdgc-mild !important;
margin-left: 3%;
margin-right: 3%;
margin: 0 3% 2% 3%;
border-radius: 5px;
}

.card-content{
font-family: Roboto;
font-style: normal;
font-weight: $cdgsz-font-weight-bold;
font-size: $cdgsz-font-size-prime;//16px
line-height: 19px;
}

.card-footer{
font-family: Roboto;
font-style: normal;
font-weight: $cdgsz-font-weight-normal !important;
font-size: $cdgsz-font-size-xs;
line-height: 14px;
}
.hyperlink{
  color: $cdgc-font-prime !important;
}
table {
    width: 95%;
    margin-left: 3%;
    margin-bottom: 25px;
    font-family: Roboto;
    font-style: normal;
    margin-top: 2%;
    text-align: center;
  
  }

  .mat-mdc-header-cell{
    font-weight: $cdgsz-font-weight-normal !important;
    font-size: $cdgsz-font-size-sm !important;
    // line-height: 17px !important;
    background-color: $cdgc-bg-prime;
    color: $cdgc-font-accent;
    height: 36px !important;
    text-align: center;
  }

  ::ng-deep .text-right .mat-sort-header-container{
    justify-content: center !important;
  }

  .mat-mdc-header-cell:first-child{
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
  }

  .mat-mdc-header-cell:last-child{
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
  }

  .mat-mdc-row:first-child{
    border-radius: 5px !important;
  }
 
 .mat-mdc-cell{
    font-weight: $cdgsz-font-weight-normal !important;
    // font-size: $cdgsz-font-size-prime;
    // line-height: 18px;
    font-size: 14px;
    line-height: 16px;
    // text-align: center !important;
 }
 .mat-mdc-footer-row{
  font-weight: $cdgsz-font-weight-bold !important;
}
.sno{
  display: block;
}
.mobile-label{
  display: none;
}
@media screen and (max-width: 600px) {
  .mobile-label{
      display: inline-block;
      width: 165px;
      font-weight: $cdgsz-font-weight-bold;
  }
  .mat-mdc-header-row{
      display: none;
  }
  .mat-mdc-row{
      flex-direction: column;
      align-items: start;
      padding: 8px 24px;
      border-radius: 12px !important;
      border: 1px solid $cdgc-border-prime;
      min-height: 28px !important;
      margin-bottom: 10px;
  }
  .mat-mdc-footer-row{
    flex-direction: column;
    align-items: start;
    padding: 8px 24px;
    border-radius: 12px !important;
    border: 1px solid $cdgc-border-prime;
    min-height: 28px !important;
    margin-bottom: 10px;
}
  :host ::ng-deep  mat-cell:first-of-type, mat-header-cell:first-of-type, mat-footer-cell:first-of-type {
   padding-left: 0px !important;
  }
  .sno{
      display: none !important;
  }
}
//   .mat-row:nth-child(even){
//     background: #f6f6f6;
// }
        
// .mat-row:nth-child(odd){
//     background-color: #CAEBFF;
//     //background-color:$cdgc-mild!important;
// }
  
.heading{
// font-family: Roboto;
// font-family:".SF NS Display";
// font-style: normal;
//font-weight: $cdgsz-font-weight-intermediate;
// font-size: 24px !important; 
// line-height: 14px !important;
padding-left: 2%;
color: $lightGrey !important;
font-size: 28px !important; 
font-weight:$cdgsz-font-weight-normal !important ;

}

.calendar{
    margin-right: 9%;
    border-radius: 2em;
}
 /* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
 .mat-form-field-appearance-outline .mdc-notched-outline {
  
    border-radius: 10px !important;
  }

  /* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  :host ::ng-deep .mat-mdc-form-field-fill .mat-mdc-form-field-flex {
    border-radius: 10px !important;
  }

  /* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  :host ::ng-deep .mat-mdc-form-field-underline {
    display: none;
  }

  // .align-icon-text{
  //   display: flex;
  //   align-items: center;
  //   padding: 0px 14px 0px 14px;
  // }

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
}


.dropdown-div{
    margin: 2% 3% 0 3%;
    width:95%;
    background-color: #E7E7E7;
    font-size: 16px;
    line-height: 18px;
}

.dropdown-div-cell{
 margin-top: 7%;
}
.asterisk{
  color: $red;
}

.dropdown{
  
   margin-bottom: 2px !important;
    margin-top: 6%;
}
.dropdown-division{
  
  margin-bottom: 2px !important;
 
}

/* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
::ng-deep .mat-mdc-select-arrow{
  border-left: 0px solid transparent !important;
  border-top: 0px solid !important;
  border-style: solid !important;
  border-right: 2px solid !important;
  border-width: 0 2px 2px 0 !important;
  content: "";
  display: inline-block !important;
  padding: 3px !important;
  transform: rotate(45deg) !important;
  vertical-align: middle !important;
  // color: red !important;
}

.mat-mdc-row:hover{
  background-color: $cdgc-bg-mild;
}

/* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
::ng-deep .mat-mdc-select-arrow-wrapper {
  vertical-align: bottom !important;
}

// ::ng-deep .mat-sort-header-arrow{
//   transform: none !important;
//   opacity: 1 !important;
//   color: $cdgc-bg-accent;
//   content: "";
// }



.mat-mdc-header-row{
  height: auto !important;
}

// ::ng-deep .mat-sort-header.ng-trigger.ng-trigger-arrowPosition{
//   opacity: 0 !important;
// }

// ::ng-deep .ng-trigger-leftPointer{
//   opacity: 0 !important;
// }

// ::ng-deep .ng-trigger-rightPointer{
//   opacity: 0 !important;
// }

// ::ng-deep .mat-sort-header-stem {
//   display: none !important;
// }

// ::ng-deep .mat-sort-header-pointer-left .mat-sort-header-pointer-right{
//   display: none !important;
// }

// ::ng-deep .mat-sort-header-pointer-middle{
//   width: 0px !important;
//   height: 0px !important;
// }

// ::ng-deep .mat-sort-header-arrow{
//      .mat-sort-header-indicator{
//        &::before {
//         //  content: "<>";
//         content: "\25c2 \25b8";
//          position: absolute;
//          opacity : 1 !important;
//          color: $cdgc-bg-accent !important;
//          font-size: 1.2 em !important ;
//          font-weight: bold;
//         transform: translate(-10%, 20%) rotate(90deg) !important;
//        }
//      }
// }

// [aria-sort="ascending"] {
//  ::ng-deep .mat-sort-header-arrow{
//     .mat-sort-header-indicator{
//       &::before {
//         content: "\25b4";
//         position: absolute;
//         opacity : 1 !important;
//         color: $cdgc-bg-accent !important;
//         font-size: 1.2 em !important;
//         font-weight: bold;
//         transform: translate(0, 0)  !important;
//       }
//     }
// }
// }

// [aria-sort="descending"] {
//   ::ng-deep .mat-sort-header-arrow{
//      .mat-sort-header-indicator{
//        &::before {
//          content: "\25be";
//          position: absolute;
//          opacity : 1 !important;
//          color: $cdgc-bg-accent !important;
//          font-size: 1.2 em !important;
//          font-weight: bold;
//          transform: translate(0,-10%)  !important;
//        }
//      }
//  }
//  }

// ::ng-deep .mat-button-wrapper{
//   padding-left: 35px !important;
// }


::ng-deep .main-container{
  background-color: $white !important;
}

::ng-deep .mat-mdc-select-panel{
  min-width: fit-content !important;
}

// For right indentation of numbers

// .mat-column-serialNo{
//   width: 6% !important;
//   padding:0 10% 0 7% !important;
//   text-align: center !important;
// }

.mat-column-month{
  width:20% !important;
  padding: 0 4% 0 3% !important;
  text-align: center !important;
  
}

// .mat-column-totalTrips{
//   width:15% !important;
//   padding:0 4% 0 4% !important;
//   text-align: right !important;
  
// }

// .mat-column-totalAmt{
//   width:15% !important;
//   padding-right: 4% !important;
//   text-align: right !important;
// }

// .mat-column-avgSpend{
//   width:22% !important;
//   padding: 0 4% 0 5% !important;
//   text-align: right !important;
// }

:host .btn{
    width:130px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
    margin: 15px !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-form-field-appearance-outline .mat-mdc-form-field-prefix, .mat-form-field-appearance-outline .mat-mdc-form-field-suffix {
  top: 8px !important;
}