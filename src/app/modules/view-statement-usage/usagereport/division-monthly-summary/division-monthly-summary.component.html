<div fxLayout="column" fxLayoutAlign="start ">
    <!-- <div style="margin-left:3%" fxLayout="row">
        <img src="\assets\images\view usage report.png">
        <p class="heading"> View Usage Report</p>
    </div> -->
    <div fxFlex fxLayout="row" fxLayoutGap="15px">
        <div class="cursor-pointer" fxLayoutAlign="start center">
            <mat-icon (click)="goToPrevPage()" class="icon">arrow_back</mat-icon>
        </div>
        <div fxFlex fxLayout="row" fxLayoutGap.lt-sm="15px">
            <div fxFlex="5%">
                <img src="\assets\images\view usage report.png" alt="">
            </div>
            <div fxFlex="95%" fxLayoutAlign="start center">
                <span class="heading pb5">View Usage Report</span>
            </div>
        </div>

    </div>
    <div>
        <span class="sub-header">Monthly Summary > Month Details > Division Details</span>
    </div>
    <div class="tbl-container pt15" >
        <mat-table #table [dataSource]="dataSource" matSort (matSortChange)="sortData($event)">
            <ng-container matColumnDef="serialNo">
                <mat-header-cell *matHeaderCellDef> S No. </mat-header-cell>
                <mat-cell *matCellDef="let element, let i = index;" class="sno" fxLayoutAlign="start center">
                    {{i + 1}}
                </mat-cell>
                <!-- <mat-footer-cell *matFooterCellDef>Grand Total</mat-footer-cell> -->
            </ng-container>
            <ng-container matColumnDef="dept">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header="dept">Department</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label">Department:</span>
                    <!-- {{element.dept}} -->
                    {{element.divName}}
                </mat-cell>
                <!-- <mat-footer-cell *matFooterCellDef></mat-footer-cell> -->
            </ng-container>
            <!-- <ng-container matColumnDef="cardNumber">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Card No.</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label"> Card No.:</span>
                    {{element.cardNumber}}
                </mat-cell>
                <mat-footer-cell *matFooterCellDef>Grand Total</mat-footer-cell>
            </ng-container> -->
            <!-- <ng-container matColumnDef="cardHolderName">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Card Holder Name
                </mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label"> Card Holder Name:</span>
                    {{element.cardHolderName}}
                </mat-cell>
                <mat-footer-cell *matFooterCellDef></mat-footer-cell>

            </ng-container> -->
            <ng-container matColumnDef="totalTrips">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header="totalTrips">Total Trips</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label"> Total Trips:</span>
                    {{element.totalTrips}}
                </mat-cell>
                <!-- <mat-footer-cell *matFooterCellDef>
                    <span class="mobile-label"> Total Trips Sum:</span>
                    {{grandTotalObj.totalTrips | currency}}
                </mat-footer-cell> -->
            </ng-container>


            <ng-container matColumnDef="totalAmount">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header="totalAmount">Total Amount ($)
                </mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label"> Total Amount ($):</span>
                    {{element.totalAmount | currency:'':''}}
                </mat-cell>
                <!-- <mat-footer-cell *matFooterCellDef>
                    <span class="mobile-label"> Total Amount Sum ($):</span>
                    {{grandTotalObj.totalAmount | currency}}
                </mat-footer-cell> -->
            </ng-container>
            <ng-container matColumnDef="avgAmountPerTrip">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header="avgAmountPerTrip">Average Amount per Trip ($)
                </mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label"> Average Amount per Trip ($):</span>
                    {{element.avgAmountPerTrip | currency:'':''}}
                </mat-cell>
                <!-- <mat-footer-cell *matFooterCellDef>
                    <span class="mobile-label"> Average Amount per Trip Sum($):</span>
                    {{grandTotalObj.avgAmountPerTrip | currency}}
                </mat-footer-cell> -->

            </ng-container>
            <mat-header-row *matHeaderRowDef="displayedColumnsPersonalCard"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumnsPersonalCard;"></mat-row>
            <!-- <mat-footer-row *matFooterRowDef="displayedColumnsPersonalCard"></mat-footer-row> -->
        </mat-table>
        <mat-paginator [pageSize]="5" [pageSizeOptions]="[5, 10, 20, 50, 100]"
            showFirstLastButtons></mat-paginator>

    </div>
</div>
