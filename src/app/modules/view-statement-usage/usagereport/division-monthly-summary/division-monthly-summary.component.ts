import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort, Sort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';

export interface ReportElement {
  dept: string;
  totalTrips: number;
  totalAmount: number;
  avgAmountPerTrip: number;
}

const DATA: ReportElement[] = [
  { dept: 'DEPT 1', totalTrips: 3, totalAmount: 95.90, avgAmountPerTrip: 31.97 },
  { dept: 'DEPT 2', totalTrips: 4, totalAmount: 95.90, avgAmountPerTrip: 31.97 },
  { dept: 'DEPT 3', totalTrips: 5, totalAmount: 95.90, avgAmountPerTrip: 31.97 },

  // { month: new Date('November 2021'), totalTrips: 5, avgSpend: 10, totalAmt: 50 }
]

@Component({
  selector: 'app-division-monthly-summary',
  templateUrl: './division-monthly-summary.component.html',
  styleUrls: ['./division-monthly-summary.component.scss'],
  providers: [DatePipe]
})
export class DivisionMonthlySummaryComponent implements OnInit {

  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  displayedColumnsPersonalCard: string[] = [];
  isShowPersonalCardHolderTbl: boolean = false;
  public months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
  useageData: any[]=[];
  dataSource = new MatTableDataSource(this.useageData);
  accountnumber: any;
  constructor(public localStorage: LocalStorageService, private fb: FormBuilder, private router: Router, private datePipe: DatePipe, private notifyService: NotificationService, public cdgService: CdgSharedService, private apiService: ApiServiceService) { }

  ngOnInit(): void {
    if(this.localStorage.localStorageGet("customerNoSelected")!=null){
      this.accountnumber=this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if(this.accountnumber=this.localStorage.localStorageGet("custNo")!=null){
      this.accountnumber=this.localStorage.localStorageGet("custNo").view;
    }
    else {
      this.accountnumber= this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
    }
    }
    console.log(this.cdgService.monthlySummaryDivObj)
    this.isShowPersonalCardHolderTbl = true;
    const data = this.cdgService.monthlySummaryDivObj.monthYear.split(" ");
    const mon = this.months.indexOf(data[0]) + 1
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "accountNo" : this.accountnumber,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accountLevel": "DEPT",
      // "divisionCode": this.cdgService.monthlySummaryDivObj.division.accountCode,
      "divisionCode": this.cdgService.monthlySummaryDivObj.divCode,
      "deptCode": null,
      "month":mon <10 ? '0'+mon : mon,
      "year": data[1]
    }
    console.log(obj);
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2  + UrlConstants.USEAGE_REPORT_MONTH_DIV_DEPT, obj).subscribe((response: any) => {
      console.log(response)
      if (response.status === 200) {
        let opt = response.body;
        if (opt.length > 0) {
          this.displayedColumnsPersonalCard = ['serialNo', 'dept', 'totalTrips', 'totalAmount', 'avgAmountPerTrip']
          this.useageData = opt;
          // this.grandTotalObj = opt.grandTotal;
          this.dataSource = new MatTableDataSource<any>(this.useageData);
          this.dataSource.sort = this.sort;
          this.dataSource.paginator = this.paginator;
          // this.dataSourceCell=opt;
        }
      }
    },
      e => {
        this.notifyService.showError(e.error.statusMessage, "Error")
        // this.isLoading = false;
      })
  }

  ngAfterViewInit() {

    this.dataSource.paginator = this.paginator;

    this.dataSource.sort = this.sort;
  }

  goToPrevPage() {
    this.router.navigateByUrl('/layout/usageReport/monthlySummary');
  }
  sortData(sort: Sort) {
    const data = this.useageData.slice();
    if (!sort.active || sort.direction === '') {
      this.dataSource.data = data;
      return;
    }

    this.dataSource.data = data.sort((x, y) => {
      const isAsc = sort.direction === 'asc';
      console.log(typeof x.division,x.division)
      switch (sort.active) {
        case 'dept': return this.compareString(x.divName, y.divName, isAsc);
        case 'totalTrips': return this.compare(x.totalTrips, y.totalTrips, isAsc);
        case 'totalAmount': return this.compare(x.totalAmount, y.totalAmount, isAsc);
        case 'totalTrips': return this.compare(x.avgAmountPerTrip, y.avgAmountPerTrip, isAsc);
        default: return 0;
      }
    });
  }
  compare(a: Date | string | number, b: Date | string | number, isAsc: boolean) {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1)
  }
  compareString(a:string, b:string, isAsc: boolean) {
    return (a.localeCompare(b)) * (isAsc ? 1 : -1)
  }
}
