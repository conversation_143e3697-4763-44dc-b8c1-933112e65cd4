<div fxLayout="column" fxLayoutAlign="start ">
    <div style="margin-left:3%" fxLayout="row">
        <img src="\assets\images\view usage report.png">
        <p class="heading"> View Usage Report</p>
    </div>
    <div *ngIf="isShowSearch">
        <form [formGroup]=" usageReportForm">
            <!-- <mat-card appearance="outlined" fxLayout="column" class="dropdown-div " [ngClass.lt-md]="'dropdown-div-cell'"> -->
                <mat-card appearance="outlined" fxLayout="column" class="dropdown-div " >
                <div fxLayout.gt-md="row" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutAlign="space-between" style="padding: 15px;">
                    <div fxFlex.gt-md="25%" fxFlex.sm="100%" fxLayout="column">
                        <!-- <p fxFlex="35%" [ngClass.lt-md]="'dropdown-division'">DIVISION <span -->
                            <p fxFlex="35%" >DIVISION <span
                                class="asterisk"><sup>*</sup></span></p>
                        <mat-form-field style="width: 95%;" appearance='outline'>
                            <!-- <mat-label>Division</mat-label> -->
                            <mat-select [(ngModel)]="divisionSelected" placeholder="- - Select Division - -"
                                formControlName="division" (ngModelChange)="showTable(divisionSelected)" required>

                                <mat-option *ngFor="let div of dataDropdown" [value]="div">
                                    {{div.nameToDisplayOnDropdown}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>

                    <div fxFlex.gt-md="25%" fxFlex.sm="100%" fxLayout="column">
                        <!-- <p fxFlex="35%" [ngClass.lt-md]="'dropdown'">DEPARTMENT</p> -->
                        <p fxFlex="35%" >DEPARTMENT</p>
                        <mat-form-field style="width: 95%;" appearance='outline'>

                            <!-- <mat-label>Department</mat-label> -->
                            <mat-select placeholder="- - Select Department - -" formControlName="department">
                                <!-- <mat-option [value]="">- - Select Department - -</mat-option> -->
                                <mat-option *ngFor="let div of deptList" [value]="div">
                                    {{div.nameToDisplayOnDropdown}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>


                    <div fxFlex.gt-md="25%" fxFlex.sm="100%" fxLayout="column">
                        <!-- <p fxFlex="35%" [ngClass.lt-md]="'dropdown'">START MONTH</p> -->
                        <p fxFlex="35%" >START MONTH</p>
                        <mat-form-field style="width: 95%;" appearance="outline">
                            <!-- <mat-label>Start Month</mat-label> -->
                            <!-- <input formControlName="startMonth" placeholder="- - Select Start Month - -" matInput
                                [matDatepicker]="picker5">
                            <mat-datepicker-toggle matSuffix [for]="picker5"></mat-datepicker-toggle>

                            <mat-datepicker #picker5></mat-datepicker> -->
                            <mat-icon matPrefix>calendar_today</mat-icon>
                            <mat-select placeholder="- - Select Start Month - -" formControlName="startMonth">
                                <!-- <mat-option [value]="">- - Select Department - -</mat-option> -->
                                <mat-option *ngFor="let month of startMonthList" [value]="month">
                                    {{month}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>


                    <div fxFlex.gt-md="25%" fxFlex.sm="100%" fxLayout="column">
                        <!-- <p fxFlex="30%" [ngClass.lt-md]="'dropdown'">END MONTH</p> -->
                        <p fxFlex="30%" >END MONTH</p>
                        <mat-form-field style="width:95%" appearance="outline">
                            <!-- <mat-label>End Month</mat-label> -->
                            <!-- <input formControlName="endMonth" placeholder="- - Select End Month - -" matInput
                                [matDatepicker]="picker6">
                            <mat-datepicker-toggle matSuffix [for]="picker6">
                            </mat-datepicker-toggle>
                            <mat-datepicker #picker6></mat-datepicker> -->
                            <mat-icon matPrefix>calendar_today</mat-icon>
                            <mat-select placeholder="- - Select Start Month - -" formControlName="endMonth">
                                <!-- <mat-option [value]="">- - Select Department - -</mat-option> -->
                                <mat-option *ngFor="let month of endMonthList" [value]="month">
                                    {{month}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>

                    </div>
                    <!-- <mat-error
                        *ngIf="usageReportForm.hasError('notValid') && usageReportForm.controls.endMonth.value!=''">
                        End Month Should Be Same Or Later Than Start Month</mat-error> -->
                </div>
                <div fxLayoutAlign.lt-md="center center">
                    <button class="btn" mat-raised-button (click)="applyFilter()">SEARCH</button>
                </div>
            </mat-card>
        </form>
    </div>
    <!-- Desktop view -->
    
    <div class="tbl-container pt15 pl20 pr20">
        <!-- <div>
            <span class="sub-header pb5">CICH <span class="asterisk"><sup>*</sup></span>:</span>
        </div> -->
        <div fxLayout="row" fxLayoutGap="40px" *ngIf="isShowCICHDropdown" class="pt5 pb10">
            <!-- <span class="date-title pt10">Department :</span> -->
            <mat-form-field appearance="outline" class="inp">
                <mat-select [(ngModel)]="selectedData" (ngModelChange)="getVal()"
                [ngModelOptions]="{standalone: true}" name="cichDrodown">
                  <mat-option *ngFor = "let val of cichDropdown;" [value]="val">
                      {{val.accountName}} ({{val.customerNo}})
                 </mat-option>
                </mat-select>
              </mat-form-field>  
        </div>
        <!-- <div class="mat-elevation-z8" fxFlex=100%> -->
        <mat-table #table class="mat-table mat-cdk-table" [dataSource]="dataSource" matSort
            (matSortChange)="sortData($event)">
            <ng-container matColumnDef="serialNo">
                <mat-header-cell *matHeaderCellDef> S No. </mat-header-cell>
                <mat-cell *matCellDef="let element, let i = index;" class="sno" fxLayoutAlign="start center">
                    {{i + 1}}
                </mat-cell>
                <mat-footer-cell *matFooterCellDef>Grand Total</mat-footer-cell>
            </ng-container>
            <ng-container matColumnDef="monthYear">
                <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Month</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label"> Month:</span>
                    <ng-container *ngIf="element.isHyperlinked === false">
                        {{element.monthYear}}
                    </ng-container>
                    <ng-container *ngIf="element.isHyperlinked === true">
                        <u class="cursor-pointer hyperlink"
                            (click)="goToHyperlinkPage(element)">{{element.monthYear}}</u>
                    </ng-container>
                    <!-- <span class="mobile-label" > Invoice Amount:</span> -->
                    <!-- <span class="fwb font-16" data-label="invoiceNo">  -->
                    <!-- </span> -->
                </mat-cell>
                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="totalTrips">
                <mat-header-cell *matHeaderCellDef class="fwb font-16">Total Trips</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label"> Total Trips:</span>
                    <!-- <span class="fwb font-16" data-label="invoiceDate">  -->
                    {{element.totalTrips}}
                    <!-- </span> -->
                </mat-cell>
                <mat-footer-cell *matFooterCellDef>
                    <span class="mobile-label">Total Trips Sum:</span>
                    <!-- {{getTotalTrips()}} -->
                    {{grandTotal.totalTrips}}
                </mat-footer-cell>
            </ng-container>


            <ng-container matColumnDef="totalAmount">
                <mat-header-cell *matHeaderCellDef class="fwb font-16">Total Amount ($)</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label"> Total Amount ($):</span>
                    <!-- <span class="fwb font-16" data-label="invoiceTo"> -->
                    {{element.totalAmount | currency:'':''}}
                    <!-- </span> -->
                </mat-cell>
                <mat-footer-cell *matFooterCellDef>
                    <span class="mobile-label"> Total Amount Sum($):</span>
                    <!-- {{getTotalAmt() | currency:'':''}} -->
                    {{grandTotal.totalAmount | currency}}
                </mat-footer-cell>
            </ng-container>
            <ng-container matColumnDef="avgAmountPerTrip">
                <mat-header-cell *matHeaderCellDef class="fwb font-16">Average Amount per Trip ($)</mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label"> Average Amount per Trip ($):</span>
                    <!-- <span class="fwb font-16" data-label="invoiceAmt">  -->
                    {{element.avgAmountPerTrip | currency:'':''}}
                    <!-- </span> -->
                </mat-cell>
                <mat-footer-cell *matFooterCellDef>
                    <span class="mobile-label"> Average Amount per Trip Sum($):</span>
                    <!-- {{getTotalAvgSpend() | currency:'':''}} -->
                    {{grandTotal.avgAmountPerTrip | currency}}

                </mat-footer-cell>

            </ng-container>
            <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            <mat-footer-row *matFooterRowDef="displayedColumns"></mat-footer-row>
        </mat-table>
        <!-- <mat-paginator [pageSizeOptions]="[5, 10, 20, 50, 100]" showFirstLastButtons></mat-paginator> -->

    </div>
    <!-- Mobile, tablet view -->
    <!-- <div fxFlex="grow">
        <mat-card fxHide.gt-md *ngFor="let i of dataSourceCell,let ind=index" class="card-table" fxFlex="grow"
            fxLayout="row">



            <div fxFlex="100%" fxLayout="row" class="card-content" fxLayoutAlign="space-between center">
                <div fxFlex=50% fxLayout="column" fxLayoutAlign="center start">
                    <p class="card-content">{{i.month}}</p>
                    <p class="card-footer">Total Trips: {{i.totalTrips}}</p>
                </div>
                <div fxFlex=50% fxLayout="column" fxLayoutAlign="center end">
                    <p class="card-content">{{i.totalAmt}}</p>
                    <p class="card-footer">Average Spend: {{i.avgSpend}}</p>
                </div>

            </div>



        </mat-card>
    </div> -->
</div>