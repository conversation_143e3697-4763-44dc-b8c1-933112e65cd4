import { Component, OnInit } from '@angular/core';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as _moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core'
import { ExportxlsxService } from 'src/app/shared/services/exportxlsx.service';
import { DatePipe } from '@angular/common';
import { NgForm, FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms'
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { HttpClient } from '@angular/common/http';
import { saveAs } from 'file-saver'
export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
}
@Component({
  selector: 'app-e-receipt-request',
  templateUrl: './e-receipt-request.component.html',
  styleUrls: ['./e-receipt-request.component.scss'],
  providers: [DatePipe, { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] }, { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]
})
export class EReceiptRequestComponent implements OnInit {
  invoiceForm: FormGroup
  byCardForm: FormGroup
  tripData = {
    radioValue: "cardNo",
    cards: 0,
    selectedStartDate: "",
    selectedEndtDate: "",
    invoiceno: ""
  }
  radioArr = [
    { value: "cardNo", viewValue: "CARD NUMBER", isDisable: false },
    { value: "invoiceno", viewValue: "INVOICE NO.", isDisable: false }
  ]
  isInvoiceDisable: boolean = true;
  cardNoArr = [13231321323123, 13231321323128, 13231321323124]
  productArr = [
    { value: 'all', viewValue: 'ALL' },
    { value: 'contactlesscorprate', viewValue: 'CONTACTLESS CORPORATE CARD' },
    { value: 'contactlessprepaid', viewValue: 'CONTACTLESS PREPAID CARD' },
    { value: 'corporatecard', viewValue: 'CORPORATE CARD' },
    { value: 'evoucher', viewValue: 'OPEN VALUE E-VOUCHER' },
    { value: 'virtualcrop', viewValue: 'VIRTUAL CORP 2 TEST' },
    { value: 'vitualcorporate', viewValue: 'VIRTUAL CORPORATE' },
    { value: 'virtualprepaid', viewValue: 'VIRTUAL PREPAID' }
  ];

  showCardDateError: boolean = false;
  isShowDivision: boolean = true;
  isProductDisable: boolean = false;
  isCardDisable: boolean = true;
  showInvoiceDateError: boolean = false;
  invoiceNoList: any[] = [];
  invoiceArray: any;
  cardNoList: any[] = [];
  cardArray: any;
  firstdigit: string;
  displaycard: any[] = [];
  showInvoiceDateGreater: boolean = false;
  showCardDateGreater: boolean = false;
  accountnumber: any;
  productList: any[] = [];
  isShowCardNoList: boolean = false;
  noCardListForFromAndTo:boolean =false;
  noCardListForCardNo:boolean =false;
  noInvoiceListFound:boolean = false;
  accNoList: any[] = []
  public months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
  lastTwlveMonths: any[] = [];
  cardArrayTo: any[] = [];
  cardArrayFrom: any[] = [];
  showCardFromGreater: boolean=false;
  canShowProcessingBtn: boolean=false;
  constructor(private http: HttpClient, public localStorage: LocalStorageService, private excelService: ExportxlsxService, private datePipe: DatePipe, private fb: FormBuilder, public cdgService: CdgSharedService, private notifyService: NotificationService, private apiService: ApiServiceService) { 
    this.invoiceForm = this.fb.group({
      invoiceNo: [{ value: '', disabled: true }, [Validators.required]],
      iStartDate: ['', [Validators.required]],
    });
    this.byCardForm= this.fb.group({
      cardNo: [{ value: '', disabled: true }],
      cardNoFrom: [{ value: '', disabled: true }],
      cardNoTo: [{ value: '', disabled: true }],
      prodType: ['', [Validators.required]],
      cStartDate: ['', [Validators.required]],
      cEndDate: ['', [Validators.required]],
    });
  }
  ngOnInit(): void {
    // this.selectedStartDate = new Date();
    // this.selectedEndtDate = new Date();
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }
    }
    let d:any;
    const today = new Date();
    for (var i = 0; i < 12; i += 1) {
      d = new Date(today.getFullYear(), today.getMonth() - i, 1);
      // const month = this.months.findIndex(o => o === this.months[d.getMonth()])
      this.lastTwlveMonths.push({
        display:this.months[d.getMonth()] + ' ' + d.getFullYear(),
        sDate: new Date(Number(d.getFullYear()), d.getMonth(), 1),
        eDate:new Date(Number(d.getFullYear()), d.getMonth() + 1, 0)
      });
    }
    //  console.log(this.lastTwlveMonths)
    this.tripData.radioValue = "cardNo"
    this.radioArr[1].isDisable = false;
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER) {
      this.isShowDivision = false;
      this.productArr.push({ value: 'personalCard', viewValue: 'PERSONAL CARD' })
      // this.tripData.productSelect = 'PERSONAL CARD'
    }
    else if (this.localStorage.localStorageGet("loggedInRole") === 'Personal Card Sub') {
      this.isShowDivision = false;
    }
    else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
      // this.tripData.radioValue="invoiceno"
      this.radioArr[1].isDisable = true;
      if (this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails.length > 0) {
        this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails.forEach((element: any) => {
          this.accNoList.push(element.account_no)
        });
      }

    }
    else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUBAPPLICANT) {
      // this.tripData.radioValue="invoiceno"
      this.radioArr[1].isDisable = true;
    }
    this.getProductVal()
    this.byCardForm.controls['prodType'].disable()
  }
  getProductVal() {
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.accountnumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.ERECIEPT_REQUEST_PRODUCT, obj).subscribe
      ((response: any) => {
        if (response.status === 200) {
          let opt = response.body;
          //  console.log(opt)
          if (opt.length > 0) {
            this.productList = opt;
          }
        }
      },
        (e: any) => {
          this.notifyService.showError(e.error.message, "Error");
        });
  }
  daysDifference() {
    if (this.tripData.radioValue === 'invoiceno') {
      if (this.invoiceForm.value.iStartDate) {
        const date = this.lastTwlveMonths.find(o => o.display === this.invoiceForm.value.iStartDate);
        // if (new Date(this.invoiceForm.value.iStartDate) < new Date(this.invoiceForm.value.iEndDate)) {
        // this.showInvoiceDateGreater = false;
        // const oneDay = 24 * 60 * 60 * 1000;
        // const differenceDays = Math.round(Math.abs((Number(this.invoiceForm.value.iStartDate) - Number(this.invoiceForm.value.iEndDate)) / oneDay));
        // if (differenceDays >= 31) {
        //   this.showInvoiceDateError = true;
        //   this.invoiceForm.controls['invoiceNo'].disable()
        // }
        // else {
        //   this.showInvoiceDateError = false;
        // this.invoiceForm.value.invoiceNo = ""
        // this.invoiceForm.get('invoiceNo')?.setValue(" ")
        this.invoiceForm.controls['invoiceNo'].reset()
        let obj
        if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
          obj = {
            "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
            "endDate": this.datePipe.transform(date.eDate, "dd-MMM-yy"),
            "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
            "startDate": this.datePipe.transform(date.sDate, "dd-MMM-yy"),
            "accountNo": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
            "accountNoList": this.accNoList
          }
        }
        else {
          obj = {
            "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
            "endDate": this.datePipe.transform(date.eDate, "dd-MMM-yy"), //date error solution
            "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
            "startDate": this.datePipe.transform(date.sDate, "dd-MMM-yy"),
            "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.accountnumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
          }
        }
        //  console.log('OBJ: ', obj)
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_INVOICE_NO, obj).subscribe
          ((response: any) => {
            if (response.status === 200) {
              let opt = response.body;
              if(!opt.invoiceNoList){
                this.noInvoiceListFound = true;
                return;
              }else{
                this.noInvoiceListFound = false;
              }
              this.invoiceNoList = opt.invoiceNoList;
              this.invoiceForm.controls['invoiceNo'].enable()
            }
          },
            (e: any) => {
              this.notifyService.showError(e.error.message, "Error");
              this.invoiceForm.controls['invoiceNo'].disable()
            });
        // }
        // }
        // else {
        //   this.showInvoiceDateGreater = true;
        // }
      }
      else {
        this.invoiceForm.value.invoiceNo = "";
        this.invoiceForm.controls['invoiceNo'].disable()
      }
    }
    else if (this.tripData.radioValue === 'cardNo') {
      if (this.byCardForm.value.cStartDate && this.byCardForm.value.cEndDate) {
        if (new Date(this.byCardForm.value.cStartDate) <= new Date(this.byCardForm.value.cEndDate)) {
          this.showCardDateGreater = false;
          const oneDay = 24 * 60 * 60 * 1000;
          const differenceDays = Math.round(Math.abs((Number(this.byCardForm.value.cStartDate) - Number(this.byCardForm.value.cEndDate)) / oneDay));
          if (differenceDays >= 31) {
            this.showCardDateError = true;
            this.byCardForm.controls['prodType'].disable()
            this.byCardForm.controls['cardNo'].disable()
          }
          else {
            this.showCardDateError = false;
            this.byCardForm.controls['prodType'].enable()
            this.byCardForm.controls['cardNo'].disable()
            this.getCardList();
          }
        } 
        else {
          this.showCardDateGreater = true;
          this.byCardForm.controls['prodType'].disable()
          this.byCardForm.controls['cardNo'].disable()
        }
      } else {
        this.byCardForm.value.cardNo = "";
        this.byCardForm.controls['cardNo'].disable()
      }
    }
  }

  isProductTypeValid(value : any){
    if(!value){ //checking here "" & null
      return false;
    }else{
      return true;
    }
  }

  getCardList() {
    
    if(!this.showCardDateError && !this.showCardDateGreater && this.byCardForm.controls.cEndDate && this.byCardForm.controls.cStartDate && this.isProductTypeValid(this.byCardForm.controls.prodType.value))
    {
    //  console.log(this.byCardForm.value.cardNo)
    this.displaycard = [];
    // this.byCardForm.get('cardNo')?.setValue(" ");
    const prodObj = this.productList.find((o: any) => o.productId === this.byCardForm.value.prodType)
    if (prodObj && prodObj.oneTimeUsage !== "Y") {
      this.isShowCardNoList = false;
      this.byCardForm.controls['cardNo'].setValidators([Validators.required])
      this.byCardForm.controls['cardNo'].reset()
      this.byCardForm.controls['cardNoTo'].clearValidators()
      this.byCardForm.controls['cardNoFrom'].clearValidators()
    }
    else {
      this.isShowCardNoList = true;
      this.byCardForm.controls['cardNoFrom'].setValidators([Validators.required])
      this.byCardForm.controls['cardNoTo'].setValidators([Validators.required])
      this.byCardForm.controls['cardNoFrom'].reset()
      this.byCardForm.controls['cardNoTo'].reset()
      this.byCardForm.controls['cardNo'].clearValidators()
    }
    //  console.log(this.byCardForm.value.cardNo)

    let obj
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
      obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "endDate": this.datePipe.transform(this.byCardForm.value.cEndDate, "dd-MMM-YY"),
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
        "startDate": this.datePipe.transform(this.byCardForm.value.cStartDate, "dd-MMM-YY"),
        "accountNo": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
        "productTypeId": this.byCardForm.value.prodType,
        "accountNoList": this.accNoList
      }
    }
    else {
      obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "endDate": this.datePipe.transform(this.byCardForm.value.cEndDate, "dd-MMM-YY"),
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
        "startDate": this.datePipe.transform(this.byCardForm.value.cStartDate, "dd-MMM-YY"),
        "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.accountnumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
        "productTypeId": this.byCardForm.value.prodType
      }
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_CARD_NO, obj).subscribe
      ((response: any) => {
        if (response.status === 200) {
          let opt = response.body;
          this.isShowCardNoList = false;
          this.cardNoList = [];
          this.displaycard = [];
          if (prodObj && prodObj.oneTimeUsage === "Y") {
            this.isShowCardNoList = true;
            if(!opt.cardNumberList){
              this.noCardListForFromAndTo = true;
              return;
            }else{
              this.noCardListForFromAndTo = false;;
            }
            opt.cardNumberList.forEach((element: any) => {
              this.cardNoList.push(element)
            });
            // if (this.cardNoList.length > 0) {
            this.byCardForm.controls['cardNoFrom'].enable()
            this.byCardForm.controls['cardNoTo'].enable()
            // }
            // else {
            //   this.byCardForm.controls['cardNoFrom'].disable()
            //   this.byCardForm.controls['cardNoTo'].disable()
            // }
          }
          else {
            if(!opt.cardNumberList){
              this.noCardListForCardNo = true;
              return;
            }else{
              this.noCardListForCardNo = false;;
            }
            this.firstdigit = String(opt.cardNumberList[0]).substr(0, 10);
            this.displaycard.push(this.firstdigit.substr(0, 6));
            this.displaycard.push(this.firstdigit.substr(6, 4));
            opt.cardNumberList.forEach((element: any) => {
              const data = element.split(this.firstdigit)
              this.cardNoList.push(data[1])
            });
            //  console.log(this.cardNoList)
            this.isShowCardNoList = false;
            this.byCardForm.controls['cardNo'].enable()

            // this.cardNoList = opt.cardNumberList;
          }
        }
      },
        (e: any) => {
          this.notifyService.showError(e.error.message, "Error");
          if (prodObj && prodObj.oneTimeUsage === "Y") {
            this.byCardForm.controls['cardNoFrom'].disable()
            this.byCardForm.controls['cardNoTo'].disable()
          }
          else {
            this.byCardForm.controls['cardNo'].disable()
          }
        });
      }
  }
  keyPress(event: any, fieldName?: any) {
    if (this.tripData.radioValue === 'invoiceno') {
      if (this.invoiceForm.value.invoiceNo) {
        this.invoiceArray = this.invoiceNoList.filter((o: any) => {
          if (o.toString().startsWith(this.invoiceForm.value.invoiceNo.toString())) {
            return o;
          }
        });
      } else {
        this.invoiceArray = this.invoiceNoList;
      }
    }
    else if (this.tripData.radioValue === 'cardNo') {
      this.cardArray = [];
      this.cardArrayFrom = [];
      this.cardArrayTo = [];
      const prodObj = this.productList.find((o: any) => o.productId === this.byCardForm.value.prodType)
      if (prodObj && prodObj.oneTimeUsage !== "Y") {
        if (this.byCardForm.value.cardNo && this.byCardForm.value.cardNo !== " ") {
          this.cardArray = this.cardNoList.filter((o: any) => {
            if (o.toString().startsWith(this.byCardForm.value.cardNo.toString())) {
              return o;
            }
          });
        }
        else {
          // if (this.byCardForm.value.prodType !== "OV") {
          this.cardArray = this.cardNoList;
          // }
        }
      }
      else {
        if (fieldName === 'cardNoFrom') {
          if (this.byCardForm.value.cardNoFrom && this.byCardForm.value.cardNoFrom !== " ") {
            this.cardArrayFrom = this.cardNoList.filter((o: any) => {
              if (o.toString().startsWith(this.byCardForm.value.cardNoFrom.toString())) {
                return o;
              }
            });
          }
          //  console.log(this.cardArrayFrom)
        }
        else if (fieldName === 'cardNoTo') {
          if (this.byCardForm.value.cardNoTo && this.byCardForm.value.cardNoTo !== " ") {
            this.cardArrayTo = this.cardNoList.filter((o: any) => {
              if (o.toString().startsWith(this.byCardForm.value.cardNoTo.toString())) {
                return o;
              }
            });
          }
        }
    //  console.log(this.cardArrayFrom,this.cardArrayTo,this.cardNoList)

      }

    }
  }

  resetVal() {
    if (this.tripData.radioValue === 'invoiceno') {
      this.byCardForm.reset();
      this.byCardForm.controls['prodType'].disable();
      this.byCardForm.controls['cardNo'].disable();
    }
    else if (this.tripData.radioValue === 'cardNo') {
      this.invoiceForm.reset();
      this.invoiceForm.controls['invoiceNo'].disable();
    }
  }
  download() {
    let obj = {};
    let getList: any[] = []
    let getListFrom: any[] = []
    let getListTo: any[] = []
    const prodObj = this.productList.find((o: any) => o.productId === this.byCardForm.value.prodType)
    //  console.log(this.cardArrayFrom,this.cardArrayTo,this.cardNoList)
    if (this.tripData.radioValue === 'cardNo') {
      if (this.byCardForm.value.cardNo === " ") {
        this.byCardForm.get('cardNo')?.setValue("");
      }
    }
    if (this.tripData.radioValue === 'invoiceno' && this.invoiceForm.valid) {
      const date = this.lastTwlveMonths.find(o => o.display === this.invoiceForm.value.iStartDate);
      //  console.log(this.invoiceForm.value.iStartDate,date)

      // const month = this.months.findIndex(o => o === date[0])
      // const firstDayOfMonth = new Date(Number(date[1]), month, 1)
      // const lastDayOfMonth = new Date(Number(date[1]), month + 1, 0)
      obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "endDate": this.datePipe.transform(date.eDate, "dd-MMM-YY"),
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "startDate": this.datePipe.transform(date.sDate, "dd-MMM-YY"),
        "downloadBy": "I",
        "invoiceNo": this.invoiceForm.value.invoiceNo,
        "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.accountnumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
        "accountNoList": this.accNoList
      }
      getList = this.invoiceArray.filter((o: any) => o === this.invoiceForm.value.invoiceNo)
    }
    else if (this.tripData.radioValue === 'cardNo' && this.byCardForm.valid) {
      obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "endDate": this.datePipe.transform(this.byCardForm.value.cEndDate, "dd-MMM-YY"),
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "startDate": this.datePipe.transform(this.byCardForm.value.cStartDate, "dd-MMM-YY"),
        "downloadBy": "C",
        "cardNumber": (prodObj && prodObj.oneTimeUsage !== "Y") ? this.firstdigit + this.byCardForm.value.cardNo : "",
        "cardNumberFrom": (prodObj && prodObj.oneTimeUsage === "Y") ? this.byCardForm.value.cardNoFrom : "",
        "cardNumberTo": (prodObj && prodObj.oneTimeUsage === "Y") ? this.byCardForm.value.cardNoTo : "",
        "productTypeId": this.byCardForm.value.prodType,
        "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.accountnumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
        "accountNoList": this.accNoList
      }
      if (prodObj && prodObj.oneTimeUsage === "Y") {
        // //  console.log(this.byCardForm.value.cardNoFrom,this.cardArrayFrom)
        getListFrom = this.cardNoList.filter((o: any) => o === this.byCardForm.value.cardNoFrom)
        getListTo = this.cardNoList.filter((o: any) => o === this.byCardForm.value.cardNoTo)
      }
      else {
        getList = this.cardArray.filter((o: any) => o === this.byCardForm.value.cardNo)

      }
    }
    //  console.log(getList,getListFrom,getListTo)
    if (getList.length > 0) {
      this.downloadAPICall(obj)
    }
    else if((getListFrom.length > 0 && getListTo.length > 0)){
      this.showCardFromGreater=false;
      if(Number(this.byCardForm.value.cardNoFrom)>Number(this.byCardForm.value.cardNoTo)){
        this.showCardFromGreater=true;
      }
      else{
        this.showCardFromGreater=false;
        this.downloadAPICall(obj)
      }
    }
    else {
      if (this.tripData.radioValue === 'invoiceno') {
        this.notifyService.showWarning('Enter valid Invoice No', "Warning")
      }
      else if (this.tripData.radioValue === 'cardNo') {
        if (prodObj && prodObj.oneTimeUsage === "Y") {
          this.notifyService.showWarning('Enter valid Card No From and Card No To', "Warning")
        }
        else {
          this.notifyService.showWarning('Enter valid Card No', "Warning")
        }
      }

    }
  }
  downloadAPICall(obj: any) {
    this.canShowProcessingBtn = true;
    let mediaType = "application/pdf"
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.DOWNLOAD_ERECEIPT, obj, { responseType: 'blob' }).subscribe(response => {
      //  console.log(response)
      this.canShowProcessingBtn = false;
      let blob = new Blob([response], { type: mediaType });
      if (this.tripData.radioValue === 'invoiceno') {
        saveAs(blob, 'Ereceipt_' + this.datePipe.transform(this.invoiceForm.value.iStartDate, "dd-MMM-YY") + '_' + this.datePipe.transform(this.invoiceForm.value.iEndDate, "dd-MMM-YY"))
      }
      else if (this.tripData.radioValue === 'cardNo') {
        saveAs(blob, 'Ereceipt_' + this.datePipe.transform(this.byCardForm.value.cStartDate, "dd-MMM-YY") + '_' + this.datePipe.transform(this.byCardForm.value.cEndDate, "dd-MMM-YY"))
      }
    },
      async e => {
        this.canShowProcessingBtn = false;
        const d = await (new Response(e.error)).json();
        //  console.log(d)
        await this.notifyService.showError(d.errorMessage, "Error")
      })
  }
}
