<div fxLayout="column" fxLayoutGap="50px">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">receipt</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">E-Receipt Request</span>
        </div>
    </div>
    <!-- <form #f="ngForm" name="form"> -->
    <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
        <div>
            <span class="sub-header pb5">DOWNLOAD BY<span class="asterisk"><sup>*</sup></span>:</span>
        </div>
        <div>
            <mat-radio-group name="radioValue" [(ngModel)]="tripData.radioValue" fxLayoutGap="30px"
                (ngModelChange)="resetVal()">
                <mat-radio-button *ngFor="let val of radioArr" [value]="val.value" [disabled]="val.isDisable"
                    labelPosition="before" color="primary">
                    {{val.viewValue}}
                </mat-radio-button>
            </mat-radio-group>
            <!-- <mat-error *ngIf="f.submitted && radioValue.invalid" class="pt15">
                    <div *ngIf="radioValue.hasError('required')">
                        Download by is required
                    </div>
                </mat-error> -->
        </div>
        <div *ngIf="tripData.radioValue == 'invoiceno'">
            <form [formGroup]="invoiceForm">
                <div fxLayout="column" fxLayoutGap="20px">
                    <div fxLayout="column">
                        <!-- <div fxLayout="row" fxLayout.lt-md="column" fxLayoutGap="80px" fxLayoutGap.lt-md="20px"> -->
                        <div fxLayout="row" fxLayoutGap="85px">
                            <span class="date-title pt10">MONTH<span class="asterisk"><sup>*</sup></span>:</span>
                            <mat-form-field appearance="outline" class="inp">
                                <mat-select name="invoiceStartDate" formControlName="iStartDate"
                                    placeholder="Select Month" (selectionChange)="daysDifference()" required>
                                    <mat-option *ngFor="let month of lastTwlveMonths" [value]="month.display">
                                        {{month.display}}
                                    </mat-option>
                                </mat-select>
                                <mat-error *ngIf="invoiceForm.controls.iStartDate.hasError('required')" class="pt15">
                                    Month Is Required
                                </mat-error>
                            </mat-form-field>
                        </div>


                        <!-- <div fxLayout="row" fxLayoutGap="60px" fxLayoutGap.lt-md="105px">
                                <span class="date-title pt10">TO<span class="asterisk"><sup>*</sup></span>:</span>
                                <mat-form-field appearance="outline">
                                    <mat-select name="invoiceEndDate" formControlName="iEndDate"
                                        placeholder="Select End Date" required>
                                        <mat-option *ngFor="let month of lastTwlveMonths" [value]="month">
                                            {{month}}
                                        </mat-option>
                                    </mat-select>
                                    <mat-error *ngIf="invoiceForm.controls.iEndDate.hasError('required')" class="pt15">
                                        To Date Is Required
                                    </mat-error>
                                </mat-form-field>
                            </div> -->


                        <!-- </div> -->
                        <!-- <div *ngIf="showInvoiceDateError" class="error pl">
                            Date Range Must Not Be More Than 31 Days
                        </div>
                        <div *ngIf="showInvoiceDateGreater" class="error pl">
                            End Date should be greater than start Date
                        </div> -->
                    </div>
                    <div fxLayout="column" fxLayoutGap="15px">
                        <div fxLayout="row" fxLayoutGap="52px">
                            <span class="date-title pt10">INVOICE NO.<span class="asterisk"><sup>*</sup></span>:</span>
                            <mat-form-field appearance="outline" class="inp" *ngIf="!noInvoiceListFound">
                                <input matInput type="text" name="invoiceno" formControlName="invoiceNo"
                                    onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                    [matAutocomplete]="auto" (input)="keyPress($event)" (click)="keyPress($event)"
                                    required>
                                <mat-autocomplete #auto="matAutocomplete">
                                    <mat-option *ngFor="let invoice of invoiceArray" [value]="invoice">
                                        {{invoice}}
                                    </mat-option>
                                </mat-autocomplete>
                                <mat-error *ngIf="invoiceForm.controls.invoiceNo.hasError('required')" class="pt15">
                                    Invoice No is required
                                </mat-error>
                            </mat-form-field>
                            <mat-error *ngIf="noInvoiceListFound" class="pt15">
                                No Invoice Details Found
                            </mat-error>
                        </div>
                    </div>


                    <div fxFlex>
                        <button mat-raised-button class="download-btn" *ngIf="!canShowProcessingBtn" (click)="download()">DOWNLOAD</button>
                        <button mat-raised-button class="download-btn" *ngIf="canShowProcessingBtn">Processing...</button>
                    </div>
                </div>
            </form>
        </div>
        <ng-container *ngIf="tripData.radioValue == 'cardNo'">
            <form [formGroup]="byCardForm">
                <div fxLayout="column" fxLayoutGap="20px">
                    <div fxLayout="column">
                        <div fxLayout="row" fxLayout.lt-md="column" fxLayoutGap="80px" fxLayoutGap.lt-md="20px">
                            <div fxLayout="row" fxLayoutGap="60px">
                                <span class="date-title pt10">START DATE<span
                                        class="asterisk"><sup>*</sup></span>:</span>
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput [matDatepicker]="picker1" name="cardStartDate"
                                        formControlName="cStartDate" (dateChange)="daysDifference()"
                                        onkeydown="return false" required>
                                    <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                                    <mat-datepicker #picker1></mat-datepicker>
                                    <mat-error *ngIf="byCardForm.controls.cStartDate.hasError('required')" class="pt15">
                                        Start Date Is Required
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <div fxLayout="row" fxLayoutGap="60px" fxLayoutGap.lt-md="105px" fxLayoutGap.lt-md="120px">
                                <span class="date-title pt10">TO<span class="asterisk"><sup>*</sup></span>:</span>
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput [matDatepicker]="picker2" name="cardEndDate"
                                        formControlName="cEndDate" (dateChange)="daysDifference()"
                                        onkeydown="return false" required>
                                    <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                                    <mat-datepicker #picker2></mat-datepicker>
                                    <mat-error *ngIf="byCardForm.controls.cEndDate.hasError('required')" class="pt15">
                                        To Date Is Required
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="150px" fxLayoutGap.md="150px" fxLayoutGap.sm="150px" fxLayoutGap.xs="0px">
                                <div *ngIf="showCardDateError" class="error" style="padding-left: 15%;">
                                    Date Range Must Not Be More Than 31 Days
                                </div>
                                <div *ngIf="showCardDateGreater" class="error" style="padding-left: 15%;">
                                    End Date should be greater than or equal to startDate
                                </div>
                        </div>
                    </div>
                    <div fxLayout="column" fxLayoutGap="15px">
                        <div fxLayout="row" fxLayoutGap="38px">
                            <span class="date-title pt10">PRODUCT TYPE<span class="asterisk"><sup>*</sup></span>:</span>
                            <div fxLayout="row" fxLayoutAlign="end center">
                                <mat-form-field appearance="outline" class="inp">
                                    <mat-select name="prodType" formControlName="prodType"
                                        placeholder="--Select Product Type--" (selectionChange)="getCardList()"
                                        required>
                                        <mat-option *ngFor="let prod of productList" [value]="prod.productId">
                                            {{prod.productName}}
                                        </mat-option>
                                    </mat-select>
                                    <mat-error *ngIf="byCardForm.controls.prodType.hasError('required')" class="pt15">
                                        Please Select Product type
                                    </mat-error>
                                </mat-form-field>
                            </div>

                        </div>
                    </div>
                    <div fxLayout="column" fxLayoutGap="15px" *ngIf="!isShowCardNoList">
                        <div fxLayout="row" fxLayoutGap="75px">
                            <span class="date-title pt10">CARD NO.<span class="asterisk"><sup>*</sup></span>:</span>
                            <div fxLayout="row" fxLayoutAlign="end center" *ngIf="!noCardListForCardNo">
                                <span class="pb10"
                                    *ngIf="this.displaycard.length > 0">{{this.displaycard.join("-")}}</span>
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput type="text" name="cardNo" formControlName="cardNo"
                                        onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                        (input)="keyPress($event,'cardNo')" (click)="keyPress($event,'cardNo')" [matAutocomplete]="auto"
                                        required />
                                    <mat-autocomplete #auto="matAutocomplete">
                                        <mat-option *ngFor="let card of cardArray" [value]="card">
                                            {{card}}
                                        </mat-option>
                                    </mat-autocomplete>
                                    <mat-error *ngIf="byCardForm.controls.cardNo.hasError('required')" class="pt15">
                                        Card No is required
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <mat-error *ngIf="noCardListForCardNo" class="pt15">
                                No Card Details Found
                            </mat-error>
                        </div>
                    </div>
                    <div fxLayout="column" fxLayoutGap="15px" *ngIf="isShowCardNoList">
                        <div fxLayout="row" fxLayoutGap="35px">
                            <span class="date-title pt10">CARD NO. FROM<span class="asterisk"><sup>*</sup></span>:</span>
                            <div fxLayout="row" fxLayoutAlign="end center" *ngIf="!noCardListForFromAndTo">
                                <!-- <span class="pb10"
                                    *ngIf="this.displaycard.length > 0 && !isShowCardNoList">{{this.displaycard.join("-")}}</span> -->
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput type="text" name="cardNoFrom" formControlName="cardNoFrom"
                                        onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                        (input)="keyPress($event,'cardNoFrom')" (click)="keyPress($event,'cardNoFrom')" [matAutocomplete]="auto"
                                        required />
                                    <mat-autocomplete #auto="matAutocomplete">
                                        <mat-option *ngFor="let card of cardArrayFrom" [value]="card">
                                            {{card}}
                                        </mat-option>
                                    </mat-autocomplete>
                                    <mat-error *ngIf="byCardForm.controls.cardNoFrom.hasError('required')" class="pt15">
                                        Card No From is required
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <mat-error *ngIf="noCardListForFromAndTo" class="pt15">
                                No Card Details Found
                            </mat-error>
                        </div>
                    </div>
                    <div fxLayout="column"  *ngIf="isShowCardNoList">
                        <div fxLayout="row" fxLayoutGap="55px">
                            <span class="date-title pt10">CARD NO. TO<span class="asterisk"><sup>*</sup></span>:</span>
                            <div fxLayout="row" fxLayoutAlign="end center" *ngIf="!noCardListForFromAndTo">
                                <!-- <span class="pb10"
                                    *ngIf="this.displaycard.length > 0 && !isShowCardNoList">{{this.displaycard.join("-")}}</span> -->
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput type="text" name="cardNoTo" formControlName="cardNoTo"
                                        onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                        (input)="keyPress($event,'cardNoTo')" (click)="keyPress($event,'cardNoTo')" [matAutocomplete]="auto"
                                        required />
                                    <mat-autocomplete #auto="matAutocomplete">
                                        <mat-option *ngFor="let card of cardArrayTo" [value]="card">
                                            {{card}}
                                        </mat-option>
                                    </mat-autocomplete>
                                    <mat-error *ngIf="byCardForm.controls.cardNoTo.hasError('required')" class="pt15">
                                        Card No To is required
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <mat-error *ngIf="noCardListForFromAndTo" class="pt15">
                                No Card Details Found
                            </mat-error>
                        </div>
                        <div fxFlex fxLayout="row" fxLayout.xs="column">
                            <div fxFlex="14%" fxFlex.md="25%" fxFlex.sm="22%"></div>
                            <div fxFlex="86%" fxFlex.md="75%" fxFlex.sm="78%">
                                <div *ngIf="showCardFromGreater" class="error">
                                    Card No To should be greater than Card No From
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- <div fxLayout="column" fxLayoutGap="15px" *ngIf="isShowCardNoList">
                        <div fxLayout="row" fxLayoutGap="70px">
                            <span class="date-title pt10">Card No.<span class="asterisk"><sup>*</sup></span>:</span>
                            <div fxLayout="row" fxLayoutAlign="end center">
                                <mat-form-field appearance="outline" class="inp">
                                    <mat-select name="cardNo" formControlName="cardNo" placeholder="--Select Product Type--" required>
                                        <mat-option *ngFor="let card of cardNoList" [value]="card">
                                            {{card}}
                                        </mat-option>
                                    </mat-select>
                                    <mat-error
                                        *ngIf="byCardForm.controls.cardNo.hasError('required')" class="pt15">
                                        Card No is required
                                    </mat-error>
                                </mat-form-field>
                            </div>

                        </div>
                    </div> -->

                    <div fxFlex>
                        <button mat-raised-button class="download-btn" *ngIf="!canShowProcessingBtn" (click)="download()">DOWNLOAD</button>
                        <button mat-raised-button class="download-btn" *ngIf="canShowProcessingBtn">Processing...</button>
                    </div>
                </div>
            </form>
        </ng-container>


    </div>
    <!-- </form> -->
</div>