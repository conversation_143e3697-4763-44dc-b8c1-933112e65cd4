@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
.mat-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
    height: 40px !important;
    width: 40px !important;
}
.sub-header{
    font-size: 19px !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version.*/
// ::ng-de
// .inp{
//     width: 178px !important;
// }
.inp{
    width: 320px !important;
}
.download-btn{
    width:191px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
}
.error{
    font-size: $cdgsz-font-size-sm !important;
    color: $cdgc-font-warn !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
}
.pl{
    padding-left: 110px !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
//  :host ::ng-deep .mat-form-field-appearance-outline .mdc-text-field--disabled .mat-mdc-form-field-flex {
//     // background-color:pink !important;
//     background-color: #f5eeee !important;
//     border: transparent !important;
//     border-radius: 10px !important;
// }
@media screen and (max-width: 600px) {
    .inp{
        width: 156px !important;
    }
}