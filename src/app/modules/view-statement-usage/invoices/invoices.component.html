<div fxFlex fxLayout="column" class="full-height">
  <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="start start">
    <div fxFlex fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="start center" fxFlex.xs=61% fxFlex.md=61%>
      <div class="cursor-pointer">
        <!-- <mat-icon>arrow_back</mat-icon> -->
        <img src="\assets\images\view usage report.png">
      </div>
      <div>
        <div class="header pb20">View / Download Invoices</div>
        <!-- class="title" -->
      </div>
    </div>
  </div><br>
  <!-- 
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
      <div fxFlex="4%" >
        <img src="\assets\images\view usage report.png">
      </div>
      <div fxFlex="96%" fxLayoutAlign="start center">
          <span class="header pb20">View / Download Invoices</span>
      </div>
  </div> -->


  <div fxLayout="row" fxLayoutAlign="center center" fxLayoutAlign.sm="start center" class="bckgrnd">

    <div fxFlex class="mt25" class="arrow-color">
      <mat-form-field appearance="fill" class="inp">
        <mat-select [(ngModel)]="this.selectedMonth" (ngModelChange)="getInvoiceData()">
          <mat-option *ngFor="let month of lastSevenMonths" [value]="month">
            {{month}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>

  <div fxLayout="row" fxFlex=5% fxFlex.sm=10% fxFlex.md=10% class="mb13">
    <p class="table-sub-title">Outstanding Invoices</p>
  </div>

  <div class="tbl-container mat-elevation-z8">
    <mat-table #table class="mat-table mat-cdk-table" [dataSource]="dataSource" matSort
      (matSortChange)="sortData($event)">
      <ng-container matColumnDef="invoiceNo">
        <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Invoice No</mat-header-cell>
        <mat-cell *matCellDef="let element">
          <span class="mobile-label"> Invoice No:</span>
          <!-- <span class="fwb font-16">  -->
          {{element.invoiceNo}}
          <!-- </span> -->
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="invoiceDate">
        <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Invoice Date</mat-header-cell>
        <mat-cell *matCellDef="let element">
          <span class="mobile-label"> Invoice Date:</span>
          <!-- <span class="fwb font-16">  -->
          {{element.invoiceDate | date:'dd/MM/yy'}}
          <!-- </span> -->
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="invoiceTo">
        <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Invoice To</mat-header-cell>
        <mat-cell *matCellDef="let element">
          <span class="mobile-label"> Invoice To:</span>
          <!-- <span class="fwb font-16">  -->
          <div class="word-break w-80">
            {{element.invoiceTo}}
          </div>
          <!-- </span> -->
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="invoiceAmt">
        <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Invoice Amount(S$)</mat-header-cell>
        <mat-cell *matCellDef="let element">
          <span class="mobile-label"> Invoice Amount(S$):</span>
          <span class="p30">
            {{element.invoiceAmount | currency:'':''}}
          </span>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="action">
        <mat-header-cell mat-header-cell *matHeaderCellDef> </mat-header-cell>
        <mat-cell *matCellDef="let element">
          <mat-icon class="actn_icon" (click)="downloadPdf(element)">get_app</mat-icon>
        </mat-cell>
      </ng-container>
      <mat-header-row *matHeaderRowDef="invoiceDisplayedColumns"></mat-header-row>
      <mat-row *matRowDef="let row; columns: invoiceDisplayedColumns;"></mat-row>
    </mat-table>
    <tr class="mat-row" *ngIf="outStandingInvoices.length == 0" fxLayoutAlign="center center">
      <td class="mat-cell" colspan="5">No Data Found</td>
    </tr>
    <mat-paginator [pageSize]="5" [pageSizeOptions]="[5, 10, 20, 50, 100]" showFirstLastButtons></mat-paginator>
  </div>



</div>