import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { ExportxlsxService } from 'src/app/shared/services/exportxlsx.service';
import { Sort } from '@angular/material/sort';
import { HttpClient } from '@angular/common/http';
import { throwError } from 'rxjs';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants'
import { DatePipe } from '@angular/common';
import * as moment from 'moment';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { saveAs } from 'file-saver'
import { Moment } from 'moment';

declare var require: any
const FileSaver = require('file-saver')

export const MY_FORMATS = {
  parse: {
    dateInput: 'MMM YYYY'
  },
  display: {
    dateInput: 'MMMM YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMM YYYY'
  }
}
@Component({
  selector: 'app-invoices',
  templateUrl: './invoices.component.html',
  styleUrls: ['./invoices.component.scss'],
  providers: [DatePipe]
})
export class InvoicesComponent implements OnInit {
  selectable = true;
  canLoadInvoiceByPrevoiusMonthData = false;
  outStandingInvoices: any[] = [];
  // dataSource: any;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  dataSource = new MatTableDataSource<any>(this.outStandingInvoices);
  // private invoicePaginator !: MatPaginator;
  accountnumber: any;
  // @ViewChild(MatPaginator) set matPaginator(mp: MatPaginator) {
    // this.invoicePaginator = mp;
    // this.setInvoiceAttributes();
  // }
  @ViewChild(MatSort) sort!: MatSort;
  public invoiceDisplayedColumns = ['invoiceNo', 'invoiceDate', 'invoiceTo', 'invoiceAmt', 'action'];
  public months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
  selectedMonth: any;
  lastSevenMonths: any = [];
  constructor(public localStorage: LocalStorageService,private http: HttpClient, private apiService: ApiServiceService, private datePipe: DatePipe, private notifyService: NotificationService, private cdgService: CdgSharedService) { }

  ngOnInit(): void {
    if(this.localStorage.localStorageGet("customerNoSelected")!=null){
      this.accountnumber=this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if(this.accountnumber=this.localStorage.localStorageGet("custNo")!=null){
      this.accountnumber=this.localStorage.localStorageGet("custNo").view;
    }
    else {
    if(this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0]){
      this.accountnumber= this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }
    }
  }
    let d;
    const today = new Date();
    for (var i = 0; i < 6; i += 1) {
      d = new Date(today.getFullYear(), today.getMonth() - i, 1);
      this.lastSevenMonths.push(this.months[d.getMonth()] + ' ' + d.getFullYear());
    }
    this.selectedMonth = this.lastSevenMonths[0]
    this.getInvoiceValue(this.selectedMonth)
  }
  sortData(sort: Sort) {
    const data = this.outStandingInvoices.slice();
    if (!sort.active || sort.direction === '') {
      this.dataSource.data = data;
      return;
    }

    this.dataSource.data = data.sort((x, y) => {
      const isAsc = sort.direction === 'asc';
      switch (sort.active) {
        case 'invoiceNo': return this.compare(x.invoiceNo, y.invoiceNo, isAsc);
        case 'invoiceDate': return this.compare(new Date(x.invoiceDate), new Date(y.invoiceDate), isAsc);
        case 'invoiceTo': return this.compare(x.invoiceTo, y.invoiceTo, isAsc);
        case 'invoiceAmt': return this.compare(Number(x.invoiceAmount), Number(y.invoiceAmount), isAsc);
        default: return 0;
      }
    });
  }
  compare(a: Date | string|number, b: Date | string|number, isAsc: boolean) {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1)
  }
  getInvoiceValue(selectedDate: any) {
    const date = selectedDate.split(" ");
    const month = this.months.findIndex(o => o === date[0])
    const firstDayOfMonth = new Date(Number(date[1]), month, 1)
    const lastDayOfMonth = new Date(Number(date[1]), month + 1, 0)
    let accNoConversion  = Number(this.accountnumber);
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accountNo":this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.accountnumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
      "startDate": this.datePipe.transform(firstDayOfMonth, "dd-MMM-yy"),   // date error solution
      "endDate": this.datePipe.transform(lastDayOfMonth, "dd-MMM-yy"),
      "masterAccountNo":accNoConversion
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_INVOICE_DETAILS, obj).subscribe((response: any) => {
      let opt = response.body;
      if (response.status === 200) {
        if(opt.length == 0 && !this.canLoadInvoiceByPrevoiusMonthData){
          this.canLoadInvoiceByPrevoiusMonthData = true;
          this.selectedMonth = this.lastSevenMonths[1];
          // //  console.log("SELEcted:", this.selectedMonth)
          this.getInvoiceValue(this.selectedMonth); 
          // //  console.log("MONth: ",  this.getInvoiceValue)
        }
        else{
          this.canLoadInvoiceByPrevoiusMonthData = true;
          this.outStandingInvoices = opt;
          this.dataSource = new MatTableDataSource<any>(this.outStandingInvoices);
          // //  console.log(this.outStandingInvoices,'datasource paginator')
          this.dataSource.paginator = this.paginator;
          //  console.log(this.paginator,'paginator')
          // //  console.log(this.dataSource.paginator,'datasource paginator')
        }
    }
    },
      e => {
        // throwError(e)
        this.notifyService.showError(e.error.message, "Error")
        this.canLoadInvoiceByPrevoiusMonthData = false;
      })
  }
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }
  getInvoiceData() {
  this.getInvoiceValue(this.selectedMonth)
  // //  console.log('Selected_MOnth: ', this.selectedMonth)
  }
  downloadPdf(data: any) {
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accountNo":this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.accountnumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
      "invoiceDate": this.datePipe.transform(data.invoiceDate, 'dd-MMM-YY'),
      "invoiceNo": data.invoiceNo
    }
    let mediaType = "application/pdf"
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2  + UrlConstants.PDF_DOWNLOAD, obj, { responseType: 'blob' }).subscribe(response => {
      let blob = new Blob([response], { type: mediaType });
      saveAs(blob, 'Invoice_' + data.invoiceNo + '_' + this.datePipe.transform(data.invoiceDate, 'MMM-YY'))
    },
      e => {
        this.notifyService.showError(e.error.message, "Error")
      })
  }
}

