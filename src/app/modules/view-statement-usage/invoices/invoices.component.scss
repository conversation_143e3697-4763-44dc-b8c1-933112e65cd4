@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';

.title {
    font-family: $cdgsz-font-family;
    font-style: normal;
    font-weight: bold;
    font-size: $cdgsz-font-size-lg;
    line-height: 23px;
}

.header {
    color: $lightGrey !important;
    font-size: 28px;
    font-weight: $cdgsz-font-weight-normal ;
}

// .mat-sort-header-pointer-middle {
//     background: white;
// }
.mat-icon {
    color: $cdgc-font-prime;
}

.mb13 {
    margin-bottom: 13px;
}

// ::ng-deep .mat-select-value {
//     color: black !important;
// }
.table-sub-title {
    font-family: $cdgsz-font-family;
    font-style: normal;
    font-weight: bold;
    font-size: 18px;
    line-height: 21px;
    color: #303030;
}

.font-12 {
    font-size: $cdgsz-font-size-xs;
}

.font-16 {
    font-size: $cdgsz-font-size-sm;
}

.full-height {
    height: 100vh
}

.actn_icon {
    font-size: 20px;
    color: $prime;
    cursor: pointer;
}

table {
    width: 100%;
}

.mat-mdc-header-cell {
    background-color: #006BA8;
    color: $white;
}

.mat-mdc-row {
    color: black;
}

.mat-mdc-cell {
    font-weight: normal;
    font-size: 14px;
    line-height: 16px;
}


.mt25 {
    margin-top: 25px;
}


.fwb {
    font-weight: $cdgsz-font-weight-bold;
}

// ::ng-deep .mat-sort-header-arrow {
//     color: $cdgc-font-accent !important;
// }
.tbl-container {
    display: flex;
    flex-direction: column;
    max-height: 500px;
}

.mat-mdc-header-row {
    border-radius: 12px;
    background-color: $cdgc-bg-prime !important;
    color: $cdgc-font-accent !important;
}

.mat-mdc-header-cell {
    color: $cdgc-font-accent !important;

}

.mat-mdc-row {
    border-radius: 12px;
    color: $lightGrey !important;

}

.mobile-label {
    display: none;
}

.p30 {
    padding-left: 30px !important;
}

@media screen and (max-width: 600px) {
    .mobile-label {
        display: inline-block;
        width: 165px;
        font-weight: $cdgsz-font-weight-bold;
    }

    .p30 {
        padding-left: 0px !important;
    }

    .mat-mdc-header-row {
        display: none;
    }

    .mat-mdc-row {
        flex-direction: column;
        align-items: start;
        padding: 8px 24px;
        border-radius: 12px !important;
        border: 1px solid $cdgc-border-prime;
        min-height: 28px !important;
    }

    ::ng-deep mat-cell:first-of-type,
    mat-header-cell:first-of-type,
    mat-footer-cell:first-of-type {
        padding-left: 0px !important;
    }
}

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .mat.mdc-form-field.mat-mdc-form-field-text-infix {
    /* padding: 0.25em 0 0.75em 0; */
    bottom: 16px;
    padding-bottom: 0px;


}

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-form-field .mat-mdc-form-field-flex {
    background-color: #006BA8 !important;
    color: white !important;
    border-radius: 30px !important;
    width: 100%;
}

// :host ::ng-deep .mat-form-field-flex{
//     border-radius: 30px !important;
// }

.arrow-color { //change by navani

    /* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
    ::ng-deep .mat-mdc-select-arrow {
        margin-top: 0px !important;
        color: white !important;
        // text-align: center  !important; //change by navani 
    }

    /* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
     ::ng-deep .mat-mdc-select-value {
        margin-top: 0px !important; //month_dropdown_Arrow
        color: white !important;
        text-align: center  !important;
    }
}

// :host::ng-deep .mat-select-value {
//     color: white !important; 
// }
//  :host::ng-deep .mat-select-arrow {
//     margin-top: 13px !important;
//     color: white !important; 
// }
//commented by neevika as dropdown in paginator is coming as white color

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
// ::ng-deep inp.mat-mdc-form-field-underline {
//     width: 0 !important;
// }

.inp {
    width: 150px !important;
}

.align {
    text-align: center !important;
}


.w-80 {
    width: 80% !important;
}
//month_dropdown
// ::ng-deep .mat-mdc-text-field-wrapper {
//     padding-left: 0 !important;
//     padding-right: 0 !important;
//     display: contents !important;
// }


::ng-deep .mat-mdc-text-field-wrapper {
    padding-left: 0 !important;
    padding-right: 0 !important;
    background-color: #F5F5F5 !important;
  }
  
  ::ng-deep  .mat-mdc-select-arrow-wrapper {
    transform: translateY(-25%);
    display: table-cell;
    vertical-align: middle;
  }
  
  ::ng-deep  .mat-mdc-select-arrow {
    height: 1px !important;
    width: 1px !important;
    margin-right: 15px !important;
  }
  
  ::ng-deep .mat-mdc-select-arrow svg {
    display: none !important;
  }
  ::ng-deep .mat-mdc-paginator .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix {
    padding-top: 5px !important;
    padding-bottom: 5px !important;
    background-color: white !important;
}

:host ::ng-deep .mat-mdc-form-field .mdc-line-ripple { //changed by navani, for month dropdown bottom line
    display: none !important
}

:host ::ng-deep .mat-mdc-paginator .mat-mdc-select{
    line-height: 1.5 !important;
    width: 75px !important;
}

:host ::ng-deep .mat-mdc-paginator .mat-mdc-select-arrow {
    margin-top: 10px !important;
}