<div fxLayout="column" fxLayoutGap="50px">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">card_travel</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">View Card Status / Card Usage</span>
        </div>
    </div>
    <form #f="ngForm" name="form">
        <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="margin-bottom: 0px;">
                <div fxFlex="15%" fxFlex.lt-md="25%" fxLayoutAlign="start center" class="head-align">
                    <span class="date-title">PRODUCT TYPE<span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxFlex="90%" fxFlex.lt-md="75%">
                    <!-- <mat-form-field appearance="outline" class="inp">
                        <mat-select [(ngModel)]="viewCard.prodSelect" name="prodSelect"
                            placeholder="Select Product Type" (ngModelChange)="getCardDetails(viewCard.prodSelect)"
                            [disabled]="isProductDisable" #prodSelect="ngModel" required>
                            <mat-option *ngFor="let acl of productArr" [value]="acl.value">
                                {{acl.viewValue}}
                            </mat-option>
                        </mat-select>
                        <mat-error *ngIf="f.submitted && prodSelect.invalid" class="pt15">
                            <div *ngIf="prodSelect.hasError('required')">
                                Product Type Is Required
                            </div>
                        </mat-error>
                    </mat-form-field> -->
                    <mat-form-field appearance="outline" class="inp">
                        <mat-select [(ngModel)]="viewCard.prodSelect" name="prodSelect"
                            placeholder="Select Product Type" (ngModelChange)="getCardDetails(viewCard.prodSelect)"
                            #prodSelect="ngModel" required>
                            <!-- [disabled]="isProductDisable" -->
                            <mat-option *ngFor="let acl of productArr" [value]="acl.productTypeId">
                                {{acl.name}}
                                <!-- {{acl.name}} -->
                            </mat-option>
                        </mat-select>

                        <mat-error *ngIf="f.submitted && prodSelect.invalid" class="pt15">
                            <div *ngIf="this.submitted && prodSelect.hasError('required')">
                                Product Type Is Required
                            </div>
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="isCardNoShow">
                <div fxFlex="15%" fxFlex.lt-md="25%" fxLayoutAlign="start center" class="head-align">
                    <span class="date-title">CARD NO<span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxFlex="90%" fxFlex.lt-md="75%" fxLayout="row" fxLayoutAlign="start center">
                    <span class="pb10" *ngIf="!openshow && !showNoCard">{{this.displaycard}} </span>
                    <mat-error *ngIf="showNoCard">
                        No Card Details Found
                    </mat-error>

                    <mat-form-field appearance="outline" class="inp" *ngIf="!showNoCard">
                        <input matInput type="text" pattern="[0-9]*" [(ngModel)]="viewCard.cardSelect" name="cardSelect"
                            #cardSelect="ngModel" required />
                        <mat-error *ngIf="f.submitted && cardSelect.invalid" class="pt15">
                            <div *ngIf="this.submitted && cardSelect.hasError('required')">
                                Card No. Is Required
                            </div>
                        </mat-error>
                        <mat-error *ngIf="cardSelect.hasError('pattern')" class="pt15">
                            Please enter a number
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
            <div fxLayout="column" fxLayoutGap="20px" *ngIf="isCardStartEndShow">
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="25%" fxLayoutAlign="start center"  class="head-align">
                        <span class="date-title">CARD NO. FROM <span class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="90%" fxFlex.lt-md="75%" fxLayout="row" fxLayoutAlign="start center">
                        <span class="pb10" *ngIf="!openshow">{{this.displaycard}} </span>
                        <mat-error *ngIf="showNoCard">
                            No Card Details Found
                        </mat-error>

                        <mat-form-field appearance="outline" class="inp" *ngIf="!showNoCard">
                            <input matInput type="text" pattern="[0-9]*" [(ngModel)]="viewCard.cardFrom" name="cardFrom"
                                #cardFrom="ngModel" required />
                            <mat-error *ngIf="f.submitted && cardFrom.invalid" class="pt15">
                                <div *ngIf="this.submitted && cardFrom.hasError('required')">
                                    Card No. From Is Required
                                </div>
                            </mat-error>
                            <mat-error *ngIf="cardFrom.hasError('pattern')" class="pt15">
                                Please enter a number
                            </mat-error>
                        </mat-form-field>

                    </div>
                </div>
               
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="25%" fxLayoutAlign="start center"  class="head-align">
                        <span class="date-title">CARD NO. TO <span class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="90%" fxFlex.lt-md="75%" fxLayout="row" fxLayoutAlign="start center">
                        <span class="pb10" *ngIf="!openshow">{{this.displaycard}} </span>
                        <mat-error *ngIf="showNoCard">
                            No Card Details Found
                        </mat-error>

                        <mat-form-field appearance="outline" class="inp" *ngIf="!showNoCard">
                            <input matInput type="text" pattern="[0-9]*" [(ngModel)]="viewCard.cardTo" name="cardTo" #cardTo="ngModel" required/>
                            <mat-error *ngIf="f.submitted && cardTo.invalid" class="pt15">
                                <div *ngIf="this.submitted && cardTo.hasError('required')">
                                    Card No. To Is Required
                                </div>
                            </mat-error>
                            <mat-error *ngIf="cardTo.hasError('pattern')" class="pt15">
                                Please enter a number
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
            </div>
            <div fxLayout="column">
                <div fxLayout="column" fxLayout.lt-md="column" fxLayout.lt-xs="column">
                    <div fxFlex fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="25%" fxLayoutAlign="start center"  class="head-align" >
                            <span class="date-title">TRAVEL START DATE<span class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxFlex="90%" fxFlex.lt-md="75%">
                            <mat-form-field appearance="outline" class="inp">
                                <input matInput [matDatepicker]="picker1" name="startDate"
                                    [(ngModel)]="viewCard.startDate" (ngModelChange)="daysDifference()"
                                    #startDate="ngModel" onkeydown="return false" required>
                                <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                                <mat-datepicker #picker1></mat-datepicker>
                                <mat-error *ngIf="f.submitted && startDate.invalid" class="pt15">
                           
                                    <div *ngIf="this.submitted && startDate.hasError('required')">
                                      
                                        Travel Start Date Is Required
                                    </div>
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div fxFlex fxLayout="row" fxLayoutGap="30px" fxLayoutGap.lt-md="80px" class="pt15">
                        <div fxFlex fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="25%" fxLayoutAlign="start center"  class="head-align" >
                                <span class="date-title">TRAVEL TO DATE<span
                                        class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="90%" fxFlex.lt-md="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="toDate" [matDatepicker]="picker2"
                                        [(ngModel)]="viewCard.toDate" (ngModelChange)="daysDifference()"
                                        #toDate="ngModel" onkeydown="return false" required>
                                    <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                                    <mat-datepicker #picker2></mat-datepicker>
                                    <mat-error *ngIf="f.submitted && toDate.invalid" class="pt15">
                                        <div *ngIf="this.submitted && toDate.hasError('required')">
                                            Travel To Date Is Required
                                        </div>
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>

                    </div>

                </div>
                <!-- <div *ngIf="showDateError" class="error">
                    Date Range Must Not Be More Than 31 Days
                </div>
                <div *ngIf="showDateRangeError" class="error">
                    End Date should be greater than start Date
                </div> -->
                <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="218px" fxLayoutGap.md="173px" fxLayoutGap.sm="160px" fxLayoutGap.xs="0px">
                    <div></div>
                    <!-- <div> -->
                    <div *ngIf="showDateError" class="error">
                        Date Range Must Not Be More Than 31 Days
                    </div>
                    <div *ngIf="showDateRangeError" class="error">
                        End Date should be greater than startDate
                    </div>
                <!-- </div> -->
                </div>
            </div>
           
            <div fxFlex>
                <button mat-raised-button class="download-btn" *ngIf="!canShowProcessingBtn" [disabled]="disablesubmit"
                (click)="download(f.form.valid)">DOWNLOAD</button>
                <button mat-raised-button class="download-btn" *ngIf="canShowProcessingBtn">Processing...</button>
            </div>
        </div>
    </form>
</div>