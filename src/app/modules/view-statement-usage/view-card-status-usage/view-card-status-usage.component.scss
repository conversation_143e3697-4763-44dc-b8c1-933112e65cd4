@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
::ng-deep .main-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
    height: 40px !important;
    width: 40px !important;
}
.err{
    color:red;
}
.download-btn{
    width:191px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
}
.error{
    font-size: $cdgsz-font-size-sm !important;
    color: $cdgc-font-warn !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
}
.inp{
    width: 320px !important;
}
.pl{
    padding-left: 154px !important;
}

@media screen and (max-width: 500px) {
    .inp{
        width: 180px !important;
    }
 }
 @media screen and (min-width: 500px) and (max-width: 800px) {
    .inp{
        width: 250px !important;
    }
 }

 .head-align{
    max-height: 55px !important;
 }