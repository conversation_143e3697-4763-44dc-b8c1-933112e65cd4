import { Component, OnInit } from '@angular/core';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as _moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core'
import { ExportxlsxService } from 'src/app/shared/services/exportxlsx.service';
import { DatePipe } from '@angular/common';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { RoleConstants } from 'src/assets/url-constants/url-constants';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { saveAs } from 'file-saver'

export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
}
export const CardUsage: any = {
  prodSelect: "",
  cardSelect: "",
  cardFrom: "",
  cardTo: "",
  startDate: "",
  toDate: ""
}
@Component({
  selector: 'app-view-card-status-usage',
  templateUrl: './view-card-status-usage.component.html',
  styleUrls: ['./view-card-status-usage.component.scss'],
  providers: [DatePipe, { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] }, { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]
})
export class ViewCardStatusUsageComponent implements OnInit {
  // productArr = [
  //   { value: 'all', viewValue: 'ALL' },
  //   { value: 'contactlesscorprate', viewValue: 'CONTACTLESS CORPORATE CARD' },
  //   { value: 'contactlessprepaid', viewValue: 'CONTACTLESS PREPAID CARD' },
  //   { value: 'corporatecard', viewValue: 'CORPORATE CARD' },
  //   { value: 'evoucher', viewValue: 'OPEN VALUE E-VOUCHER' },
  //   { value: 'virtualcrop', viewValue: 'VIRTUAL CORP 2 TEST' },
  //   { value: 'vitualcorporate', viewValue: 'VIRTUAL CORPORATE' },
  //   { value: 'virtualprepaid', viewValue: 'VIRTUAL PREPAID' }
  // ];
  data = {

    cardNo: ""
  }
  productArr: any = [];
  cardArr: any = [];
  firstdigit: any;
  lastdigit: any = [];
  // cardArr = ['601089-6511-1239', '601089-6511-1237', '601089-6511-1235', '601089-6511-1233', '601089-6511-1231', '601089-6511-1230'];
  isCardNoShow: boolean = false;
  isCardStartEndShow: boolean = false;

  viewCard = CardUsage;
  showDateError: boolean = false;
  showDateRangeError: boolean = false;
  isProductDisable: boolean = false;
  disablesubmit: boolean = false;
  accountnumber: any;
  list: any = [];
  displaycard: string;
  showNoCard: boolean = false;
  submitted: boolean = false;
  openshow: boolean = false;
  fullCardList: any[] = [];
  cardFromlist: any;
  cardTolist: any;
  canShowProcessingBtn: boolean = false;
  constructor(public localStorage: LocalStorageService, private apiService: ApiServiceService, private excelService: ExportxlsxService, private datePipe: DatePipe, public cdgService: CdgSharedService, private notifyService: NotificationService) { }

  ngOnInit(): void {
    this.viewCard.prodSelect = "";
    this.viewCard.startDate = "";
    this.viewCard.toDate = "";
    this.viewCard.cardSelect = "";
    this.viewCard.cardFrom = "";
    this.viewCard.cardTo = "";
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }
    }
    this.getAvailableProductTypes();
    // if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER) {
    //   this.isProductDisable = true;
    //   this.isCardNoShow = true;
    // this.productArr.push({ value: 'personalCard', viewValue: 'PERSONAL CARD' })
    // this.viewCard.prodSelect = 'personalCard'
    // this.viewCard.cardSelect = '601089-6511-1239'
    //}
  }
  getAvailableProductTypes() {
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "masterAccountNo": this.accountnumber,
      "accountNo": Number(this.accountnumber)
    }
    // GET_PRODUCTTYPE
    // FETCH_PRODUCTS_CARD
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_PRODUCTTYPE, obj).subscribe((response: any) => {
      if (response.status === 200) {
        //  console.log(response)
        // let opt = response.body;
        let opt = response.body.productType;
        opt.forEach((element: any) => {
          this.productArr.push(element);
          //  console.log(this.productArr);
        });
        //  console.log(opt);
        //  Array.from(opt).forEach((element: any) => {
        //     this.productArr.push(element);
        //     //  console.log(this.productArr);
        //     // this.viewCard.prodSelect = this.productArr;
        //   });

        // let opt = response.body.productType;

      }
    });


  }
  getCardDetails(selectedProd: any) {
    this.disablesubmit = false;
    this.openshow = false;
    this.viewCard.cardSelect = "";
    this.viewCard.cardTo = "";
    this.viewCard.cardFrom = "";
    this.displaycard = "";
    this.list = [];
    this.cardFromlist = [];
    this.cardTolist = [];

    let cardnumberobj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "productType": selectedProd,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      "masterAccountNo": this.accountnumber,
      "accountNo": this.accountnumber
    }

    if (this.viewCard.prodSelect != "OV") {
      // GET_CARDDETAILSUSAGE
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_CARDLIST, cardnumberobj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body.cardNo;
          if (!opt.length) {
            this.showNoCard = true;
            this.disablesubmit = true;
          }
          else {
            this.showNoCard = false;
            this.disablesubmit = false;
          }
          this.list = [];
          this.cardArr = []
            this.firstdigit = String(opt[0]).substr(0, 10);
            this.displaycard = this.firstdigit.substr(0, 6) + "-" + this.firstdigit.substr(6, 4);
            opt.forEach((element: any) => {
              this.list.push((String(element).substr(-6)));
              this.cardArr.push((String(element).substr(-6)));
            });
            this.openshow = false;
        }
      });
    }
    else  if (this.viewCard.prodSelect === "OV") {
      this.openshow = true;
    }    
    
    if (selectedProd === 'VV' || selectedProd === 'OV') {
      this.isCardStartEndShow = true;
      this.isCardNoShow = false;
    } else {
      this.isCardStartEndShow = false;
      this.isCardNoShow = true;
    }
  }

  daysDifference() {
    if (this.viewCard.startDate && this.viewCard.toDate) {
      //  console.log(this.viewCard.startDate);
      //  console.log(this.viewCard.toDate);
      let sDate: any = new Date(this.viewCard.startDate);
      let eDate: any = new Date(this.viewCard.toDate)
      const oneDay = 24 * 60 * 60 * 1000;
      const differenceDays = Math.round(Math.abs((sDate - eDate) / oneDay));
      if (differenceDays >= 31) {
        this.showDateError = true;
      }
      else {
        this.showDateError = false;
      }
    }
  }

  checkValidCard(value : any){
    if(!value){ //checking cardNo is not "" & null
      return false;
    }else{
      return true;
    }
  }

  download(valid: boolean) {
    this.submitted = true;
    if ((this.viewCard.startDate) > (this.viewCard.toDate)) {

      this.showDateRangeError = true;
    }
    else {
      this.showDateRangeError = false;
      if (valid) {
        if (!this.showDateError && !this.showDateRangeError) {
          this.showDateRangeError = false;
          this.showDateError = false;
          if (valid) {
            // if ( || ) {
            if (!this.showDateError && !this.showDateRangeError) {
              this.showDateRangeError = false;
              this.showDateError = false;
              this.canShowProcessingBtn = true;
              if (this.checkValidCard(this.viewCard.cardSelect)) { 
                let obj = {
                  "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
                  "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
                  "masterAccountNo": this.accountnumber,
                  productTypeId: this.viewCard.prodSelect,
                  "cardNoFrom": this.firstdigit + this.viewCard.cardSelect,
                  "cardNoTo": "",
                  invoiceNo: "",
                  "contactPersonNo": "",
                  "accountCategory": "",
                  "divisionCode": "",
                  "departmentCode": "",
                  tripStartDt: this.datePipe.transform(this.viewCard.startDate, 'dd-MMM-yyyy'),
                  tripEndDt: this.datePipe.transform(this.viewCard.toDate, 'dd-MMM-yyyy')
                }
                //  console.log("OBJ:", obj)
                let mediaType = "text/csv"
                this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 +
                  UrlConstants.CARD_STATUS_REPORTS, obj, { responseType: 'text' }).subscribe((response: any) => {
                    this.canShowProcessingBtn = false;
                    let blob = new Blob([response], { type: mediaType });
                    saveAs(blob, 'Card Usage status' + '.csv')
                  },
                    e => {
                      this.canShowProcessingBtn = false;
                      this.notifyService.showError(JSON.parse(e.error).message, "Error")
                    })

              }
              else if (this.checkValidCard(this.viewCard.cardFrom) && this.checkValidCard(this.viewCard.cardTo)) {
                let obj = {
                  "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
                  "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
                  "masterAccountNo": this.accountnumber,
                  productTypeId: this.viewCard.prodSelect,
                  "cardNoFrom": this.viewCard.cardFrom,
                  "cardNoTo": this.viewCard.cardTo,
                  invoiceNo: "",
                  "contactPersonNo": "",
                  "accountCategory": "",
                  "divisionCode": "",
                  "departmentCode": "",
                  tripStartDt: this.datePipe.transform(this.viewCard.startDate, 'dd-MMM-YYYY'),
                  tripEndDt: this.datePipe.transform(this.viewCard.toDate, 'dd-MMM-YYYY')
                }
                let mediaType = "text/csv"
                this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 +
                  UrlConstants.CARD_STATUS_REPORTS, obj, { responseType: 'text' }).subscribe((response: any) => {
                    this.canShowProcessingBtn = false;
                    let blob = new Blob([response], { type: mediaType });
                    saveAs(blob, 'Card Usage status' + '.csv')
                  },
                    e => {
                      this.canShowProcessingBtn = false;
                      this.notifyService.showError(JSON.parse(e.error).message, "Error")
                    })
              }
              else {
                  this.canShowProcessingBtn = false;
                this.notifyService.showWarning('Enter valid Card No for required field', "Warning")
              }
            }
            else {
              this.notifyService.showError("Please enter correct date range.", "Error")
            }

          }
        }

      }
    }
    // }
  }
}