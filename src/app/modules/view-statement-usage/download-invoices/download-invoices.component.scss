@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .mat-form-field-appearance-outline .mat-mdc-form-field-text-infix {
    padding: 0 0 0 0!important;
    bottom: 6px !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep  .mat-mdc-form-field:not(.mat-form-field-appearance-legacy) .mat-mdc-form-field-suffix .mat-mdc-icon-button {
    bottom: 6px !important;
}
.inp{
    width: 190px !important;
}
.mat-sort-header-arrow {
    color: $cdgc-font-accent;
}
.tbl-container{
    display: flex;
    flex-direction: column;
    // max-width: 300px;
    max-height: 500px;
}
.mat-mdc-header-row{
    border-radius: 12px;
    background-color:$cdgc-bg-prime !important;
    color: $cdgc-font-accent !important;
}
.mat-mdc-header-cell {
    color: $cdgc-font-accent !important;
    
}
.mat-mdc-row{
    border-radius: 12px;
    color: $lightGrey !important;

}
.mobile-label{
    display: none;
}
.download-icon{
    color: $cdgc-font-prime;
}
.sno{
    display: block;
}
::ng-deep .mat-sort-header-arrow {
    color: $cdgc-font-accent !important;
}
@media screen and (max-width: 600px) {
    .inp{
        width: 149px !important;
    }
    .date-title{
        font-size: $cdgsz-font-size-xs !important;
    }
    .mobile-label{
        display: inline-block;
        width: 165px;
        font-weight: $cdgsz-font-weight-bold;
    }
    .mat-mdc-header-row{
        display: none;
    }
    .mat-mdc-row{
        flex-direction: column;
        align-items: start;
        padding: 8px 24px;
        border-radius: 12px !important;
        border: 1px solid $cdgc-border-prime;
        min-height: 28px !important;
        margin-bottom: 15px;
    }
    .sno{
        display: none;
    }
}
