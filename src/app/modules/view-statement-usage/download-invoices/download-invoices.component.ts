import { Component, OnInit, Pipe } from '@angular/core';
import { DatePipe } from '@angular/common';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core'
import { MatTableDataSource } from '@angular/material/table';
import { Sort } from '@angular/material/sort';
import { ExportxlsxService } from 'src/app/shared/services/exportxlsx.service';
import { Moment } from 'moment';
import { FormControl } from '@angular/forms';
import { HttpClient } from '@angular/common/http';

import { MatDatepicker } from '@angular/material/datepicker';
import { throwError } from 'rxjs';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { saveAs } from 'file-saver'

// import { Moment } from 'moment';
export const MY_FORMATS = {
  parse: {
    dateInput: 'MMM YYYY'
  },
  display: {
    dateInput: 'MMMM YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMM YYYY'
  }
}
export interface DownloadInvoice {
  invoiceNo: number,
  invoiceDate: string,
  invoiceTo: string,
  invoiceAmt: number
}
@Component({
  selector: 'app-download-invoices',
  templateUrl: './download-invoices.component.html',
  styleUrls: ['./download-invoices.component.scss'],
  providers: [DatePipe, { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] }, { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]
})
export class DownloadInvoicesComponent implements OnInit {
  selectedDate = new FormControl(moment());
  isLoading: boolean = true;
  invoices = [
    { invoiceNo: 1339287, invoiceDate: "10/01/2020", invoiceTo: "Corporate Office (1005)", invoiceAmt: 8.00 },
    { invoiceNo: 1155861, invoiceDate: "03/15/2021", invoiceTo: "Corporate Office (1005)", invoiceAmt: 8.00 },
    { invoiceNo: 1155862, invoiceDate: "02/29/2021", invoiceTo: "Corporate Office (1005)", invoiceAmt: 3.00 },
    { invoiceNo: 1155863, invoiceDate: "01/30/2021", invoiceTo: "Corporate Office (1005)", invoiceAmt: 5.00 },
    { invoiceNo: 1155864, invoiceDate: "09/14/2020", invoiceTo: "Corporate Office (1005)", invoiceAmt: 7.00 },
    { invoiceNo: 1155865, invoiceDate: "12/31/2020", invoiceTo: "Corporate Office (1005)", invoiceAmt: 9.00 }
  ]
  displayedColumns = ['slno', 'invoiceNo', 'invoiceDate', 'invoiceTo', 'invoiceAmt', 'action']
  sortedData: DownloadInvoice[];
  accountnumber: any;
  constructor(public localStorage: LocalStorageService, public cdgService: CdgSharedService, private excelService: ExportxlsxService, private http: HttpClient, private datePipe: DatePipe, private apiService: ApiServiceService,) {
    this.sortedData = this.invoices.slice();
  }

  ngOnInit(): void {
    // window.location.reload();
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }
    }
  }

  // dummydata to be deleted after verification.

  downloadPdf(data: any) {
    let obj = {
      "accessId": "accessid",
      "accountCategory": "CORP",
      "role": "dfd",
      "startDate": this.datePipe.transform(data.invoiceDate, 'dd-MMM-YY'),
      "endDate": "",
      "invoiceNo": data.invoiceNo,
      "accountNo": this.accountnumber
    }
    let mediaType = "application/pdf"
    // 9090
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.PDF_DOWNLOAD, obj, { responseType: 'blob' }).subscribe(response => {
      let blob = new Blob([response], { type: mediaType });
      saveAs(blob, 'Invoice_' + data.invoiceNo + '_' + this.datePipe.transform(data.invoiceDate, 'MMM-YY'))
    },
      e => { throwError(e) })
  }

  sortData(sort: Sort) {
    const data = this.invoices.slice();
    if (!sort.active || sort.direction === '') {
      this.sortedData = data;
      return;
    }
    this.sortedData = data.sort((x, y) => {
      const isAsc = sort.direction === 'asc';
      switch (sort.active) {
        case 'invoiceNo': return this.campare(x.invoiceNo, y.invoiceNo, isAsc);
        default: return 0;
      }
    });
  }
  campare(a: number | string, b: number | string, isAsc: boolean) {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1)
  }
  chosenYearHandler(normalizedYear: Moment) {
    const ctrlValue = this.selectedDate.value;
    if (ctrlValue)
      ctrlValue.year(normalizedYear.year());
    this.selectedDate.setValue(ctrlValue)
  }
  chosenMonthHandler(normalizedMonth: Moment, datepicker: MatDatepicker<Moment>) {
    const ctrlValue = this.selectedDate.value;
    if (ctrlValue)
      ctrlValue.month(normalizedMonth.month());
    this.selectedDate.setValue(ctrlValue);
    this.filterTable();
    datepicker.close();
  }
  filterTable() {
    let arr: any = []
    const selectedYear = this.selectedDate.value ? moment(this.selectedDate.value, "DD/MM/YYYY").year() : null;
    const selectedMonth = this.selectedDate.value ? moment(this.selectedDate.value, "DD/MM/YYYY").month() : null;
    this.invoices.forEach(element => {
      const date = element.invoiceDate.split('/');
      if (selectedYear === Number(date[2]) && selectedMonth === Number(date[1])) {
        arr.push(element)
      }
    });
    this.sortedData = arr
  }
}
