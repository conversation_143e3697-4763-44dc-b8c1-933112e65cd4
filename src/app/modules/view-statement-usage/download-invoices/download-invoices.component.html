<div fxLayout="column">
    <div fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="end start">
        <span class="date-title pt10">Date:</span>
        <mat-form-field appearance="outline" class="inp">
            <input matInput [matDatepicker]="picker" [formControl]="selectedDate">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker startView="multi-year" (yearSelected)="chosenYearHandler($event)" (monthSelected)="chosenMonthHandler($event,picker)"></mat-datepicker>
        </mat-form-field>
    </div>
    <div class="tbl-container mat-elevation-z8">
        <mat-table #table [dataSource]="sortedData" matSort (matSortChange)="sortData($event)">
            <ng-container matColumnDef="slno">
                <mat-header-cell *matHeaderCellDef> S No. </mat-header-cell>
                <mat-cell *matCellDef="let element;let i=index" class="sno">
                    {{i+1}}
                </mat-cell>
            </ng-container>
            <ng-container matColumnDef="invoiceNo">
                <mat-header-cell *matHeaderCellDef mat-sort-header="invoiceNo"> Invoice No. </mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label"> Invoice No. :</span>
                    {{element.invoiceNo}}
                </mat-cell>
            </ng-container>
            <ng-container matColumnDef="invoiceDate">
                <mat-header-cell *matHeaderCellDef> Invoice Date </mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label"> Invoice Date :</span>
                    {{element.invoiceDate}}
                </mat-cell>
            </ng-container>
            <ng-container matColumnDef="invoiceTo">
                <mat-header-cell *matHeaderCellDef> Invoice To </mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <span class="mobile-label"> Invoice To</span>
                    {{element.invoiceTo}}
                </mat-cell>
            </ng-container>
            <ng-container matColumnDef="invoiceAmt">
                <mat-header-cell *matHeaderCellDef> Invoice Amount(S$) </mat-header-cell>
                <mat-cell *matCellDef="let element" fxLayoutAlign="center center">
                    <span class="mobile-label"> Balance :</span>
                    {{element.invoiceAmt | currency}}
                </mat-cell>
            </ng-container>
            <ng-container matColumnDef="action">
                <mat-header-cell *matHeaderCellDef> </mat-header-cell>
                <mat-cell *matCellDef="let element" fxLayoutAlign="center center">
                    <span class="mobile-label"> </span>
                    <button mat-icon-button class="inp register-btn" (click)="downloadPdf(element)">
                        <mat-icon class="download-icon">get_app</mat-icon>
                    </button>
                </mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
        </mat-table>
        <div *ngIf="isLoading" class="loader">
            <mat-progress-spinner color="primary" mode="indeterminate" diameter="55" strokeWidth="5">
            </mat-progress-spinner>
          </div>
    </div>
</div>