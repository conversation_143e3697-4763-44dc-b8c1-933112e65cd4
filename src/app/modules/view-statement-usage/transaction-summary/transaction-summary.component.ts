import { Component, OnInit } from '@angular/core';
import { ExportxlsxService } from 'src/app/shared/services/exportxlsx.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { throwError } from 'rxjs';
import { saveAs } from 'file-saver';

@Component({
  selector: 'app-transaction-summary',
  templateUrl: './transaction-summary.component.html',
  styleUrls: ['./transaction-summary.component.scss']
})
export class TransactionSummaryComponent implements OnInit {
  invoiceno: any;
  invoicearr = [********, ********, ********, ********, ********, ********]
  isShowErrorMsg: boolean = false;
  accountnumber: any;
  canShowProcessingBtn: boolean = false;
  constructor(private notifyService: NotificationService,private apiService: ApiServiceService,private excelService: ExportxlsxService,public localStorage: LocalStorageService,public cdgService: CdgSharedService) { }

  ngOnInit(): void {
    if(this.localStorage.localStorageGet("customerNoSelected")!=null){
      this.accountnumber=this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if(this.accountnumber=this.localStorage.localStorageGet("custNo")!=null){
      this.accountnumber=this.localStorage.localStorageGet("custNo").view;
    }
    else {
      this.accountnumber= this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
    }
    }
  }
  download() {
    this.isShowErrorMsg = false;
    if (this.invoiceno) {
      this.canShowProcessingBtn = true;
      const invoice = this.invoicearr.find(o => o === Number(this.invoiceno));
     // if (this.invoiceno) {
        let obj={
         
          "accessId" :  this.localStorage.localStorageGet("loginUserDetails").accessId,
          "role" :this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
	        "invoiceNo" :this.invoiceno,
          "accountCategory":this.localStorage.localStorageGet("loginUserDetails").roles.accountcategory,
          "accountNo": this.accountnumber

        }
        let mediaType = "application/pdf"
        // 9090
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_TRANSACTIONSUMMARY, obj, { responseType: 'blob' }).subscribe(response => {
          this.canShowProcessingBtn = false;
          let blob = new Blob([response], { type: mediaType });
          if(blob.size>63){
          saveAs(blob, 'Transaction-Summary' + this.invoiceno)
          }
          else{
            this.notifyService.showError('Invalid invoice no, please check invoice no again','Transaction Summary');
          }
         
         },
          e => { 
            this.canShowProcessingBtn = false;
            throwError(e)
           })
     // }
      // else {
      //   this.notifyService.showError("Invoice does not exist", "Error")
      // }
    }
    else {
      this.isShowErrorMsg = true;
    }
  }
}
