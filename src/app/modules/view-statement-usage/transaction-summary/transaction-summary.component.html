<div fxLayout="column" fxLayoutGap="50px">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined">assignment</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">View Card Transaction Summary</span>
        </div>
    </div>
    <div fxLayout="column" class="custom-padding-left">
        <div fxLayout="row" fxLayoutGap="30px">
            <span class="pt10">INVOICE NO.<span style="color: red;">*</span>:</span>
            <mat-form-field appearance="outline" class="inp">
                <input matInput  type="number"  [(ngModel)]="invoiceno">
            </mat-form-field>
        </div>
        <div *ngIf="isShowErrorMsg" class="error pl">
            Invoice No. cannot be blank
        </div>
        <div fxFlex class="pt15">
            <button mat-raised-button class="download-btn" *ngIf="!canShowProcessingBtn" (click)="download()">DOWNLOAD</button>
            <button mat-raised-button class="download-btn" *ngIf="canShowProcessingBtn">Processing...</button>
        </div>
    </div>
</div>