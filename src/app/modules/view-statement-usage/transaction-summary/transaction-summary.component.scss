@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button{
  -webkit-appearance: none;
  margin: 0;
}
.mat-icon{ // changed by navani for icon
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
    height: 40px !important;
    width: 40px !important;
}
.download-btn{
    width:191px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
}
.error{
    font-size: $cdgsz-font-size-sm;
    color: $cdgc-font-warn;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
}
.inp{
    width: 178px !important;
}
.pl{
    padding-left: 110px !important;
}