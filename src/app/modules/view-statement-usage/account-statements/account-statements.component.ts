import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort, Sort } from '@angular/material/sort';
import { ExportxlsxService } from 'src/app/shared/services/exportxlsx.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';

declare var require: any
const FileSaver = require('file-saver')

@Component({
  selector: 'app-account-statements',
  templateUrl: './account-statements.component.html',
  styleUrls: ['./account-statements.component.scss']
})
export class AccountStatementsComponent implements AfterViewInit {
  divisionSelected: any;
  divisions: any[] = [];
  deptArray: any[] = [];
  Paginator: any;
  deptList: any;
  isLoading: boolean = true;
  loggedInRole: any;
  accountnumber: any;
  responseDiv: any;
  dataResponse: any;

  @ViewChild(MatPaginator, { static: false })
  set paginator(value: MatPaginator) {
    this.Paginator = value;
    this.invoiceDataSource.paginator = value;
  }
  public accDetailsbyDuration: any[] = []
  public outStandingInvoices: any[] = []
  dataSource = this.accDetailsbyDuration;
  // invoiceDataSource =this.outStandingInvoices;
  invoiceDataSource = new MatTableDataSource<any>(this.outStandingInvoices);
  //  dataSource!: MatTableDataSource<any>;
  //  outstandingInvoiceSource!: MatTableDataSource<any>;

  // private paginator! : MatPaginator;
  // private invoicePaginator! : MatPaginator;
  // @ViewChild(MatPaginator) set matPaginator(mp: MatPaginator){
  //   this.paginator = mp;
  //   this.invoicePaginator = mp;
  // this.setAccAttributes();
  // this.setInvoiceAttributes();
  // }
  //  @ViewChild(MatSort) sort:MatSort;

  public displayedColumns: any[] = [];
  public invoiceDisplayedColumns: any[] = [];
  placeName: string = "";
  accNo: number;
  isShowTable: boolean = false;
  response: any;
  deptSelected: any;
  isShowDeptList: boolean = false
  constructor(public localStorage: LocalStorageService, private notifyService: NotificationService, private http: HttpClient, private excelService: ExportxlsxService, public cdgService: CdgSharedService, private apiService: ApiServiceService) { }

  ngOnInit(): void {
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        console.log(this.accountnumber, "else")
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }

    }
    this.loggedInRole = this.localStorage.localStorageGet("loggedInRole");
    if (this.loggedInRole === RoleConstants.ROLE_CORPADMIN) {
      this.isShowTable = false;
      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "divisionCode": null,
        "deptCode": null,
        "accountNo": null
      }

      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_STATEMENT_OF_ACCOUNTS, obj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body;
          this.response = opt;
          if (Object.keys(opt.corpAccountDto).length > 0) {
            this.divisions.push(opt.corpAccountDto)
            this.divisionSelected = opt.corpAccountDto;
            // this.showTable(this.divisionSelected)
            this.selectDept(this.divisionSelected);
          }
          if (opt.divAccountList.length > 0) {
            opt.divAccountList.forEach((element: any) => {
              this.divisions.push(element.divAccount)
            });
          }
        }
      },
        e => {
          console.log(e)
        })
    }
    if (this.loggedInRole === RoleConstants.ROLE_MASTERUSER) {
      this.isShowTable = false;
      // let obj = {
      //   "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      //   "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      //   "divisionCode": null,
      //   "deptCode": null,
      //   "accountNo": this.accountnumber
      // }
      this.getDivision();

      // this.apiService.post(this.cdgService.localhostUrl + UrlConstants.GET_DIV_DEPT_FOR_MASTER_STATEMENTS, obj).subscribe((response: any) => {
      //   if (response.status === 200) {
      //     let opt = response.body;
      //     this.response = opt;
      //     if (Object.keys(opt.corpAccountDto).length > 0) {
      //       this.divisions.push(opt.corpAccountDto)
      //       this.divisionSelected=opt.corpAccountDto;
      //       this.showTable(this.divisionSelected)
      //     }
      //     if (opt.divAccountList.length > 0) {
      //       opt.divAccountList.forEach((element: any) => {
      //         this.divisions.push(element.divAccount)
      //       });
      //     }
      //   }
      // },
      //   e => {
      //     console.log(e)
      //   })
    }
    else if (this.loggedInRole === RoleConstants.ROLE_PERSCARDHOLDER) {
      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "divisionCode": null,
        "deptCode": null,
        "accountNo": null
      }
      this.getStatementOfAcc(obj)
    }
    else {
      this.isShowTable = true;
    }
  }
  ngAfterViewInit() {
    // this.invoiceDataSource.paginator = this.paginator;
  }
  sortData(sort: Sort) {
    const data = this.outStandingInvoices.slice();
    if (!sort.active || sort.direction === '') {
      this.invoiceDataSource.data = data;
      return;
    }

    this.invoiceDataSource.data = data.sort((x, y) => {
      const isAsc = sort.direction === 'asc';
      switch (sort.active) {
        case 'invoiceNumber': return this.compare(x.invoiceNumber, y.invoiceNumber, isAsc);
        case 'invoiceDate': return this.compare(x.invoiceDate, y.invoiceDate, isAsc);
        case 'dueDate': return this.compare(new Date(x.dueDate), new Date(y.dueDate), isAsc);
        case 'invoiceAmount': return this.compare(Number(x.invoiceAmount), Number(y.invoiceAmount), isAsc);
        case 'totalPaidAmount': return this.compare(Number(x.totalPaidAmount), Number(y.totalPaidAmount), isAsc);
        case 'outstandingAmount': return this.compare(Number(x.outstandingAmount), Number(y.outstandingAmount), isAsc);

        default: return 0;
      }
    });
  }
  compare(a: Date | string | number, b: Date | string | number, isAsc: boolean) {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1)
  }
  downloadPdf(data: any) {
    this.excelService.exportAsExcel([data], data.invoiceNo + '_accountstatement')
  }
  showTable(selectedDivision: any) {
    this.isShowDeptList = false;
    let obj: any;
    if (selectedDivision.nameToDisplayOnDropdown === '-CORPORATE INVOICE-') {
      obj = {
        accessId: this.localStorage.localStorageGet("loginUserDetails").accessId,
        role: this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        divisionCode: null,
        deptCode: null,
        accountNo: selectedDivision.accountNo
      }
      this.getStatementOfAcc(obj);
    }
    else {
      const dataSeleted = this.response.divAccountList.find((o: any) => o.divAccount.nameToDisplayOnDropdown === selectedDivision.nameToDisplayOnDropdown)
      console.log(dataSeleted)
      if (dataSeleted) {
        this.deptArray = [];
        if (dataSeleted.deptList.length > 0) {
          console.log(dataSeleted.deptList);
          // this.deptList = dataSeleted.deptList;
          this.isShowDeptList = true;
          this.deptArray.push({
            accountCategory: "",
            accountCode: "",
            accountName: "",
            accountNo: "",
            nameToDisplayOnDropdown: "-DIVISION INVOICE-"
          })
          // if dept selected value is selected as -DIVISION INVOICE- pass deptCode as deptCode:"" during submit
          dataSeleted.deptList.forEach((val: any) => {
            this.deptArray.push(val);
          });
          this.deptSelected = this.deptArray[0];
        }
        // else {
        obj = {
          accessId: this.localStorage.localStorageGet("loginUserDetails").accessId,
          role: this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          divisionCode: selectedDivision.accountCode,
          deptCode: null,
          accountNo: selectedDivision.accountNo
        }
        this.getStatementOfAcc(obj);
        // }
      }
    }
  }
  getDivision() {
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "divisionCode": null,
      "deptCode": null,
      "accountNo": this.accountnumber
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_DIV_DEPT_FOR_MASTER_STATEMENTS, obj).subscribe((response: any) => {
      if (response.status === 200) {

        let data = response.body;
        this.responseDiv = data;
        if (data.corpAccountDto == null) {
          if (data.divAccountList.length > 1) {
            var obj = {
              "deptList": [],
              "divAccount": {
                "accountCategory": "",
                "accountCode": "",
                "accountName": "ALL",
                "accountNo": '',
                "nameToDisplayOnDropdown": "ALL"
              }
            }
            this.divisions.push(obj)

            data.divAccountList.forEach((element: any) => {
              this.divisions.push(element.divAccount);
            });
            this.divisionSelected = this.divisions[0];
          }
        }
        if (data.corpAccountDto) {
          if (Object.keys(data.corpAccountDto).length > 0) {
            let obj = {
              accountCategory: data.corpAccountDto.accountCategory,
              accountCode: data.corpAccountDto.accountCode,
              accountName: data.corpAccountDto.accountName,
              accountNo: data.corpAccountDto.accountNo,
              nameToDisplayOnDropdown: data.corpAccountDto.nameToDisplayOnDropdown
            }
            this.divisions.push(obj);
          }

        }

        if (data.divAccountList) {
          if (data.divAccountList.length > 0) {
            data.divAccountList.forEach((element: any) => {
              // this.fetchDept.push(element);
              this.divisions.push(element.divAccount);
              this.divisionSelected = this.divisions[0];
              // if (element.deptList.length > 0) {
              this.selectDept(this.divisions[0]);
              // }
            });
          }
        }
      }
    });


  }
  selectDept(value: any) {
    console.log("i am inside select department");
    this.divisionSelected = value;
    console.log(this.divisionSelected);
    this.isShowDeptList = false;
    if (this.divisionSelected.nameToDisplayOnDropdown == "-CORPORATE INVOICE-") {
      let obj = {
        accessId: this.localStorage.localStorageGet("loginUserDetails").accessId,
        role: this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        divisionCode: null,
        deptCode: null,
        accountNo: value.accountNo
      }
      this.getStatementOfAcc(obj);
    }


    if (value.nameToDisplayOnDropdown !== "ALL" && value.nameToDisplayOnDropdown !== "-CORPORATE INVOICE-") {
      let objs
      if (this.loggedInRole === RoleConstants.ROLE_MASTERUSER) {
        objs = this.responseDiv.divAccountList.find((x: any) => x.divAccount.nameToDisplayOnDropdown === value.nameToDisplayOnDropdown)
      }
      else {
        objs = this.response.divAccountList.find((x: any) => x.divAccount.nameToDisplayOnDropdown === value.nameToDisplayOnDropdown)
      }
      this.deptArray = [];
      if (objs.deptList.length > 0) {
        this.isShowDeptList = true;
        //commented on aug 6
        // this.deptArray.push({
        //   accountCategory: "",
        //   accountCode: "",
        //   accountName: "",
        //   accountNo: "",
        //   nameToDisplayOnDropdown: "-DIVISION INVOICE-"
        // })
        //if dept selected value is selected as -DIVISION INVOICE- pass deptCode as deptCode:"" during submit
        objs.deptList.forEach((val: any) => {
          this.deptArray.push(val);
        });
        this.deptSelected = this.deptArray[0];
      }
      if (this.deptSelected) {


        if (this.divisionSelected.nameToDisplayOnDropdown != "-CORPORATE INVOICE-" && this.deptSelected.nameToDisplayOnDropdown === "-DIVISION INVOICE-") {
          let obj = {
            accessId: this.localStorage.localStorageGet("loginUserDetails").accessId,
            role: this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
            divisionCode: value.accountCode,
            deptCode: null,
            accountNo: value.accountNo
          }
          this.getStatementOfAcc(obj);
        }
        // if(this.deptSelected.nameToDisplayOnDropdown != "-DIVISION INVOICE-"){
        //   let obj = {
        //     accessId: this.localStorage.localStorageGet("loginUserDetails").accessId,
        //     role: this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        //     divisionCode: value.accountCode,
        //     deptCode: this.deptSelected ? this.deptSelected.accountCode.toString() : "",
        //     accountNo: value.accountNo
        //   }
        //   this.getStatementOfAcc(obj);
        // }
      }
      else {
        let obj = {
          accessId: this.localStorage.localStorageGet("loginUserDetails").accessId,
          role: this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          divisionCode: value.accountCode,
          deptCode: null,
          accountNo: value.accountNo
        }
        this.getStatementOfAcc(obj);

      }

    }

  }
  getDeptSelected(deptSelected: any) {
    let value = this.divisionSelected;
    console.log(deptSelected)
    if (this.deptSelected.nameToDisplayOnDropdown != "-DIVISION INVOICE-") {
      let obj = {
        accessId: this.localStorage.localStorageGet("loginUserDetails").accessId,
        role: this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        divisionCode: value.accountCode,
        deptCode: this.deptSelected ? this.deptSelected.accountCode.toString() : "",
        accountNo: deptSelected.accountNo
      }
      this.getStatementOfAcc(obj);
    }
    else {
      console.log("i am inside");
      let obj = {
        accessId: this.localStorage.localStorageGet("loginUserDetails").accessId,
        role: this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        divisionCode: value.accountCode,
        deptCode: null,
        accountNo: deptSelected.accountNo
      }
      this.getStatementOfAcc(obj);

    }
    // let obj = {
    //   accessId: this.localStorage.localStorageGet("loginUserDetails").accessId,
    //   role: this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
    //   divisionCode: this.divisionSelected.accountCode,
    //   deptCode: deptSelected.accountCode,
    //   accountNo: this.divisionSelected.accountNo
    // }
    // if (obj) {
    //   this.getStatementOfAcc(obj);
    // }
  }

  getStatementOfAcc(reqObj: any) {
    this.isShowTable = false;
    this.accDetailsbyDuration = [];
    this.outStandingInvoices = [];
    this.dataSource = [];
    this.placeName = '';
    this.invoiceDataSource = new MatTableDataSource<any>([]);
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_STATEMENT_OF_ACCOUNTS, reqObj).subscribe((response: any) => {

      console.log(response);
      if (response.status === 200) {
        console.log(response);

        let opt = response.body;
        this.dataSource = [];
        this.placeName = '';
        this.invoiceDataSource = new MatTableDataSource<any>([]);
        if (opt.debtTo) {
          this.placeName = opt.debtTo;
          this.accNo = opt.customerNo;
          if (Object.keys(opt.amountForDays).length > 0) {
            this.displayedColumns = ["daysOne", "daysTwo", "daysThree", "daysFour", "grandTotal"];
            this.accDetailsbyDuration.push(opt.amountForDays);
            this.dataSource = this.accDetailsbyDuration;
          }
          if (opt.outstandingInvoicesList.length > 0) {
            this.invoiceDisplayedColumns = ['slNo', 'invoiceNumber', 'invoiceDate', 'dueDate', 'invoiceAmount', 'totalPaidAmount', 'outstandingAmount'];
            this.outStandingInvoices = opt.outstandingInvoicesList;
            this.outStandingInvoices.forEach((val, i) => {
              val['id'] = i + 1;
            });
            this.invoiceDataSource = new MatTableDataSource<any>(this.outStandingInvoices);
            this.invoiceDataSource.paginator = this.paginator;
          }
          this.isShowTable = true;
          this.isLoading = false;
        }
        else {
          this.accDetailsbyDuration = [];
          this.outStandingInvoices = [];
          this.isShowTable = false;
          this.isLoading = false;
          this.dataResponse = opt;

          // this.notifyService.showInfo(opt.message, "Information")
        }
      }
    },
      e => {
        console.log(e);
        this.isLoading = false;
        this.notifyService.showError(e.error.errorMessage, "Error")

      })
  }
}










