@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
.bckgrnd{
background-color: #E7E7E7;
}
.title {
    font-family: $cdgsz-font-family;
    font-style: normal;
    font-weight: bold;
    font-size: $cdgsz-font-size-lg;
    line-height: 23px;
}
.mat-icon{
    color: $cdgc-font-prime;
}
.mb13{
    margin-bottom: 13px;
}
.table-sub-title {
    font-family: $cdgsz-font-family;
    font-style: normal;
    font-weight: bold;
    font-size: 18px;
    line-height: 21px;
    color: #303030;
}
.padding{
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 15px;
    padding-right: 15px;
}
.placeName{
    font-weight: $cdgsz-font-weight-normal;
}
.font-12{ 
    font-size:  $cdgsz-font-size-xs;
}
.font-16{
   font-size: $cdgsz-font-size-sm; 
}
// .full-height{
//     height:100vh
// }

.actn_icon{
    font-size: 20px;
    color: $prime;
    cursor:pointer;
}
table {
  width: 100%;
}
.mat-mdc-header-cell{ 
    background-color: #006BA8;
    color:$white;
}
.mat-mdc-row{
    color:black;
}
.mat-mdc-cell{
font-weight: normal;
font-size: 14px;
line-height: 16px;
}
// .division-inp{
//     width: 500px;
// }
.sno{
    display: block;
}
// .mat-column-till30Days{
//     width:20%;
// }
// .mat-column-till60Days{
//     width:20%;
// }
// .mat-column-till90Days{
//     width:20%;
// }
// .mat-column-more90Days{
//     width:30%;
// }
// .mat-column-total{
//     width:20%;
// }
.mt25{
    margin-top: 25px;
    margin-left: 15px;
    margin-right: 15px;

}

// .mat-column-invoiceNo{
//     width:20%;
// }
// .mat-column-invoiceDate{
//     width:20%;
// }
// .mat-column-invoiceTo{
//     width:35%;
// }
// .mat-column-invoiceAmt{
//     width:20%;
// }
// .mat-column-action{
//     width:10%;
// }

// ::ng-deep .th.mat-header-cell:last-of-type, td.mat-cell:last-of-type, td.mat-footer-cell:last-of-type {
//     padding-left: 50px !important;
//     padding-right: 0px !important;
// }
.fwb{
    font-weight:$cdgsz-font-weight-bold;
}
.mat-sort-header-arrow {
    color: $cdgc-font-accent;
}
.tbl-container{
    display: flex;
    flex-direction: column;
    max-height: 500px;
}
.mat-mdc-header-row{
    border-radius: 12px;
    background-color:$cdgc-bg-prime !important;
    color: $cdgc-font-accent !important;
}
.mat-mdc-header-cell {
    color: $cdgc-font-accent !important;
    
}
.mat-mdc-row{
    border-radius: 12px;
    color: $lightGrey !important;

}

.mobile-label{
    display: none;
}
@media screen and (min-width: 601px){
    .division-inp{
        width: 500px;
    }
}
.p30{
    padding-left: 42px !important;
}
::ng-deep .mat-sort-header-arrow {
    color: $cdgc-font-accent !important;
}

:host::ng-deep .mySort .mat-sort-header-content {
    width:76px !important;
}
@media screen and (max-width: 600px) {
    .mobile-label{
        display: inline-block;
        width: 165px;
        font-weight: $cdgsz-font-weight-bold;
    }
    .p30{
        padding-left: 0px !important;
    }
    .mat-mdc-header-row{
        display: none;
    }
    .mat-mdc-row{
        flex-direction: column;
        align-items: start;
        padding: 8px 24px;
        border-radius: 12px !important;
        border: 1px solid $cdgc-border-prime;
        min-height: 28px !important;
        margin-bottom: 10px;
    }
    :host ::ng-deep  mat-cell:first-of-type, mat-header-cell:first-of-type, mat-footer-cell:first-of-type {
     padding-left: 0px !important;
    }
    .sno{
        display: none !important;
    }
}


// @media screen and(min-width: 768px) and (max-width: 1240px) {
//     .ml50{
//         margin-left:50px;
//     }
//     .mr50{
//         margin-right:50px; 
//     }
//     .mat-column-action{
//         width:10%
//     }
//  }
//  @media screen and(min-width: 1030px) and (max-width: 1366px) {
//     .ml50{
//         margin-left:50px;
//     }
//     .mr50{
//         margin-right:50px; 
//     }
//     .mat-column-action{
//         width:10%
//     }
//  }
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
.loader{
    display:flex;
    justify-content: center;
    align-items: center;
    background: $white;
}
.noData{
    padding-left:40px;
    color: $cdgc-font-warn !important;
    font-weight: $cdgsz-font-weight-bold !important ;
}
