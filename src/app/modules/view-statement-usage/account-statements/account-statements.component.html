<div fxFlex fxLayout="column" class="full-height" fxLayoutGap="20px"> 
        <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
          <div fxFlex="4%" >
            <img src="\assets\images\view usage report.png">
          </div>
          <div fxFlex="96%" fxLayoutAlign="start center">
              <span class="header pb20">View Statement of Accounts</span>
          </div>
      </div>
       <div fxLayout="column"  class="bckgrnd padding" *ngIf="this.loggedInRole=='ROLE_CORPADMIN' || this.loggedInRole=='ROLE_MASTERUSER'">
       <div fxLayoutAlign="start center">
        <span class="date-title">DIVISION <span class="asterisk"><sup>*</sup></span>:</span>
       </div>
        <div  fxLayout="row" fxLayoutAlign="center center" fxLayoutAlign.sm = "start center">
            <div fxFlex > 
                <mat-form-field appearance="outline" class="division-inp">
                    <mat-select placeholder="Division" [(ngModel)]="divisionSelected" (ngModelChange)="selectDept(divisionSelected)">
                      <!-- (ngModelChange)="showTable(divisionSelected)" -->
                      <!-- (ngModelChange)='onDivisionSelected($event)' -->
                      <mat-option *ngFor = "let division of divisions;" [value]="division" >{{division.nameToDisplayOnDropdown}}</mat-option>
                    </mat-select>
                    <!-- <mat-error *ngIf="f.submitted && divisionSelected.invalid" class="pt15">
                      <div *ngIf="nameOnCard.hasError('required')">
                         Name is required
                      </div>
                  </mat-error> -->
                  </mat-form-field>
            </div>
        </div>
      </div>
      <div fxLayout="column"  class="bckgrnd padding" *ngIf="isShowDeptList">
        <div fxLayoutAlign="start center">
         <span class="date-title">DEPARTMENT <span class="asterisk"><sup>*</sup></span>:</span>
        </div>
         <div  fxLayout="row" fxLayoutAlign="center center" fxLayoutAlign.sm = "start center">
             <div fxFlex > 
                 <mat-form-field appearance="outline" class="division-inp">
                     <mat-select [(ngModel)]="deptSelected" name="defaultDep" (ngModelChange)="getDeptSelected(deptSelected)">
                      <!-- placeholder="Department"  -->
                      <!-- (ngModelChange)="getDeptSelected(deptSelected)" -->
                       <!-- (ngModelChange)='onDivisionSelected($event)' -->
                       <mat-option *ngFor = "let dept of deptArray;" [value]="dept" >{{dept.nameToDisplayOnDropdown}}</mat-option>
                     </mat-select>
                     <!-- <mat-error *ngIf="f.submitted && divisionSelected.invalid" class="pt15">
                       <div *ngIf="nameOnCard.hasError('required')">
                          Name is required
                       </div>
                   </mat-error> -->
                   </mat-form-field>
             </div>
         </div>
       </div>


  <div class="tbl-container" *ngIf="isShowTable">
    <div fxLayout="row" fxFlex=5% fxFlex.sm=10% fxFlex.md=10% *ngIf="this.loggedInRole=='ROLE_PERSCARDHOLDER' || this.loggedInRole=='ROLE_CORPADMIN' || this.loggedInRole=='ROLE_MASTERUSER'">
      <p class="table-sub-title">Debt To : <span class="placeName">{{placeName}}</span></p>
      </div>
    <div fxLayout="row" fxFlex=5% fxFlex.sm=10% fxFlex.md=10%  *ngIf="this.loggedInRole=='ROLE_CORPADMIN' || this.loggedInRole=='ROLE_MASTERUSER'">
      <p class="table-sub-title">Account No. <span class="placeName">{{accNo}}</span></p>
      </div>
   
          <mat-table #table class="mat-table mat-cdk-table" [dataSource]="dataSource" 
          matSort>      
          <ng-container matColumnDef="daysOne" >
              <mat-header-cell *matHeaderCellDef class="fwb font-16">0-30Days</mat-header-cell>
              <mat-cell *matCellDef="let element">
                <span class="mobile-label"> 0-30Days</span>
             
                 {{element.days1 | currency:'':''}} 
             
              </mat-cell>
           </ng-container>
             
            <ng-container matColumnDef="daysTwo">
              <mat-header-cell *matHeaderCellDef class="fwb font-16">31-60Days</mat-header-cell>
              <mat-cell *matCellDef="let element">
                <span class="mobile-label"> 0-60Days</span>
             
                 {{element.days2 | currency:'':''}} 
              
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="daysThree">
              <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>61-90Days</mat-header-cell>
              <mat-cell *matCellDef="let element">
                <span class="mobile-label">0-90Days</span>
              
                  {{element.days3 | currency:'':''}}
                
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="daysFour">
              <mat-header-cell *matHeaderCellDef class="fwb font-16">More than 90 Days</mat-header-cell>
              <mat-cell *matCellDef="let element">
                <span class="mobile-label"> More than 90 Days</span>
             
                 {{element.days4 | currency:'':''}} 
              
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="grandTotal">
              <mat-header-cell *matHeaderCellDef class="fwb font-16">Grand Total</mat-header-cell>
              <mat-cell *matCellDef="let element">
                <span class="mobile-label"> Grand Total</span>
              
                 {{element.grandTotal | currency:'':''}} 
            
              </mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            <tr class="mat-row" *matNoDataRow fxLayoutAlign="center center">
              <td class="mat-cell" colspan="12">No Data Found</td>
            </tr>
          </mat-table>
          <div *ngIf="isLoading" class="loader">
            <mat-progress-spinner color="primary" mode="indeterminate" diameter="55" strokeWidth="5">
            </mat-progress-spinner>
          </div>

</div>
       

        <div fxLayout="row" fxFlex=5% fxFlex.sm=10% fxFlex.md=10% *ngIf="isShowTable">
        <p class="table-sub-title">Outstanding Invoices</p>
        </div>
        <div class="tbl-container" *ngIf="isShowTable">
            <!-- <div class="mat-elevation-z8" fxFlex=100%> -->
                <mat-table #table class="mat-table mat-cdk-table" [dataSource]="invoiceDataSource" 
                matSort (matSortChange)="sortData($event)">  
                <ng-container matColumnDef="slNo">
                  <mat-header-cell *matHeaderCellDef> S No. </mat-header-cell>
                  <mat-cell *matCellDef="let element" class="sno" fxLayoutAlign="start center">
                      {{element.id}}
                  </mat-cell>
              </ng-container>    
                <ng-container matColumnDef="invoiceNumber" >
                    <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Invoice No</mat-header-cell>
                    <mat-cell *matCellDef="let element">
                      <span class="mobile-label" > Invoice Amount:</span>
                    <!-- <span class="fwb font-16" data-label="invoiceNo">  -->
                      {{element.invoiceNumber}} 
                    <!-- </span> -->
                    </mat-cell>
                 </ng-container>
                   
                  <ng-container matColumnDef="invoiceDate">
                    <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Invoice Date</mat-header-cell>
                    <mat-cell *matCellDef="let element">
                      <span class="mobile-label"> Invoice Date:</span>
                    <!-- <span class="fwb font-16" data-label="invoiceDate">  -->
                      {{element.invoiceDate}} 
                    <!-- </span> -->
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="dueDate">
                    <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Due Date</mat-header-cell>
                    <mat-cell *matCellDef="let element">
                      <span class="mobile-label"> Due Date:</span>
                    <!-- <span class="fwb font-16" data-label="invoiceTo"> -->
                       {{element.dueDate}}
                       <!-- </span> -->
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="invoiceAmount">
                    <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Invoice Amount(S$)</mat-header-cell>
                    <mat-cell *matCellDef="let element">
                      <span class="mobile-label"> Invoice Amount:</span>
                    <!-- <span class="fwb font-16" data-label="invoiceAmt">  -->
                      <span class="p30"> 
                      {{element.invoiceAmount | currency:'':''}} 
                      <!-- | currency:'':'' -->
                    </span>
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="totalPaidAmount">
                    <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header>Total Amount Paid(S$)</mat-header-cell>
                    <mat-cell *matCellDef="let element">
                      <span class="mobile-label"> Total Amount Paid(S$):</span>
                    <!-- <span class="fwb font-16" data-label="invoiceAmt">  -->
                      <span class="p30">
                      {{element.totalPaidAmount | currency:'':''}} 
                    </span>
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="outstandingAmount">
                    <mat-header-cell *matHeaderCellDef class="fwb font-16" mat-sort-header [ngClass]="[this.cdgService.expSidebar ? 'mySort' : '']">Outstanding Amount(S$)</mat-header-cell>
                    <mat-cell *matCellDef="let element">
                      <span class="mobile-label"> Outstanding Amount(S$):</span>
                    <!-- <span class="fwb font-16" data-label="invoiceAmt">  -->
                      <span class="p30">
                      {{element.outstandingAmount | currency:'':''}} 
                    </span>
                    </mat-cell>
                  </ng-container>
                  <mat-header-row *matHeaderRowDef="invoiceDisplayedColumns"></mat-header-row>
                  <mat-row *matRowDef="let row; columns: invoiceDisplayedColumns;"></mat-row>
                  <tr class="mat-row" *matNoDataRow fxLayoutAlign="center center">
                    <td class="mat-cell" colspan="12">No Data Found</td>
                  </tr>
                </mat-table>
                <div *ngIf="isLoading" class="loader">
                  <mat-progress-spinner color="primary" mode="indeterminate" diameter="55" strokeWidth="5">
                  </mat-progress-spinner>
                </div>
                <mat-paginator [pageSizeOptions]="[5, 10, 20, 50, 100]" showFirstLastButtons></mat-paginator>
      
    </div>
    <div fxLayout="row" fxFlex=5% fxFlex.sm=10% fxFlex.md=10% *ngIf="!isShowTable">
      <p class="noData">{{dataResponse?.message}}</p>
      </div>
     </div>
