import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common'
import { NgForm, FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms'
import { NotificationService } from './../../shared/services/notification.service'
import { Router } from '@angular/router';
import { CdgSharedService } from './../../shared/services/cdg-shared.service'
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { CommonModalComponent } from 'src/app/shared/shared-modules/common-modal/common-modal.component';

@Component({
  selector: 'app-access-id',
  templateUrl: './access-id.component.html',
  styleUrls: ['./access-id.component.scss']
})
export class AccessIdComponent implements OnInit {
  accessform:FormGroup

  status: any = "";
  // id: any;
  // userDetails: any;
  selectedCard: any;
  constructor(public dialog: MatDialog, private location: Location, private http: HttpClient, private router: Router, public cdgService: CdgSharedService, private fb: FormBuilder, private notifyService: NotificationService, private apiService: ApiServiceService) {
    this.accessform= this.fb.group({
      accessId: ['', [Validators.required, Validators.email]],
    });
   }

  ngOnInit(): void {
    if (this.cdgService.isDataFromAccessId) {
      this.accessform.get('accessId')?.setValue(this.cdgService.rightsId)
    }
    else {
      this.cdgService.showMatrix = false;
      this.cdgService.showAccessIdDetails = false;
    }
    console.log(this.router.url)
  }

  submit() {
    if (this.accessform.invalid) {
      return;
    }
    else {
      this.cdgService.showMatrix = false;
      this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_BY_ACCESS_ID + this.accessform.value.accessId).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body.accessIdDetails;
          if (response.body.isAccessIdActive === "Y") {
            if (opt.length > 0) {
              this.cdgService.userDetails = []
              opt.forEach((element: any) => {
                let arr: any[] = []
                if (element.cardNumber) {
                  (element.cardNumber).forEach((card: any) => {
                    let obj = {
                      cardNumber: card
                    };
                    arr.push(obj)
                  })
                }
                else if (element.contactPersonId) {
                  (element.contactPersonId).forEach((person: any) => {
                    let obj = {
                      contactPersonId: person
                    };
                    arr.push(obj)
                  })
                }
                let roleName = "";
                if (element.role === RoleConstants.ROLE_CORPADMIN) {
                  roleName = "Corporate Admin Role"
                }
                else if (element.role === RoleConstants.ROLE_CORPCARDHOLDER) {
                  roleName = "Corporate Individual Role"
                }
                else if (element.role === RoleConstants.ROLE_PERSCARDHOLDER) {
                  roleName = "Personal Account Role"
                }
                else if (element.role === RoleConstants.ROLE_SUBAPPLICANT) {
                  roleName = "Subapplicant Account Role"
                }
                else if (element.role === RoleConstants.ROLE_MASTERUSER) {
                  roleName = "Master Access Role"
                }
                let obj = {
                  role: roleName,
                  details: arr,
                }
                this.cdgService.userDetails.push(obj)
              });
              if (this.cdgService.userDetails.length > 0) {
                this.cdgService.showMatrix = true;
                this.cdgService.showAccessIdDetails = true;
                this.cdgService.id = this.accessform.value.accessId;
              }
              else {
                this.cdgService.showAccessIdDetails = true;
                this.cdgService.id = this.accessform.value.accessId;
              }
              // if (response.body.isAccessIdActive === "Y") {
              this.cdgService.status = "Active"
              // }
              // else {
              //   this.cdgService.status = "Deactive"
              // }
            }
            // this.notifyService.showError(response.body.successMessage, "Success")
          }
          else if (response.body.isAccessIdActive === "N") {
            this.cdgService.status = "Deactive"
            this.cdgService.id = this.accessform.value.accessId;
            this.cdgService.showAccessIdDetails = true;
            // this.notifyService.showSuccess(response.body.statusMessage, "Success")
          }
          else if (response.body.isAccessIdActive === "" && response.body.errorMessage) {
            this.cdgService.showAccessIdDetails = false;
            this.notifyService.showError(response.body.errorMessage, "Error")
          }
        }
      },
        e => {
          this.notifyService.showError(e.errorMessage, "Error")
        })
    }
  }
  goToPrevPage() {
    this.router.navigateByUrl('/layout/admindashboard');
    this.cdgService.showMatrix = false;
  }
  aclAccessId() {
    this.cdgService.isDataFromAccessId = true;
    this.cdgService.rightsId = this.accessform.value.accessId;
    this.router.navigateByUrl('layout/rights');
  }
  activateAccessId() {
    this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.ACTIVATE_ACCESS_ID + this.accessform.value.accessId).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body;
        if (opt.statusMessage) {
          this.notifyService.showSuccess(opt.statusMessage, "Success")
          this.submit()
        }
        else {
          this.notifyService.showError(opt.errorMessage, "Error")
        }
      }
    },
      e => {
        this.notifyService.showError(e.errorMessage, "Error")
      })
  }
  deactivateAccessId() {
    const dialogConfig = new MatDialogConfig()
    dialogConfig.data = {
      title: "Confirmation",
      msg: "Are you sure you want to deactivate Access Id?",
      btnClose: "Cancel",
      btnConfirm: "Ok",
      func: 'delete'
    }
    const dialogRef = this.dialog.open(CommonModalComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.DEACTIVATE_ACCESS_ID + this.accessform.value.accessId).subscribe((response: any) => {
          if (response.status === 200) {
            let opt = response.body;
            if (opt.statusMessage) {
              this.notifyService.showSuccess(opt.statusMessage, "Success")
              this.submit()
            }
            else {
              this.notifyService.showError(opt.errorMessage, "Error")
            }
          }
        },
          e => {
            this.notifyService.showError(e.errorMessage, "Error")
          })
      }
    });
  }
  deleteAccessId() {
    const dialogConfig = new MatDialogConfig()
    dialogConfig.data = {
      title: "Confirmation",
      msg: "Are you sure you want to delete Access Id?",
      btnClose: "Cancel",
      btnConfirm: "Ok",
      func: 'delete'
    }
    const dialogRef = this.dialog.open(CommonModalComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.DELETE_ACCESS_ID + this.accessform.value.accessId).subscribe((response: any) => {
          if (response.status === 200) {
            let opt = response.body;
            if (opt.statusMessage) {
              this.notifyService.showSuccess(opt.statusMessage, "Success")
              this.accessform.controls['accessId'].reset()
              // this.submit()
              this.cdgService.status = "Deactive"
              this.cdgService.id = "";
              this.cdgService.showAccessIdDetails = false;
              this.cdgService.showMatrix = false;

            }
            else {
              this.notifyService.showError(opt.errorMessage, "Error")
            }
          }
        },
          e => {
            this.notifyService.showError(e.errorMessage, "Error")
          })
      }
    });
  }
  delete(data: any) {
    if (data.contactPersonId) {
      const dialogConfig = new MatDialogConfig()
      dialogConfig.data = {
        title: "Confirmation",
        msg: "Are you sure you want to delete Contact Person Id?",
        btnClose: "Cancel",
        btnConfirm: "Ok",
        func: 'delete'
      }
      const dialogRef = this.dialog.open(CommonModalComponent, dialogConfig);
      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.DELETE_CONTACT_PERSON + data.contactPersonId + '&accessId=' + this.accessform.value.accessId).subscribe((response: any) => {
            if (response.status === 200) {
              let opt = response.body;
              if (opt.statusMessage) {
                this.notifyService.showSuccess(opt.statusMessage, "Success")
                this.submit()
              }
              else {
                this.notifyService.showError(opt.errorMessage, "Error")
              }
            }
          },
            e => {
              this.notifyService.showError(e.errorMessage, "Error")
            })
        }
      });

    }
    else if (data.cardNumber) {
      const dialogConfig = new MatDialogConfig()
      dialogConfig.data = {
        title: "Confirmation",
        msg: "Are you sure you want to delete Card Number?",
        btnClose: "Cancel",
        btnConfirm: "Ok",
        func: 'delete'
      }
      const dialogRef = this.dialog.open(CommonModalComponent, dialogConfig);
      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.DELETE_CARD_DETAILS + this.accessform.value.accessId + "&cardNumber=" + data.cardNumber).subscribe((response: any) => {
            if (response.status === 200) {
              let opt = response.body;
              if (opt.statusMessage) {
                this.notifyService.showSuccess(opt.statusMessage, "Success")
                this.submit()
              }
              else {
                this.notifyService.showError(opt.errorMessage, "Error")
              }
            }
          },
            e => {
              this.notifyService.showError(e.errorMessage, "Error")
            })
        }
      });
    }
  }
}
