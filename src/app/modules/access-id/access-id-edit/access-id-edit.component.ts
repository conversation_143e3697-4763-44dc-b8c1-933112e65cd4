import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { CdgSharedService } from './../../../shared/services/cdg-shared.service'
import { Location } from '@angular/common'
import { FormGroup, FormControl, FormBuilder, Validators, NgForm } from '@angular/forms';
import { MatAutocompleteSelectedEvent, MatAutocomplete } from '@angular/material/autocomplete'
import { MatChipInputEvent } from '@angular/material/chips'
import { COMMA, ENTER } from '@angular/cdk/keycodes'
import { Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators'
@Component({
  selector: 'app-access-id-edit',
  templateUrl: './access-id-edit.component.html',
  styleUrls: ['./access-id-edit.component.scss']
})
export class AccessIdEditComponent implements OnInit {
  userData = {};
  status: any = 'Active';
  visible = true;
  selectable = true;
  removable = true;
  sepratorKeyCodes: number[] = [ENTER, COMMA]
  // editForm=NgForm;
  options: string[] = ['12341', '12342', '12343', '12344', '11111', '11129'];
  cardList:string[]=["***********","***********","***********","***********","***********","***********"]
  rolesList: string[] = ['Corporate Admin','Corporate Individual','Personal Accountant','Master','Sub Applicant']
  filteredOptionsContact: any;
  filteredOptionsCard: any;
  filteredOptionsRole: any;
  role: string[] = ['']
  @ViewChild('fruitsInput') fruitInput: any;
  @ViewChild('autoComp') matAutoComplete: any;
  // @Input() data:any;
  editForm :FormGroup
  constructor(private fb: FormBuilder, private location: Location, public cdgService: CdgSharedService) {
    this.editForm = this.fb.group({
      contactperosonid: ['', Validators.required],
      cardno: ['', Validators.required],
      roles: ['', Validators.required],
  
    });
  }

  ngOnInit(): void {
    this.role=[]
    this.cdgService.selectedCard.role.forEach(element => {
      this.role.push(element);
    });
    this.filteredOptionsContact = this.editForm.get('contactperosonid')!.valueChanges.pipe(
      startWith(''),
      map(value => this.filter(value))
    )
    this.filteredOptionsCard = this.editForm.get('cardno')!.valueChanges.pipe(
      startWith(''),
      map(value => this.filter(value))
    )
    this.filteredOptionsRole = this.editForm.get('roles')!.valueChanges.pipe(
      startWith(''),
      map((fruit :string | null)=> fruit ? this.roleFilter(fruit) : this.rolesList.slice())
    )
  }
  add(event: MatChipInputEvent) {
    const input = event.input;
    const value = event.value;
    if ((value || '').trim()) {
      this.role.push(value.trim());
    }
    if (input) {
      input.value = '';
    }
    this.editForm.value.roles=null;
  }
  remove(fruit:string):void{
    const index =this.role.indexOf(fruit);
    if(index >= 0 ){
      this.role.splice(index,1);
    }
  }
  selected(event :MatAutocompleteSelectedEvent):void{
    this.role.push(event.option.viewValue);
    this.fruitInput.nativeElement.value='';
    this.editForm.value.roles=null;
  }

  private roleFilter(value: string) {
    const filterValue = value.toLowerCase();
    return this.rolesList.filter(option => option.toLowerCase().indexOf(filterValue) === 0)
  }
  private filter(value: string) {
    const filterValue = value.toLowerCase();
    return this.options.filter(option => option.toLowerCase().includes(filterValue))
  }
  onSubmit(){
    this.cdgService.selectedCard.role=this.role;
    // this.cdgService.getUpdatedCardDetail(this.cdgService.selectedCard);
  }
  cancel(){
    this.location.back();
  }
  goToPrevPage() {
    this.location.back();
  }
}
