@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
.title{
    font-weight: bold;
    font-size: 20px;
    line-height: 23px;
}
.mat-icon{
    color: $cdgc-font-prime;
}
.id-style{
    font-weight: bold;
    font-size: 20px;
}
.inp{
    width: 350px !important;
}
.label{
    color: #303030;
}
.submit-btn{
    background-color: $cdgc-hue !important;
    color:$cdgc-font-accent !important;
    width: 131px !important;
    border-radius:20px !important;
    font-size: 12px !important;
}
.cancel-btn{
    background-color: $cdgc-bg-accent !important;
    color: $cdgc-font-prime;
    border: 1px solid $cdgc-border-prime;
    width: 131px !important;
    border-radius:20px !important;
    font-size: 12px !important;
}
.save-icon{
    color:$cdgc-font-accent !important;
    font-size: 20px !important;
}

@media screen and (max-width: 500px) {
    .inp{
        width: 270px !important;
    }
  }
  @media screen and (min-width: 500px) and (max-width: 800px) {
    .inp{
        width: 270px !important;
    }
  }