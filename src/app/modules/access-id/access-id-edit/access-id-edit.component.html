<div fxLayout="column" fxLayoutGap="10px" class="main-content">
	<!-- <div fxLayout="row" fxHide.lt-md> -->
	<div fxLayout="row">
		<div fxFlex fxLayout="row" fxLayoutGap="15px">
			<div class="cursor-pointer">
				<mat-icon (click)="goToPrevPage()">arrow_back</mat-icon>
			</div>
			<div>
				<span class="title">Edit Email</span>
			</div>
		</div>
	</div>
	<div class="mb20 mt20" fxLayout="row" fxLayoutGap="10px">
		<span class="id-style">{{cdgService.selectedCard.accessId}}</span>
		<mat-chip-grid> 
			<mat-chip-option>{{status}}</mat-chip-option>
		</mat-chip-grid>
	</div>
	<div fxLayout="column" fxLayoutGap="10px">
		<form [formGroup]="editForm">
			<div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
				<div fxFlex="20%"><span class="label">Contact Person ID:</span></div>
				<div fxFlex="80%">
					<mat-form-field appearance="outline" class="inp">
						<input type="text" matInput formControlName="contactperosonid"
							[(ngModel)]="this.cdgService.selectedCard.contactperosonid" [matAutocomplete]="auto"
							required>
						<mat-autocomplete #auto="matAutocomplete">
							<mat-option *ngFor="let opt of filteredOptionsContact | async" [value]="opt">
								{{opt}}
							</mat-option>
						</mat-autocomplete>
					</mat-form-field>
				</div>

			</div>
			<div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
				<div fxFlex="20%"><span class="label">Card No.:</span></div>
				<div fxFlex="80%">
					<mat-form-field appearance="outline" class="inp">
						<input type="text" matInput formControlName="cardno"
							[(ngModel)]="this.cdgService.selectedCard.cardsno" [matAutocomplete]="auto" required>
						<mat-autocomplete #auto="matAutocomplete">
							<mat-option *ngFor="let opt of filteredOptionsCard | async" [value]="opt">
								{{opt}}
							</mat-option>
						</mat-autocomplete>
					</mat-form-field>
				</div>

			</div>
			<div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
				<div fxFlex="20%"><span class="label">Card No.:</span></div>
				<div fxFlex="80%">
					<mat-form-field appearance="outline" class="inp">
						<mat-chip-grid #chipList>
							<mat-chip-row *ngFor="let data of role"
								(removed)="remove(data)">
								{{data}}
								<button matChipRemove [attr.aria-label]="'remove ' + data">
									<mat-icon>close</mat-icon>
								</button>
							</mat-chip-row>
						</mat-chip-grid>
						<input matInput formControlName="roles" [(ngModel)]="this.cdgService.selectedCard.role"
							[matAutocomplete]="autoComp" [matChipInputFor]="chipList"
							[matChipInputSeparatorKeyCodes]="sepratorKeyCodes" (matChipInputTokenEnd)="add($event)"
							required>
						<mat-autocomplete #autoComp="matAutocomplete" (optionSelected)="selected($event)">
							<mat-option *ngFor="let opt of filteredOptionsRole | async" [value]="opt">
								{{opt}}
							</mat-option>
						</mat-autocomplete>
					</mat-form-field>
				</div>

			</div>
			<div fxLayout="row" fxLayoutGap="15px">
				<div>
					<button mat-raised-button class="submit-btn" (click)="onSubmit()"><mat-icon
							class="save-icon">save</mat-icon>Save</button>
				</div>
				<div>
					<button mat-raised-button class="cancel-btn" (click)="cancel()">cancel</button>
				</div>
			</div>
		</form>
	</div>
</div>