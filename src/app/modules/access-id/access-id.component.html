<div fxLayout="column" fxLayoutGap="10px" class="main-content">
	<!-- <div fxLayout="row" fxHide.lt-md> -->
	<div fxLayout="row">
		<div fxFlex fxLayout="row" fxLayoutGap="15px">
			<div class="cursor-pointer">
				<mat-icon (click)="goToPrevPage()">arrow_back</mat-icon>
			</div>
			<div>
				<span class="title">Access ID</span>
			</div>
		</div>
	</div>
	<div>
		<span>Manage your organization’s access IDs</span>
	</div>
	<div>
		<form [formGroup]="accessform">
			<mat-form-field appearance="outline" class="inp-width">

				<input id="accessId" matInput formControlName="accessId" placeholder="Enter Email" required>
				<mat-icon matSuffix (click)="submit()" class="cursor-pointer">arrow_forward</mat-icon>
				<!-- style="background-color:white;color:black" formControlName="username" type="email"  -->
				<mat-error *ngIf="accessform.controls.accessId.hasError('required')">
					Please Enter Email
				</mat-error>
				<mat-error *ngIf="accessform.controls.accessId.hasError('email')">
					Please Enter Valid Email
				</mat-error>
			</mat-form-field>
		</form>
	</div>
	<div>
		<div class="mb20" fxLayout="row" fxLayoutGap="10px" *ngIf="cdgService.showAccessIdDetails">
			<!-- <div f?xFlex fxLayoutGap="10px" fxLayout="row" fxLayoutAlign="start center"> -->
			<span class="id-style">{{cdgService.id}}</span>
			<mat-chip-grid>
				<mat-chip>{{cdgService.status}}</mat-chip>
			</mat-chip-grid>
		</div>

		<div>
			<div fxLayout="row" fxLayout.lt-sm="column" fxLayout.sm="column" fxLayoutGap="4px"
				fxLayoutAlign="end center" class="pb10" *ngIf="cdgService.showAccessIdDetails">
				<div fxLayout="row" fxLayoutGap="4px">
					<div>
						<button mat-raised-button class="acl-btn" (click)="aclAccessId()">
							<mat-icon class="icon">view_compact</mat-icon>ACL
						</button>
					</div>
					<div *ngIf="cdgService.status == 'Deactive'">
						<button mat-raised-button class="active-btn" (click)="activateAccessId()">
							<mat-icon class="icon">check</mat-icon>Activate
						</button>
					</div>
				</div>
				<div fxLayout="row" fxLayoutGap="4px">
					<div *ngIf="cdgService.status == 'Active'">
						<button mat-raised-button class="close-btn" (click)="deactivateAccessId()">
							<mat-icon class="icon">close</mat-icon>Deactivate
						</button>
					</div>
					<div>
						<button mat-raised-button class="del-btn" (click)="deleteAccessId()">
							<mat-icon class="icon">delete</mat-icon>Delete
						</button>
					</div>
				</div>
			</div>
			<div *ngIf="cdgService.showMatrix">
				<ng-container *ngFor="let data of this.cdgService.userDetails; let i =index">
					<mat-card appearance="outlined" class="mb20">
						<div fxLayout="row">
							<div fxFlex fxLayout="row">
								<div fxFlex="30%" fxFlex.xs="40%" fxLayoutAlign="start center">
									<p class="role">{{data.role}}</p>
								</div>
								<!-- <div fxFlex="70%" fxFlex.xs="60%" fxLayoutAlign="start center">{{data.contactPersonId}}</div> -->
							</div>
						</div>
						<ng-container *ngFor="let val of data.details; let i =index">
							<div fxLayout="row" *ngIf="val.contactPersonId">
								<div fxFlex fxLayout="row" >
									<div fxFlex="30%" fxFlex.xs="40%" fxLayoutAlign="start center">
										<p style="margin-bottom:0px;">Contact Person ID :</p>
									</div>
									<div fxFlex="70%" fxFlex.xs="60%" fxLayoutAlign="start center">{{val.contactPersonId}}</div>
								</div>
								<div fxFlex fxLayoutAlign="end center">
									<button mat-icon-button  (click)="delete(val)">
										<mat-icon class="del-icon">delete_forever</mat-icon>
									</button>
								</div>
							</div>
								<div fxLayout="row" *ngIf="val.cardNumber">
									<div fxFlex="20%">
										<p>Card No.:</p>
									</div>
									<div fxFlex="80%">{{val.cardNumber}}</div>
									<div fxFlex fxLayoutAlign="end center">
										<button mat-icon-button  (click)="delete(val)">
											<mat-icon class="del-icon">delete_forever</mat-icon>
										</button>
									</div>
								</div>
						</ng-container>
					
						
						<!-- <div fxLayout="row">
				<div fxFlex="30%" fxFlex.lt-sm="40%"><p>Role:</p></div>
				<div fxFlex="70%" fxFlex.lt-sm="60%">{{data.role.join(', ')}}</div>
			</div> -->
						
					</mat-card>
				</ng-container>
			</div>
		</div>
	</div>
</div>




<!-- <app-access-id-edit [data]="selectedCard"></app-access-id-edit> -->