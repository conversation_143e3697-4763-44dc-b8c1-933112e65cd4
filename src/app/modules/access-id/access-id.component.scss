@import '@angular/material/theming';
@import './../../../styles/colors.scss';
@import './../../../styles/sizing.scss';
@import './../../../styles/main.scss';
.title{
    font-weight: bold;
    font-size: 20px;
    line-height: 23px;
}
.mat-icon{
    color: $cdgc-font-prime;
}
.id-style{
    font-weight: bold;
    font-size: 20px;
}
.inp-width{
    width: 350px;
}
.mat-mdc-card{
    background-color: $cdgc-bg-mild !important;
    border-radius: 5px !important;
    border: 1px solid $cdgc-border-prime !important;
    padding: 10px !important;
}
.p {
    font-weight: $cdgsz-font-weight-normal !important;
}
.icon{
    color:$cdgc-bg-accent !important;
    font-size: 22px !important;
}
.acl-btn{
    background-color: #519585 !important;
    width: 170px !important;
    font-size: 12px !important;
}
.active-btn{
    background-color: $green !important;
    width: 170px !important;
    font-size: 12px !important;

}
.close-btn{
    background-color: #10A2F5 !important;
    width: 170px !important;
    font-size: 12px !important;

}
.del-btn{
    background-color:$cdgc-warn !important;
    width: 170px !important;
    font-size: 12px !important;

}
.del-icon{
    color:$cdgc-warn !important;
}
.role{
    margin-bottom:0px;
    font-weight:  $cdgsz-font-weight-bold !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version. */
::ng-deep .mat-mdc-raised-button.mat-button-disabled{
    background-color:  #808080 !important;
}
@media screen and (max-width: 600px) {
    .acl-btn{
        width: 131px !important;
    }
    .active-btn{
        width: 131px !important;
    
    }
    .close-btn{
        width: 131px !important;
    
    }
    .del-btn{
        width: 131px !important;    
    }
}

@media screen and (max-width: 500px) {
    .inp-width{
        width: 250px !important;
    }
  }
  @media screen and (min-width: 500px) and (max-width: 800px) {
    .inp-width{
        width: 250px !important;
    }
  }

  .mat-mdc-chip{ //change by navani 
    background-color: #006ba8 !important;
    color: white !important;
}
