<div fxLayout="column" fxLayoutGap="50px">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">assignment_turned_in</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">Assign / Return Card</span>
        </div>
    </div>
    <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
        <div fxFlex>
            <mat-button-toggle-group fxFlex fxLayoutAlign="start center" #toggleGroup="matButtonToggleGroup"
                [value]="selectedVal">
                <mat-button-toggle value="assign">
                    <div fxLayout="row" fxLayoutGap="15px" class="toggle-div">
                        <p class="login-type">Assign Card</p>
                    </div>

                </mat-button-toggle>
                <mat-button-toggle value="return" class="toggle-div">
                    <div fxLayout="row" fxLayoutGap="15px">
                        <p class="login-type">Return Card</p>
                    </div>
                </mat-button-toggle>
            </mat-button-toggle-group>
        </div>
        <div *ngIf="toggleGroup.value == 'assign'">
            <app-assign-card></app-assign-card>
        </div>
        <div *ngIf="toggleGroup.value == 'return'">
            <app-return-card></app-return-card>
        </div>
    </div>
</div>