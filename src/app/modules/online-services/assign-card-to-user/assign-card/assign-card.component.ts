import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { Validators, FormBuilder, FormGroup, AbstractControl, ValidatorFn } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { Observable, of } from 'rxjs';
import { DatePipe } from '@angular/common';
import { MtxDatetimepickerType } from '@ng-matero/extensions/datetimepicker';
import * as moment from 'moment';
import { catchError } from 'rxjs/operators';


export interface Assign {
  cardNo: number,
  reason: string,

}
const DATA: Assign[] = [];

@Component({
  selector: 'app-assign-card',
  templateUrl: './assign-card.component.html',
  styleUrls: ['./assign-card.component.scss'],
  providers: [DatePipe]
})
export class AssignCardComponent implements OnInit {
  productArr: any = [];
  cards: any = [];
  @ViewChild('picker') picker: any;

  assignForm:FormGroup
  // mtxType!:MtxDatetimepickerType


  displayedColumns = ['cardNo', 'reason']
  dataSource = new MatTableDataSource(DATA);
  dataSourceCell: Observable<any>
  // sortedData: DownloadInvoice[];
  cardArr: any = [];
  responsearray: any = [];
  showdiv: boolean = false;
  isCardNoShow: boolean = false;
  isCardStartEndShow: boolean = false;
  isCardno: boolean = false;
  isCardStart: boolean = false;
  isCardEnd: boolean = false;
  showcarderror: boolean = false;
  cardNo: any;
  isCardSelected: boolean = false;
  division: string;
  dept: string;
  expiryDate: string;
  cardlist: any = [];
  name: string;
  status: string;
  firstdigit: any;
  lastdigit: any = [];
  list: any;
  value: any;
  displaycard: any;
  display: any = [];
  accountnumber: any;
  cardNostring: any;
  date: any;



  constructor(private apiService: ApiServiceService, public datepipe: DatePipe, public localStorage: LocalStorageService, private fb: FormBuilder, public cdgService: CdgSharedService, private notifyService: NotificationService) { 
    this.assignForm = this.fb.group({
      productType: ['', [Validators.required]],
      name: ['', [Validators.required]],
      issueDate: ['', [Validators.required]],
      cardNo: [''],
      cardFrom: [''],
      cardTo: ['', [greaterThanValidator('cardFrom'), Validators.required]]
    })
  }


  ngOnInit(): void {
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber= this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }
    }
    let prodtypeobj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accountNo": this.accountnumber,
      "masterAccountNo": Number(this.accountnumber)
    }

    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_PRODUCTTYPE, prodtypeobj).subscribe((response: any) => {
      if (response.status === 200) {
        //  console.log(response)
        let opt = response.body.productType;


        opt.forEach((element: any) => {
          this.productArr.push(element);
          //  console.log(this.productArr);
        });
      }

    });


  }
  
  
  getCardDetails(item: any) {
    this.cards = []; // onchanging the productTypeId need to reset this array. Because this array contains the cardnumbers of the earlier selected productTypeId.
    this.displaycard = "";
    this.assignForm.controls['cardNo'].reset();
    this.assignForm.controls['cardNo'].setErrors(null);
    this.showdiv = false;
    this.division = "";
    this.dept = "";
    this.expiryDate = "";
    this.name = "";
    this.status = "";
    this.isCardSelected = false;
    this.isCardNoShow = false;
    this.showcarderror = false;
    this.assignForm.controls['name'].reset();
    this.assignForm.controls['issueDate'].reset();    
    if (item.value === 'OV' || item.value === 'VV' || item.value === 'TP') {
      this.isCardStartEndShow = true;
      this.isCardNoShow = false;
    } else {
      this.isCardStartEndShow = false;
      this.isCardNoShow = true;
    }

    //  console.log("Assign_Form: ", this.assignForm)

    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      "productType": item.value,
      "accountNo": this.accountnumber,
      "masterAccountNo": Number(this.accountnumber)
    }

    if (item.value !== "OV") {
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_CARDLIST, obj).subscribe
        ((response: any) => {
          this.cards = [];
          if (response.status === 200) {
            let opt = response.body.cardNo;
            if (!opt.length) {
              this.showcarderror = true;
            }
            else {
              this.showcarderror = false;
            }
            this.firstdigit = String(opt[0]).substr(0, 10);
            this.displaycard = this.firstdigit.substr(0, 6) + "-" + this.firstdigit.substr(6, 4);
            // opt.forEach((element: any) => {
            //   this.cards.push((String(element).substr(-6)));
            // });
          }
        });
    }
  }

  cardDetails(type: string) {

    if (type === 'cardNo') {
      if (this.assignForm.value.cardNo) {

        this.isCardno = false;

      }
      else {
        this.isCardno = true;
      }
    }
    else if (type === 'cardFrom') {
      if (this.assignForm.value.cardFrom) {
        this.isCardStart = false
      }
      else {
        this.isCardStart = true
      }
    }
    else if (type === 'cardTo') {
      if (this.assignForm.value.cardTo) {
        this.isCardEnd = false
      }
      else {
        this.isCardEnd = true
      }
    }
  }

  focusOutFunction(){
     
    //checking cardNo is null or empty. if is empty no need to call the api
    if(!this.checkValidCard(this.assignForm.value.cardNo)){
      this.isCardSelected = false;
       
      return;
    }
    this.cardNo = this.assignForm.value.cardNo.toString();
    //make api call and retrieve the info for selected card no and bind it to view


    if(this.displaycard != ''){
      this.cardNostring = this.firstdigit + this.assignForm.value.cardNo.toString();
    }else{ 
      this.cardNostring = this.assignForm.value.cardNo.toString();
    }
    this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.GET_CARDDETAILSASSIGNCARD + this.cardNostring).subscribe((response: any) => {
      if (response.status === 200 && response.body.cardDetailsDto) {
        let opt = response.body.cardDetailsDto;
        this.division = opt.accountNameToDisplay;
        this.dept = opt.accountNameToDisplay;
        this.expiryDate = opt.expiryDate;
        // this.name = opt.cardHolderName;
        this.name = opt.nameOnProduct;
        this.status = opt.status;
        this.isCardSelected = true;
      }else{
        this.isCardSelected = false;
      }
    });    
  }

  checkValidCard(value : any){
     
    if(!value){   //checking here "" & null;
      return false;
       
    }else{
      return true;
    }
  }

  submit(isValid: boolean) {
     
    //  console.log( this.assignForm);
    if (isValid) {
       
      //  console.log("Date3: v",this.assignForm.controls.issueDate.value);
      if (this.assignForm.value.productType === 'OV' || this.assignForm.value.productType === 'VV' || this.assignForm.value.productType === 'TP') {
        if (this.checkValidCard(this.assignForm.value.cardFrom) && this.checkValidCard(this.assignForm.value.cardTo)) {
           
          this.isCardStart = false
          this.isCardEnd = false
          this.date = moment(this.assignForm.controls.issueDate.value, 'DD/MM/YYYY, HH:mm:ss').format('DD-MM-YYYY HH:mm:ss')
           console.log('Date2:',this.date);
          let obj = {
            "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
            "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
            "accountNumber": this.accountnumber,  //==> not mandatory for corpadmin
            "productTypeId": this.assignForm.controls.productType.value,
            "cardNoFrom": this.assignForm.controls.cardFrom.value,
            "cardNoTo": this.assignForm.controls.cardTo.value,
            "issuedTo": this.assignForm.controls.name.value,
            "issuedOn": this.date
          }
           
          this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS+ UrlConstants.ASSIGNCARD_SUBMIT, obj)
          .pipe(
            catchError(error => { 
              console.error('Error caught:', error);
              this.notifyService.showError(error.message, 'Assign Card')
              return of(null);  
            })           
          )
          .subscribe((response: any) => {
            if (response.status === 200) {
              this.showdiv = true;
              this.dataSource.data = response.body;
              if (response.body[0].STATUS === 'SUCCESS') {
                this.clearGotoMainScreen();
                this.notifyService.showSuccess('Your request has been successfully submitted for processing', 'Assign Card')
              }

            }
            else {
              this.notifyService.showError(response.message, 'Assign Card')
            }
          });
        }
        else if (this.assignForm.value.cardFrom) {
          this.isCardStart = true
        }
        else if (this.assignForm.value.cardTo) {
          this.isCardEnd = true
        }
      } else {
        if (this.checkValidCard(this.assignForm.value.cardNo)) {
          this.isCardno = false;
          this.date = moment(this.assignForm.controls.issueDate.value,'DD/MM/YYYY, HH:mm:ss').format('DD-MM-YYYY HH:mm:ss')
          console.log('Date3:',this.date);    
                let obj = {
            "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
            "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
            "accountNumber": this.accountnumber,  //==> not mandatory for corpadmin
            "productTypeId": this.assignForm.controls.productType.value,
            "cardNoFrom": this.firstdigit+this.assignForm.controls.cardNo.value,
            "cardNoTo": this.firstdigit+this.assignForm.controls.cardNo.value,
            "issuedTo": this.assignForm.controls.name.value,
            "issuedOn": this.date
          }
          this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.ASSIGNCARD_SUBMIT, obj).subscribe((response: any) => {
            if (response.status === 200) {
            
              this.dataSource.data = response.body;
              if (response.body[0] && response.body[0].STATUS === 'SUCCESS') {
                this.clearGotoMainScreen();
                this.notifyService.showSuccess('Your request has been successfully submitted for processing', 'Assign Card')
              }
              else{
                this.showdiv = true;
              }

              // this.responsearray.forEach((element:any) => {
              //   this.dataSource.data=element;
              // });
            }
            else {
              this.notifyService.showError(response.message, 'Assign Card')
            }
          });

        }
        else {
          this.isCardno = true;


        }
      }
      // this.notifyService.showSuccess('Details Updated Successfully','Success')
    }
    else {
      this.notifyService.showWarning('Mandatory Fields cannot be blank', 'Warning')
    }
  }

  clearGotoMainScreen(){
    this.displaycard = "";    
    this.showdiv = false;
    this.division = "";
    this.dept = "";
    this.expiryDate = "";
    this.name = "";
    this.status = "";
    this.isCardSelected = false;
    this.isCardNoShow = false;
    this.showcarderror = false;
    this.assignForm.reset();
    this.assignForm.controls['productType'].setErrors(null);
    this.assignForm.controls['name'].setErrors(null);
    this.assignForm.controls['issueDate'].setErrors(null);
  }
  
  formatChange(event: any) {
    //  console.log("EVENt: ", event.value)
    const data:any = moment(event.value).format('DD/MM/YYYY, HH:mm:ss')
    //  console.log("Date:", data);
    this.assignForm.patchValue({
      issueDate: data //
    })
    // this.assignForm.value.issueDate=data;
    //  console.log("Assign_Form: ", this.assignForm.value.issueDate)
  }

  
}
export function greaterThanValidator(controlNameToCompare: string): ValidatorFn {
  return (control: AbstractControl): {[key: string]: any} | null => {
    const controlToCompare = control.root.get(controlNameToCompare);
    if (controlToCompare) {
      const subscription = controlToCompare.valueChanges.subscribe(() => {
        control.updateValueAndValidity();
        subscription.unsubscribe();
      });
    }
    if(controlToCompare && controlToCompare.value !== undefined && control.value !== undefined ){
      const compareFrom = controlToCompare.value.toString();
      const compareTo = control.value.toString();
      return BigInt(compareTo) > BigInt(compareFrom) + BigInt(10000) ? {'greaterThan': true} : null; 
    }
     return null;
  };
}