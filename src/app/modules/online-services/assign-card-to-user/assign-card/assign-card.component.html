<form [formGroup]="assignForm">
    <div fxLayout="column" fxLayoutGap="20px">
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">PRODUCT TYPE <span class="asterisk"><sup>*</sup></span>:</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%">
                <mat-form-field appearance="outline" class="inp">
                    <mat-select name="productType" formControlName="productType"
                        (selectionChange)="getCardDetails($event)" placeholder="Select Product Type">
                        <mat-option *ngFor="let acl of productArr" [value]="acl.productTypeId">
                            {{acl.name}}
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="assignForm.controls.productType.hasError('required')" class="pt15">
                        Product Type Cannot Be Blank
                    </mat-error>
                </mat-form-field>
            </div>
        </div>

        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="isCardNoShow">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">CARD NO.<span class="asterisk"><sup>*</sup></span>:</span>
            </div>
            <div fxFlex="91%" fxFlex.lt-md="65%" fxFlex.md="75%" fxLayout="row" fxLayoutAlign="start center">
                <span class="pb10" *ngIf="!showcarderror">{{this.displaycard}} </span>
                <mat-error *ngIf="showcarderror">
                    No Card Details Found
                </mat-error>
                <mat-form-field appearance="outline" class="inp mat-wrap" *ngIf="!showcarderror">
                    <input matInput type="text" pattern="[0-9]*" name="cardNo" formControlName="cardNo"
                        (focusout)="focusOutFunction()" required />
                    <mat-error *ngIf="assignForm.controls.cardNo.hasError('required')" class="pt15">
                        Card No. Is Required
                    </mat-error>
                    <mat-error *ngIf="assignForm.controls.cardNo.hasError('pattern')" class="pt15">
                        Please enter a number
                    </mat-error>
                </mat-form-field>
            </div>
        </div>
        <div *ngIf="isCardSelected && isCardNoShow" fxLayout="column" fxLayoutGap="10px">
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <span fxFlex="15%" fxFlex.lt-md="40%">DIVISION/DEPARTMENT NAME:</span>
                <span>{{division}}</span>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <span fxFlex="15%" fxFlex.lt-md="35%">EXPIRY DATE:</span>
                <span>{{expiryDate}}</span>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <span fxFlex="15%" fxFlex.lt-md="35%">NAME ON PRODUCT:</span>
                <span>{{name}}</span>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <span fxFlex="15%" fxFlex.lt-md="35%">STATUS:</span>
                <span>{{status}}</span>
            </div>
        </div>
        <div fxLayout="column" fxLayoutGap="20px" *ngIf="isCardStartEndShow">
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">CARD NO. FROM<span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%">
                    <mat-form-field appearance="outline" class="inp" *ngIf="!showcarderror">
                        <input matInput type="text" pattern="[0-9]*" name="cardFrom" formControlName="cardFrom"
                            required />
                        <mat-error *ngIf="assignForm.controls.cardFrom.hasError('required')" class="pt15">
                            Card No From. Cannot Be Blank
                        </mat-error>
                        <mat-error *ngIf="assignForm.controls.cardFrom.hasError('pattern')" class="pt15">
                            Please enter a number
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">CARD NO. TO<span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%">
                    <mat-form-field appearance="outline" class="inp" *ngIf="!showcarderror">
                        <input matInput type="text" pattern="[0-9]*" name="cardTo" formControlName="cardTo" required />
                        <mat-error *ngIf="assignForm.controls.cardTo.hasError('required')" class="pt15">
                            Card No To. Cannot Be Blank
                        </mat-error>
                        <mat-error *ngIf="assignForm.controls.cardTo.hasError('pattern')" class="pt15">
                            Please enter a number
                        </mat-error>
                        <mat-error *ngIf="assignForm.controls.cardTo.hasError('greaterThan')" class="pt15">
                            Card Number Range Of A Min 1pc And Max 10,000pcs
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
        </div>
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">NAME<span class="asterisk"><sup>*</sup></span>:</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%">
                <mat-form-field appearance="outline" class="inp">
                    <input matInput name="name" autocomplete="off" formControlName="name">
                    <!-- onkeypress="return event.charCode >= 65 && event.charCode <=90 || event.charCode >= 95 && event.charCode <=122 || event.charCode === 32" -->
                    <mat-error *ngIf="assignForm.controls.name.hasError('required')" class="pt15">
                        Name Cannot Be Blank
                    </mat-error>
                </mat-form-field>
            </div>
        </div>
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">ISSUE DATE<span class="asterisk"><sup>*</sup></span>:</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%">

                <mat-form-field appearance="outline" class="inp">
                    <input matInput [mtxDatetimepicker]="picker" name="issueDate" autocomplete="off"
                        formControlName="issueDate"  (dateChange)="formatChange($event)">
                    <mtx-datetimepicker-toggle matSuffix [for]="$any(picker)"></mtx-datetimepicker-toggle>
                    <mtx-datetimepicker #picker multiYearSelector="true" type="datetime"></mtx-datetimepicker>
                    <mat-error *ngIf="assignForm.controls.issueDate.hasError('required')" class="pt15">
                        Issue Date Cannot Be Blank
                    </mat-error>
                </mat-form-field>
            </div>
        </div>
        <div fxFlex class="mb20">
            <button mat-raised-button class="update-btn" (click)="submit(assignForm.valid)">SUBMIT</button>
        </div>
        <div class="tbl-container pt15 pl20 pr20" *ngIf="showdiv">
            <mat-table #table class="mat-table mat-cdk-table" [dataSource]="dataSource" matSort>


                <!-- Serial No. Column  -->
                

                <!-- Card No. Column -->
                <ng-container matColumnDef="cardNo">
                    <mat-header-cell *matHeaderCellDef class="fwb font-16">Card No</mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label"> CARD No.:</span>
                        <div class="word-break width80">
                            {{element.CARD_NO }}
                        </div>

                    </mat-cell>
                </ng-container>

                <!-- Job No. Column -->
                <ng-container matColumnDef="reason">
                    <mat-header-cell *matHeaderCellDef class="fwb font-16">Reason</mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label"> REASON.:</span>
                        {{element.REASON}}
                    </mat-cell>
                </ng-container>

                <!-- Taxi No. Column -->
                <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                <mat-row *matRowDef="let row; columns: displayedColumns;"
                    [ngStyle]="{'background-color': row.colorHex ? row.colorHex : '#cccccc'}"></mat-row>
                <!-- <tr class="mat-row" *matNoDataRow fxLayoutAlign="center center">
                    <td class="mat-cell" colspan="4">No Data Present</td>
                  </tr> -->
            </mat-table>
            <tr class="mat-row" *ngIf="dataSource.data.length == 0" fxLayoutAlign="center center">
                <td class="mat-cell" colspan="5">No Records are Found</td>
            </tr>
            <!-- <mat-paginator class="paginator" [pageSizeOptions]="[5, 10, 20, 50, 100]"
                showFirstLastButtons></mat-paginator> -->
        </div>

    </div>
</form>