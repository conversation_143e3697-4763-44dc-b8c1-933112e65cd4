@import '@angular/material/theming';
@import './../../../../../styles/colors.scss';
@import './../../../../../styles/sizing.scss';
@import './../../../../../styles/main.scss';
.inp{
    width: 260px !important;
}
::ng-deep th.mat-mdc-header-cell{
    height: 36px !important;
     padding-top: 0 !important;
  }
  .mat-mdc-header-row{
    height: auto !important;
  }

  .width80{
    width:80% 
  }
  .w20{
    width:20% 
  }

::ng-deep .mat-column-cardNo{
  width: 80% !important;
}
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
::ng-deep .main-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
}
.update-btn{
    width:205px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
}
.inp{
    width: 320px !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
 }
 .error{
    font-size: $cdgsz-font-size-sm;
    color: $cdgc-font-warn;
}
.pl{
    padding-left: 252px !important;
}
.search-btn{
    width:205px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 5px !important;
    margin-bottom: 10px;
}
.reset-btn{
  width:205px;
  background-color:$cdgc-warn !important;
  color: $cdgc-font-accent !important;
  border-radius: 5px !important;
  margin-bottom: 10px;
}
.download-btn{
    width:205px;
    background-color:$cdgc-font-accent !important;
    color: $cdgc-bg-hue !important;
    border: 1px solid $cdgc-bg-hue;
    border-radius: 5px !important; 
    margin-bottom: 10px;
}

table {
    width: 100%;
    font-family: Roboto;
    font-style: normal;
    text-align: center;
  
  }

  .mat-mdc-header-cell{
    font-weight: $cdgsz-font-weight-normal !important;
    font-size: $cdgsz-font-size-sm !important;
    // line-height: 17px !important;
    background-color: $cdgc-bg-prime;
    color: $cdgc-font-accent;
    height: 36px !important;
    text-align: center;
  }

  .mat-mdc-header-cell:first-child{
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
  }

  .mat-mdc-header-cell:last-child{
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
  }

  .mat-mdc-cell{
    font-weight: $cdgsz-font-weight-normal !important;
    // font-size: $cdgsz-font-size-prime;
    // line-height: 18px;
    font-size: 14px;
    line-height: 16px;
    // text-align: center !important;
 }

 .mat-column-pickupDest{
    width:14% !important;
    padding: 0px 4px 0px 6px !important; 
  }
 

  .mat-column-travelPeriod{
    width:15% !important;
    padding: 0 0px 0 10px;
  }
 

  .download-icon{
    color: $cdgc-font-prime;
    cursor: pointer;
}

// ::ng-deep .mat-sort-header-arrow{
//     transform: none !important;
//     opacity: 1 !important;
//     color: $cdgc-bg-accent;
//   }

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .paginator .mat-form-field-appearance-legacy .mat-mdc-form-field-text-infix{
    padding: 1.5em 0 !important;
}


/* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
:host ::ng-deep .paginator .mat-mdc-select-arrow{
    
    margin-top:0px !important;
    

}


  // ::ng-deep .mat-sort-header.ng-trigger.ng-trigger-arrowPosition{
  //   opacity: 0 !important;
  // }
  
  // ::ng-deep .ng-trigger-leftPointer{
  //   opacity: 0 !important;
  // }
  
  // ::ng-deep .ng-trigger-rightPointer{
  //   opacity: 0 !important;
  // }
  
  // ::ng-deep .mat-sort-header-stem {
  //   display: none !important;
  // }
  
  // ::ng-deep .mat-sort-header-pointer-left .mat-sort-header-pointer-right{
  //   display: none !important;
  // }
  
  // ::ng-deep .mat-sort-header-pointer-middle{
  //   width: 0px !important;
  //   height: 0px !important;
  // }
  
  // ::ng-deep .mat-sort-header-arrow{
  //      .mat-sort-header-indicator{
  //        &::before {
  //         //  content: "<>";
  //         content: "\25c2 \25b8";
  //          position: absolute;
  //          opacity : 1 !important;
  //          color: $cdgc-bg-accent !important;
  //          font-size: 1.2 em !important ;
  //          font-weight: bold;
  //         transform: translate(-10%, 20%) rotate(90deg) !important;
  //        }
  //      }
  // }
  
  // [aria-sort="ascending"] {
  //  ::ng-deep .mat-sort-header-arrow{
  //     .mat-sort-header-indicator{
  //       &::before {
  //         content: "\25b4";
  //         position: absolute;
  //         opacity : 1 !important;
  //         color: $cdgc-bg-accent !important;
  //         font-size: 1.2 em !important;
  //         font-weight: bold;
  //         transform: translate(0, 0)  !important;
  //       }
  //     }
  // }
  // }
  
  // [aria-sort="descending"] {
  //   ::ng-deep .mat-sort-header-arrow{
  //      .mat-sort-header-indicator{
  //        &::before {
  //          content: "\25be";
  //          position: absolute;
  //          opacity : 1 !important;
  //          color: $cdgc-bg-accent !important;
  //          font-size: 1.2 em !important;
  //          font-weight: bold;
  //          transform: translate(0,-10%)  !important;
  //        }
  //      }
  //  }
  //  }

  //  ::ng-deep .text-right .mat-sort-header-container{
  //   justify-content: center !important;
  // }

  .card-table{
    border-radius: 12px !important;
    border: 1px solid $cdgc-border-prime;
    margin-bottom: 10px !important;
  }

  .label{
    font-weight: $cdgsz-font-weight-bold;
  }

  .mb-0{
    margin-bottom: 0 !important;
  }

  .cell-inp{
    width: 100%;
  }
  .construction-icon{
    font-size: 32px !important;
  }
  .setting-font{
    color: $cdgc-font-prime;
  }
  .inp-cardno{
    width: 150px !important;
  }
  @media screen and (max-width: 500px) {
    .inp-cardno{
        width: 95px !important;
    }
    .inp{
      width: 100% !important;
    }
 }

 .mt20{
   margin-top: 20px !important;
 }

 .sno{
  display: block;
}
.mobile-label{
  display: none;
}
@media screen and (max-width: 600px) {
  .mobile-label{
      display: inline-block;
      width: 72px !important;
      font-weight: $cdgsz-font-weight-bold;
  }
  .mat-mdc-header-row{
      display: none;
  }
  .mat-mdc-row{
      flex-direction: column;
      align-items: start;
      padding: 8px 24px;
      border-radius: 12px !important;
      border: 1px solid $cdgc-border-prime;
      min-height: 28px !important;
      margin-bottom: 10px;
  }
  .mat-column-pickupDest{
    width:100% !important;
    padding: 8px 24px  8px 0px!important;  
  }

  .mat-column-travelPeriod{
    width:100% !important;
    padding: 8px 24px 8px 0px!important;
  }
 
  .mat-mdc-footer-row{
    flex-direction: column;
    align-items: start;
    padding: 8px 24px;
    border-radius: 12px !important;
    border: 1px solid $cdgc-border-prime;
    min-height: 28px !important;
    margin-bottom: 10px;
}
  :host ::ng-deep  mat-cell:first-of-type, mat-header-cell:first-of-type, mat-footer-cell:first-of-type {
   padding-left: 0px !important;
  }
  .sno{
      display: none !important;
  }
}
.update-btn{
    width:205px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
}
.login-type{
    font-size: $cdgsz-font-size-prime;
    font-weight: $cdgsz-font-weight-normal;
    margin-bottom: 0px !important;
    // padding-top: 10px;
}
.error {
    color: $red;
    font-weight: $cdgsz-font-weight-bold;
    font-size: 12px;
    height: 12px;
    padding-left: 10px;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-form-field-text-infix {
    padding-bottom: 8px !important;
 }
 /* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
 :host ::ng-deep .mat-wrap .mat-mdc-form-field-text-infix {
    padding-bottom: 0px !important;
 }
 @media screen and (max-width: 600px) {
    .inp{
        width: 183px !important;
    }
 }