import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Val<PERSON><PERSON>, FormBuilder, FormGroup } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import * as moment from 'moment';
import { element } from 'protractor';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';

@Component({
  selector: 'app-return-card',
  templateUrl: './return-card.component.html',
  styleUrls: ['./return-card.component.scss'],
  providers: [DatePipe]
})
export class ReturnCardComponent implements OnInit {
  productArr: any[] = [];
  cards: any[] = [];
  returnForm: FormGroup
  cardArr: any[] = []
  isCardNoShow: boolean = false;
  isCardStartEndShow: boolean = false;
  disablesubmit: boolean = false;
  cardNo: any;
  isCardSelected: boolean = false;
  division: string;
  dept: string;
  expiryDate: string;
  name: string;
  status: string;
  accountnumber: any;
  cardlist: any[] = [];
  firstdigit: string;
  displaycard: string;
  showcarderror: boolean = false;
  showcarderrorto: boolean = false;
  showcarderrorfrom: boolean = false;
  cardNolist: any[] = [];
  showCardErrorReq: boolean = false;
  showCardFromErrorReq: boolean;
  showCardToErrorReq: boolean;
  dataList: any[] = [];
  returnedOn: any
  public displayedColumns: any[] = [];
  dataSource = new MatTableDataSource<any>(this.dataList);
  isShowTable: boolean = false;
  isShowBtn: boolean = false;
  productType: any;
  cardNostring: any;
  response: any[] = [];
  constructor(public datepipe: DatePipe, private apiService: ApiServiceService, public localStorage: LocalStorageService, private fb: FormBuilder, public cdgService: CdgSharedService, private notifyService: NotificationService) {
    this.returnForm = this.fb.group({
      productType: ['', [Validators.required]],
      // name: [''],
      // withDate: [true],
      cardNo: [''],
      cardFrom: [''],
      cardTo: [''],
      // returnDate: ['', [Validators.required]],

    })
  }

  ngOnInit(): void {
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }
    }
    let prodtypeobj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      "accountNo": this.accountnumber,
      "masterAccountNo": Number(this.accountnumber)
    }

    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_PRODUCTTYPE, prodtypeobj).subscribe((response: any) => {
      if (response.status === 200) {
        //  console.log(response)
        let opt = response.body.productType;
        opt.forEach((element: any) => {
          this.productArr.push(element);
          //  console.log(this.productArr);
        });
      }

    });
  }
  getCardDetails(selected: any) {
    this.cards = []; // onchanging the productTypeId need to reset this array. Because this array contains the cardnumbers of the earlier selected productTypeId.
    this.displaycard = "";
    this.returnForm.controls['cardNo'].reset();
    this.returnForm.controls['cardNo'].setErrors(null);
    if (selected.value) {
      this.showcarderror = false;
      this.disablesubmit = false;
      this.showcarderrorto = false;
      this.showcarderrorfrom = false;
    }
    if (selected.value === 'OV' || selected.value === 'VV' || selected.value === 'TP') {
      this.isCardStartEndShow = true;
      this.isCardNoShow = false;
    } else {
      this.isCardStartEndShow = false;
      this.isCardNoShow = true;
    }

    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      "productType": selected.value,
      "accountNo": this.accountnumber,
      "masterAccountNo": Number(this.accountnumber)
    }
    if (selected.value !== "OV") {
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_CARDLIST, obj).subscribe
        ((response: any) => {
          this.cards = [];
          if (response.status === 200) {
            let opt = response.body.cardNo;
            if (!opt.length) {
              this.showcarderror = true;
            }
            else {
              this.showcarderror = false;
            }
            this.firstdigit = String(opt[0]).substr(0, 10);
            this.displaycard = this.firstdigit.substr(0, 6) + "-" + this.firstdigit.substr(6, 4);
          }
        });
    }
  }

  onCardSelect(item: any) {
    this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.GET_CARDDETAILSASSIGNCARD + this.returnForm.value.cardNo.toString()).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body.cardDetailsDto;
        this.division = opt.accountNameToDisplay;
        this.dept = opt.accountNameToDisplay;
        this.expiryDate = opt.expiryDate;
        // this.name = opt.cardHolderName;
        this.name = opt.nameOnProduct;
        this.status = opt.status;
        this.isCardSelected = true;
      }
    });
  }

  checkValidCard(value: any) {
    if (!value) { //checking here "" & null
      return false;
    } else {
      return true;
    }
  }

  focusOutFunction() {

    //checking cardNo is null or empty. if is empty no need to call the api
    if (!this.checkValidCard(this.returnForm.value.cardNo)) {
      this.isCardSelected = false;
      return;
    }
    this.cardNo = this.returnForm.value.cardNo.toString();
    //make api call and retrieve the info for selected card no and bind it to view

    if(this.displaycard != ''){
      this.cardNostring = this.firstdigit + this.returnForm.value.cardNo.toString();
    }else{ 
      this.cardNostring = this.returnForm.value.cardNo.toString();
    }

    this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.GET_CARDDETAILSASSIGNCARD + this.cardNostring).subscribe((response: any) => {
      if (response.status === 200 && response.body.cardDetailsDto) {
        let opt = response.body.cardDetailsDto;
        this.division = opt.accountNameToDisplay;
        this.dept = opt.accountNameToDisplay;
        this.expiryDate = opt.expiryDate;
        // this.name = opt.cardHolderName;
        this.name = opt.nameOnProduct;
        this.status = opt.status;
        this.isCardSelected = true;
      } else {
        this.isCardSelected = false;
      }
    });
  }

  search(isValid: any) {
    if (isValid) {
      if (this.returnForm.value.productType === 'OV' || this.returnForm.value.productType === 'VV' || this.returnForm.value.productType === 'TP') {
        this.showCardFromErrorReq = false;
        this.showCardToErrorReq = false;
        if (!this.returnForm.value.cardFrom && !this.returnForm.value.cardTo) {
          this.showCardFromErrorReq = true;
        }
        else if (!this.returnForm.value.cardFrom) {
          this.showCardFromErrorReq = true;
        }
        else if (!this.returnForm.value.cardTo) {
          this.showCardToErrorReq = true;
        }
        else {
          this.getViewCardApiResponse();
        }
      } else {
        this.showCardErrorReq = false;
        if (!this.returnForm.value.cardNo) {
          this.showCardErrorReq = true;
        }
        else {
          this.getViewCardApiResponse();
        }
      }
    }
  }
  getViewCardApiResponse() {
    this.isShowTable = false;
    this.isShowBtn = false;
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "accountNumber": this.accountnumber,
      "cardNoFrom": this.returnForm.value.productType === 'OV' || this.returnForm.value.productType === 'VV' || this.returnForm.value.productType === 'TP' ? this.returnForm.value.cardFrom : this.firstdigit + this.returnForm.value.cardNo,
      "cardNoTo": this.returnForm.value.productType === 'OV' || this.returnForm.value.productType === 'VV' || this.returnForm.value.productType === 'TP' ? this.returnForm.value.cardTo : this.firstdigit + this.returnForm.value.cardNo,
      "issuedTo": null,
      "productTypeId": this.returnForm.value.productType,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "withoutReturnDate": "N"
    };
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.GET_RETURN_VIEW_CARD, obj).subscribe((response: any) => {
      if (response.status === 200) {
        this.response = response.body.filter((o: any) => o.returnedOn === null);
        let opt = response.body;
        this.displayedColumns = ['slNo', 'name', 'cardNo', 'issuedOn', 'entryDate', 'returnedOn'];
        this.productType = this.productArr.find((o: any) => o.productTypeId === this.returnForm.value.productType).name;
        this.dataList = opt;
        if (opt.length > 0) {
          this.dataList.forEach((val, i) => {
            val['id'] = i + 1;
          });
        }
        //  console.log(this.dataList)
        const editableList = this.dataList.filter((o: any) => o.isCardReturnable === true)
        if (editableList.length > 0) {
          this.isShowBtn = true;
        }
        this.dataSource = new MatTableDataSource<any>(this.dataList);
        this.isShowTable = true;
      }
    });
  }
  resetForm() {
    if (this.returnForm.value.productType === 'OV' || this.returnForm.value.productType === 'VV' || this.returnForm.value.productType === 'TP') {
      this.isCardStartEndShow = false;
    } else {
      this.isCardNoShow = false;
    }
    this.returnForm.reset();
    this.disablesubmit = false;
    this.returnForm.get('productType')?.setValue(" ");
    this.returnForm.controls['cardNo'].setErrors(null);
  }

  resetTbl() {
    this.dataList.forEach(element => {
      if (element.isCardReturnable) {
        element.returnedOn = null;
      }
    });
  }

  submit() {
    // const returnedData=this.response.filter((o: any) => o.returnedOn === null);
    //  console.log(this.response)
    const data = this.dataList.filter((o: any) => o.returnedOn === null);
     console.log('data:', data)
    if (data.length > 0 && data.length === this.response.length) {
      this.notifyService.showWarning('Please Fill returned On Field', 'Warning')
    }
    else {
      let list: any[] = []
      this.dataList.forEach(element => {
        let obj = {}
        if (element.isCardReturnable && element.returnedOn !== null) {
          obj = {
            "issuanceNo": element.issuanceNo,
            "returnedOn": moment(element.returnedOn,'DD/MM/YYYY, HH:mm:ss').format('DD-MM-YYYY HH:mm:ss')
          }
          list.push(obj);
        }
      });
      let reqObj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "toBeReturnedList": list
      }
      console.log("Date: ", reqObj)
      //  console.log(reqObj)
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.GET_RETURN_CARD_SUBMIT, reqObj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body;
          this.notifyService.showSuccess(opt.successMessage, 'Success')
          this.returnForm.reset();
          this.returnForm.get('productType')?.setValue(" ")
          this.isShowTable=false;
          this.isShowBtn=false;
          if (this.returnForm.value.productType === 'OV' || this.returnForm.value.productType === 'VV' || this.returnForm.value.productType === 'TP') {
            this.isCardStartEndShow = false;
          } else {
            this.isCardNoShow = false;
          }
        }
      });
    }
  }

  // for date update in returnedOn 
  formatChange(event: any, id:any) {
     console.log("EVENt: ", event, id)
    const data: any = moment(event.value).format('DD/MM/YYYY, HH:mm:ss')
    // element.returnedOn = data
    //  console.log("Date:", data);
    this.dataList[id-1].returnedOn=data;
  }

}
