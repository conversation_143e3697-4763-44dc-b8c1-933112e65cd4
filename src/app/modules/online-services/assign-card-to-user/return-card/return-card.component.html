<form [formGroup]="returnForm">
    <div fxLayout="column" fxLayoutGap="20px">
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">PRODUCT TYPE <span class="asterisk"><sup>*</sup></span>:</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%">
                <mat-form-field appearance="outline" class="inp">
                    <mat-select name="productType" formControlName="productType" placeholder="Select Product Type"
                        (selectionChange)="getCardDetails($event)">
                        <mat-option *ngFor="let acl of productArr" [value]="acl.productTypeId">
                            {{acl.name}}
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="returnForm.controls.productType.hasError('required')" class="pt15">
                        Product Type Cannot Be Blank
                    </mat-error>
                </mat-form-field>
            </div>
        </div>
        
        
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="isCardNoShow">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">CARD NO.<span class="asterisk"><sup>*</sup></span>:</span>
            </div>
 
            <div fxFlex="91%" fxFlex.lt-md="65%" fxFlex.md="75%" fxLayout="row" fxLayoutAlign="start center">
                <span class="pb10" *ngIf="!showcarderror">{{this.displaycard}} </span>
                <mat-error *ngIf="showcarderror">
                    No Card Details Found
                </mat-error>
                <mat-form-field appearance="outline" class="inp mat-wrap" *ngIf="!showcarderror">
                    <input matInput type="text" pattern="[0-9]*" name="cardNo" formControlName="cardNo" (focusout)="focusOutFunction()"  required />
                    <mat-error *ngIf="returnForm.controls.cardNo.hasError('required')" class="pt15">
                        Card No. Cannot Be Blank
                    </mat-error>
                    <mat-error *ngIf="returnForm.controls.cardNo.hasError('pattern')" class="pt15">
                        Please enter a number
                    </mat-error>
                </mat-form-field>
            </div>
        </div>                
        <div fxLayout="column" fxLayoutGap="20px" *ngIf="isCardStartEndShow">
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">CARD NO. FROM<span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxLayout="column" fxFlex="85%" fxFlex.lt-md="65%" fxLayoutGap="2px">
                    <mat-form-field appearance="outline" class="inp mat-wrap" *ngIf="!showcarderrorfrom">
                        <input matInput type="text" pattern="[0-9]*" name="cardFrom" formControlName="cardFrom" required />
                        <mat-error *ngIf="returnForm.controls.cardFrom.hasError('required')" class="pt15">
                            Card No From. Cannot Be Blank
                        </mat-error>
                        <mat-error *ngIf="returnForm.controls.cardFrom.hasError('pattern')" class="pt15">
                            Please enter a number
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">CARD NO. TO<span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxLayout="column" fxFlex="85%" fxFlex.lt-md="65%" fxLayoutGap="2px">
                    <mat-form-field appearance="outline" class="inp mat-wrap" *ngIf="!showcarderrorto">
                        <input matInput type="text" pattern="[0-9]*" name="cardTo" formControlName="cardTo" required />
                        <mat-error *ngIf="returnForm.controls.cardTo.hasError('required')" class="pt15">
                            Card No To. Cannot Be Blank
                        </mat-error>
                        <mat-error *ngIf="returnForm.controls.cardTo.hasError('pattern')" class="pt15">
                            Please enter a number
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
        </div>
        <div *ngIf="isCardSelected && isCardNoShow" fxLayout="column" fxLayoutGap="10px">
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <span fxFlex="15%" fxFlex.lt-md="35%">DIVISION/DEPARTMENT NAME:</span>
                <span>{{division}}</span>
            </div>
            <!-- <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <span fxFlex="15%" fxFlex.lt-md="35%">Department:</span>
                <span>{{dept}}</span>
            </div> -->
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <span fxFlex="15%" fxFlex.lt-md="35%">EXPIRY DATE:</span>
                <span>{{expiryDate}}</span>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <span fxFlex="15%" fxFlex.lt-md="35%">NAME:</span>
                <span>{{name}}</span>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <span fxFlex="15%" fxFlex.lt-md="35%">STATUS:</span>
                <span>{{status}}</span>
            </div>
        </div>
        <div fxFlex class="mb20 mt10" fxLayoutGap="10px" fxLayout="row" fxLayout.lt-sm="column">
            <!-- One more condition for disable button needs to be added(if card details are not present for selected card) -->
            <!-- <button *ngIf="isCardNoShow" [disabled]="returnForm.controls.returnDate.invalid" mat-raised-button class="update-btn" (click)="submit(returnForm.valid)">SUBMIT</button> -->
            <button mat-raised-button class="reset-btn" (click)="resetForm()">RESET</button>
            <button mat-raised-button class="update-btn" [disabled]="disablesubmit" (click)="search(returnForm.valid)">SEARCH</button>
        </div>
    </div>
</form>

<!-- <div class="mat-elevation-z8" fxFlex=100%> -->
<div fxFlex fxLayoutAlign="start center" *ngIf="isShowTable" class="mb10">
    <div class="subHeading" fxLayout="row">Product Type:   <span class="prodType pl10"> {{this.productType}}</span></div>
</div>
<div class="tbl-container mat-elevation-z8" *ngIf="isShowTable">

    <mat-table #table class="mat-table mat-cdk-table" [dataSource]="dataSource">
        <ng-container matColumnDef="slNo">
            <mat-header-cell *matHeaderCellDef> S No. </mat-header-cell>
            <mat-cell *matCellDef="let element" class="sno" fxLayoutAlign="start center">
                {{element.id}}
            </mat-cell>
        </ng-container>
        <ng-container matColumnDef="name">
            <mat-header-cell *matHeaderCellDef class="fwb font-16">NAME</mat-header-cell>
            <mat-cell *matCellDef="let element">
                <span class="mobile-label"> NAME:</span>
                {{element.issuedTo}}
            </mat-cell>
        </ng-container>

        <ng-container matColumnDef="cardNo">
            <mat-header-cell *matHeaderCellDef class="fwb font-16">CARD NO.</mat-header-cell>
            <mat-cell *matCellDef="let element">
                <span class="mobile-label"> CARD NO.:</span>
                {{element.cardNo}}
            </mat-cell>
        </ng-container>
        <ng-container matColumnDef="issuedOn">
            <mat-header-cell *matHeaderCellDef class="fwb font-16">ISSUED ON</mat-header-cell>
            <mat-cell *matCellDef="let element">
                <span class="mobile-label"> ISSUED ON:</span>
                <!-- <span class="fwb font-16" data-label="invoiceTo"> -->
                {{element.issuedOn}}
                <!-- </span> -->
            </mat-cell>
        </ng-container>
        <ng-container matColumnDef="entryDate">
            <mat-header-cell *matHeaderCellDef class="fwb font-16">ENTRY DATE</mat-header-cell>
            <mat-cell *matCellDef="let element">
                <span class="mobile-label"> ENTRY DATE:</span>
                <!-- <span class="fwb font-16" data-label="invoiceAmt">  -->
                <span class="p30">
                    {{element.createdDate}}
                </span>
            </mat-cell>
        </ng-container>
        <ng-container matColumnDef="returnedOn">
            <mat-header-cell *matHeaderCellDef class="fwb font-16">RETURN ON</mat-header-cell>
            <mat-cell *matCellDef="let element">
                <span class="mobile-label"> RETURN ON</span>
                <!-- <span class="fwb font-16" data-label="invoiceAmt">  -->
                <div *ngIf="!element.isCardReturnable">
                    {{element.returnedOn}}
                </div>
                <div *ngIf="element.isCardReturnable" class="mt5">
                    <mat-form-field appearance="outline" class="date-inp">
                        <input matInput [mtxDatetimepicker]="picker_1" autocomplete="off" name="returndOn" style="font-size: 11px;"
                            [(ngModel)]="element.returnedOn"  (dateChange)="formatChange($event, element.id)">
                        <!-- <mat-datepicker-toggle matSuffix [for]="$any(picker)"></mat-datepicker-toggle> -->
                        <mtx-datetimepicker-toggle matSuffix [for]="$any(picker_1)"  ></mtx-datetimepicker-toggle> 
                        <mtx-datetimepicker #picker_1 multiYearSelector="true" type="datetime" ></mtx-datetimepicker>
                        <!-- <ngx-mat-datetime-picker #picker [showSpinners]="true" [showSeconds]="true" [stepHour]="1"
                            [stepMinute]="1" [stepSecond]="1" [touchUi]="false" [enableMeridian]="false">
                        </ngx-mat-datetime-picker> -->
                    </mat-form-field>
                </div>
            </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>

    </mat-table>
    <tr class="mat-row" *ngIf="dataSource.data.length == 0" fxLayoutAlign="center center">
        <td class="mat-cell" colspan="5">No Records are Found</td>
    </tr>
    <!-- <mat-paginator [pageSizeOptions]="[5, 10, 20, 50, 100]" showFirstLastButtons></mat-paginator> -->

</div>
<div fxFlex class="mb20 mt10" *ngIf="isShowBtn" fxLayoutGap="10px" fxLayout="row" fxLayout.lt-sm="column">
    <!-- One more condition for disable button needs to be added(if card details are not present for selected card) -->
    <!-- <button *ngIf="isCardNoShow" [disabled]="returnForm.controls.returnDate.invalid" mat-raised-button class="update-btn" (click)="submit(returnForm.valid)">SUBMIT</button> -->
    <button mat-raised-button class="reset-btn" (click)="resetTbl()">RESET</button>
    <button mat-raised-button class="update-btn" (click)="submit()">SUBMIT</button>
</div>




<!-- <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
        <span class="date-title">Name<span class="asterisk"><sup>#</sup></span>:</span>
    </div>
    <div fxFlex="85%" fxFlex.lt-md="65%" fxLayout="row" fxLayoutGap="5px">
        <mat-form-field appearance="outline" class="inp">
            <input matInput name="name" formControlName="name">
        </mat-form-field>
        <div fxLayoutAlign="start center">
       <mat-icon class="info cursor-pointer" matTooltip="Name is optional field">info</mat-icon>
        </div>

    </div>
</div>
<div *ngIf="isCardStartEndShow" fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
        <span class="date-title">Show Result:</span>
    </div>
    <div fxFlex="85%" fxFlex.lt-md="65%">
            <mat-checkbox name="withDate" formControlName="withDate" color="primary">Without Returned Date</mat-checkbox>
    </div>
</div>
<div *ngIf="isCardNoShow" fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
        <span class="date-title">Return On:<span class="asterisk"><sup>*</sup></span>:</span>
    </div>
    <div fxFlex="85%" fxFlex.lt-md="65%">
        <mat-form-field appearance="outline" class="inp">
            <input matInput [matDatepicker]="picker1" name="returnDate" formControlName="returnDate">
            <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
            <mat-error *ngIf="returnForm.controls.returnDate.hasError('required')" class="pt15">
                Return Date Cannot Be Blank
            </mat-error>
        </mat-form-field>
    </div>
</div> -->