@import '@angular/material/theming';
@import './../../../../../styles/colors.scss';
@import './../../../../../styles/sizing.scss';
@import './../../../../../styles/main.scss';
.inp{
    width: 320px !important;
}
.update-btn{
    width:205px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
}
.reset-btn{
    width:205px;
    border-radius: 10px !important;
}
.subHeading{
    font-weight: $cdgsz-font-weight-bold;

   }
   .prodType{
    font-weight: $cdgsz-font-weight-normal;

   }
.login-type{
    font-size: $cdgsz-font-size-prime;
    font-weight: $cdgsz-font-weight-normal;
    margin-bottom: 0px !important;
    // padding-top: 10px;
}
.error {
    color: $red;
    font-weight: $cdgsz-font-weight-bold;
    font-size: 12px;
    height: 12px;
    padding-left: 10px;
}
// :host ::ng-deep .mat-form-field-wrapper {
//     padding-bottom: 8px !important;
//  }
//  :host ::ng-deep .mat-wrap .mat-form-field-wrapper {
//     padding-bottom: 0px !important;
//  }
 .info{
     color: $cdgc-font-prime;
 }
//  :host ::ng-deep .mat-checkbox-inner-container {
//      border: 1px solid $lightGrey;
// }
.mat-mdc-header-cell{ 
    background-color: #006BA8;
    color:$white;
}
.mat-mdc-row{
    color:black;
}
.mat-mdc-cell{
font-weight: normal;
font-size: 14px;
line-height: 16px;
}
.mat-mdc-header-row{
    border-radius: 12px;
    background-color:$cdgc-bg-prime !important;
    color: $cdgc-font-accent !important;
}
.mat-mdc-header-cell {
    color: $cdgc-font-accent !important;
    
}
.mat-mdc-row{
    border-radius: 12px;
    color: $lightGrey !important;

}
.date-inp{
    width: 150px !important;
    justify-content: center;
    margin: 5px 1px;
}
:host ::ng-deep .mat-mdc-form-field-subscript-wrapper {
    display: none !important;
}
.mobile-label{
    display: none;
}
div span.show-on-hover{
visibility: visible;
background-color: $lightGrey;
    color: $cdgc-font-accent;
    height: 50px!important;
    width:  150px!important;
}
div:hover span.show-on-hover{
    visibility: visible;
    background-color: $lightGrey;
    color: $cdgc-font-accent;
    height: 50px!important;
    width:  150px!important;
    // text-align: center;
    }
 
 @media screen and (max-width: 600px) {
    .inp{
        width: 183px !important;
    }
    .mobile-label{
        display: inline-block;
        width: 165px;
        font-weight: $cdgsz-font-weight-bold;
    }
    .mat-mdc-header-row{
        display: none;
    }
    .mat-mdc-row{
        flex-direction: column;
        align-items: start;
        padding: 8px 24px;
        border-radius: 12px !important;
        border: 1px solid $cdgc-border-prime;
        min-height: 28px !important;
    }
    ::ng-deep mat-cell:first-of-type, mat-header-cell:first-of-type, mat-footer-cell:first-of-type {
        padding-left: 0px !important;
    }
}
 @media screen and (max-width: 500px) {
    .inp{
        width: 157px !important;
    }
 }

 .search-btn{
    width:150px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important; 
 }