<div fxLayout.gt-md="row" fxLayout.lt-md="column" class="full-height" fxLayoutGap.lt-md="14px">
    <div class="left-div" fxFlex.gt-md="65%" fxFlex.lt-md="100%" fxLayout="column" fxLayoutAlign.lt-md="center stretch"
        fxLayoutGap="20px">
        <div fxLayout="row" fxLayoutGap="10px">
            <img fxFlex.lt-md="20%" src="\assets\images\view usage report.png">
            <p class="heading"> Loyalty Rewards Redemption</p>
        </div>

        <div fxLayout="row" fxHide.lt-md fxLayoutGap.gt-md="30px" class="main-carousel-div pl10 pr10">

            <div fxFlex="90%" fxHide.lt-md>
                <mat-carousel timings="250ms ease-in" [autoplay]="true" color="primary" maxWidth="auto" [loop]="true"
                    [hideArrows]="true" [hideIndicators]="true" [useKeyboard]="false" [useMouseWheel]="true"
                    orientation="rtl">
                    <mat-carousel-slide #matCarouselSlide overlayColor="#fff" [hideOverlay]="false"
                        *ngFor="let slide of existingCardsList; let i = index">
                        <div fxLayout="row" fxLayoutGap="30px" fxLayoutAlign="center center" class="mt30 mb15 mr10">
                            <ng-container *ngFor="let card of slide.cardList; let i = index">
                                <!-- <mat-card  appearance="outlined"  [ngClass]="(cardName==card.cardName)? 'card-hover' :'card'" (mouseover)="onCardHover(card.cardName)" (mouseleave)="removeCardHover()"> -->
                                <mat-card appearance="outlined"
                                    [ngClass]="(cardName==card.cardName)? 'card-hover' :'card'"
                                    (mouseover)="onCardHover(card.cardName)" (mouseleave)="removeCardHover()">
                                    <img mat-card-image class="card-image-main" src={{card.img}} alt="product"
                                        style="background-color: bisque;" height="170px">
                                    <mat-card-content class="prodTitle">{{card.cardName}}</mat-card-content>
                                </mat-card>
                            </ng-container>
                        </div>
                    </mat-carousel-slide>
                </mat-carousel>

            </div>
        </div>
        <div fxFlex="100%" fxHide.gt-md>
            <mat-carousel timings="250ms ease-in" [autoplay]="true" color="primary" maxWidth="auto" [loop]="true"
                [hideArrows]="true" [hideIndicators]="true" [useKeyboard]="false" [useMouseWheel]="true"
                orientation="rtl">
                <mat-carousel-slide style="height: fit-content !important;" #matCarouselSlide overlayColor="#f7f7f7"
                    [hideOverlay]="false" *ngFor="let slide of existingCardsListForCell; let i = index">
                    <div fxLayout="row" fxLayoutGap="30px" fxLayoutAlign="center center" class="mt30 mb15 mr10">
                        <ng-container *ngFor="let card of slide.cardList; let i = index">
                            <!-- <mat-card  class="card-cell" [ngClass]="(cardName==card.cardName)? 'card-cell-hover' :'card-cell'" (mouseover)="onCardHover(card.cardName)" (mouseleave)="removeCardHover()">
                                <img mat-card-image src={{card.img}} alt="product" width="271px" height="170px">
                                <mat-card-content class="prodTitle prodTitle-cell">{{card.cardName}}</mat-card-content> 
                            </mat-card> -->
                            <mat-card appearance="outlined" class="background-color: #f7f7f7 !important;"
                                [ngClass]="(cardName==card.cardName)? 'card-hover' :'card'"
                                (mouseover)="onCardHover(card.cardName)" (mouseleave)="removeCardHover()">
                                <img mat-card-image class="card-image-main" src={{card.img}} alt="product"
                                    style="background-color: bisque;" height="170px">
                                <mat-card-content class="prodTitle prodTitle-cell">{{card.cardName}}</mat-card-content>
                            </mat-card>
                        </ng-container>
                    </div>
                </mat-carousel-slide>
            </mat-carousel>

        </div>
        <!-- <div [ngClass.gt-md]="'pb34'" fxLayoutAlign.lt-md="center center"> -->
        <div fxLayoutAlign.lt-md="center center">

            <mat-chip-set>
                <mat-chip>
                    <img matChipAvatar src="\assets\images\view usage report.png" />
                    300 points
                </mat-chip>
            </mat-chip-set>

        </div>
        <div fxLayout="column" fxLayoutGap="14px" fxLayoutAlign.lt-md=" center stretch">
            <mat-card appearance="outlined" (mouseover)="onHover(card.itemName)"
                (mouseover)=" onMatCardClickEvent(card)" fxLayout="row"
                [ngClass]="(itemName==card.itemName)?'redemption-card-hover':'redemption-card'"
                *ngFor="let card of redemptions" fxLayoutGap="14px">
                <div fxFlex.gt-md="22%" fxFlex.lt-md="55%" class="card-image">
                    <img [src]="card.img" height="100%" width="90%">
                </div>
                <div fxFlex.gt-md="50%" fxFlex.lt-md="25%" fxLayout="column" fxLayoutAlign="center start">
                    <p class="title">{{card.itemName}}</p>
                    <span class="category">{{card.category}}</span>
                </div>
                <div fxFlex.gt-md="25%" fxFlex.lt-md="20%" fxLayout="column" fxLayoutAlign="center end">
                    <span class="title">{{card.points}}</span>
                </div>
            </mat-card>
        </div>
    </div>
    <!-- <div [ngClass.gt-md]="'right-div'" fxLayout="column" fxFlex.gt-md="35%" fxFlex.lt-md="100%" -->
    <div fxLayout="column" fxFlex.gt-md="35%" fxFlex.lt-md="100%" fxLayoutAlign=" center center"
        fxLayoutGap.gt-md="111px">
        <!-- <div fxFlex.gt-md="70%" fxFlex.lt-md="100%" [ngClass.lt-md]="'cell-width'" [ngClass.gt-md]="'desktop-width'" -->
        <div fxFlex.gt-md="70%" fxFlex.lt-md="100%" fxLayout="column" fxLayoutAlign=" center" fxLayoutGap.gt-md="38px">
            <!-- <div [ngClass.gt-md]="'pt54'"> -->
            <div>
                <img *ngIf="!isMatCardClicked" src="/assets/images/view usage report.png" height="214px" width="300px"
                    alt="">
                <img *ngIf="isMatCardClicked" [src]="imgUrl" height="214px" width="300px" alt="">
            </div>
            <div>
                <p class="itemHeader" *ngIf="!isMatCardClicked">$5 Taxi Voucher</p>
                <p class="itemHeader" *ngIf="isMatCardClicked">{{itemName}}</p>
            </div>
            <div>
                <p class="content" *ngIf="!isMatCardClicked">Looking for the perfect gift for your clients or staff?
                    Look no further as
                    our ComfortDelGro's taxi
                    vouchers might just be the perfect solution for you. Available in $5 and $10 denominations, it is a
                    hassle-free way to travel on board our cabs.
                </p>
                <p class="content" *ngIf="isMatCardClicked">{{itemInfo}}</p>
            </div>

        </div>
        <!-- <div fxFlex.gt-md="70%" fxFlex.lt-md="100%" [ngClass.lt-md]="'cell-width'" [ngClass.gt-md]="'desktop-width'" -->
        <div fxFlex.gt-md="70%" fxFlex.lt-md="100%" fxLayout="column">
            <div fxLayout="row" class="pb34" fxLayoutAlign.gt-md="start center" fxLayoutAlign.lt-md="center center">
                <span class="pr20 label">Quantity</span>
                <mat-form-field style="width:25%" appearance="outline">
                    <input matInput type="number">
                </mat-form-field>
            </div>

            <button class="btn" mat-raised-button>
                ADD TO CART
            </button>

        </div>
    </div>

</div>