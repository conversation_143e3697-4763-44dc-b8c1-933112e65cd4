@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';

.left-div{
    background-color: #f7f7f7 !important;
}

.right-div{
    background-color: $cdgc-bg-accent;
}

.full-height{
    min-height: 100% !important;
}

::ng-deep .carousel{
    height:300px !important;
    min-height:300px !important;
}
::ng-deep.carousel-slide{
    height:300px !important;
    min-height:300px !important;
}
::ng-deep .carousel-inner{
    height:300px !important;
    min-height:300px !important;
}
::ng-deep .carousel-inner>.item{
    height:300px !important;
    min-height:300px !important;
}
::ng-deep .carousel-inner>.item>a>img, .carousel-inner>.item>img{
    line-height: 1;
}
::ng-deep .carousel-slide-overlay{
    height:300px !important;
}
.card{
    width: 289px !important;
    height: 215px !important;
    border-radius: 10px !important;
    
}
.card-hover{
    width: 289px !important;
    height: 215px !important;
    border-radius: 10px !important;
    border: 1px solid $cdgc-bg-prime !important;
    background: $cdgc-bg-mild !important;
}


.main-carousel-div{
    border-radius: 10px !important;
    background-color: $cdgc-bg-accent !important;
}

 .mat-mdc-chip{
    height: 59px !important;
    width: 220px !important ;
    border-radius: 29.5px !important;
    background-color:#ffb681 !important;
    color: #212743 !important;
    font-size: $cdgsz-font-size-md !important;
    line-height: 22px !important;
    font-weight: $cdgsz-font-weight-bold !important;
    flex-direction: column !important;
}

.redemption-card{
   width:95%;
   height: 96px;
   padding: 0 !important;
   padding-right: 25px !important;
}
.redemption-card-hover{
    border: 1px solid $cdgc-bg-prime !important;
    background: $cdgc-bg-mild !important;
    width:95%;
    height: 96px;
    padding: 0 !important;
    padding-right: 25px !important;
}

.pr21{
    padding-right: 21px !important;
}

.mt30{
    margin-top: 30px;
}

.cell-width{
    width:100%;
}

.desktop-width{
    width: 300px;
}


/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
  }

.pb34{
    padding-bottom: 34px;
}
.pt54{
    padding-top: 54px;
}

// .card-cell{
//     width: 95% !important;
//     height: 230px !important;
//     padding: 0 10px 0 10px !important;
//     border-radius: 10px !important;
    
// }
// .card-cell-hover{
//     width: 95% !important;
//     height: 230px !important;
//     padding: 0 10px 0 10px !important;
//     border: 1px solid $cdgc-bg-prime !important;
//     border-radius: 10px !important;
//     background: $cdgc-bg-mild !important;
// }

.heading{
   font-size: 24px !important;
   line-height: 26px !important;
   color: $lightGrey;
}

.title{
    font-size: $cdgsz-font-size-prime !important;
    line-height: 19px !important;
    font-weight: $cdgsz-font-weight-bold !important;
}

.category{
    font-size: $cdgsz-font-size-xs !important;
    line-height: 14px !important;
    font-weight: $cdgsz-font-weight-normal !important;
}

.itemHeader{
    font-size: 26px !important;
    line-height: 31px !important;
    font-weight: $cdgsz-font-weight-bold !important;
}

.content{
    font-size: $cdgsz-font-size-lg !important;
    line-height: 24px ;
    font-weight:$cdgsz-font-weight-normal !important ;
}

.label{
    font-size: 19px !important ;
    line-height: 23px !important ;
    font-weight:  $cdgsz-font-weight-intermediate !important;;
}

.btn{
    background-color: $cdgc-bg-hue !important;
    border-radius: 5px !important;
}

.prodTitle{
    font-size: 18px !important;
    line-height: 28 px !important;
    
}

.prodTitle-cell{
text-align: center !important;

}

.card-image-main{
    //padding: 30px !important;
    //box-sizing: border-box !important;
    width: 100% !important;
    border-radius: 10px !important;
    margin:0px !important;
    text-align: center !important;
}



// .card-cell{
//     background-color: #f7f7f7 !important;
// }