import { Component, OnInit } from '@angular/core';
// import { MatCarousel, MatCarouselSlideComponent } from '@ngbmodule/material-carousel';

@Component({
  selector: 'app-loyalty-rewards-redemption',
  templateUrl: './loyalty-rewards-redemption.component.html',
  styleUrls: ['./loyalty-rewards-redemption.component.scss']
})
export class LoyaltyRewardsRedemptionComponent implements OnInit {

  slides: [{image:"/assets/images/view usage report.png"},{image:"/assets/images/cab-charge-label.png"},{image:"/assets/images/cab-charge-label.png"},{image:"/assets/images/cab-charge-label.png"}];
  existingCardsList = [
    {
      page: 1,
      cardList: [
        {
          img: "/assets/images/cab-charge-label.png",
          cardName: "Carcharge Personal Card"
        },
        {
          img: "/assets/images/cab-charge-label.png",
          cardName: "Carcharge Corporate Card"
        }
        
      ]
    },
    {
      page: 2,
      cardList: [
        {
          img: "/assets/images/cab-charge-label.png",
          cardName: "Open value E-voucher"
        }
      ]
    }
  ];

  existingCardsListForCell=[
    {
      page: 1,
      cardList: [
        {
          img: "/assets/images/cab-charge-label.png",
          cardName: "Carcharge Personal Card"
        },       
      ]},
    {
      page: 2,
      cardList: [
        {
          img: "/assets/images/cab-charge-label.png",
          cardName: "Carcharge Corporate Card"
        },
       
        
      ]
    },
    {
      page: 3,
      cardList: [
        {
          img: "/assets/images/cab-charge-label.png",
          cardName: "Open value E-voucher"
        },
       
        
      ]
    }];



  redemptions=[
    {
      img:"/assets/images/view usage report.png",
      itemName: "$5 Taxi Voucher",
      itemInfo:`Looking for the perfect gift for your clients or staff? Look no further as our ComfortDelGro's taxi
      vouchers might just be the perfect solution for you. Available in $5 and $10 denominations, it is a
      hassle-free way to travel on board our cabs.`,
      category: "Vouchers",
      points: "25 points"
    },
    {
      img:"",
      itemName: "Mouse Pad",
      itemInfo: " gfdajk   jh fgjkdfjfkhjfhf         fhgjdhfgdkjf  g jdfhgjdhgjd gdf  hgjhdfjhgdjhgfjh hjg fdhfkjgdf",
      category: "Others",
      points: "10 points"
    },
    {
      img:"",
      itemName: "Notebook",
      itemInfo: " gfdajk   jh fgjkdfjfkhjfhf         fhgjdhfgdkjf  g jdfhgjdhgjd gdf  hgjhdfjhgdjhgfjh hjg fdhfkjgdf",
      category: "Others",
      points: "10 points"
    }
  ];
  imgUrl: any;
  itemName: any;
  isMatCardClicked: boolean = false;
  itemInfo: any;
  isHover: boolean;
  cardName: any;

  constructor() { }

  ngOnInit(): void {
  }
  
  onHover(item:any){
   
    this.itemName = item;
  }

  removeHover(){
    this.itemName = null;
  }

  onCardHover(card:any){
    this.cardName = card;
  }

  removeCardHover(){
    this.cardName = null;
  }
  onMatCardClickEvent(item:any){
    this.imgUrl = item.img;
    this.itemName = item.itemName;
    this.itemInfo = item.itemInfo;
    this.isMatCardClicked = true;
     
}
}
