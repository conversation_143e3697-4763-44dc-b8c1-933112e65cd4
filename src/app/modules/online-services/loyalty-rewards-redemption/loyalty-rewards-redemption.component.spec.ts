import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LoyaltyRewardsRedemptionComponent } from './loyalty-rewards-redemption.component';

describe('LoyaltyRewardsRedemptionComponent', () => {
  let component: LoyaltyRewardsRedemptionComponent;
  let fixture: ComponentFixture<LoyaltyRewardsRedemptionComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ LoyaltyRewardsRedemptionComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(LoyaltyRewardsRedemptionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
