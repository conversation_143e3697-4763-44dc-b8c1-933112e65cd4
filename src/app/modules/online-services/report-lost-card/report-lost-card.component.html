<div fxFlex fxLayout="column" class="full-height">
  <div fxLayout="row" class="mt50 ml20">
    <p>Lost Card? Not to worry, we'll suspend your lost card and send a replacement to
      you.</p>
  </div>
  <div fxLayout="row" class="ml20">
    <!-- fxLayoutAlign.xs="center center" -->
    <form [formGroup]="lostCardForm">
      <div fxLayout="row">
        <mat-form-field appearance="outline" class="inp">
          <!-- class="inp" -->
          <mat-select placeholder="Select Product Type" name="productType" (selectionChange)="onSelect($event)"
            formControlName="productType" [disabled]="isDisable">
            <mat-option *ngFor="let type of products;" [value]="type.productTypeId">
              {{type.name}}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="submitted && lostCardForm.controls.productType.hasError('required')">
            Please Select One
          </mat-error>
        </mat-form-field>
      </div>
      <div fxLayout="row">
        <span class="pt10" *ngIf="!showDisplayErr && !hideDis">{{this.displaycard}}</span>
        <mat-error *ngIf="showDisplayErr" class="pt15">
          No Card Details Found
        </mat-error>

          <mat-form-field appearance="outline" class="inp" *ngIf="!showDisplayErr">
            <input matInput type="text" pattern="[0-9]*" name="cardNo" formControlName="cardNo" placeholder="Card No."
               required />
            <mat-error *ngIf="submitted && lostCardForm.controls.cardNo.hasError('required')">
              Card No. Cannot Be Blank
            </mat-error>
            <mat-error *ngIf="lostCardForm.controls.cardNo.hasError('pattern')">
              Please enter a number
            </mat-error>
          </mat-form-field>
      </div>
      <div fxLayout="row" fxLayoutGap="15px">
        <div>
          <!-- fxFlex="50%" fxFlex.md ="40%" fxFlex.lg ="30%" -->
          <mat-label class="text-cls font-16">REPLACEMENT OF CARD?<span class="astriek">*</span></mat-label>
        </div>
        <div>
          <mat-radio-group [disabled]="isDisableRadioButton" formControlName="reqCancel" fxLayout="row" color="primary"
            fxLayoutGap=".25rem">
            <!-- fxFlex="40%" -->
            <mat-radio-button [value]="'Yes'">Yes</mat-radio-button>
            <mat-radio-button checked [value]="'No'">No</mat-radio-button>
          </mat-radio-group>
        </div>
      </div>

      <div fxFlex=82%>
        <p style="font-size: 12px;">
          * Card replacement fee applies<br>
          Please wait for the acknowledgement message to ensure we receive your submission.
          If you do not see it, please resubmit again.
        </p>
      </div>

      <div fxFlex style="text-align: center" class="pt5" fxLayoutAlign="start start" fxLayoutAlign.xs="start center">
        <div fxFlexFill>
          <button [disabled]="btnClicked" class="sub-btn" (click)="onSubmit()">
            SUBMIT
          </button>
        </div>
      </div>

    </form>
  </div>
</div>