import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { RoleConstants } from 'src/assets/url-constants/url-constants';
import { NotificationService } from '../../../shared/services/notification.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { HttpClient, HttpResponse } from '@angular/common/http';



@Component({
  selector: 'app-report-lost-card',
  templateUrl: './report-lost-card.component.html',
  styleUrls: ['./report-lost-card.component.scss']
})
export class ReportLostCardComponent implements OnInit {
  products: any = [];
  cards: any[] = [];

  submitted = false;
  isDisable: boolean = false;
  lostCardForm:FormGroup;
  isDisableRadioButton: boolean = false;
  accountnumber: any;
  list: any = [];
  requestvalue: any;
  btnClicked: boolean = false;
  firstdigit: string;
  displaycard: string;
  cardsList: any;
  showDisplayErr: boolean=false;
  hideDis: boolean=false;
  constructor(public localStorage: LocalStorageService, private http: HttpClient, private apiService: ApiServiceService, private formBuilder: FormBuilder, private notifyService: NotificationService, public cdgService: CdgSharedService) { 
    this.lostCardForm = this.formBuilder.group({
      productType: ['', Validators.required],
      cardNo: ['', Validators.required],
      reqCancel: ['']
    });
  }

  ngOnInit(): void {
    // if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER) {
    //   this.products.push({ value: 'personalCard', viewValue: 'PERSONAL CARD'});
    //   this.lostCardForm.get('productType')?.setValue('personalCard');
    //   this.lostCardForm.get('cardNo')?.setValue('6100-1234-7890')
    //   this.isDisable = true;
    // }
    // else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
    //   this.lostCardForm.get('productType')?.setValue('corporatecard');
    //   this.lostCardForm.get('cardNo')?.setValue('6100-1234-7891')
    //   this.isDisable = true;
    // }
    // else if(this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUBAPPLICANT){
    //   this.products.splice(0,this.products.length);
    //   this.products.push({value: 'personalCard', viewValue: 'PERSONAL CARD'});
    //   this.products.push({value: 'priorityCard', viewValue: 'PRIORITY CARD'});
    // }
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }
    }
    let prodtypeobj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accountNo": this.accountnumber
    }

    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_PRODUCTTYPE, prodtypeobj).subscribe((response: any) => {
      if (response.status === 200) {
        console.log(response)
        let opt = response.body.productType;
        opt.forEach((element: any) => {
          this.products.push(element);
          console.log(this.products);
        });
      }
    });

  }
  get formControls() {
    return this.lostCardForm.controls;
  }

  onSelect(item: any) {
    this.lostCardForm.controls['cardNo'].reset();
    if (item.value === "OV") {
      this.isDisableRadioButton = true;
      this.lostCardForm.controls.reqCancel.reset();
    }
    else {
      this.isDisableRadioButton = false;
      this.lostCardForm.get('reqCancel')?.setValue('No')
    }
    this.hideDis=false;
    this.showDisplayErr = false;
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      "productType": item.value,
      "accountNo": this.accountnumber,
      "masterAccountNo": Number(this.accountnumber)
    }

    if (item.value !== "OV") {
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_CARDLIST, obj).subscribe
        ((response: any) => {
          this.cards = []; this.cardsList = [];
          if (response.status === 200) {
            let opt = response.body.cardNo;
              this.firstdigit = String(opt[0]).substr(0, 10);
              this.displaycard = this.firstdigit.substr(0, 6) + "-" + this.firstdigit.substr(6, 4);
              opt.forEach((element: any) => {
                this.cards.push((String(element).substr(-6)));
                this.cardsList.push((String(element).substr(-6)));
              });
              if (response.body.cardNo.length > 0) {
                this.showDisplayErr = false;
              }
              else {
                this.showDisplayErr = true;
              }
              this.hideDis = false;

          }
        });
    }
    else {
      this.hideDis = true;
    }    
  }

  onSubmit() {
    this.submitted = true;

    if (this.lostCardForm.invalid) {
      return;
    }
    else {
      if (this.lostCardForm.value.reqCancel === 'Yes') {
        this.requestvalue = "true";
      }
      else {
        this.requestvalue = "false";
      }
      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
        "accountNo": this.accountnumber,
        "cardNo": this.lostCardForm.value.productType !== "OV" ?this.firstdigit+this.lostCardForm.value.cardNo:this.lostCardForm.value.cardNo,
        "replacementOfCard": this.requestvalue,
        "productTypeId": this.lostCardForm.value.productType
      }

      //  this.http.post("http://localhost:9021/api/account/reportLostCard",obj).subscribe((response: any) =>{

      //  if (response.status === 200) {
      // let opt = response;
      // console.log(opt);
      // this.btnClicked = true;
      // if(opt.successMessage!== null){
      // this.notifyService.showSuccess(response.successMessage,'Report lost card');

      // }
      // else{

      //   this.notifyService.showError(response.errorMessage,'Report lost card');
      // }
      //  }     
      //  });

      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.GET_LOSTCARD, obj).subscribe((response: any) => {
        if (response.status === 200) {
          console.log(response)
          let opt = response.body;
          if (response.body.successMessage !== null) {
            this.notifyService.showSuccess(response.body.successMessage, 'Report lost card');
          }
          else {
            this.notifyService.showError(response.body.errorMessage, 'Report lost card');
          }
          this.lostCardForm.reset();
          this.displaycard = '';
          this.lostCardForm.get('reqCancel')?.setValue('No')
          this.submitted = false;
        }
      });

    }
  }

}






