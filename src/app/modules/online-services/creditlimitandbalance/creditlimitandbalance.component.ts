import { Component, OnInit } from '@angular/core';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';

export interface DivisionInfo {
  accNo: string,
  code: string,
  limit: string,
  balance: string
}
export interface AccountInfo {
  accNo: string,
  limit: string,
  balance: string
}
@Component({
  selector: 'app-creditlimitandbalance',
  templateUrl: './creditlimitandbalance.component.html',
  styleUrls: ['./creditlimitandbalance.component.scss']
})
export class CreditlimitandbalanceComponent implements OnInit {
  typeArr = [
    { value: 'account', viewValue: 'Enquire Account Limit/Balance' },
    { value: 'card', viewValue: 'Enquire Card Limit/Balance' }
  ];
  productArr: any = [];
  divitionArr = [
    { value: 'cabincrew', viewValue: 'CABIN CREW (1003)' },
    { value: 'corporateoffice', viewValue: 'CORPORATE OFFICE (1005)' },
    { value: 'csm1', viewValue: 'CSM1 (1004)' }
  ];
  deptArr = [
    { value: 'dept1', viewValue: 'DEPT1 (001A)' }
  ];
  creditLimitObj = {
    type: "",
    division: "",
    dept: "",
    productType: "",
    cardNo: ""
  }
  isShowProductType: boolean = false;
  firstdigit: any;
  lastdigit: any = [];
  lastfuzz: any = [];
  divArray: any[] = [];
  deptArray: any[] = [];
  defaultDept: any;
  frstDivSelection: any;
  cardArr: any = [];
  cardvalue: any = [];
  fetchDept: any[] = [];
  list: any;
  value: any;
  isCabinCrew: boolean;
  displaycard: any;
  isShowCardNo: boolean = false;
  isShowDivision: boolean = false;
  isShowDept: boolean = false;
  showcarderror: boolean = false;
  isShowCompanyName: boolean = false;
  showcardfield: boolean = false;
  disablesubmit: boolean = false;

  displayedColumns = ['serialNo', 'accountName', 'code', 'creditLimit', 'creditBalance'];
  deparmentColumns = ['serialNo', 'accountName', 'code', 'creditLimit', 'creditBalance'];
  accountDisplayedColumns = ['serialNo', 'accountName', 'creditLimit', 'creditBalance']
  cardDisplayedColumns = ['serialNo', 'nameOnCard', 'productName', 'cardNo', 'creditLimit', 'creditBalance', 'status']
  isDisable: boolean = false
  divisionCreditList = [
    {
      accNo: "CABIN CREW",
      code: "1003",
      limit: "98,000.00",
      balance: "68,000.00"
    },
    {
      accNo: "CORPORATE OFFICE",
      code: "1005",
      limit: "78,000.00",
      balance: "68,000.00"
    },
    {
      accNo: "CSM1",
      code: "1004",
      limit: "78,000.00",
      balance: "57,030.00"
    },
  ]
  accountCreditList = [
    {
      accName: "Test Account Alphabeth Company",
      limit: "78,000.00",
      balance: "68,000.00"
    }
  ]
  cardCreditList = [
    {
      nameOnCard: "Father",
      productName: "Personal Card (PC)",
      cardNo: ****************,
      limit: "600.00",
      balance: "450.00",
      status: "Active"
    },
    {
      nameOnCard: "Father",
      productName: "CONTACTLESS CORPORATE CARD",
      cardNo: ****************,
      limit: "600.00",
      balance: "450.00",
      status: "Active"
    }
  ]
  divisionDataSource: any[] = [];
  listcard: any[] = [];

  isShowDepartment: boolean = false;
  accDataSource: any[] = []
  deptDataSource: any[] = [];
  cardDataSource: any[] = []
  showDepTbl: boolean = false;
  isShowDivisionTbl: boolean = false;
  isShowAccountTbl: boolean = false;
  isShowCardTbl: boolean = false;
  isDisableType: boolean = false;
  accountnumber: any;
  companyName: any;
  isCorpInvoice: boolean;
  deptvalue: string;
  isShowErrorOnReset: boolean = false;
  cardshowerror: boolean = false;
  openshow: boolean = false;
  customernumber: any;
  // filterValue: any;
  constructor(public localStorage: LocalStorageService, private apiService: ApiServiceService, public cdgService: CdgSharedService, private notifyService: NotificationService) {
  }

  ngOnInit(): void {
    this.isShowErrorOnReset = false;
    this.creditLimitObj = {
      type: "",
      division: "",
      dept: "",
      productType: "",
      cardNo: ""
    }
    this.isShowErrorOnReset = true;
    this.frstDivSelection = "",
      this.companyName = "",
      this.isShowCardNo = false;
    this.isShowProductType = false;

    this.isShowDivisionTbl = false;
    this.isShowAccountTbl = false;
    this.showDepTbl = false;
    this.isShowCardTbl = false;
    this.isShowCompanyName = false;
    this.isShowDepartment = false;
    this.isShowDivision = false;
    this.defaultDept = ""
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;;
      }
    }
    if (this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails) {
      this.customernumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number;
    }
    else {
      console.log(this.customernumber = this.localStorage.localStorageGet('customerviewvalue'));
     
      if(this.localStorage.localStorageGet('customerviewvalue')===null){
        this.customernumber =this.localStorage.localStorageGet('viewvalueonload');
      }
      else{
        this.customernumber = this.localStorage.localStorageGet('customerviewvalue').substr(0, 6);
      }
    

    }
    let prodtypeobj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accountNo": this.accountnumber,
      "masterAccountNo": Number(this.accountnumber)
    }

    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_PRODUCTTYPE, prodtypeobj).subscribe((response: any) => {
      if (response.status === 200) {
        console.log(response)
        let opt = response.body.productType;
        opt.forEach((element: any) => {
          this.productArr.push(element);
          console.log(this.productArr);
        });


      }

    });
    // if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER) {
    //   this.productArr.push({ value: 'personalCard', viewValue: 'PERSONAL CARD' })
    //   this.creditLimitObj.type = "Enquire Card Limit/Balance";
    //   this.creditLimitObj.productType = "PERSONAL CARD"
    //   this.creditLimitObj.cardNo = Number(this.cdgService.loggedInUserDetails.cardNo)
    //   this.isShowProductType = true;
    //   this.isShowCardNo = true;
    //   this.isDisable = true;
    // }else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
    //   this.creditLimitObj.type = "Enquire Card Limit/Balance";
    //   this.creditLimitObj.productType = "CORPORATE CARD"
    //   this.creditLimitObj.cardNo = Number(this.cdgService.loggedInUserDetails.cardNo)
    //   this.isShowProductType = true;
    //   this.isShowCardNo = true;
    //   this.isDisable = true;
    //   this.isDisableType=true;
    // } 
    // else if(this.localStorage.localStorageGet("loggedInRole") === 'Personal Card Sub'){
    //   this.creditLimitObj.type = "Enquire Card Limit/Balance";
    //   this.isShowProductType = true;
    //   this.isDisableType=true;

    // }
    // else if(this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUBAPPLICANT){
    //   this.creditLimitObj.type = "Enquire Card Limit/Balance";
    //   this.productArr.splice(0,this.productArr.length);
    //   this.productArr.push({value: 'personalCard', viewValue: 'PERSONAL CARD'});
    //   this.productArr.push({value: 'priorityCard', viewValue: 'PRIORITY CARD'});
    //   this.isShowProductType = true;
    //   this.isDisableType=true;
    // }
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUBAPPLICANT) {
      this.creditLimitObj.type = this.typeArr[1].viewValue;
      this.onSelection();
      this.isDisableType = true;
    }
    else {
      this.isDisableType = false;
    }
  }
  onSelect(item: any) {
    this.isShowCompanyName = false;
    this.disablesubmit = false;
    this.displaycard = "";
    this.isShowDivisionTbl = false;
    this.isShowAccountTbl = false;
    this.showDepTbl = false;
    this.isShowCardTbl = false;
    this.openshow = false;
    this.showcarderror = false;
    this.creditLimitObj.cardNo = "";
    this.cardshowerror = false;
    this.list = [];
    this.cardArr = [];
    if (item.value === "OV") {
      this.isShowCardNo = true;
      this.openshow = true;
    }
    else {
      this.isShowCardNo = true;
    }
    if (item.value && item.value != "OV") {

      let cardnumberobj = {

        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
        "productType": item.value,
        "accountNo": this.accountnumber


      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_CARDLIST, cardnumberobj).subscribe((response: any) => {
        if (response.status === 200) {
          console.log(response)
          let opt = response.body.cardNo;
          if (response.body.cardNo.length >= 1) {
            this.list = response.body.cardNo;
            this.lastfuzz = [];
            this.list.forEach((element: any) => {
              this.lastfuzz.push((String(element).substr(-6)));
            });
            this.firstdigit = String(opt[0]).substr(0, 10);

            this.displaycard = this.firstdigit.substr(0, 6) + "-" + this.firstdigit.substr(6, 4);
            this.showcardfield = false;
          }
          else {
            this.showcarderror = true;
            this.showcardfield = true;
            this.disablesubmit = true
          }

        }
      });

    } else if (item.value === "OV") {
      this.isShowCardNo = true;
      this.openshow = true;
      this.showcardfield = false;
    }
  }

  onSelection() {
    if (this.creditLimitObj.type === 'Enquire Card Limit/Balance') {

      this.creditLimitObj.productType = ""
      this.isShowDivisionTbl = false;
      this.isShowAccountTbl = false;
      this.showDepTbl = false;
      this.isShowCardTbl = false;
      this.isShowProductType = true;
      this.isShowDivision = false;
      this.isShowCompanyName = false;
      this.isShowDept = false;
      this.isShowDepartment = false;
      this.isShowErrorOnReset = true;
      this.disablesubmit = false;

    }
    else if (this.creditLimitObj.type === 'Enquire Account Limit/Balance') {
      //this.creditLimitObj.type="A";

      this.frstDivSelection = ""
      this.isShowCardTbl = false;
      this.disablesubmit = false;
      this.isShowAccountTbl = false;
      this.isShowDivisionTbl = false;
      this.showDepTbl = false;
      this.isShowProductType = false;
      this.isShowCardNo = false;
      this.openshow = false;
      this.isShowDivision = true;
      this.isShowCompanyName = true;
      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "masterAccountNo": Number(this.accountnumber)
      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_DOWNLOADBYACCDEPT, obj).subscribe((response: any) => {
        this.divArray = [];
        if (response.status === 200) {
          console.log(response);

          let data = response.body;
          this.companyName = data.corpAccountDto.accountName;
          console.log(data);
          if (data.corpAccountDto == null) {
            if (data.divAccountList.length > 1) {
              var obj = {
                // "accountCategory": "",
                // "accountCode": "",
                // "accountName": "ALL",
                // "accountNo":'' ,
                // "nameToDisplayOnDropdown": "ALL",
                // "divAccountList":[
                // {
                "deptList": [],
                "divAccount": {
                  "accountCategory": "",
                  "accountCode": "",
                  "accountName": "ALL",
                  "accountNo": '',
                  "nameToDisplayOnDropdown": "ALL"
                }

                // }             
                //  ]

              }
              this.divArray.push(obj)

              data.divAccountList.forEach((element: any) => {
                console.log(element);
                this.divArray.push(element.divAccount);
                console.log(this.divArray);
              });
              this.frstDivSelection = this.divArray[0];
              console.log(this.divArray)
            }
          }
          if (data.corpAccountDto) {
            if (Object.keys(data.corpAccountDto).length > 0) {
              let obj = {
                accountCategory: data.corpAccountDto.accountCategory,
                accountCode: data.corpAccountDto.accountCode,
                accountName: data.corpAccountDto.accountName,
                accountNo: data.corpAccountDto.accountNo,
                nameToDisplayOnDropdown: data.corpAccountDto.nameToDisplayOnDropdown
              }
              this.divArray.push(obj);
              this.isCorpInvoice = true;
              console.log(this.divArray);
            }

          }

          if (data.divAccountList) {
            if (data.divAccountList.length > 0) {
              data.divAccountList.forEach((element: any) => {
                this.fetchDept.push(element);
                this.divArray.push(element.divAccount);
                console.log(this.divArray);
                this.frstDivSelection = this.divArray[0];
                if (element.deptList.length > 0) {
                  this.selectDept(this.divArray[0]);
                }
                console.log(element);
                // this.selectDept(this.frstDivSelection);              
              });
            }
          }



          // let opts = response.body.divAccountList;
          // opts.forEach((element:any) => {
          //   this.divArray.push(element.divAccount);
          //   if(element.deptList.length>1){
          //   this.deptArray.push(element.deptList);
          //   }
          // });
        }
      });


    }
  }
  onSubSelection(selected: string) {
    if (selected === 'productType') {
      if (this.creditLimitObj.productType) {
        this.isShowCardNo = true;
      }
    }
    else if (selected === 'division') {
      if (this.creditLimitObj.division === 'CABIN CREW (1003)') {
        this.isShowDept = true;
        this.isShowCompanyName = false;
      }
      else if (this.creditLimitObj.division) {
        this.isShowDept = false;
        this.isShowCompanyName = false;
      }
    }
  }
  selectDept(value: any) {
    console.log(value);
    this.defaultDept = ""
    this.isShowDivisionTbl = false;
    this.isShowAccountTbl = false;
    this.showDepTbl = false;
    this.isShowDepartment = false;

    this.isShowCompanyName = false;
    // this.isCabinCrew = false;
    console.log(this.divArray)
    console.log(value);
    if (value.nameToDisplayOnDropdown === "-CORPORATE INVOICE-") {
      this.isShowCompanyName = true;
    }
    else {
      this.isShowCompanyName = false;
    }
    if (value.nameToDisplayOnDropdown !== "ALL" && value.nameToDisplayOnDropdown !== "-CORPORATE INVOICE-") {
      const obj = this.fetchDept.find(x => x.divAccount.nameToDisplayOnDropdown === value.nameToDisplayOnDropdown)
      console.log(obj)
      this.deptArray = [];
      if (obj.deptList.length > 0) {
        this.isShowDepartment = true;
        this.isCabinCrew = true;
        this.deptArray.push({
          accountCategory: "",
          accountCode: "",
          accountName: "",
          accountNo: "",
          nameToDisplayOnDropdown: "-DIVISION INVOICE-"
        })
        obj.deptList.forEach((val: any) => {
          this.deptArray.push(val);
        });
        this.creditLimitObj.dept = this.deptArray[0];
        this.defaultDept = this.deptArray[0];
      }
      console.log(obj);
    }
  }
  reset(valid: boolean) {
    this.creditLimitObj = {
      type: "",
      division: "",
      dept: "",
      productType: "",
      cardNo: ""
    }
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUBAPPLICANT) {
      this.creditLimitObj.type = this.typeArr[1].viewValue;
      this.onSelection();
      this.isDisableType = true;
      this.isShowProductType = true;
    }
    else {
      this.isDisableType = false;
      this.isShowProductType = false;

    }
    this.isShowErrorOnReset = true;
    this.frstDivSelection = "",
      this.companyName = "",
      this.isShowCardNo = false;

    this.isShowDivisionTbl = false;
    this.isShowAccountTbl = false;
    this.showDepTbl = false;
    this.isShowCardTbl = false;
    this.isShowCompanyName = false;
    this.isShowDepartment = false;
    this.isShowDivision = false;
    this.openshow = false;

    this.defaultDept = ""
  }

  checkValidCard(value : any){
    if(!value){ //checking here "" & null
      return false;
    }else{
      return true;
    }
  }

  onSearch(valid: boolean) {
    this.isShowErrorOnReset = false;
    this.cardshowerror = true;
    if (valid) {
      if (this.creditLimitObj.type === 'Enquire Account Limit/Balance') {
        if (this.frstDivSelection) {
          // const selectedDivision = this.creditLimitObj.division.split(' (');
          // this.divisionDataSource = this.divisionCreditList.filter(o => o.accNo === selectedDivision[0]);
          // this.accDataSource = this.accountCreditList;
          // console.log(this.divisionDataSource)
          // this.isShowDivisionTbl = this.divisionDataSource.length ? true : false;
          // this.isShowAccountTbl = this.accDataSource.length ? true : false;
          if (this.defaultDept === "") {
            this.deptvalue = "";
          }
          else {
            this.deptvalue = this.defaultDept?.accountCode;
          }


          let obj = {


            "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
            "type": "A",
            "accountCategory": this.localStorage.localStorageGet("loginUserDetails").roles.accountcategory,
            "divisionCode": this.frstDivSelection?.accountCode,
            "departmentCode": this.deptvalue,
            "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
            "customerNo": this.customernumber,
            "accountNo": this.accountnumber
          }

          this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.CHECKLIMIT_ACCOUNT, obj).subscribe((response: any) => {
            if (response.status === 200) {

              this.divisionDataSource = [response.body.checkCreditLimitDivisionResponseDto];
              this.accDataSource = [response.body.checkCreditLimitAccountResponseDto];
              this.deptDataSource = [response.body.checkCreditLimitDeptResponseDto];
              console.log(this.divisionDataSource)
              if (response.body.successMessage !== null) {
                this.notifyService.showSuccess(response.body.successMessage, 'Credit Limit And  Balance')
              }
              else {
                this.notifyService.showError(response.body.errorMessage, 'Credit Limit And Balance')
              }
              if (this.divisionDataSource[0] !== null) {
                this.isShowDivisionTbl = true;
              }
              else {
                this.isShowDivisionTbl = false;
              }
              if (this.accDataSource[0] !== null) {
                this.isShowAccountTbl = true;
              }
              else {
                this.isShowAccountTbl = false;
              }
              if (this.deptDataSource[0] !== null) {
                this.showDepTbl = true;
              }
              else {
                this.showDepTbl = false;
              }

            }
          });
        }
        else {
          this.accDataSource = this.accountCreditList;
          this.isShowAccountTbl = this.accDataSource.length ? true : false;
        }
      }
      if (this.creditLimitObj.type === 'Enquire Card Limit/Balance') {
        if (this.creditLimitObj.productType !== "OV") {
          if (this.checkValidCard(this.creditLimitObj.cardNo)) {

            let obj = {
              "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
              "type": "C",
              "accountCategory": this.localStorage.localStorageGet("loginUserDetails").roles.accountcategory,
              "divisionCode": "",
              "departmentCode": "",
              "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
              "cardNo": this.firstdigit + this.creditLimitObj.cardNo,
              "productTypeId": this.creditLimitObj.productType,
              "customerNo": this.customernumber,
              "accountNo": this.accountnumber
            }
            this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.CHECKLIMIT_ACCOUNT, obj).subscribe((response: any) => {
              if (response.status === 200) {
                this.cardDataSource = [response.body.checkCreditLimitCardResponseDto];
                this.accDataSource = [response.body.checkCreditLimitAccountResponseDto];
                this.deptDataSource=[response.body.checkCreditLimitDeptResponseDto];
                this.divisionDataSource = [response.body.checkCreditLimitDivisionResponseDto];
                if (response.body.successMessage !== null) {
                  this.notifyService.showSuccess(response.body.successMessage, 'Credit Limit And Balance')
                }
                else {
                  this.notifyService.showError(response.body.errorMessage, 'Credit Limit And Balance')
                }
                if (this.cardDataSource || this.accDataSource || this.divisionDataSource || this.deptDataSource) {
                  if (this.cardDataSource[0] !== null) {
                    this.isShowCardTbl = true
                  }
                  else {
                    this.isShowCardTbl = false
                  }

                  if (this.accDataSource[0] !== null) {
                    this.isShowAccountTbl = true;
                  }
                  else {
                    this.isShowAccountTbl = false;
                  }
                  if (this.divisionDataSource[0] !== null) {
                    this.isShowDivisionTbl = true;
                  }
                  else {
                    this.isShowDivisionTbl = false;
                  }
                  if(this.deptDataSource[0]){
                    this.showDepTbl = true; 
                  }
                  else{
                    this.showDepTbl = false;
                  }
                }
                // else {
                //   this.notifyService.showError('No Records Found', 'Error')
                // }
              }
            });
            // this.cardDataSource = this.cardCreditList.filter(o => o.cardNo === this.creditLimitObj.cardNo && o.productName === this.creditLimitObj.productType);

          }
          else {
            if (this.showcarderror === true) {
              this.notifyService.showError('No Records Found', 'Credit Limit Balance')
            }
            else {
              this.notifyService.showWarning('Please enter valid CardNo', 'Credit Limit Balance')
            }
          }
        }
        else {
          if (this.checkValidCard(this.creditLimitObj.cardNo)) {
            let obj = {
              "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
              "type": "C",
              "accountCategory": this.localStorage.localStorageGet("loginUserDetails").roles.accountcategory,
              "divisionCode": "",
              "departmentCode": "",
              "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
              "cardNo": this.creditLimitObj.cardNo,
              "productTypeId": this.creditLimitObj.productType,
              "customerNo": this.customernumber,
              "accountNo": this.accountnumber
            }
            this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.CHECKLIMIT_ACCOUNT, obj).subscribe((response: any) => {
              if (response.status === 200) {
                this.cardDataSource = [response.body.checkCreditLimitCardResponseDto];
                this.accDataSource = [response.body.checkCreditLimitAccountResponseDto];

                this.divisionDataSource = [response.body.checkCreditLimitDivisionResponseDto];
                if (response.body.successMessage !== null) {
                  this.notifyService.showSuccess(response.body.successMessage, 'Credit Limit And Balance')
                }
                else {
                  this.notifyService.showError(response.body.errorMessage, 'Credit Limit And Balance')
                }
                if (this.cardDataSource || this.accDataSource || this.divisionDataSource) {
                  if (this.cardDataSource[0] !== null) {
                    this.isShowCardTbl = true
                  }
                  else {
                    this.isShowCardTbl = false
                  }

                  if (this.accDataSource[0] !== null) {
                    this.isShowAccountTbl = true;
                  }
                  else {
                    this.isShowAccountTbl = false;
                  }
                  if (this.divisionDataSource[0] !== null) {
                    this.isShowDivisionTbl = true;
                  }
                  else {
                    this.isShowDivisionTbl = false;
                  }
                }
                // else {
                //   this.notifyService.showError('No Records Found', 'Error')
                // }
              }
            });
          }
          else {
            if (this.showcarderror === true) {
              this.notifyService.showError('No Records Found', 'Credit Limit Balance')
            }
            else {
              this.notifyService.showWarning('Please enter valid CardNo', 'Credit Limit Balance')
            }
          }
        }
      }


    }

  }
}
  // sortData(sort: Sort) {
  //   const data = this.dataSource.slice();
  //   if (!sort.active || sort.direction === '') {
  //     this.sortedData = data;
  //     return;
  //   }
  //   this.sortedData = data.sort((x, y) => {
  //     const isAsc = sort.direction === 'asc';
  //     switch (sort.active) {
  //       case 'cardNo': return this.campare(x.cardNo, y.cardNo, isAsc);
  //       default: return 0;
  //     }
  //   });
  // }
  // campare(a: number | string, b: number | string, isAsc: boolean) {
  //   return (a < b ? -1 : 1) * (isAsc ? 1 : -1)
  // }
  // applyFilter(event: Event) {
  //   const filterValue = (event.target as HTMLInputElement).value;
  //   this.sortedData = this.dataSource.filter(o => o.cardNo.trim().toLowerCase().includes(filterValue.trim().toLowerCase()) || o.productType.trim().toLowerCase().includes(filterValue.trim().toLowerCase()) || 
  //   o.balance.trim().toLowerCase().includes(filterValue.trim().toLowerCase()) || o.limit.trim().toLowerCase().includes(filterValue.trim().toLowerCase()))
  // }

