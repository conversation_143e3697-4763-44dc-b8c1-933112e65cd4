<div fxLayout="column" fxLayoutGap="20px">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">price_change</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">Check Limit and Credit Balance</span>
        </div>
    </div>
    <form #f="ngForm" name="form">
        <div fxLayout="column" fxLayoutGap="5px" class="custom-padding-left">
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center" style="max-height: 55px;">
                    <span class="date-title">TYPE <span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%" fxFlex.md="75%">
                    <mat-form-field appearance="outline" class="inp">
                        <mat-select name="type" [(ngModel)]="creditLimitObj.type" #type="ngModel"
                            placeholder="Select Type" (selectionChange)="onSelection()" [disabled]="isDisableType" required>
                            <mat-option *ngFor="let acl of typeArr" [value]="acl.viewValue">
                                {{acl.viewValue}}
                            </mat-option>
                        </mat-select>
                        <mat-error *ngIf="f.submitted && type.invalid && !isShowErrorOnReset" class="pt5">
                            <div *ngIf="type.hasError('required')">
                                Type Cannot Be Blank
                            </div>
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px"
                *ngIf="isShowProductType">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center" style="max-height: 55px;">
                    <span class="date-title">PRODUCT TYPE<span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%" fxFlex.md="75%">
                    <mat-form-field appearance="outline" class="inp">
                        <!-- <mat-select name="productType" [(ngModel)]="creditLimitObj.productType" #productType="ngModel"
                            placeholder="Select Product Type" (selectionChange)="onSubSelection('productType')" [disabled]="isDisable" required>
                            <mat-option *ngFor="let acl of productArr" [value]="acl.viewValue">
                                {{acl.viewValue}}
                            </mat-option>
                        </mat-select> -->
                        <mat-select  name="productType" #productType="ngModel" [(ngModel)]="creditLimitObj.productType" (selectionChange)="onSelect($event)" placeholder="Select Product Type"
                        [disabled]="isDisable"   placeholder="Select Product Type" required>
                        <!-- [ngModel]="firstProductSelected" -->
                        <mat-option *ngFor="let acl of productArr" [value]="acl.productTypeId">
                            {{acl.name}}
                        </mat-option>
                    </mat-select>
                        <mat-error *ngIf="f.submitted && productType.invalid && !isShowErrorOnReset" class="pt5">
                            <div *ngIf="productType.hasError('required')">
                                Product Type Cannot Be Blank
                            </div>
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px" *ngIf="isShowCardNo">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
                    <span class="date-title">CARD NO.<span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxFlex="91%" fxFlex.lt-md="65%" fxFlex.md="75%" fxLayout="row" fxLayoutAlign="start center"> 
                    <span class="pb10" *ngIf="!openshow">{{this.displaycard}} </span>
                    <mat-error *ngIf="showcarderror" class="pt15">
                      No Card Details Found
                    </mat-error>
                    <mat-form-field appearance="outline" class="inp-cardno" *ngIf="!showcardfield">
                        <input matInput type="text" pattern="[0-9]*" name="cardNo"  [(ngModel)]="creditLimitObj.cardNo" #cardNo="ngModel" required/>
                        <mat-error *ngIf="f.submitted && cardNo.invalid && !isShowErrorOnReset && cardshowerror" class="pt15">
                            <div *ngIf="cardNo.hasError('required')">
                                Card No. Cannot Be Blank
                            </div>
                        </mat-error>
                        <mat-error *ngIf="cardNo.hasError('pattern')" class="pt15">
                            Please enter a number
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
            <!-- <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px" *ngIf="openshow">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
                    <span class="date-title">Card No.<span class="asterisk"><sup>*</sup></span>:</span>
                </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px" >
            <mat-form-field appearance="outline" class="inp">
                <mat-select placeholder="Card No:" name="cardNo" [(ngModel)]="creditLimitObj.cardNo"  #cardNo="ngModel"  required>              
                  <mat-option *ngFor = "let card of list;" [value]="card"  >
                    {{card}}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="f.submitted && cardNo.invalid && !isShowErrorOnReset && cardshowerror" class="pt15">
                    <div *ngIf="cardNo.hasError('required')">
                        Card No. Cannot Be Blank
                    </div>
                </mat-error>
              </mat-form-field>
            </div>
            </div> -->
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px"
                *ngIf="isShowDivision">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
                    <span class="date-title">DIVISION:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%" fxFlex.md="75%">
                    <!-- <mat-form-field appearance="outline" class="inp">
                        <mat-select name="division" [(ngModel)]="creditLimitObj.division" #division="ngModel"
                            placeholder="Select Division" (selectionChange)="onSubSelection('division')">
                            <mat-option *ngFor="let acl of divitionArr" [value]="acl.viewValue">
                                {{acl.viewValue}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field> -->
                    <mat-form-field fxFlex.gt-md="35%" fxFlex.lt-md="25%" fxFlex.md="50%" style="width: 100%;"
                    appearance='outline'>
                    <!-- <mat-label>Division</mat-label> -->
                    <mat-select name="division" [(ngModel)]="frstDivSelection"  #division="ngModel"
                    (selectionChange)="selectDept(frstDivSelection)">
                    <!-- (ngModelChange)="selectDept(frstDivSelection)" -->
                    <!-- (selectionChange)="onDivSelect($event)" -->

                    <mat-option *ngFor="let div of divArray" [value]="div">
                       {{div.nameToDisplayOnDropdown}}
                    </mat-option> 
                        <!-- <mat-option value="-CORPORATE INVOICE-">-CORPORATE INVOICE-</mat-option>
                        <mat-option value="CABIN CREW (1003)">CABIN CREW (1003)</mat-option>
                        <mat-option value="CORPORATE OFFICE (1005)">CORPORATE OFFICE (1005)</mat-option>
                        <mat-option value="CSM1 (1004)">CSM1 (1004)</mat-option> -->
                    </mat-select>
                </mat-form-field>
                </div>
            </div>
            <div fxFlex fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px"
                *ngIf="isShowCompanyName">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
                    <span class="date-title">COMPANY NAME:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%" fxFlex.md="75%">
                    {{companyName}}
                </div>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px" *ngIf="isShowDepartment">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
                    <span class="date-title">DEPARTMENT:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%" fxFlex.md="75%">
                    <!-- <mat-form-field appearance="outline" class="inp">
                        <mat-select name="dept" [(ngModel)]="creditLimitObj.dept" #dept="ngModel"
                            placeholder="Select Department">
                            <mat-option *ngFor="let acl of deptArr" [value]="acl.viewValue">
                                {{acl.viewValue}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field> -->
                    <mat-form-field fxFlex.gt-md="35%" fxFlex.lt-md="25%" fxFlex.md="50%" style="width: 100%;"
                    appearance='outline'>
                    <!-- <mat-label>Division</mat-label> -->
                    <mat-select name="dept" [(ngModel)]="defaultDept" #dept="ngModel">
                        <!-- <mat-option *ngIf="deptArray.length > 0" [value]="deptValue">ALL</mat-option> -->
                     <mat-option *ngFor="let dept of deptArray" [value]="dept" >
                {{dept?.nameToDisplayOnDropdown}}
            </mat-option> 
                        <!-- <mat-option value="-DIVISION INVOICE-">-DIVISION INVOICE-</mat-option>
                        <mat-option value="DEPT 1 (001A)">DEPT 1 (001A)</mat-option> -->
                    </mat-select>
                </mat-form-field>
                </div>
            </div>
            <div fxFlex class="pt10">
                <button mat-raised-button class="search-btn" [disabled]="this.disablesubmit" (click)="onSearch(f.form.valid)">SEARCH</button>
                &nbsp;
                <button mat-raised-button class="search-btn" (click)="reset(f.form.valid)">RESET</button>
            </div>
        </div>
    </form>
    <div fxLayout="column" *ngIf="isShowDivisionTbl" class="custom-padding-left" fxLayoutGap="5px">
        <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
            <span class="heading">DIVISION INFO</span>
        </div>
        <div class="tbl-container mat-elevation-z8">
            <mat-table #table [dataSource]="divisionDataSource">
                <ng-container matColumnDef="serialNo">
                    <mat-header-cell *matHeaderCellDef> S No. </mat-header-cell>
                    <mat-cell *matCellDef="let element;let i=index" class="sno" fxLayoutAlign="start center">
                        {{i+1}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="accountName">
                    <mat-header-cell *matHeaderCellDef>ACCOUNT NAME </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label"> ACCOUNT NAME :</span>
                        {{element.accountName}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="code">
                    <mat-header-cell *matHeaderCellDef> CODE </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label"> CODE:</span>
                        {{element.code}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="creditLimit">
                    <mat-header-cell *matHeaderCellDef>CREDIT LIMIT(S$) </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label">CREDIT LIMIT(S$):</span>
                        {{element.creditLimit | currency:'':''}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="creditBalance">
                    <mat-header-cell *matHeaderCellDef>CREDIT BALANCE(S$) </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label">CREDIT BALANCE(S$) :</span>
                        {{element.creditBalance | currency:'':''}}
                    </mat-cell>
                </ng-container>
                <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            </mat-table>
        </div>
    </div>
    <div fxLayout="column" *ngIf="showDepTbl" class="custom-padding-left" fxLayoutGap="5px">
        <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
            <span class="heading">DEPARTMENT INFO</span>
        </div>
        <div class="tbl-container mat-elevation-z8">
            <mat-table #table [dataSource]="deptDataSource">
                <ng-container matColumnDef="serialNo">
                    <mat-header-cell *matHeaderCellDef> S No. </mat-header-cell>
                    <mat-cell *matCellDef="let element;let i=index" class="sno" fxLayoutAlign="start center">
                        {{i+1}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="accountName">
                    <mat-header-cell *matHeaderCellDef>ACCOUNT NAME </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label"> ACCOUNT NAME:</span>
                        {{element.accountName}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="code">
                    <mat-header-cell *matHeaderCellDef> CODE </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label"> CODE:</span>
                        {{element.code}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="creditLimit">
                    <mat-header-cell *matHeaderCellDef>CREDIT LIMIT(S$) </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label">CREDIT LIMIT(S$):</span>
                        {{element.creditLimit | currency:'':''}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="creditBalance">
                    <mat-header-cell *matHeaderCellDef>CREDIT BALANCE(S$) </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label">CREDIT BALANCE(S$) :</span>
                        {{element.creditBalance | currency:'':''}}
                    </mat-cell>
                </ng-container>
                <mat-header-row *matHeaderRowDef="deparmentColumns"></mat-header-row>
                <mat-row *matRowDef="let row; columns: deparmentColumns;"></mat-row>
            </mat-table>
        </div>
    </div>
    
    <div fxLayout="column" *ngIf="isShowCardTbl" class="custom-padding-left" fxLayoutGap="5px">
        <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
            <span class="heading">CARD INFO</span>
        </div>
        <div class="tbl-container mat-elevation-z8">
            <mat-table #table [dataSource]="cardDataSource">
                <ng-container matColumnDef="serialNo">
                    <mat-header-cell *matHeaderCellDef> S No. </mat-header-cell>
                    <mat-cell *matCellDef="let element;let i=index" class="sno" fxLayoutAlign="start center">
                        {{i+1}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="nameOnCard">
                    <mat-header-cell *matHeaderCellDef>NAME ON CARD</mat-header-cell>
                    <mat-cell *matCellDef="let element" class="mr5">
                        <span class="mobile-label"> NAME ON CARD :</span>
                        {{element.nameOnCard}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="productName">
                    <mat-header-cell *matHeaderCellDef> PRODUCT NAME </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label"> PRODUCT NAME :</span>
                        {{element.productName}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="cardNo">
                    <mat-header-cell *matHeaderCellDef> CARD NO. </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label"> CARD NO. :</span>
                        {{element.cardNo}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="creditLimit">
                    <mat-header-cell *matHeaderCellDef>CREDIT LIMIT(S$) </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label">CREDIT LIMIT(S$):</span>
                        {{element.creditLimit | currency:'':''}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="creditBalance">
                    <mat-header-cell *matHeaderCellDef>CREDIT BALANCE(S$) </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label">CREDIT BALANCE(S$) :</span>
                        {{element.creditBalance | currency:'':''}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="status">
                    <mat-header-cell *matHeaderCellDef>STATUS</mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label">Status:</span>
                        {{element.status}}
                    </mat-cell>
                </ng-container>
                <mat-header-row *matHeaderRowDef="cardDisplayedColumns"></mat-header-row>
                <mat-row *matRowDef="let row; columns: cardDisplayedColumns;"></mat-row>
            </mat-table>
        </div>
    </div>
    <div fxLayout="column" *ngIf="isShowAccountTbl" class="custom-padding-left" fxLayoutGap="5px">
        <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
            <span class="heading">ACCOUNT INFO</span>
        </div>
        <div class="tbl-container mat-elevation-z8">
            <mat-table #table [dataSource]="accDataSource">
                <ng-container matColumnDef="serialNo">
                    <mat-header-cell *matHeaderCellDef> S No. </mat-header-cell>
                    <mat-cell *matCellDef="let element;let i=index" class="sno" fxLayoutAlign="start center">
                        {{i+1}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="accountName" >
                    <mat-header-cell *matHeaderCellDef>ACCOUNT NAME </mat-header-cell>
                    <mat-cell *matCellDef="let element" class="mr5">
                        <span class="mobile-label" > ACCOUNT NAME :</span>
                        {{element.accountName}}
                    </mat-cell>
                </ng-container>
                <!-- <ng-container matColumnDef="code">
                    <mat-header-cell *matHeaderCellDef> Code </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label"> Code :</span>
                        {{element.code}}
                    </mat-cell>
                </ng-container> -->
                <ng-container matColumnDef="creditLimit">
                    <mat-header-cell *matHeaderCellDef>CREDIT LIMIT(S$) </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label">CREDIT LIMIT(S$):</span>
                        {{element.creditLimit | currency:'':''}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="creditBalance">
                    <mat-header-cell *matHeaderCellDef>CREDIT BALANCE(S$) </mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <span class="mobile-label">CREDIT BALANCE(S$) :</span>
                        {{element.creditBalance | currency:'':''}}
                    </mat-cell>
                </ng-container>
                <mat-header-row *matHeaderRowDef="accountDisplayedColumns"></mat-header-row>
                <mat-row *matRowDef="let row; columns: accountDisplayedColumns;"></mat-row>
            </mat-table>
        </div>
    </div>
</div>