@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';

.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
:host input[type=number]::-webkit-outer-spin-button{
    opacity: 0;
}
:host input[type=number]::-webkit-inner-spin-button{
    opacity: 0;
}
.main-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
    height: 40px !important;
    width: 40px !important;
}
.mat-sort-header-arrow {
    color: $cdgc-font-accent;
}
.tbl-container{
    display: flex;
    flex-direction: column;
    // background-color: transparent !important;
    // max-width: 300px;
    max-height: 500px;
}
.mat-mdc-header-row{
    border-radius: 10px;
    background-color:$cdgc-bg-prime !important;
    color: $cdgc-font-accent !important;
}
.mat-mdc-header-cell {
    color: $cdgc-font-accent !important;
    
}
.mat-mdc-row{
    border-radius: 12px;
    color: $lightGrey !important;

}
.sno{
    display: block;
}
.mobile-label{
    display: none;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
 } 
 /* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
 :host ::ng-deep .mat-wrap .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
 }
 :host input[type=number]::-webkit-outer-spin-button{
    opacity: 0;
}
:host input[type=number]::-webkit-inner-spin-button{
    opacity: 0;
}
 .inp{
    width: 320px !important;
}
.search-icon{
    color: $cdgc-font-prime !important;
}
.heading{
    font-size: 18px;
    font-weight: $cdgsz-font-weight-bold;
}
@media screen and (max-width: 600px) {
    .mobile-label{
        display: inline-block;
        width: 165px;
        font-weight: $cdgsz-font-weight-bold;
    }
    .mat-mdc-header-row{
        display: none;
    }
    .mat-mdc-row{
        flex-direction: column;
        align-items: start;
        padding: 8px 24px;
        border-radius: 12px !important;
        border: 1px solid $cdgc-border-prime;
        min-height: 28px !important;
        // margin-bottom: 10px !important;
    }
    .sno{
        display: none !important;
    }
    .inp{
        width: 183px !important;
    }
}
