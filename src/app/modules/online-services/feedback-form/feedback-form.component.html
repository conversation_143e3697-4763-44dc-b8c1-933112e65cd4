<div fxLayout="column" >
    <!-- <div fxLayout="row" fxLayoutGap="40px" *ngIf="isShowCICHDropdown" class="pt5 pb10"> -->
        <!-- <span class="date-title pt10">Department :</span> -->
        <!-- <mat-form-field appearance="outline" class="inp">
            <mat-select [(ngModel)]="selectedData" (ngModelChange)="getVal()"
            [ngModelOptions]="{standalone: true}" name="cichDrodown">
              <mat-option *ngFor = "let val of cichDropdown;" [value]="val">
                  {{val}}
             </mat-option>
            </mat-select>
          </mat-form-field>  
    </div> -->
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">feedback</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">Feedback Form</span>
        </div>
    </div>
    <div fxLayout="row" class="pl pb50">
            <span class="sub-header">Got feedback? send us message and we'll get back to you.</span>
    </div>
    <div fxLayout="column" class="custom-padding-left sub-title">
        <span class="sub-header">INSTRUCTIONS:</span><br>
        <span>Items marked with <span style="color: red;font-weight: bold;">*</span>are mandatory.Please ensure all mandatory fields are filled</span>
</div><br>
    
    <form [formGroup]="feedbackForm">
    <div fxLayout="column" class="custom-padding-left" fxLayoutGap="30px">
        <!-- <div fxLayout="row" fxLayoutGap="40px" *ngIf="isShowCICHDropdown" class="pt5 pb10">
            <mat-form-field appearance="outline" class="inp">
                <mat-select [(ngModel)]="selectedData" (ngModelChange)="getVal()"
                [ngModelOptions]="{standalone: true}" name="cichDrodown">
                  <mat-option *ngFor = "let val of cichDropdown;" [value]="val">
                      {{val}}
                 </mat-option>
                </mat-select>
              </mat-form-field>  
        </div> -->
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">EMAIL<span class="asterisk"><sup>*</sup></span>:</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%">
                <mat-form-field appearance="outline" class="inp">
                    <input matInput name="email" formControlName="email" type="email"  placeholder="Email">
                    <!-- <input matInput name="email" formControlName="email"> -->
                    <!-- <mat-error *ngIf="feedbackForm.controls.email.hasError('required') || feedbackForm.controls.email.hasError('email')">
                        Enter valid Email
                    </mat-error> -->
                    <mat-error *ngIf="feedbackForm.controls.email.hasError('required')" class="mt15">
                        Please Fill in Email
                    </mat-error>
                    <mat-error *ngIf="submitted && feedbackForm.controls.email.hasError('email')" class="mt15">
                        Enter A Valid Email
                    </mat-error>
                </mat-form-field>
               
            <!-- </form> -->
            </div>
        </div>
        <!-- showMasterAccDetails -->
        <div *ngIf="showMasterAccDetails" fxLayout="column" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">ACCOUNT NUMBER:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%">
                  {{this.customerNumber}}
                </div>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">ACCOUNT NAME:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%">
                  {{this.accountName}}   
                </div>
            </div>
        </div>
       
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="!this.showcardno && !showMasterAccDetails">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">CONTACT PERSON:</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%">
              <!-- {{this.cdgService.loggedInUserDetails.userName}} -->
              {{this.contactPerson}}
            </div>
        </div>
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="this.showcardno && !showMasterAccDetails">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">CARD NO:</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%" *ngIf="!isShowCICHDropdown">
              <!-- {{this.cdgService.loggedInUserDetails.cardNo}} -->
              {{this.cardNo}}
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%" *ngIf="isShowCICHDropdown">
                <mat-form-field appearance="outline" class="inp">
                    <mat-select [(ngModel)]="selectedData" (ngModelChange)="getVal()"
                    [ngModelOptions]="{standalone: true}" name="cichDrodown">
                      <mat-option *ngFor = "let val of cichDropdown;" [value]="val">
                          {{val}}
                     </mat-option>
                    </mat-select>
                  </mat-form-field> 
            </div>
        </div>
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="!showMasterAccDetails">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">CONTACT NO:</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%">
              <!-- {{this.cdgService.loggedInUserDetails.telephone}} -->
              {{this.telephone}}
            </div>
        </div>
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayout="column">
                <span class="date-title">ATTACH DOCUMENTS:</span>
                <span class="attach">Accepted file formats jpg,docx,pdf,<br/>xlsx,csv,zip,doc,xls</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%" >
                <div fxFlex fxLayoutGap="10px">
                    <button mat-raised-button class="upload-btn" (click)="fileInput.click()">
                        upload
                        <input #fileInput type="file" id="attachment" name="attachment" formControlName="attachment" onclick="this.value=null" accept=".xls,.csv,jpg,.doc,.pdf,.zip" exclude=".com" (change)="onFileInput($event)" style="display: none;"/>
                    </button>
                    <!-- onclick="this.value=null" -->
                    <span >{{uploadFileName}}  <mat-icon *ngIf="isUpload" class="delete-icon" (click)="removeFile()">delete</mat-icon> </span>              
                </div>
                <mat-error *ngIf="fileSizeExceed" class="mt15">
                    File size should not exceed 1MB
                </mat-error>
                <mat-error *ngIf="notFileType" class="mt15">
                   Please upload correct file type
                </mat-error> 
            </div>
        </div>
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">COMMENTS<span class="asterisk"><sup>*</sup></span>:</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%" fxLayout="column" fxLayoutGap="8px">
                <mat-form-field appearance="outline" class="inp" >
                    <textarea matInput placeholder="Message" rows="8" name="comment" formControlName="comment" maxlength="1200" type="text"></textarea>
                    <mat-error *ngIf="submitted && feedbackForm.controls.comment.hasError('required')" class="mt15">
                        Please Fill in comments
                    </mat-error>
                    <mat-error *ngIf="submitted && feedbackForm.controls.comment.hasError('pattern')" class="mt15">Please Check the Comments</mat-error>
                    
                </mat-form-field>
                <span class="attach" *ngIf="feedbackForm.value.comment">Limited to 1200 characters ({{1200 - feedbackForm.value.comment.length}} characters left)</span>
                <span class="attach" *ngIf="!feedbackForm.value.comment">Limited to 1200 characters)</span>
            </div>
        </div>
        <p class="sub-title">
            Please wait for the acknowledgement message to ensure we recieve your submission. <br>
            If you do not see the acknowledgement message, please resubmit again
        </p>
        <div fxFlex>
            <button mat-raised-button class="download-btn" (click)="submit()">SUBMIT</button>
            &nbsp;
            <button mat-raised-button class="update-btn"
                (click)="resetForm()">RESET</button>
        </div>
    </div>
    
    </form>
    
</div>