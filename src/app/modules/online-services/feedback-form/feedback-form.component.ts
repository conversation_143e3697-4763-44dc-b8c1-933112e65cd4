import { Component, OnInit, Type } from '@angular/core';
import { Validators, FormBuilder, FormGroup, FormControl, AbstractControl, ValidationErrors } from '@angular/forms';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { RoleConstants } from 'src/assets/url-constants/url-constants';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { SupplementaryCardFormComponent } from '../../application-form/supplementary-card-form/supplementary-card-form.component';

@Component({
  selector: 'app-feedback-form',
  templateUrl: './feedback-form.component.html',
  styleUrls: ['./feedback-form.component.scss']
})
export class FeedbackFormComponent implements OnInit {
  feedbackForm:FormGroup
  uploadFile: any;
  uploadFileName: any;
  isUpload: boolean = false;
  showcardno: boolean = false;
  cardNo: any;
  telephone: any = '';
  accountnumber: any;
  accountName: any;
  email: any;
  contactPerson: any = '';
  customerNoListMaster: any;
  landingScreen: any;
  viewArrMain: any = [];
  accvalue: boolean;
  mainAccountNumber: any = [];
  showMasterAccDetails: boolean = false;
  customerNumber: any;
  fileBlob: any;
  b64Blob: any;
  uploadedFileSize: any;
  fileSizeExceed: boolean = false;
  submitted: boolean = false;
  isShowCICHDropdown: boolean = false;
  cichDropdown: any[] = [];
  selectedData: any;
  cichList: any[] = [];
  customernumber: any;
  fileType: any;
  notFileType: boolean = false;
  commentRegex = /^[a-zA-Z]+(?:[a-zA-Z\S,.!?]+[^\d]|)+\S*$/;
form: any;

// public noSpaceAllowed(control:FormControl) {
//   const isWhitespace = (control.value || '').trim().length === 0;
//   const isValid = !isWhitespace;
//   return isValid
// }

  constructor(private fb: FormBuilder, private apiService: ApiServiceService,private http:HttpClient, public cdgService: CdgSharedService, public localStorage: LocalStorageService, private notifyService: NotificationService) { 
    this.feedbackForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      name: [''],
      contactNo: [''],
      attachment: [''],
      comment: ['', [Validators.required, Validators.pattern(this.commentRegex)]]
    })
  }

  

  ngOnInit(): void {
    this.isShowCICHDropdown = false;
    this.landingScreen = this.localStorage.localStorageGet("landingScreenNamesDto")
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        console.log(this.accountnumber, "else")
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }
      console.log(this.accountnumber);
    }
    if (this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails) {
      this.customernumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number;
    }
    else {
      console.log(this.customernumber = this.localStorage.localStorageGet('customerviewvalue'));

      if (this.localStorage.localStorageGet('customerviewvalue') === null) {
        this.customernumber = this.localStorage.localStorageGet('viewvalueonload');
      }
      else {
        this.customernumber = this.localStorage.localStorageGet('customerviewvalue').substr(0, 6);
      }


    }
    this.feedbackForm.get('email')?.setValue(this.localStorage.localStorageGet("loginUserDetails").accessId);
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUBAPPLICANT || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
      this.showcardno = true;
    }
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER) {
      this.showMasterAccDetails = false;
      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName
      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.VIEW_FEEDBACK_FORM, obj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body;
          this.accountName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].main_account_name;
          this.cardNo = opt.cardNo;
          if (opt.cardNo = undefined) {
            this.cardNo = '';
          }
          this.email = opt.mainContactEmail;
          this.feedbackForm.get('email')?.setValue(this.email);
          if (opt.mainContactTel == 'null') {
            this.telephone = '';
          }
          else {
            this.telephone = opt.mainContactTel;
          }
          console.log(response.body);
        }
      })
    }
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUBAPPLICANT) {
      this.showMasterAccDetails = false;
      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName
      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.VIEW_FEEDBACK_FORM, obj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body;
          this.accountName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].main_account_name;
          this.cardNo = opt.subCardNo;
          this.email = opt.subMainContactEmail;
          this.feedbackForm.get('email')?.setValue(this.email);
          this.contactPerson = '';
          if (opt.subMainContactTel == null || opt.subMainContactTel == '') {
            this.telephone = '';
          }
          else {
            this.telephone = opt.subMainContactTel;
          }
          console.log(response.body)

        }
      })
    }
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
      this.showMasterAccDetails = false;
      this.isShowCICHDropdown = true;
      this.getCICHDropdownValue();
    }
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_MASTERUSER) {
      this.showMasterAccDetails = true;
      this.customerNoListMaster = this.landingScreen.masterDropdownDto.customerNumberList
      console.log(this.customerNoListMaster, "list")
      Object.entries(this.customerNoListMaster).forEach(
        ([key, value]) => {
          this.viewArrMain.push([key, value])
          console.log(this.viewArrMain);
        });
      this.mainAccountNumber = this.viewArrMain.find((x: any) => x[0] === this.accountnumber)
      console.log(this.mainAccountNumber)
      if (this.mainAccountNumber) {
        this.accountName = this.mainAccountNumber[1][1];
        this.customerNumber = this.mainAccountNumber[1][0];
      }
    }
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPADMIN) {
      this.showMasterAccDetails = false;
      console.log("master")
      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.VIEW_FEEDBACK_FORM, obj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body;
          this.accountName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].main_account_name;
          this.email = opt.mainContactEmail;
          this.feedbackForm.get('email')?.setValue(this.email);
          if (opt.mainContactName == null || opt.mainContactName == '') {
            this.contactPerson = '';
          }
          else {
            this.contactPerson = opt.mainContactName
          }

          if (opt.mainContactTel == null || opt.mainContactTel == '') {
            this.telephone = '';
          }
          else {
            this.telephone = opt.mainContactTel;
          }
        }
      })
    }
  }



  getCICHDropdownValue() {
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.CICH_CUSTOMER_DROPDOWN, obj).subscribe
      ((response: any) => {
        if (response.status === 200) {
          let opt = response.body.dropdownList;
          this.cichList = Object.values(opt);
          if (Object.values(opt).length > 0) {
            Object.values(opt).forEach((element: any) => {
              if (element.cardNumber.length > 0) {
                element.cardNumber.forEach((card: any) => {
                  this.cichDropdown.push(card);
                });
              }
            });
            this.selectedData = this.cichDropdown[0];
            this.getVal()
          }
          console.log(this.cichDropdown)
        }
      }, e => {
        this.notifyService.showError(e.error.errorMessage, "Error")

      })
  }
  getVal() {
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "cardNo": this.selectedData
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.VIEW_FEEDBACK_FORM, obj).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body;
        if (this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].main_account_name === '') {
          this.accountName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name;
        }
        else {
          this.accountName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].main_account_name;
        }

        this.email = opt.cardHolderEmail;
        this.feedbackForm.get('email')?.setValue(this.email);
        this.cardNo = '';
        console.log(opt.cardHolderTel)
        if (opt.cardHolderTel === 'null') {
          this.telephone = '';
        }
        if (opt.cardHolderTel != 'null') {
          this.telephone = opt.cardHolderTel;
        }
      }
    })
  }
  onFileInput(event: any) {
    console.log(event.target.file)
    if (event.target.files.length > 0) {
      this.uploadFile = event.target.files[0];
      console.log('size', this.uploadFile.size);
      this.uploadedFileSize = this.uploadFile.size;
      this.fileType = this.uploadFile.type;
      console.log(this.fileType,'testing file type');
      if(this.fileType == 'application/octet-stream'){
        this.notFileType = true;
        this.notifyService.showWarning("Please check Uploaded File Type", 'Warning!')
       
      }
      else{
        this.notFileType = false;
      }
      if (this.uploadedFileSize > 1000000) {
        this.fileSizeExceed = true;
        this.notifyService.showWarning("Uploaded File size exceeded", 'Warning!')
      }
      else {
        this.fileSizeExceed = false;
      }
      this.uploadFileName = this.uploadFile.name
      console.log(this.uploadFile)
      this.isUpload = true;
    }
  }


  removeFile() {
    this.uploadFile = "";
    this.uploadFileName = "";
    this.isUpload = false;
    this.fileSizeExceed = false;
    this.notFileType = false;
    //this.feedbackForm.controls.attachment.reset();
  }
  submit() {
    this.submitted = true;
    if (this.feedbackForm.valid && !this.fileSizeExceed && !this.notFileType) {

      let formData = new FormData();
      if (this.uploadFile !== undefined) {
        formData.append('file', this.uploadFile);
        formData.append('attachmentName', this.uploadFileName);
      }
      if (this.uploadFile === undefined) {
        // || this.uploadFile === ''
        this.uploadFile = '';
        this.uploadFileName = '';
        formData.append('file', this.uploadFile);
        formData.append('attachmentName', this.uploadFileName);
      }

      formData.append('accessId', this.localStorage.localStorageGet("loginUserDetails").accessId);
      formData.append('role', this.localStorage.localStorageGet("loginUserDetails").roles.roleName);
      formData.append('email', this.feedbackForm.controls.email.value);
      formData.append('contactNo', this.telephone);
      if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
        console.log(this.cichList)
        const data = this.cichList.filter((o: any) => o.cardNumber.includes(this.selectedData))
        console.log(data)
        formData.append('accountName', data[0].accountName);
      }
      else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPADMIN) {
        formData.append('accountName', this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name);
      }
      else {
        formData.append('accountName', this.accountName);
      }

      formData.append('comments', this.feedbackForm.controls.comment.value);
      if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUBAPPLICANT) {
        formData.append('card', this.cardNo);
      }
      if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
        formData.append('card', this.selectedData);
      }
      if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPADMIN) {
        formData.append('contactPerson', this.contactPerson);
      }
      if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
        console.log(this.selectedData,this.cichList)
        let cusNo:any
        this.cichList.forEach(element => {
          if (element.cardNumber.includes(this.selectedData)) {
            cusNo=element.customerNo;
          }
        });
        formData.append('customerNo', cusNo);
      }
      else {
        formData.append('customerNo', this.customernumber);
      }

      console.log(this.uploadFile)

      const options = {
        headers: new HttpHeaders().set('Accept','application/json').set("Authorization", this.localStorage.localStorageGet("auth"))
      }
      // Content-Type', 'multipart/form-data,boundary
      // this.cdgService.localhostMicroServiceUrl
      this.http.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.FILE_UPLOAD, formData,options
      ).subscribe
        ((response: any) => {
          console.log(response)
          if (response.successMessage != null) {
            this.notifyService.showSuccess(response.successMessage, "Success!");
            this.submitted = false;
            this.uploadFile = "";
            this.uploadFileName = "";
            this.isUpload = false;
            this.feedbackForm.get('comment')?.setValue('');

            // this.resetForm();
          }
          else {
            this.notifyService.showError(response.errorMessage, "Failed!");
          }

        })
    }
    else {
      return
    }

  }
  resetForm() {
    this.uploadFile = "";
    this.uploadFileName = "";
    this.isUpload = false;
    this.fileSizeExceed = false;
    this.notFileType = false;
    this.feedbackForm.reset();
  }
}
