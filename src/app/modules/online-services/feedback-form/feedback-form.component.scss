@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal !important ;
}
.sub-title {
    font-family: $cdgsz-font-family !important;
    font-style: normal;
    font-weight: bold;
    font-size: 12px;
    line-height: 16px;
}
:host .main-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
    height: 40px !important;
    width: 40px !important;
}

.delete-icon{
    color: $cdgc-bg-prime !important;
    cursor: pointer;
    vertical-align: middle !important;
}
.pl{
    padding-left: 65px !important;
}
.sub-header{
    font-size:$cdgsz-font-size-sm !important;
}
.inp{
    width: 300px !important;
}
.pb50{
    padding-bottom: 25px;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
 }
 .attach{
    font-size: 11px;
    font-weight: 400;
 }
 .upload-btn{
    background: #006BA8 !important;
    color: #fff !important;
    height: 50px;
    border-radius: 10px !important;
    font-size: 1.25rem !important;
    padding-right: 24px !important;
    padding-left: 24px !important;
 }
// .date-title{
//     font-size: $cdgsz-font-size-xs !important;
// }
@media screen and (max-width: 500px) {
    .inp{
        width: 180px !important;
    }
 }
 @media screen and (min-width: 500px) and (max-width: 800px) {
    .inp{
        width: 250px !important;
    }
 }