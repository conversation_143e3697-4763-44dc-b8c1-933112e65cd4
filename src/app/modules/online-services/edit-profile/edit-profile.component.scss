@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
::ng-deep .main-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
}
.update-btn{
    width:205px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
}
.login-type{
    font-size: $cdgsz-font-size-prime !important;
    font-weight: $cdgsz-font-weight-normal !important;
    margin-bottom: 0px !important;
    // padding-top: 10px;
}
.toggle-div{
    padding: 5px 60px;
}
.toggle-icon{
    font-size: 35px !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
::ng-deep .mat-mdc-button-toggle-group-appearance-standard .mat-button-toggle + .mat-button-toggle {
    border-radius: 10px;
    border: 1px solid $cdgc-border-hue;
}
.header-div{
 background-color: $cdgc-bg-prime;
 color: $cdgc-font-accent;
 border-radius: 5px;
 padding: 6px 25px;
 margin-bottom: 15px;
 font-size: 19px;
 font-weight: $cdgsz-font-weight-bold;
}
.cd{
    font-size: 19px;
    font-weight: $cdgsz-font-weight-normal;
}
.inp{
    width: 310px !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
   padding-bottom: 0px !important;
}


.dark-theme-mode .mat-app-background, .dark-theme-mode.mat-app-background {
    background-color: #143754;
    min-height: 100vh;   
}
.dark-theme-mode .header {
    color:#F9F9F9 !important;
    font-size: 28px;
    font-weight: 400;
}

.dark-theme-mode .darkMode{
    background-color:#F9F9F9 !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
:host ::ng-deep .dark-theme-mode .mat-button-toggle-appearance-standard {
    color: white ;
    background: #143754;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
:host ::ng-deep .dark-theme-mode .mat-mdc-button-toggle-checked.mat-button-toggle-appearance-standard{
    border-radius: 5px !important;
    background-color: #DA6C2A;
    color: #fff;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .dark-theme-mode .mat-form-field-appearance-outline .mat-mdc-form-field-flex {
    background-color: #1D374A !important;
    border: #454E71 !important;
    border-radius: 10px;
}
:host ::ng-deep .dark-theme-mode .header-div {
    background-color: #365E80 !important;
    color: #fff;
}
@media screen and (max-width: 570px) {
    .toggle-div{
        padding: 5px 30px;
    }
    .inp{
        width: auto !important;
    }
 }
 @media screen and (max-width: 500px) {
    .inp{
        width: 170px !important;
    }
 }
 @media screen and (max-width: 330px) {
    .inp{
        width: 156px !important;
    }

    .toggle-div{
        padding: 5px 10px 5px 10px;
    }

    .cd{
        font-size: 17px;
        font-weight: $cdgsz-font-weight-normal;
    }
 }
 .error {
    color: $cdgc-font-warn;
    font-weight: 600;
    font-size: $cdgsz-font-size-xs;
}

.mb25{
    margin-bottom: 25px !important;
}
.dropdown-div{
    // margin: 2% 3% 0 3%;
    // width:95%;
    background-color: #E7E7E7;
    font-size: 16px;
    line-height: 18px;
}
.error {
    font-size: 12px;
}
:host input[type=number]::-webkit-outer-spin-button{
    opacity: 0;
}
:host input[type=number]::-webkit-inner-spin-button{
    opacity: 0;
}
.validationBox{
    width: 375px !important;
    height: 272px !important;
    position: absolute;
    top: 25.1%;
    left: 50%;
    margin: 0;
    transform: translate(-50%,-50%);
    -ms-transform:translate(-50%,-50%);
    opacity: 1 !important;
    z-index: 9;
    border-radius: 10px;
    // background-color: $cdgc-bg-prime;
    box-shadow: 2px 2px 4px 2px rgba(243, 6, 6, 0.14);
    // box-shadow: 0 2px 4px 0 rgba(0,0,0,0.14);
    border: 4px double $cdgc-accent;
  
  }
  .validationBoxMaster{
    width: 375px !important;
    height: 272px !important;
    position: absolute;
    top: 47.1%;
    left: 50%;
    margin: 0;
    transform: translate(-50%,-50%);
    -ms-transform:translate(-50%,-50%);
    opacity: 1 !important;
    z-index: 9;
    border-radius: 10px;
    // background-color: $cdgc-bg-prime;
    box-shadow: 2px 2px 4px 2px rgba(243, 6, 6, 0.14);
    // box-shadow: 0 2px 4px 0 rgba(0,0,0,0.14);
    border: 4px double $cdgc-accent;
  
  }