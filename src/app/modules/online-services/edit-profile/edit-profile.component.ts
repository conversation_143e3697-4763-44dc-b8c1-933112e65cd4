import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { Val<PERSON>tors, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { RoleConstants } from 'src/assets/url-constants/url-constants';
import { NotificationService } from '../../../shared/services/notification.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
// export const editAdminForm = {
//   name: "",
//   telephone: 0,
//   faxno: 0,
//   mobno:0,
//   email:
// }
@Component({
  selector: 'app-edit-profile',
  templateUrl: './edit-profile.component.html',
  styleUrls: ['./edit-profile.component.scss']
})
export class EditProfileComponent implements OnInit {
  isDarkTheme: boolean = false;
  isUpdateBtn: boolean = false;
  selectedVal: any = 'edit'
  selectedSubApplicant = ""
  // , Validators.pattern(/^[0-9]{8,8}$/)]
  editAdminForm: FormGroup
  editPersonalForm:FormGroup
  editPersonalSubAppForm:FormGroup

  editIndividualForm:FormGroup
  editSubapplicantForm:FormGroup

  countryList: any[] = []
  subApplicantList: any[] = []
  subAppDetailsList: any[] = [];
  resp: any;
  tabs = ['Main Applicant'];
  selected = new FormControl(0);
  isShowRestForm: boolean = true;
  showSubAppError: boolean = false;
  cardNo: number | undefined;
  isViewOnly: boolean = false;
  loggedInRole: any;
  accountnumber: any;
  isAPIFromUpdate: boolean = false;
  
  indiCardSelected: any;
  isShowCardList: boolean = false;
  userCardSelected: any;
  indiCardList: any[] = [];
  billingAddress: boolean = true;
  shippingAddress: boolean = true;
  data: any;
  customernumber: any;
  isStateCountryDisable: boolean = false;

  constructor(private apiService: ApiServiceService, private http: HttpClient, public localStorage: LocalStorageService, private fb: FormBuilder, public cdgService: CdgSharedService, private notifyService: NotificationService) { 
    this.editAdminForm = this.fb.group({
      name: ['', [Validators.required]],
      telephone: ['', [Validators.required]],
      faxNo: [''],
      mobNo: [''],
      email: ['', [Validators.email]],
      alterName: [''],
      alterTelephone: [''],
      alterFaxNo: [''],
      alterMobNo: [''],
      alterEmail: ['', [Validators.email]],
      blockNo: [''],
      unitNo: [''],
      streetName: ['', [Validators.required]],
      buildingName: [''],
      area: [''],
      country: ['', [Validators.required]],
      city: [{ value: '', disabled: false }],
      state: [{ value: '', disabled: false }],
      postalCode: ['', [Validators.required]]
    });
    this.editPersonalForm = this.fb.group({
      telephone: ['', [Validators.required]],
      officeNo: [''],
      mobNo: [''],
      email: ['', [Validators.email]],
      blockNo: [''],
      unitNo: [''],
      streetName: ['', [Validators.required]],
      buildingName: [''],
      area: [''],
      country: ['', [Validators.required]],
      city: [''],
      state: [''],
      postalCode: ['', [Validators.required]],
      billingAddressSameAs: [true],
      bBlockNo: [''],
      bUnitNo: [''],
      bStreetName: ['', [Validators.required]],
      bBuildingName: [''],
      bArea: [''],
      bCountry: ['', [Validators.required]],
      bCity: [''],
      bState: [''],
      bPostalCode: ['', [Validators.required]],
      shipingAddressSameAs: [true],
      sBlockNo: [''],
      sUnitNo: [''],
      sStreetName: ['', [Validators.required]],
      sBuildingName: [''],
      sArea: [''],
      sCountry: ['', [Validators.required]],
      sCity: [''],
      sState: [''],
      sPostalCode: ['', [Validators.required]],
  
    });
    this.editPersonalSubAppForm = this.fb.group({
      telephone: ['', [Validators.required]],
      officeNo: [''],
      mobNo: [''],
      email: ['', [Validators.email]],
      blockNo: [''],
      unitNo: [''],
      streetName: ['', [Validators.required]],
      buildingName: [''],
      area: [''],
      country: ['', [Validators.required]],
      city: ['', [Validators.required]],
      state: ['', [Validators.required]],
      postalCode: ['', [Validators.required]]
    });
    this.editIndividualForm = this.fb.group({
      corporateName: [''],
      name: [''],
      cardNo: [''],
      position: [''],
      telephone: [''],
      mobNo: [''],
      email: ['', [Validators.email]],
    });
    this.editSubapplicantForm = this.fb.group({
      cardNo: [''],
      name: [''],
      telephone: [''],
      mobNo: ['', [Validators.minLength(8)]],
      officeNo: [''],
      email: ['', [Validators.email]],
      blockNo: [''],
      unitNo: [''],
      streetName: [''],
      buildingName: [''],
      area: [''],
      country: [''],
      city: [''],
      state: [''],
      postalCode: ['']
    });
  }

  ngOnInit(): void {
    this.isDarkTheme = localStorage.getItem('theme') === "Dark" ? true : false;
    this.loggedInRole = this.localStorage.localStorageGet("loggedInRole")
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = null;
      }
    }
    if (this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails) {
      this.customernumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number;
    }
    else {
      //  console.log(this.customernumber = this.localStorage.localStorageGet('customerviewvalue'));

      if (this.localStorage.localStorageGet('customerviewvalue') === null) {
        this.customernumber = this.localStorage.localStorageGet('viewvalueonload');
      }
      else {
        this.customernumber = this.localStorage.localStorageGet('customerviewvalue').substr(0, 6);
      }


    }
    let obj = {};
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPADMIN || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
      obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "accountNo": this.accountnumber
      }

    }
    else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUBAPPLICANT) {
      this.tabs.push('Sub Applicant')
      obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      }
    }
    else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_MASTERUSER) {
      this.selectedVal = 'reset';
    }
    this.getUserDetails(obj);

  }
  getUserDetails(obj: any) {
    // this.cdgService.localhostMicroServiceUrl
    // 'http://localhost:9072'
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.VIEW_PROFILE, obj).subscribe((response: any) => {
      let opt = response;
      // this.resp = response.body;
      this.localStorage.localStorageSet("editViewApiRes", response.body)
      this.resp = this.localStorage.localStorageGet("editViewApiRes")
      if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPADMIN) {
        this.isViewOnly = false;
        this.countryList = this.localStorage.localStorageGet("editViewApiRes").countryList;
        this.setValue(this.localStorage.localStorageGet("editViewApiRes"));
      }
      else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
        this.isViewOnly = false;
        this.isShowCardList = false;
        if (this.localStorage.localStorageGet("editViewApiRes").length > 0 && this.localStorage.localStorageGet("editViewApiRes").length === 1) {
          this.userCardSelected = this.localStorage.localStorageGet("editViewApiRes")[0];
          this.setValue(this.userCardSelected);
          this.isShowCardList = false;
        }
        else if (this.localStorage.localStorageGet("editViewApiRes").length > 1) {
          this.localStorage.localStorageGet("editViewApiRes").forEach((element: any) => {
            this.indiCardList.push(element.cardNo);
          });
          if (!this.isAPIFromUpdate) {
            this.indiCardSelected = this.localStorage.localStorageGet("editViewApiRes")[0].cardNo;
          } else {
            const data = this.localStorage.localStorageGet("editViewApiRes").find((o: any) => o.cardNo === this.indiCardSelected);
            this.indiCardSelected = data.cardNo;
          }
          this.userCardSelected = this.localStorage.localStorageGet("editViewApiRes")[0];
          this.setValue(this.userCardSelected);
          this.isShowCardList = true;
        }
      }
      else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER) {
        this.isViewOnly = false;
        this.countryList = this.localStorage.localStorageGet("editViewApiRes").countryList;
        this.setValue(this.localStorage.localStorageGet("editViewApiRes"));
        if (this.localStorage.localStorageGet("editViewApiRes").viewSappProfileResponseList.length > 0) {
          this.subAppDetailsList = this.localStorage.localStorageGet("editViewApiRes").viewSappProfileResponseList;
          // this.selectedSubApplicant=this.subAppDetailsList[0];
          // this.cardNo=this.subAppDetailsList[0].subCardNo;
          //  console.log(this.subAppDetailsList)
        }
      }
      else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUBAPPLICANT) {
        this.isViewOnly = true;
      }
    },
      e => {
        //  console.log(e)
      });
    // }

  }
  setValue(value: any) {
    if (this.loggedInRole === RoleConstants.ROLE_CORPADMIN) {
      this.editAdminForm.get('name')?.setValue(value.mainContactName !== "null" ? value.mainContactName : "")
      this.editAdminForm.get('telephone')?.setValue(value.mainContactTel !== "null" ? value.mainContactTel : "")
      this.editAdminForm.get('faxNo')?.setValue(value.mainContactFax !== "null" ? value.mainContactFax : "")
      this.editAdminForm.get('mobNo')?.setValue(value.mainContactMobile !== "null" ? value.mainContactMobile : "")
      this.editAdminForm.get('email')?.setValue(value.mainContactEmail !== "null" ? value.mainContactEmail : "")
      this.editAdminForm.get('alterName')?.setValue(value.subContactName !== "null" ? value.subContactName : "")
      this.editAdminForm.get('alterTelephone')?.setValue(value.subContactTel !== "null" ? value.subContactTel : "")
      this.editAdminForm.get('alterFaxNo')?.setValue(value.subContactFax !== "null" ? value.subContactFax : "")
      this.editAdminForm.get('alterMobNo')?.setValue(value.subContactMobile !== "null" ? value.subContactMobile : "")
      this.editAdminForm.get('alterEmail')?.setValue(value.subContactEmail !== "null" ? value.subContactEmail : "")
      this.editAdminForm.get('blockNo')?.setValue(value.addressBlock !== "null" ? value.addressBlock : "")
      this.editAdminForm.get('unitNo')?.setValue(value.addressUnit !== "null" ? value.addressUnit : "")
      this.editAdminForm.get('streetName')?.setValue(value.addressStreet !== "null" ? value.addressStreet : "")
      this.editAdminForm.get('buildingName')?.setValue(value.addressBuilding !== "null" ? value.addressBuilding : "")
      this.editAdminForm.get('area')?.setValue(value.addressArea !== "null" ? value.addressArea : "")
      this.editAdminForm.get('country')?.setValue(value.masterCode !== "null" ? value.masterCode : "")
      this.editAdminForm.get('city')?.setValue(value.addressCity !== "null" ? value.addressCity : "")
      this.editAdminForm.get('state')?.setValue(value.addressState !== "null" ? value.addressState : "")
      this.editAdminForm.get('postalCode')?.setValue(value.addressPostal !== "null" ? value.addressPostal : "")
      this.validateEmail(this.editAdminForm.value.email);
      this.countryValidation(this.editAdminForm.value.country)
    }
    else if (this.loggedInRole === RoleConstants.ROLE_CORPCARDHOLDER) {
      this.editIndividualForm.get('corporateName')?.setValue(value.nameOnCard !== "null" ? value.nameOnCard : "")
      this.editIndividualForm.get('name')?.setValue(value.cardHolderName !== "null" ? value.cardHolderName : "")
      this.editIndividualForm.get('cardNo')?.setValue(value.cardNo !== "null" ? value.cardNo : "")
      this.editIndividualForm.get('position')?.setValue(value.cardHolderTitle !== "null" ? value.cardHolderTitle : "")
      this.editIndividualForm.get('telephone')?.setValue(value.cardHolderTel !== "null" ? value.cardHolderTel : "")
      this.editIndividualForm.get('mobNo')?.setValue(value.cardHolderMobile !== "null" ? value.cardHolderMobile : "")
      this.editIndividualForm.get('email')?.setValue(value.cardHolderEmail !== "null" ? value.cardHolderEmail : "")
      // this.validateEmail(this.editIndividualForm.value.email);
    }
    //  else if (this.loggedInRole === RoleConstants.ROLE_SUBAPPLICANT) {
    //   this.editSubapplicantForm.get('cardNo')?.setValue(this.cdgService.loggedInUserDetails.cardNo)
    //   this.editSubapplicantForm.get('name')?.setValue(this.cdgService.loggedInUserDetails.userName)
    //   this.editSubapplicantForm.get('telephone')?.setValue(this.cdgService.loggedInUserDetails.telephone)
    //   this.editSubapplicantForm.get('mobNo')?.setValue(this.cdgService.loggedInUserDetails.mobNo)
    //   this.editSubapplicantForm.get('officeNo')?.setValue(this.cdgService.loggedInUserDetails.officeNo)
    //   this.editSubapplicantForm.get('email')?.setValue(this.cdgService.loggedInUserDetails.email)
    //   this.editSubapplicantForm.get('blockNo')?.setValue(this.cdgService.loggedInUserDetails.blockNo)
    //   this.editSubapplicantForm.get('unitNo')?.setValue(this.cdgService.loggedInUserDetails.unitNo)
    //   this.editSubapplicantForm.get('streetName')?.setValue(this.cdgService.loggedInUserDetails.streetName)
    //   this.editSubapplicantForm.get('buildingName')?.setValue(this.cdgService.loggedInUserDetails.buildingName)
    //   this.editSubapplicantForm.get('area')?.setValue(this.cdgService.loggedInUserDetails.area)
    //   this.editSubapplicantForm.get('country')?.setValue(this.cdgService.loggedInUserDetails.country)
    //   this.editSubapplicantForm.get('city')?.setValue(this.cdgService.loggedInUserDetails.city)
    //   this.editSubapplicantForm.get('state')?.setValue(this.cdgService.loggedInUserDetails.state)
    //   this.editSubapplicantForm.get('postalCode')?.setValue(this.cdgService.loggedInUserDetails.postalCode)
    // }
    else if (this.loggedInRole === RoleConstants.ROLE_PERSCARDHOLDER) {
      this.billingAddress = value.billingAddressSameAsMain === "Y" ? true : false;
      this.shippingAddress = value.shippingAddressSameAsMain === "Y" ? true : false;
      this.editPersonalForm.get('telephone')?.setValue(value.mainContactTel !== "null" ? value.mainContactTel : "")
      this.editPersonalForm.get('officeNo')?.setValue(value.contactTelephone !== "null" ? value.contactTelephone : "")
      this.editPersonalForm.get('mobNo')?.setValue(value.mainContactMobile !== "null" ? value.mainContactMobile : "")
      this.editPersonalForm.get('email')?.setValue(value.mainContactEmail !== "null" ? value.mainContactEmail : "")
      this.editPersonalForm.get('blockNo')?.setValue(value.addressBlock !== "null" ? value.addressBlock : "")
      this.editPersonalForm.get('unitNo')?.setValue(value.addressUnit !== "null" ? value.addressUnit : "")
      this.editPersonalForm.get('streetName')?.setValue(value.addressStreet !== "null" ? value.addressStreet : "")
      this.editPersonalForm.get('buildingName')?.setValue(value.addressBuilding !== "null" ? value.addressBuilding : "")
      this.editPersonalForm.get('area')?.setValue(value.addressArea !== "null" ? value.addressArea : "")
      this.editPersonalForm.get('country')?.setValue(value.countryCode !== "null" ? value.countryCode : "")
      this.editPersonalForm.get('city')?.setValue(value.addressCity !== "null" ? value.addressCity : "")
      this.editPersonalForm.get('state')?.setValue(value.addressState !== "null" ? value.addressState : "")
      this.editPersonalForm.get('postalCode')?.setValue(value.addressPostal !== "null" ? value.addressPostal : "")
      this.editPersonalForm.get('billingAddressSameAs')?.setValue(value.billingAddressSameAsMain === "Y" ? true : false)
      this.editPersonalForm.get('bBlockNo')?.setValue(value.baddressBlock !== "null" ? value.baddressBlock : "")
      this.editPersonalForm.get('bUnitNo')?.setValue(value.baddressUnit !== "null" ? value.baddressUnit : "")
      this.editPersonalForm.get('bStreetName')?.setValue(value.baddressStreet !== "null" ? value.baddressStreet : "")
      this.editPersonalForm.get('bBuildingName')?.setValue(value.baddressBuilding !== "null" ? value.baddressBuilding : "")
      this.editPersonalForm.get('bArea')?.setValue(value.baddressArea !== "null" ? value.baddressArea : "")
      this.editPersonalForm.get('bCountry')?.setValue(value.bcountryCode !== "null" ? value.bcountryCode : "")
      this.editPersonalForm.get('bCity')?.setValue(value.baddressCity !== "null" ? value.baddressCity : "")
      this.editPersonalForm.get('bState')?.setValue(value.baddressState !== "null" ? value.baddressState : "")
      this.editPersonalForm.get('bPostalCode')?.setValue(value.baddressPostal !== "null" ? value.baddressPostal : "")
      this.editPersonalForm.get('shipingAddressSameAs')?.setValue(value.shippingAddressSameAsMain === "Y" ? true : false)
      this.editPersonalForm.get('sBlockNo')?.setValue(value.saddressBlock !== "null" ? value.saddressBlock : "")
      this.editPersonalForm.get('sUnitNo')?.setValue(value.saddressUnit !== "null" ? value.saddressUnit : "")
      this.editPersonalForm.get('sStreetName')?.setValue(value.saddressStreet !== "null" ? value.saddressStreet : "")
      this.editPersonalForm.get('sBuildingName')?.setValue(value.saddressBuilding !== "null" ? value.saddressBuilding : "")
      this.editPersonalForm.get('sArea')?.setValue(value.saddressArea !== "null" ? value.saddressArea : "")
      this.editPersonalForm.get('sCountry')?.setValue(value.scountryCode !== "null" ? value.scountryCode : "")
      this.editPersonalForm.get('sCity')?.setValue(value.saddressCity !== "null" ? value.saddressCity : "")
      this.editPersonalForm.get('sState')?.setValue(value.saddressState !== "null" ? value.saddressState : "")
      this.editPersonalForm.get('sPostalCode')?.setValue(value.saddressPostal !== "null" ? value.saddressPostal : "")
      // this.validateEmail(this.editPersonalForm.value.email);
      this.countryValidation(this.editPersonalForm.value.country, "country")
      this.countryValidation(this.editPersonalForm.value.bCountry, "bCountry")
      this.countryValidation(this.editPersonalForm.value.sCountry, "sCountry")
    }
    //  console.log(this.editAdminForm)
  }
  getBillingAddress() {
    this.billingAddress = !this.billingAddress;
    let value = this.editPersonalForm.value;
    if (this.billingAddress) {
      this.editPersonalForm.get('bBlockNo')?.setValue(value.blockNo !== "null" ? value.blockNo : "")
      this.editPersonalForm.get('bUnitNo')?.setValue(value.unitNo !== "null" ? value.unitNo : "")
      this.editPersonalForm.get('bStreetName')?.setValue(value.streetName !== "null" ? value.streetName : "")
      this.editPersonalForm.get('bBuildingName')?.setValue(value.buildingName !== "null" ? value.buildingName : "")
      this.editPersonalForm.get('bArea')?.setValue(value.area !== "null" ? value.area : "")
      this.editPersonalForm.get('bCountry')?.setValue(value.country !== "null" ? value.country : "")
      this.editPersonalForm.get('bCity')?.setValue(value.city !== "null" ? value.city : "")
      this.editPersonalForm.get('bState')?.setValue(value.state !== "null" ? value.state : "")
      this.editPersonalForm.get('bPostalCode')?.setValue(value.postalCode !== "null" ? value.postalCode : "")
    }
    else {
      this.editPersonalForm.get('bBlockNo')?.setValue(this.localStorage.localStorageGet("editViewApiRes").baddressBlock !== "null" ? this.localStorage.localStorageGet("editViewApiRes").baddressBlock : "")
      this.editPersonalForm.get('bUnitNo')?.setValue(this.localStorage.localStorageGet("editViewApiRes").baddressUnit !== "null" ? this.localStorage.localStorageGet("editViewApiRes").baddressUnit : "")
      this.editPersonalForm.get('bStreetName')?.setValue(this.localStorage.localStorageGet("editViewApiRes").baddressStreet !== "null" ? this.localStorage.localStorageGet("editViewApiRes").baddressStreet : "")
      this.editPersonalForm.get('bBuildingName')?.setValue(this.localStorage.localStorageGet("editViewApiRes").baddressBuilding !== "null" ? this.localStorage.localStorageGet("editViewApiRes").baddressBuilding : "")
      this.editPersonalForm.get('bArea')?.setValue(this.localStorage.localStorageGet("editViewApiRes").baddressArea !== "null" ? this.localStorage.localStorageGet("editViewApiRes").baddressArea : "")
      this.editPersonalForm.get('bCountry')?.setValue(this.localStorage.localStorageGet("editViewApiRes").bcountryCode !== "null" ? this.localStorage.localStorageGet("editViewApiRes").bcountryCode : "")
      this.editPersonalForm.get('bCity')?.setValue(this.localStorage.localStorageGet("editViewApiRes").baddressCity !== "null" ? this.localStorage.localStorageGet("editViewApiRes").baddressCity : "")
      this.editPersonalForm.get('bState')?.setValue(this.localStorage.localStorageGet("editViewApiRes").baddressState !== "null" ? this.localStorage.localStorageGet("editViewApiRes").baddressState : "")
      this.editPersonalForm.get('bPostalCode')?.setValue(this.localStorage.localStorageGet("editViewApiRes").baddressPostal !== "null" ? this.localStorage.localStorageGet("editViewApiRes").baddressPostal : "")
    }
  }
  getShipingAddress() {
    this.shippingAddress = !this.shippingAddress;
    let value = this.editPersonalForm.value;
    if (this.shippingAddress) {
      this.editPersonalForm.get('sBlockNo')?.setValue(value.blockNo !== "null" ? value.blockNo : "")
      this.editPersonalForm.get('sUnitNo')?.setValue(value.unitNo !== "null" ? value.unitNo : "")
      this.editPersonalForm.get('sStreetName')?.setValue(value.streetName !== "null" ? value.streetName : "")
      this.editPersonalForm.get('sBuildingName')?.setValue(value.buildingName !== "null" ? value.buildingName : "")
      this.editPersonalForm.get('sArea')?.setValue(value.area !== "null" ? value.area : "")
      this.editPersonalForm.get('sCountry')?.setValue(value.country !== "null" ? value.country : "")
      this.editPersonalForm.get('sCity')?.setValue(value.city !== "null" ? value.city : "")
      this.editPersonalForm.get('sState')?.setValue(value.state !== "null" ? value.state : "")
      this.editPersonalForm.get('sPostalCode')?.setValue(value.postalCode !== "null" ? value.postalCode : "")
    }
    else {
      this.editPersonalForm.get('sBlockNo')?.setValue(this.localStorage.localStorageGet("editViewApiRes").saddressBlock !== "null" ? this.localStorage.localStorageGet("editViewApiRes").saddressBlock : "")
      this.editPersonalForm.get('sUnitNo')?.setValue(this.localStorage.localStorageGet("editViewApiRes").saddressUnit !== "null" ? this.localStorage.localStorageGet("editViewApiRes").saddressUnit : "")
      this.editPersonalForm.get('sStreetName')?.setValue(this.localStorage.localStorageGet("editViewApiRes").saddressStreet !== "null" ? this.localStorage.localStorageGet("editViewApiRes").saddressStreet : "")
      this.editPersonalForm.get('sBuildingName')?.setValue(this.localStorage.localStorageGet("editViewApiRes").saddressBuilding !== "null" ? this.localStorage.localStorageGet("editViewApiRes").saddressBuilding : "")
      this.editPersonalForm.get('sArea')?.setValue(this.localStorage.localStorageGet("editViewApiRes").saddressArea !== "null" ? this.localStorage.localStorageGet("editViewApiRes").saddressArea : "")
      this.editPersonalForm.get('sCountry')?.setValue(this.localStorage.localStorageGet("editViewApiRes").scountryCode !== "null" ? this.localStorage.localStorageGet("editViewApiRes").scountryCode : "")
      this.editPersonalForm.get('sCity')?.setValue(this.localStorage.localStorageGet("editViewApiRes").saddressCity !== "null" ? this.localStorage.localStorageGet("editViewApiRes").saddressCity : "")
      this.editPersonalForm.get('sState')?.setValue(this.localStorage.localStorageGet("editViewApiRes").saddressState !== "null" ? this.localStorage.localStorageGet("editViewApiRes").saddressState : "")
      this.editPersonalForm.get('sPostalCode')?.setValue(this.localStorage.localStorageGet("editViewApiRes").saddressPostal !== "null" ? this.localStorage.localStorageGet("editViewApiRes").saddressPostal : "")
    }
  }
  storeThemeSelection() {
    localStorage.setItem('theme', this.isDarkTheme ? "Dark" : "Light");
  }
  getSelectedGrp() {
    if (this.selected.value === 1) {
      this.isShowRestForm = false;
      this.selectedSubApplicant = ""
    }
  }
  subApplicant() {
    this.isViewOnly = false;
    this.showSubAppError = false;
    this.data = this.subAppDetailsList.find(o => o.subAccountName === this.selectedSubApplicant)
    //  console.log(this.data)
    this.isShowRestForm = true;
    this.cardNo = this.data?.subCardNo;
    this.editPersonalSubAppForm.get('telephone')?.setValue(this.data?.subContactTelephone)
    this.editPersonalSubAppForm.get('officeNo')?.setValue(this.data?.subMainContactTel)
    this.editPersonalSubAppForm.get('mobNo')?.setValue(this.data?.subMainContactMobile)
    this.editPersonalSubAppForm.get('email')?.setValue(this.data?.subMainContactEmail)
    this.editPersonalSubAppForm.get('blockNo')?.setValue(this.data?.subAddressBlock)
    this.editPersonalSubAppForm.get('unitNo')?.setValue(this.data?.subAddressUnit)
    this.editPersonalSubAppForm.get('streetName')?.setValue(this.data?.subAddressStreet)
    this.editPersonalSubAppForm.get('buildingName')?.setValue(this.data?.subAddressBuilding)
    this.editPersonalSubAppForm.get('area')?.setValue(this.data?.subAddressArea)
    this.editPersonalSubAppForm.get('country')?.setValue(this.data?.subCountryCode)
    this.editPersonalSubAppForm.get('city')?.setValue(this.data?.subAddressCity)
    this.editPersonalSubAppForm.get('state')?.setValue(this.data?.subAddressState)
    this.editPersonalSubAppForm.get('postalCode')?.setValue(this.data?.subAddressPostal)
    //  console.log(this.editPersonalSubAppForm)
    this.countryValidation(this.editPersonalSubAppForm.value.country, "subApps")
  }
  onUpdate() {
    if (this.loggedInRole === RoleConstants.ROLE_CORPADMIN) {
      //   if(this.editAdminForm.value.telephone.toString().length<8){
      //     this.notifyService.showWarning('Please check the Mandatory Field', 'Warning')
      //        return; 
      //   }
      //   if(this.editAdminForm.value.mobNo!=='' && this.editAdminForm.value.mobNo.toString().length<8){

      //     this.notifyService.showWarning('Please check the Mobile No field', 'Warning')
      //        return;  

      // }
      // if(this.editAdminForm.value.alterMobNo!=='' && this.editAdminForm.value.alterMobNo.toString().length<8){

      //     this.notifyService.showWarning('Please check the Alter Mobile No field', 'Warning')
      //     return;

      // }
      // if(this.editAdminForm.value.alterTelephone!=='' && this.editAdminForm.value.alterTelephone.toString().length<8){

      //     this.notifyService.showWarning('Please check the Alter Telephone No field', 'Warning')
      //     return;

      // }
      if (this.editAdminForm.invalid) {
        this.notifyService.showWarning('Please check if Mandatory fields are blank or some field value entered is Invalid', 'Warning')
        return;
      }
      else {
        let obj = {
          accessId: this.localStorage.localStorageGet("loginUserDetails").accessId,
          accountName: this.resp.accountName + "(ID:" + this.customernumber + ")",
          custNo: this.customernumber,
          mainContactName: this.editAdminForm.value.name,
          mainContactTel: this.editAdminForm.value.telephone,
          mainContactMobile: this.editAdminForm.value.mobNo,
          mainContactFax: this.editAdminForm.value.faxNo,
          mainContactEmail: this.editAdminForm.value.email,
          subContactName: this.editAdminForm.value.alterName,
          subContactTel: this.editAdminForm.value.alterTelephone ? this.editAdminForm.value.alterTelephone.toString() : "",
          subContactMobile: this.editAdminForm.value.alterMobNo,
          subContactFax: this.editAdminForm.value.alterFaxNo,
          subContactEmail: this.editAdminForm.value.alterEmail,
          addressBlock: this.editAdminForm.value.blockNo,
          addressUnit: this.editAdminForm.value.unitNo,
          addressStreet: this.editAdminForm.value.streetName,
          addressBuilding: this.editAdminForm.value.buildingName,
          addressArea: this.editAdminForm.value.area,
          addressPostal: this.editAdminForm.value.postalCode,
          addressCity: this.editAdminForm.value.city,
          addressState: this.editAdminForm.value.state,
          masterCode: this.editAdminForm.value.country
        }
        //  console.log(obj)
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.UPDATE_PROFILE_CROPADMIN, obj).subscribe((response: any) => {
          let opt = response;
          //  console.log(opt);
          if (response.status === 200) {
            if (opt.body.successMessage) {
              this.notifyService.showSuccess(opt.body.successMessage, 'Success')
            }
            else {
              this.notifyService.showError(opt.body.errorMessage, 'Error')
            }
          }
        },
          e => {
            //  console.log(e)
          });
        //this.notifyService.showSuccess('Details Updated Successfully', 'Success')
      }
    }
    else if (this.loggedInRole === RoleConstants.ROLE_PERSCARDHOLDER) {
      //  console.log(this.editPersonalForm)
      // if(this.editPersonalForm.value.telephone.toString().length<8){
      //   this.notifyService.showWarning('Please check the Mandatory Field', 'Warning')
      //      return; 
      // }
      //   if( this.editPersonalForm.value.mobNo!=='' && this.editPersonalForm.value.mobNo.toString().length<8 ){
      //     this.notifyService.showWarning('Please check the Mobile No field', 'Warning')
      //        return;  
      // }
      // if(this.editPersonalForm.value.alterMobNo!=='' && this.editPersonalForm.value.alterMobNo.toString().length<10){
      //     this.notifyService.showWarning('Please check the Alter Mobile No field', 'Warning')
      //     return;
      // }
      // if(this.editPersonalForm.value.alterTelephone!=='' && this.editPersonalForm.value.alterTelephone.toString().length<10){
      //     this.notifyService.showWarning('Please check the Alter Telephone No Field', 'Warning')
      //     return;
      // }

      if (this.selected.value === 0) {
        //  console.log(this.editPersonalForm.value)
        if (this.editPersonalForm.invalid) {
          this.notifyService.showWarning('Please check if Mandatory fields are blank or some field value entered is Invalid', 'Warning')
          return;
        }
        else {
          let obj = {
            "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
            "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
            "cardNo": this.localStorage.localStorageGet("editViewApiRes").cardNo,
            "accountName": this.localStorage.localStorageGet("editViewApiRes").accountName + "(ID:" + this.customernumber + ")",
            "custNo": this.customernumber,
            "accountNo": this.localStorage.localStorageGet("editViewApiRes").accountNo,
            "contactTelephone": this.editPersonalForm.value.officeNo,
            "mainContactTel": this.editPersonalForm.value.telephone,
            "mainContactMobile": this.editPersonalForm.value.mobNo,
            "mainContactEmail": this.editPersonalForm.value.email,
            "addressBlock": this.editPersonalForm.value.blockNo,
            "addressUnit": this.editPersonalForm.value.unitNo,
            "addressStreet": this.editPersonalForm.value.streetName,
            "addressBuilding": this.editPersonalForm.value.buildingName,
            "addressArea": this.editPersonalForm.value.area,
            "addressPostal": this.editPersonalForm.value.postalCode,
            "addressCity": this.editPersonalForm.value.city,
            "addressState": this.editPersonalForm.value.state,
            "countryCode": this.editPersonalForm.value.country,
            "bcountryCode": this.editPersonalForm.value.billingAddressSameAs?this.editPersonalForm.value.country: this.editPersonalForm.value.bCountry,
            "scountryCode": this.editPersonalForm.value.shipingAddressSameAs?this.editPersonalForm.value.country:this.editPersonalForm.value.sCountry,
            "billingAddressSameAsMain": this.editPersonalForm.value.billingAddressSameAs === true ? "Y" : "N",
            "baddressBlock":this.editPersonalForm.value.billingAddressSameAs?this.editPersonalForm.value.blockNo: this.editPersonalForm.value.bBlockNo,
            "baddressUnit":this.editPersonalForm.value.billingAddressSameAs?this.editPersonalForm.value.unitNo: this.editPersonalForm.value.bUnitNo,
            "baddressStreet": this.editPersonalForm.value.billingAddressSameAs?this.editPersonalForm.value.streetName:this.editPersonalForm.value.bStreetName,
            "baddressBuilding": this.editPersonalForm.value.billingAddressSameAs?this.editPersonalForm.value.buildingName:this.editPersonalForm.value.bBuildingName,
            "baddressArea": this.editPersonalForm.value.billingAddressSameAs?this.editPersonalForm.value.area:this.editPersonalForm.value.bArea,
            "baddressPostal": this.editPersonalForm.value.billingAddressSameAs?this.editPersonalForm.value.postalCode:this.editPersonalForm.value.bPostalCode,
            "baddressCity": this.editPersonalForm.value.billingAddressSameAs?this.editPersonalForm.value.city:this.editPersonalForm.value.bCity,
            "baddressState": this.editPersonalForm.value.billingAddressSameAs?this.editPersonalForm.value.state:this.editPersonalForm.value.bState,
            "shippingAddressSameAsMain": this.editPersonalForm.value.shipingAddressSameAs === true ? "Y" : "N",
            "saddressBlock": this.editPersonalForm.value.shipingAddressSameAs?this.editPersonalForm.value.blockNo:this.editPersonalForm.value.sBlockNo,
            "saddressUnit": this.editPersonalForm.value.shipingAddressSameAs?this.editPersonalForm.value.unitNo:this.editPersonalForm.value.sUnitNo,
            "saddressStreet":this.editPersonalForm.value.shipingAddressSameAs?this.editPersonalForm.value.streetName: this.editPersonalForm.value.sStreetName,
            "saddressBuilding": this.editPersonalForm.value.shipingAddressSameAs?this.editPersonalForm.value.buildingName:this.editPersonalForm.value.sBuildingName,
            "saddressArea": this.editPersonalForm.value.shipingAddressSameAs?this.editPersonalForm.value.area:this.editPersonalForm.value.sArea,
            "saddressPostal": this.editPersonalForm.value.shipingAddressSameAs?this.editPersonalForm.value.postalCode:this.editPersonalForm.value.sPostalCode,
            "saddressCity": this.editPersonalForm.value.shipingAddressSameAs?this.editPersonalForm.value.city:this.editPersonalForm.value.sCity,
            "saddressState": this.editPersonalForm.value.shipingAddressSameAs?this.editPersonalForm.value.state:this.editPersonalForm.value.sState
          }
          this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.UPDATE_PROFILE_PERSCARDHOLDER, obj).subscribe((response: any) => {
            let opt = response;
            if (response.status === 200) {
              if (opt.body.successMessage) {
                this.notifyService.showSuccess(opt.body.successMessage, 'Success')
              }
              else {
                this.notifyService.showError(opt.body.errorMessage, 'Error')
              }
            }
          },
            e => {
              this.notifyService.showError(e.error.errorMessage, 'Error')
            });
        }
      }
      else if (this.selected.value === 1) {
        if (this.editPersonalSubAppForm.invalid) {
          this.notifyService.showWarning('Please check if Mandatory fields are blank or some field value entered is Invalid', 'Warning')
          return;
        }
        else {
          let obj = {
            "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
            "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
            "subCardNo": this.data.subCardNo,
            "subAccountName": this.data.subAccountName + "(ID:" + this.customernumber + ")",
            "custNo": this.customernumber,
            "subMainContactTel": this.editPersonalSubAppForm.value.officeNo,
            "subMainContactMobile": this.editPersonalSubAppForm.value.mobNo,
            "subMainContactEmail": this.editPersonalSubAppForm.value.email,
            "subAddressBlock": this.editPersonalSubAppForm.value.blockNo,
            "subAddressUnit": this.editPersonalSubAppForm.value.unitNo,
            "subAddressStreet": this.editPersonalSubAppForm.value.streetName,
            "subAddressBuilding": this.editPersonalSubAppForm.value.buildingName,
            "subAddressArea": this.editPersonalSubAppForm.value.area,
            "subAddressPostal": this.editPersonalSubAppForm.value.postalCode,
            "subAddressCity": this.editPersonalSubAppForm.value.city,
            "subAddressState": this.editPersonalSubAppForm.value.state,
            "subCountryCode": this.editPersonalSubAppForm.value.country,
            "subContactTelephone": this.editPersonalSubAppForm.value.telephone

          }
          //  console.log(obj)
          this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.UPDATE_PROFILE_PERSCARDHOLDER_SUBAPP, obj).subscribe((response: any) => {
            let opt = response;
            //  console.log(opt)
            if (response.status === 200) {
              if (opt.body.successMessage) {
                let obj = {
                  "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
                  "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
                }
                this.getUserDetails(obj);
                this.notifyService.showSuccess(opt.body.successMessage, 'Success')
              }
              else {
                this.notifyService.showError(opt.body.errorMessage, 'Error')
              }
            }
          },
            e => {
              this.notifyService.showError(e.error.errorMessage, 'Error')
            });
        }

      }

    }
    else if (this.loggedInRole === RoleConstants.ROLE_CORPCARDHOLDER) {
      if (this.editIndividualForm.invalid) {
        this.notifyService.showWarning('Please check Email Format', 'Warning')
        return;
      }

      //   if(this.editIndividualForm.value.telephone.toString().length<8){
      //     this.notifyService.showWarning('Please check the Mandatory Field', 'Warning')
      //        return; 
      //   }
      //   if( this.editIndividualForm.value.mobNo!==null && this.editIndividualForm.value.mobNo.toString().length<8 ){
      //     this.notifyService.showWarning('Please check the Mobile No field', 'Warning')
      //        return;  
      // }
      // if(this.editIndividualForm.value.alterMobNo!==null && this.editIndividualForm.value.alterMobNo.toString().length<10){
      //     this.notifyService.showWarning('Please check the Alter Mobile No field', 'Warning')
      //     return;
      // }
      // if(this.editIndividualForm.value.alterTelephone!==null && this.editIndividualForm.value.alterTelephone.toString().length<10){
      //     this.notifyService.showWarning('Please check the Alter Telephone No Field', 'Warning')
      //     return;
      // }
      else {
        let obj = {
          accessId: this.localStorage.localStorageGet("loginUserDetails").accessId,
          cardNo: this.userCardSelected.cardNo,
          cardHolderName: this.userCardSelected.cardHolderName,
          cardHolderTitle: this.editIndividualForm.value.position,
          cardHolderTel: this.editIndividualForm.value.telephone,
          cardHolderMobile: this.editIndividualForm.value.mobNo,
          cardHolderEmail: this.editIndividualForm.value.email,
          nameOnCard: this.userCardSelected.nameOnCard
        }
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.UPDATE_PROFILE_INDIVIDUALCARDHOLDER, obj).subscribe((response: any) => {
          let opt = response;
          if (response.status === 200) {
            if (opt.body.successMessage) {
              this.isAPIFromUpdate = true;
              let obj = {
                "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
                "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
              }
              this.getUserDetails(obj)
              this.notifyService.showSuccess(opt.body.successMessage, 'Success')
            }
            else {
              this.notifyService.showError(opt.body.errorMessage, 'Error')
            }
          }
        },
          e => {
            //  console.log(e)
          });
      }
    }
    // else if (this.loggedInRole === 'Subapplicant') {
    //   if (this.editSubapplicantForm.invalid) {
    //     this.notifyService.showWarning('Mandatory Fields cannot be blank', 'Warning')
    //     return;
    //   }
    //   else {
    //     this.notifyService.showSuccess('Details Updated Successfully', 'Success')
    //   }
    // }
  }
  getSelectedCardDetails(cardSelected: any) {
    //  console.log(cardSelected, this.resp)
    this.userCardSelected = this.resp.find((o: any) => o.cardNo === cardSelected);
    this.setValue(this.userCardSelected);
  }
  validateEmail(val?: any) {
    this.isUpdateBtn = false;
    if (this.loggedInRole === RoleConstants.ROLE_CORPADMIN && val.toLowerCase() !== this.localStorage.localStorageGet("loginUserDetails").accessId.toLowerCase()) {
      this.isUpdateBtn = true;
      this.editAdminForm.disable();
    }
  }
  countryValidation(event: any, fieldName?: any) {
    //  console.log(event, fieldName)
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPADMIN) {
      if (event === "SG") {
        this.editAdminForm.controls['city'].reset()
        this.editAdminForm.controls['state'].reset()
        this.editAdminForm.controls['city'].disable()
        this.editAdminForm.controls['state'].disable()
      }
      else {
        this.editAdminForm.controls['city'].enable()
        this.editAdminForm.controls['state'].enable()
      }
    }
    else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER) {
      if (fieldName === "country") {
        if (event === "SG") {
          this.editPersonalForm.controls['city'].reset()
          this.editPersonalForm.controls['state'].reset()
          this.editPersonalForm.controls['city'].disable()
          this.editPersonalForm.controls['state'].disable()
        } else {
          this.editPersonalForm.controls['city'].enable()
          this.editPersonalForm.controls['state'].enable()
        }
      }
      else if (fieldName === "bCountry") {
        if (event === "SG") {
          this.editPersonalForm.controls['bCity'].reset()
          this.editPersonalForm.controls['bState'].reset()
          this.editPersonalForm.controls['bCity'].disable()
          this.editPersonalForm.controls['bState'].disable()
        } else {
          this.editPersonalForm.controls['bCity'].enable()
          this.editPersonalForm.controls['bState'].enable()
        }
      }
      else if (fieldName === "sCountry") {
        if (event === "SG") {
          this.editPersonalForm.controls['sCity'].reset()
          this.editPersonalForm.controls['sState'].reset()
          this.editPersonalForm.controls['sCity'].disable()
          this.editPersonalForm.controls['sState'].disable()
        } else {
          this.editPersonalForm.controls['sCity'].enable()
          this.editPersonalForm.controls['sState'].enable()
        }
      }
      else if (fieldName === "subApps") {
        if (event === "SG") {
          this.editPersonalSubAppForm.controls['city'].reset()
          this.editPersonalSubAppForm.controls['state'].reset()
          this.editPersonalSubAppForm.controls['city'].disable()
          this.editPersonalSubAppForm.controls['state'].disable()
        } else {
          this.editPersonalSubAppForm.controls['city'].enable()
          this.editPersonalSubAppForm.controls['state'].enable()
        }
      }
    }

    //  console.log(this.isStateCountryDisable)
  }
}
