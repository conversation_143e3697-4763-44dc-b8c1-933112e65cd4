<div fxLayout="column" *ngIf="loggedInRole !== 'ROLE_MASTERUSER'" class="mat-app-background" fxLayoutGap="50px" [ngClass]="{'dark-theme-mode':isDarkTheme}">
    <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap.lt-md="30px" fxLayoutGap.lt-sm="10px">
        <div fxFlex fxLayout="row" class="mt5 ml5" fxLayoutGap="10px" fxLayoutGap.lt-md="20px">
            <div fxFlex="5%">
                <img src="/assets/images/edit profile.png" alt="" class="darkMode">
            </div>
            <div fxFlex="95%" fxLayoutAlign="start center">
                <span class="header pb5">Edit Profile</span>
            </div>
        </div>
        <div fxFlex fxLayoutAlign="end end" >
            <span class="header pb5">
                <mat-slide-toggle [(ngModel)]="isDarkTheme" labelPosition="before" (change)="storeThemeSelection()">
                    <mat-icon style="color: #fff;">bedtime</mat-icon> Dark Mode
                </mat-slide-toggle>
            </span>
        </div>
    </div>

    <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
        <div fxLayout="row" fxLayout.lt-sm="column" fxLayout.sm="column" fxLayoutGap.lt-sm="20px" fxLayoutGap.sm="20px">
            <div fxFlex="60%">
                <!-- <mat-button-toggle-group fxFlex fxLayoutAlign="center center" #toggleGroup="matButtonToggleGroup"
                    [value]="selectedVal">
                    <mat-button-toggle value="edit">
                        <div fxLayout="row" fxLayoutGap="15px" class="toggle-div">
                            <p *ngIf="!isViewOnly" class="login-type">Edit Profile</p>
                            <p *ngIf="isViewOnly" class="login-type">View Profile</p>
                        </div>

                    </mat-button-toggle>
                    <mat-button-toggle value="reset" class="toggle-div">
                        <div fxLayout="row" fxLayoutGap="15px">
                            <p class="login-type">Reset Password</p>
                        </div>
                    </mat-button-toggle>
                </mat-button-toggle-group> -->
            </div>
            <div fxFlex="40%">
                <div fxFlex fxLayoutAlign="center center" fxLayoutAlign.lt-sm="end center"
                    fxLayoutAlign.sm="end center">
                    <button *ngIf="!isViewOnly" mat-raised-button class="update-btn" [disabled]="isUpdateBtn"
                        (click)="onUpdate()">UPDATE
                        PROFILE</button>
                    <!-- (click)="download(radioValue)" -->
                </div>
            </div>

        </div>
        <div >
            <!-- *ngIf="toggleGroup.value == 'edit'" -->
            <ng-container *ngIf="loggedInRole === 'ROLE_CORPADMIN'">
                <form [formGroup]="editAdminForm">
                    <div fxLayout="column" fxLayoutGap="20px">
                        <div fxFlex class="header-div">
                            <span>COMPANY DETAILS:</span>
                            <!-- <span *ngIf="loggedInRole=='Admin'">Company Details</span> -->

                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" >
                                <span class="cd">CORPORATE NAME:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.accountName}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">ACCOUNT NO:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.custNo}}</span>
                            </div>
                        </div>
                        <div fxFlex class="header-div">
                            <span>MAIN CONTACT PERSON DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">NAME<span
                                    class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="name" formControlName="name" required>
                                    <mat-error *ngIf="editAdminForm.controls.name.hasError('required')" class="pt15">
                                        Name Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span fxHide.lt-md class="cd">TELEPHONE<span
                                        class="asterisk"><sup>*</sup></span>:</span>
                                <span fxHide.gt-md fxHide.md class="cd">TELE-PHONE:<span
                                        class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput type="number" name="telephone" formControlName="telephone" required>
                                    <mat-error *ngIf="editAdminForm.controls.telephone.hasError('required')"
                                        class="pt15">
                                        Telephone Number Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                                <!-- onkeypress="if(this.value.length>7)return false;" -->
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">FAX NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput type="number" name="faxNo" formControlName="faxNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">MOBILE NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput type="number" name="mobNo" formControlName="mobNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">EMAIL:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="email" formControlName="email"
                                        (input)="validateEmail($event)">
                                    <mat-error *ngIf="editAdminForm.controls.email.hasError('email') && !isUpdateBtn"
                                        class="pt15">
                                        Please Enter Valid Email
                                    </mat-error>

                                </mat-form-field>
                                <!-- <mat-error *ngIf="isUpdateBtn" class="error">
                                    Please enter Email same as Loggin In user Access ID
                                </mat-error> -->
                            </div>
                        </div>

                        <div fxFlex class="header-div">
                            <span>ALTERNATE CONTACT PERSON DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">NAME:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="alterName" formControlName="alterName">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span fxHide.lt-md class="cd">TELEPHONE:</span>
                                <span fxHide.gt-md fxHide.md class="cd">TELE-PHONE:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput type="number"  name="alterTelephone" formControlName="alterTelephone">
                                    <!-- <mat-error *ngIf="editAdminForm.controls.alterTelephone.hasError('pattern') "
                                        class="pt15">
                                        Please Enter  Telephone Number with 8 Digits Only
                                    </mat-error> -->
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">FAX NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput type="number" name="alterFaxNo" formControlName="alterFaxNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">MOBILE NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput type="number" name="alterMobNo" formControlName="alterMobNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">EMAIL:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="alterEmail" formControlName="alterEmail">
                                    <mat-error *ngIf="editAdminForm.controls.alterEmail.hasError('email')" class="pt15">
                                        Please Enter Valid Email
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>


                        <div fxFlex class="header-div">
                            <span>ADDRESS DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">BLOCK NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="blockNo" formControlName="blockNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">UNIT NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="unitNo" formControlName="unitNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">STREET NAME<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="streetName" formControlName="streetName" required>
                                    <mat-error *ngIf="editAdminForm.controls.streetName.hasError('required')"
                                        class="pt15">
                                        Street Name Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">BUILDING NAME:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="buildingName" formControlName="buildingName">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">AREA:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="area" formControlName="area">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">COUNTRY<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <mat-select formControlName="country" name="country"  (selectionChange)="countryValidation($event.value)" required >
                                        <mat-option *ngFor="let acl of countryList" [value]="acl.masterCode">
                                            {{acl.masterValue}}
                                        </mat-option>
                                    </mat-select>
                                    <mat-error *ngIf="editAdminForm.controls.country.hasError('required')" class="pt15">
                                        Country Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">CITY:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="city" formControlName="city">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">STATE:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="state" formControlName="state">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">POSTAL CODE<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp mb25">
                                    <input matInput name="postalCode" formControlName="postalCode" required>
                                    <mat-error *ngIf="editAdminForm.controls.postalCode.hasError('required')"
                                        class="pt15">
                                        Postal Code Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                </form>
            </ng-container>
            <ng-container *ngIf="loggedInRole === 'ROLE_PERSCARDHOLDER'">
                <mat-tab-group [selectedIndex]="selected.value" (selectedIndexChange)="selected.setValue($event)"
                    (click)="getSelectedGrp()">
                    <mat-tab *ngFor="let tab of tabs; let index = index" [label]="tab">
                    </mat-tab>
                </mat-tab-group>
                <ng-container *ngIf="selected.value === 0">
                <form [formGroup]="editPersonalForm">
                    <div fxLayout="column" fxLayoutGap="20px">
                        <div fxFlex class="header-div">
                            <span>PERSONAL DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="selected.value === 0">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">CARD NO:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp.cardNo}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="selected.value === 0">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">NAME:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp.accountName}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="selected.value === 0">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">ACCOUNT NO:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp.accountNo}}</span>
                            </div>
                        </div>
                        <div fxFlex class="header-div">
                            <span>CONTACT DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span fxHide.lt-md class="cd">TELEPHONE<span
                                        class="asterisk"><sup>*</sup></span>:</span>
                                <span fxHide.gt-md fxHide.md class="cd">TELE-PHONE<span
                                        class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="telephone" type="number" formControlName="telephone" required>
                                    <mat-error *ngIf="editPersonalForm.controls.telephone.hasError('required')"
                                        class="pt15">
                                        Telephone Number Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">OFFICE NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field
                                    appearance="outline" class="inp">
                                    <input matInput type="number" name="officeNo" formControlName="officeNo" >
                                </mat-form-field>
                                <!-- [ngClass]="{'mt20' : editPersonalForm.controls.officeNo.invalid && editPersonalForm.controls.officeNo.touched}" -->
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">MOBILE NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="mobNo" type="number" formControlName="mobNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">EMAIL:</span>
                            </div>
                            <div fxFlex="75%">
                                <!-- <mat-form-field [ngClass]="{'mt20' : editPersonalForm.controls.email.invalid }" -->
                                    <mat-form-field
                                    appearance="outline" class="inp">
                                    <input matInput name="email" formControlName="email" (input)="validateEmail($event)">
                                    <mat-error *ngIf="editPersonalForm.controls.email.hasError('email')" class="pt15">
                                        Please Enter Valid Email
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>

                        <div fxFlex class="header-div" >
                            <span>MAIN ADDRESS DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">BLOCK NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="blockNo" formControlName="blockNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">UNIT NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="unitNo" formControlName="unitNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">STREET NAME<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="streetName" formControlName="streetName" required>
                                    <mat-error *ngIf="editPersonalForm.controls.streetName.hasError('required')"
                                        class="pt15">
                                        Street Name Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">BUILDING NAME:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="buildingName" formControlName="buildingName">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">AREA:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="area" formControlName="area">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">COUNTRY<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <mat-select formControlName="country" name="country" (selectionChange)="countryValidation($event.value,'country')" required>
                                        <mat-option *ngFor="let acl of countryList" [value]="acl.masterCode">
                                            {{acl.masterValue}}
                                        </mat-option>
                                    </mat-select>
                                    <mat-error *ngIf="editPersonalForm.controls.country.hasError('required')"
                                        class="pt15">
                                        Country Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">CITY:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="city" formControlName="city">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">STATE:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="state" formControlName="state">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">POSTAL CODE<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="postalCode" formControlName="postalCode" required>
                                    <mat-error *ngIf="editPersonalForm.controls.postalCode.hasError('required')"
                                        class="pt15">
                                        Postal Code Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div fxFlex class="pl20 pb10 pt15" fxLayoutAlign="start center" *ngIf="selected.value === 0">
                        <div>
                            <mat-checkbox color="primary" name="billingAddressSameAs" (input)="getBillingAddress()"
                                 formControlName="billingAddressSameAs" labelPosition="before"
                                value="billingAddressSameAs">
                                <span class="cd">BILLING ADDRESS SAME AS MAIN</span>
                            </mat-checkbox>
                        </div>
                    </div>
                    <!-- </div> -->
                    <div *ngIf="!billingAddress">
                        <div fxFlex class="header-div" >
                            <span>BILLING ADDRESS DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">BLOCK NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="bBlockNo" formControlName="bBlockNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">UNIT NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="bUnitNo" formControlName="bUnitNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">STREET NAME<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="bStreetName" formControlName="bStreetName" required>
                                    <mat-error *ngIf="editPersonalForm.controls.bStreetName.hasError('required')"
                                        class="pt15">
                                        Street Name Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">BUILDING NAME:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="bBuildingName" formControlName="bBuildingName">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">AREA:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="bArea" formControlName="bArea">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">COUNTRY<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <mat-select formControlName="bCountry" name="bCountry" (selectionChange)="countryValidation($event.value,'bCountry')" required>
                                        <mat-option *ngFor="let acl of countryList" [value]="acl.masterCode">
                                            {{acl.masterValue}}
                                        </mat-option>
                                    </mat-select>
                                    <mat-error *ngIf="editPersonalForm.controls.bCountry.hasError('required')"
                                        class="pt15">
                                        Country Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">CITY:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="bCity" formControlName="bCity">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">STATE:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="bState" formControlName="bState">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">POSTAL CODE<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="bPostalCode" formControlName="bPostalCode" required>
                                    <mat-error *ngIf="editPersonalForm.controls.bPostalCode.hasError('required')"
                                        class="pt15">
                                        Postal Code Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <!--  -->
                    </div>
                    <!-- </div> -->
                    <div fxFlex class="pl20 pb10 pt15" fxLayoutAlign="start center" *ngIf="selected.value === 0">
                        <div>
                            <mat-checkbox color="primary" name="shipingAddressSameAs" (input)="getShipingAddress()"
                                formControlName="shipingAddressSameAs" labelPosition="before"
                                value="shipingAddressSameAs">
                                <span class="cd">SHIPPING ADDRESS SAME AS MAIN</span>
                            </mat-checkbox>
                        </div>
                    </div>
                    <div *ngIf="!shippingAddress">
                        <div fxFlex class="header-div" >
                            <span>SHIPPING ADDRESS DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">BLOCK NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="sBlockNo" formControlName="sBlockNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">UNIT NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="sUnitNo" formControlName="sUnitNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">STREET NAME<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="sStreetName" formControlName="sStreetName" required>
                                    <mat-error *ngIf="editPersonalForm.controls.sStreetName.hasError('required')"
                                        class="pt15">
                                        Street Name Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">BUILDING NAME:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="sBuildingName" formControlName="sBuildingName">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">AREA:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="sArea" formControlName="sArea">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center"8 style="max-height: 55px !important;">
                                <span class="cd">COUNTRY<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <mat-select formControlName="sCountry" name="sCountry" (selectionChange)="countryValidation($event.value,'sCountry')" required>
                                        <mat-option *ngFor="let acl of countryList" [value]="acl.masterCode">
                                            {{acl.masterValue}}
                                        </mat-option>
                                    </mat-select>
                                    <mat-error *ngIf="editPersonalForm.controls.sCountry.hasError('required')"
                                        class="pt15">
                                        Country Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">CITY:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="sCity" formControlName="sCity">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">STATE:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="sState" formControlName="sState">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            >
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">POSTAL CODE<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="sPostalCode" formControlName="sPostalCode" required>
                                    <mat-error *ngIf="editPersonalForm.controls.sPostalCode.hasError('required')"
                                        class="pt15">
                                        Postal Code Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <!--  -->
                    </div>
                </form>
            </ng-container>
            <ng-container *ngIf="selected.value === 1">
                <form [formGroup]="editPersonalSubAppForm">
                    <div fxLayout="column" fxLayoutGap="20px">
                        <div fxFlex class="header-div">
                            <span>SUPPLEMENTARY DETAILS</span>
                        </div> 
                         <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">SUB APPLICANT <span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <mat-select name="selectedSubApplicant" [(ngModel)]="selectedSubApplicant"
                                        [ngModelOptions]="{standalone:true}" placeholder="Please Select"
                                        (selectionChange)="subApplicant()" required>
                                        <mat-option *ngFor="let acl of subAppDetailsList" [value]="acl.subAccountName">
                                            {{acl.subAccountName}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                                <div *ngIf="showSubAppError" class="error">
                                    Sub Applicant is required
                                </div>
                            </div>
                        </div>

                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="isShowRestForm">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">CARD NO:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.cardNo}}</span>
                            </div>
                        </div>
                        <div fxFlex class="header-div" *ngIf="isShowRestForm">
                            <span>CONTACT DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="isShowRestForm">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span fxHide.lt-md class="cd">TELEPHONE<span
                                        class="asterisk"><sup>*</sup></span>:</span>
                                <span fxHide.gt-md fxHide.md class="cd">TELE-PHONE<span
                                        class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="telephone" type="number" formControlName="telephone" required>
                                    <mat-error *ngIf="editPersonalSubAppForm.controls.telephone.hasError('required')"
                                        class="pt15">
                                        Telephone Number Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="isShowRestForm">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">OFFICE NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <!-- <mat-form-field [ngClass]="{'mt20' : editPersonalSubAppForm.controls.officeNo.invalid && editPersonalSubAppForm.controls.officeNo.touched}" -->
                                    <mat-form-field 
                                    appearance="outline" class="inp">
                                    <input matInput name="officeNo" formControlName="officeNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="isShowRestForm">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">MOBILE NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="mobNo" type="number" formControlName="mobNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="isShowRestForm">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">EMAIL:</span>
                            </div>
                            <div fxFlex="75%">
                                <!-- <mat-form-field [ngClass]="{'mt20' : editPersonalSubAppForm.controls.email.invalid }" -->
                                    <mat-form-field 
                                    appearance="outline" class="inp">
                                    <input matInput name="email" formControlName="email">
                                    <mat-error *ngIf="editPersonalSubAppForm.controls.email.hasError('email')" class="pt15">
                                        Please Enter Valid Email
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxFlex class="header-div" *ngIf="isShowRestForm">
                            <span>MAIN ADDRESS DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="isShowRestForm">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">BLOCK NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="blockNo" formControlName="blockNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="isShowRestForm">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">UNIT NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="unitNo" formControlName="unitNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="isShowRestForm">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">STREET NAME<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="streetName" formControlName="streetName" required>
                                    <mat-error *ngIf="editPersonalSubAppForm.controls.streetName.hasError('required')"
                                        class="pt15">
                                        Street Name Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="isShowRestForm">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">BUILDING NAME:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="buildingName" formControlName="buildingName">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="isShowRestForm">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">AREA:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="area" formControlName="area">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="isShowRestForm">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">COUNTRY<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <mat-select formControlName="country" name="country" (selectionChange)="countryValidation($event.value,'subApps')" required>
                                        <mat-option *ngFor="let acl of countryList" [value]="acl.masterCode">
                                            {{acl.masterValue}}
                                        </mat-option>
                                    </mat-select>
                                    <mat-error *ngIf="editPersonalSubAppForm.controls.country.hasError('required')"
                                        class="pt15">
                                        Country Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="isShowRestForm">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">CITY<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="city" formControlName="city">
                                    <mat-error *ngIf="editPersonalSubAppForm.controls.city.hasError('required')"
                                    class="pt15">
                                    City Cannot Be Blank
                                </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="isShowRestForm">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">STATE<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="state" formControlName="state">
                                    <mat-error *ngIf="editPersonalSubAppForm.controls.state.hasError('required')"
                                    class="pt15">
                                    State Cannot Be Blank
                                </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                            *ngIf="isShowRestForm">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">POSTAL CODE<span class="asterisk"><sup>*</sup></span>:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="postalCode" formControlName="postalCode" required>
                                    <mat-error *ngIf="editPersonalSubAppForm.controls.postalCode.hasError('required')"
                                        class="pt15">
                                        Postal Code Cannot Be Blank
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                    <!-- </div> -->
                    </div>
                </form>
            </ng-container>
            </ng-container>
            <!-- <ng-container *ngIf="loggedInRole === 'ROLE_CORPCARDHOLDER'">
                <mat-card fxLayout="column" class="dropdown-div">
                    <div fxFlex > 
                        <mat-form-field appearance="outline" class="inp">
                            <mat-select name="indiCard" [(ngModel)]="indiCardSelected" #indiCard="ngModel" (ngModelChange)="getSelectedCardDetails(indiCardSelected)"  >
                              <mat-option *ngFor = "let card of indiCardList;" [value]="card" >{{card}}</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </mat-card>
            </ng-container> -->
            <ng-container *ngIf="loggedInRole === 'ROLE_CORPCARDHOLDER'">
                <mat-card appearance="outlined" fxLayout="column" class="dropdown-div" *ngIf="isShowCardList">
                    <div fxFlex>
                        <mat-form-field appearance="outline" class="inp">
                            <mat-select name="indiCard" [(ngModel)]="indiCardSelected"
                                [ngModelOptions]="{standalone:true}"
                                (ngModelChange)="getSelectedCardDetails(indiCardSelected)">
                                <mat-option *ngFor="let card of indiCardList;" [value]="card">{{card}}</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </mat-card>
                <form [formGroup]="editIndividualForm">
                    <div fxLayout="column" fxLayoutGap="20px">
                        <div fxFlex class="header-div">
                            <span *ngIf="loggedInRole=='ROLE_CORPCARDHOLDER'">PERSONAL DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">CORPORATE NAME:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd pl10">{{this.userCardSelected.nameOnCard}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">NAME:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd pl10">{{this.userCardSelected.cardHolderName}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">CARD NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd pl10">{{this.userCardSelected.cardNo}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">POSITION:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput name="position" formControlName="position">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxFlex class="header-div">
                            <span>CONTACT DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span fxHide.lt-md class="cd">TELEPHONE:</span>
                                <span fxHide.gt-md fxHide.md class="cd">Tele-phone:</span>
                            </div>
                            <div fxFlex="75%">
                                <mat-form-field appearance="outline" class="inp">
                                    <input matInput type="number" name="telephone" formControlName="telephone">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">MOBILE NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <!-- [ngClass]="{'mt20' : editIndividualForm.controls.mobNo.invalid && editIndividualForm.controls.mobNo.touched}" -->
                                <mat-form-field
                                    appearance="outline" class="inp">
                                    <input matInput type="number" name="mobNo" formControlName="mobNo">
                                </mat-form-field>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">EMAIL:</span>
                            </div>
                            <div fxFlex="75%">
                                <!-- <mat-form-field [ngClass]="{'mt20' : editIndividualForm.controls.mobNo.invalid && editIndividualForm.controls.mobNo.touched}" -->
                                    <mat-form-field 
                                    appearance="outline" class="inp pb15">
                                    <input matInput name="email" formControlName="email" (input)="validateEmail($event)">
                                    <mat-error *ngIf="editIndividualForm.controls.email.hasError('email')" class="pt15">
                                        Please Enter Valid Email
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                </form>
            </ng-container>
            <ng-container *ngIf="loggedInRole === 'ROLE_SUBAPPLICANT'">
                <form [formGroup]="editSubapplicantForm">
                    <div fxLayout="column" fxLayoutGap="20px">
                        <div fxFlex class="header-div">
                            <span>PERSONAL DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">CARD NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{resp?.subCardNo}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">NAME:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.subAccountName}}</span>
                            </div>
                        </div>
                        <div fxFlex class="header-div">
                            <span>CONTACT DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span fxHide.lt-md class="cd">TELEPHONE:</span>
                                <span fxHide.gt-md fxHide.md class="cd">Tele-phone:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.subContactTelephone}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">MOBILE NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.subMainContactMobile}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">OFFICE NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.subMainContactTel}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">EMAIL:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.subMainContactEmail}}</span>
                            </div>
                        </div>
                        <div fxFlex class="header-div">
                            <span>ADDRESS DETAILS</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">BLOCK NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.subAddressBlock}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">UNIT NO.:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.subAddressUnit}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">STREET NAME:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.subAddressStreet}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">BUILDING NAME:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.subAddressBuilding}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">AREA:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.subAddressArea}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">COUNTRY:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.subMasterValue}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">CITY:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.subAddressCity}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">STATE:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.subAddressState}}</span>
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                            <div fxFlex="25%" fxLayoutAlign="start center" style="max-height: 55px !important;">
                                <span class="cd">POSTAL CODE:</span>
                            </div>
                            <div fxFlex="75%">
                                <span class="cd">{{this.resp?.subAddressPostal}}</span>
                            </div>
                        </div>
                    </div>
                </form>
            </ng-container>
        </div>
        <!-- <div *ngIf="toggleGroup.value == 'reset'">
            <app-change-password></app-change-password>
        </div> -->
    </div>
</div>
<!-- <div fxLayout="column" *ngIf="loggedInRole === 'ROLE_MASTERUSER'" class="mat-app-background" fxLayoutGap="50px" [ngClass]="{'dark-theme-mode':isDarkTheme}">
    <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap.lt-md="30px" fxLayoutGap.lt-sm="10px">
        <div fxFlex fxLayout="row" class="mt5 ml5" fxLayoutGap="10px" fxLayoutGap.lt-md="20px">
            <div fxFlex="5%">
                <img src="/assets/images/edit profile.png" alt="" class="darkMode">
            </div>
            <div fxFlex="95%" fxLayoutAlign="start center">
                <span class="header pb5">Edit Profile</span>
            </div>
        </div>
    </div>
    <div *ngIf="cdgService.showRegMsgDiv && cdgService.popupObj.function === 'passwordValidation'" class="validationBoxMaster" [ngStyle]="{'background-color': '#b9b9bb'}">
		<div  fxLayout="column" class="p5">
            <p class="head">Password Must Include</p>
            <ng-container *ngFor="let val of cdgService.passwordValidation; let i = index">
                <p class="msg" fxLayoutAlign="start start"> <mat-icon class="material-icons-outlined main-preview-icon" [ngStyle]="{'color': val.icon == 'done' ? ' #28a745' :  '#ff0000'}">{{val.icon}}</mat-icon>{{val.msg}}</p>
            </ng-container>
			<div fxFlex fxLayoutAlign="end center" >
				<button mat-raised-button class="ok-btn" [ngStyle]="{'background-color': '#b9b9bb'}" (click)="passValidation()">Ok</button>
			</div>
		</div>
	</div>
    <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
        <app-change-password></app-change-password>
    </div>
</div> -->