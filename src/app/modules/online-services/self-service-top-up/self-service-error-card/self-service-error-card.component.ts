import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { Router } from '@angular/router';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { NotificationService } from 'src/app/shared/services/notification.service';

@Component({
  selector: 'app-self-service-error-card',
  templateUrl: './self-service-error-card.component.html',
  styleUrls: ['./self-service-error-card.component.scss']
})
export class SelfServiceErrorCardComponent implements OnInit {
  displayedColumns: string[] = ['slNo', 'cardNo', 'errorMessage'];
  dataSource: any[] = []
  
  constructor(private router: Router,public localStorage: LocalStorageService, private fb: FormBuilder, public cdgService: CdgSharedService, private notifyService: NotificationService) { }

  ngOnInit(): void {
    if(this.localStorage.localStorageGet('errorCard')){
      this.dataSource=this.localStorage.localStorageGet('errorCard')
    }
  }

  back(){
    this.cdgService.upload.isfromback = true;
    this.cdgService.upload.function = "back"
    this.router.navigateByUrl('/layout/selftopup'); 
    
  }
  getTotalCost() {
    return this.dataSource.map(t => t.topupAmt).reduce((acc, value) => acc + value, 0);
  }
}
