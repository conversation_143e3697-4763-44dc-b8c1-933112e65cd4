<div fxLayout="column" fxLayoutGap="50px">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">Self Service Top-Up</span>
        </div>
    </div>
<div class="tbl-container mat-elevation-z8" *ngIf="this.dataSource.length > 0">
    <mat-table #table [dataSource]="dataSource">
        <ng-container matColumnDef="slNo">
            <mat-header-cell *matHeaderCellDef> Sl No. </mat-header-cell>
            <mat-cell *matCellDef="let element;let i=index"> {{i+1}} </mat-cell>
            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
        </ng-container>

        <!-- <ng-container matColumnDef="cardNo">
            <mat-header-cell *matHeaderCellDef> Card No </mat-header-cell>
            <mat-cell *matCellDef="let element"> {{element.cardNo}} </mat-cell>
            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
        </ng-container> -->

        <!-- <ng-container matColumnDef="productName">
            <mat-header-cell *matHeaderCellDef> Product Name </mat-header-cell>
            <mat-cell *matCellDef="let transaction"> {{transaction.productName}} </mat-cell>
            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
        </ng-container> -->

        <ng-container matColumnDef="cardNo">
            <mat-header-cell *matHeaderCellDef> Card No </mat-header-cell>
            <mat-cell *matCellDef="let transaction"> {{transaction.cardNo}} </mat-cell>
            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
        </ng-container>

        <ng-container matColumnDef="errorMessage">
            <mat-header-cell *matHeaderCellDef> Error Message </mat-header-cell>
            <mat-cell *matCellDef="let transaction"> {{transaction.errorMessage}} </mat-cell>
            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
        </ng-container>

        <!-- <ng-container matColumnDef="expDate">
            <mat-header-cell *matHeaderCellDef> Expiry Date </mat-header-cell>
            <mat-cell *matCellDef="let transaction"> {{transaction.expDate}} </mat-cell>
            <mat-footer-cell *matFooterCellDef> Total </mat-footer-cell>
        </ng-container>

        <ng-container matColumnDef="topupAmt">
            <mat-header-cell *matHeaderCellDef> Top-up Amount ($) </mat-header-cell>
            <mat-cell *matCellDef="let transaction;let i=index;">
                <div fxLayout="column">
                    <mat-form-field appearance="outline" class="tbl-inp">
                        <input matInput type="number" name="topupAmt{{i}}"
                            [(ngModel)]="transaction.topupAmt" [ngModelOptions]="{standalone:true}">
                    </mat-form-field>
                    <mat-error class="error" *ngIf="isShowTopUpError" fxLayoutAlign="start start">
                        Top-up Amount Is Required
                    </mat-error> -->
                <!-- </div>
            </mat-cell>
            <mat-footer-cell *matFooterCellDef> {{getTotalCost() | currency}} </mat-footer-cell>
        </ng-container>  -->
      

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
        <mat-footer-row *matFooterRowDef="displayedColumns"></mat-footer-row>
    </mat-table>
    
</div>
<div  >
    <button mat-raised-button class="submit-btn" (click)="back()">Back</button>
    </div>
</div>