import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON>tors, FormBuilder, FormControl } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { SelectionModel } from '@angular/cdk/collections';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { CommonModalComponent } from 'src/app/shared/shared-modules/common-modal/common-modal.component';
import{DecimalPipe} from '@angular/common';
import * as XLSX from 'xlsx';

import { distinct } from 'rxjs/operators';
import { threadId } from 'worker_threads';
// import { GridAlignColumnsDirective } from '@angular/flex-layout/grid/typings/align-columns/align-columns';

@Component({
  selector: 'app-self-service-top-up',
  templateUrl: './self-service-top-up.component.html',
  styleUrls: ['./self-service-top-up.component.scss']
})
export class SelfServiceTopUpComponent implements OnInit {

    // { value: 'contactlesscorprate', viewValue: 'CONTACTLESS CORPORATE CARD' },
    // { value: 'contactlessprepaid', viewValue: 'CONTACTLESS PREPAID CARD' },
    // { value: 'corporatecard', viewValue: 'CORPORATE CARD' },
    // { value: 'evoucher', viewValue: 'OPEN VALUE E-VOUCHER' },
    // { value: 'virtualcrop', viewValue: 'VIRTUAL CORP 2 TEST' },
    // { value: 'vitualcorporate', viewValue: 'VIRTUAL CORPORATE' },
    // { value: 'virtualprepaid', viewValue: 'VIRTUAL PREPAID' },
    // { value: 'giftcard', viewValue: 'CONTACTLESS GIFT CARD' },
    // { value: 'prioritycard', viewValue: 'PRIORITY CARD' }

  // cardArr = ['60108965111239', '60108965111237', '60108965111235', '60108965111233', '60108965111231', '60108965111230'];

  topupForm = {
    productType: "",
    radioValue: "cardNo",
    cardNo: [],
    amt: "",
  }
  // ['', [Validators.required, Validators.pattern(/((\d+)((\.\d{1,2})?))$/)]]
  isCardNoShow = false;
  data:any;
  isShowAddBtn: boolean = false;
  isDraftBtn:boolean =false;
  displayedColumns: any[] = ['slNo', 'nameOnCard', 'productName', 'cardNo', 'creditBalance', 'cardExpiry', 'topUpAmount','select'];
  radioArr = [
    { value: "cardNo", viewValue: "CARD NUMBER", isDisable: false },
    { value: "displayAll", viewValue: "DISPLAY ALL", isDisable: false },
    { value: "upload", viewValue: "UPLOAD", isDisable: false }
  ]
  cardNoSelectedList = new FormControl();
  fileName='SelfserviceTopup.xlsx'
  // displayedColumns: string[] = ['slNo', 'nameOnCard', 'productName', 'cardNo', 'creditBalance', 'expDate', 'topupAmt'];
  // transactions: Transaction[] = [
  //   { item: 'Beach ball', cost: 4 },
  //   { item: 'Towel', cost: 5 },
  //   { item: 'Frisbee', cost: 2 },
  //   { item: 'Sunscreen', cost: 4 },
  //   { item: 'Cooler', cost: 25 },
  //   { item: 'Swim suit', cost: 15 },
  // ];
  // personalTbl = [
  //   {
  //     nameOnCard: 'CHRIST CARD ONE',
  //     productName: 'CONTACTLESS GIFT CARD(C3)',
  //     cardNo: '6010896517000036',
  //     creditBalance: 4328.18,
  //     expDate: '20-03-2021',
  //     topupAmt: 5,
  //     select:true
  //   },
  //   {
  //     nameOnCard: 'CHRIST CARD ONE',
  //     productName: 'CONTACTLESS GIFT CARD(C3)',
  //     cardNo: '60108965111235',
  //     creditBalance: 4328.18,
  //     expDate: '20-03-2021',
  //     topupAmt: 5,
  //     select:true
  //   },
  //   {
  //     nameOnCard: 'CHRIST CARD TWO',
  //     productName: 'PRIORITY CARD',
  //     cardNo: '60108965111238',
  //     creditBalance: 2198.00,
  //     expDate: '12-03-2021',
  //     topupAmt: 10,
  
  //     select:true
  //   }
  // ]
  errorTbl = [
    {
      nameOnCard: 'CHRIST CARD THREE',
      productName: 'CONTACTLESS GIFT CARD(C3)',
      cardNo: '6010896517000036',
      creditBalance: 4328.18,
      expDate: '20-03-2021',
      topupAmt: 5,
      select:true
    },
    {
      nameOnCard: 'CHRIST CARD FIVE',
      productName: 'CONTACTLESS GIFT CARD(C3)',
      cardNo: '60108965111235',
      creditBalance: 4328.18,
      expDate: '20-03-2021',
      topupAmt: 5,
      select:true
    }
  ]
  dataSource: any[] = [];
  drafttable:boolean=false;
  fullcardno:any[] =[];
  personalTbl :any []=[];
  productArr : any[] =[];
  cardArr : any[] =[];
  isShowTbl: boolean = false;
  amtshow:boolean =false;
  isShowTopUpError: boolean = false;
  lastdigit: any;
  dataSourcedata:any[] =[];
  firstdigit: string;
  displaycard: string;
  uploadFile: any;
  uploadedFileSize: any;
  fileSizeExceed: boolean;
  topuperror:boolean =false;
  showcarderror:boolean =false;
  fielddisable: boolean =false;
  showerror:boolean=false;
  carderrorrad:boolean=false;
  uploadFileName: any;
  accountnumber:any;
  amount:[];
  count:[];
  isUpload: boolean;
  selection = new SelectionModel<any>(true,[]);
  datas: any;
  accountName: any;
  customerName: any;
  submitdata: any;
  customernumber: any;
  selectedvalue: string;
  cdvalue: string;
  valuede: any;
  deci: any;
  arr: any;
  amtvalue: number;
  uploadback: any;
  cdvalues: any;
  elearray: any=[];
  constructor(private route:ActivatedRoute,public decimal:DecimalPipe,public dialog: MatDialog,public apiService: ApiServiceService,private router: Router,public localStorage: LocalStorageService, private fb: FormBuilder, public cdgService: CdgSharedService, private notifyService: NotificationService) { }

  ngOnInit(): void {
    if(this.cdgService.upload){
   if(this.cdgService.upload.function === "back"){
    this.isCardNoShow = true
    this.isShowAddBtn = true
    this.isUpload = true;
    this.selectedvalue=this.localStorage.localStorageGet('prodtype');
    this.topupForm.productType = this.selectedvalue;
    this.topupForm.radioValue = 'upload';
    this.uploadFileName = this.localStorage.localStorageGet('uploadFileName');
    this.dataSource= this.localStorage.localStorageGet('tabledata');
   if(this.dataSource){
    if(this.dataSource.length>0){
    this.fielddisable=true;
    this.amtshow =true;
    this.isDraftBtn=true;
    this.cdvalue=this.localStorage.localStorageGet('prodtype');
    // this.onProductSelection(this.cdvalue);
   this.selectedvalue=this.localStorage.localStorageGet('prodtype');
    }
  }
    else{
      this.fielddisable=false;
      this.amtshow =false; 
    }
  
   }
  }
  if(this.cdgService.upload.function !== "back"){
    this.isDraftBtn =false;
    this.isShowAddBtn = false;
    this.isUpload = false;
  }
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        if(this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0]){
            this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
            this.customerName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name
        }
      }
      if (this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails) {
        this.customernumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number;
        this.customerName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name
  
      }
      else {
        console.log(this.localStorage.localStorageGet('customerviewvalue'), this.localStorage.localStorageGet('viewvalueonload'));
  
        if (this.localStorage.localStorageGet('customerviewvalue') === null) {
          this.customernumber = this.localStorage.localStorageGet('viewvalueonload').substr(0, 6);
          this.customerName = this.localStorage.localStorageGet('viewvalueonload').substr(9)
        }
        else {
          this.customernumber = this.localStorage.localStorageGet('customerviewvalue').substr(0, 6);
          this.customerName = this.localStorage.localStorageGet('customerviewvalue').substr(9)
        }
      }
    }
    this.accountName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails ? this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name : this.customerName;
    // this.isShowAddBtn = false
    let ret={
      "accountNo":this.accountnumber,
     // "productTypeId": "C3",
      "roleId":this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      "role":this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accessId":this.localStorage.localStorageGet("loginUserDetails").accessId
   
     
    }

    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/loadDraftedTopupAmount', ret).subscribe
    ((response: any) => {
      console.log(response)
      if (response.status === 200) {
         this.data = response.body;
         if(this.data.prepaidValidCardDtoList){
           if(this.data.prepaidValidCardDtoList.length>0){
         this.cdvalue=this.data.prepaidValidCardDtoList[0].productTypeId;
        // this.onProductSelection(this.cdvalue);
       this.selectedvalue=this.data.prepaidValidCardDtoList[0].productTypeId;
         }
        }
       if(this.data.prepaidValidCardDtoList){
          if(this.data.prepaidValidCardDtoList.length>0){           
              this.data.prepaidValidCardDtoList.forEach((element:any) => {
              // this.selectedvalue= element[0].productTypeId;
              // this.cdvalue=element[0].productTypeId;
                this.cardArr.push(element.cardNo);
                });
                const distarr=this.cardArr.filter((a,i)=>
                this.cardArr.indexOf(a)===i);
                this.cardArr=distarr;
                this.fielddisable=true;
          }else{
              this.fielddisable=false; 
              if(this.cdgService.upload.function !== "back"){
              this.topupForm.radioValue=""
              }
          } 
      }
      else{
        this.fielddisable=false; 
        if(this.cdgService.upload.function !== "back"){
        this.topupForm.radioValue=""
        }
      } 
    }        
    });
    
    let obj={      
      "accessId":  this.localStorage.localStorageGet("loginUserDetails").accessId,
      "accountNo": this.accountnumber,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,           
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/topupProductType', obj).subscribe
    ((response: any) => {
      console.log(response)
      if (response.status === 200) {
         this.data = response.body;
        this.productArr= this.data;
        // this.data.forEach((element :any)=> {
        //   // this.selectedvalue= element.productId;
        //   // this.cdvalue =element.productId;
        // });       
      }
    });
    // this.firstdigit = String(this.cardArr[0]).substr(0, 10);
    // this.displaycard = this.firstdigit.substr(0, 6) + "-" + this.firstdigit.substr(6, 4);

    // this.cardArr.forEach((element: any) => {
    //   this.lastdigit.push((String(element).substr(-6)));
    // });
  }
  // }
  add(event: any) {
    console.log(event)
  }
  getDetails() {
  
    this.isCardNoShow = true
    this.isShowAddBtn = true
  
    let obj={
      "accessId":  this.localStorage.localStorageGet("loginUserDetails").accessId,
      "accountNo": this.accountnumber,
      "productType": this.topupForm.productType,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId
    }

    this.selection = new SelectionModel<any>(true, []); // resetting the selection values
    
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/getActiveCardList ', obj).subscribe
    ((response: any) => {
      console.log(response)
      if (response.status === 200) {
        this.lastdigit = [];
        this.data = response.body;
        this.cardArr= this.data.cardNo;
        if(this.cardArr.length==0){
          this.showerror=true;
        }
        else{
          this.showerror=false;
        }
      }
      if(this.cardArr.length > 0) {
        this.firstdigit = String(this.cardArr[0]).substr(0, 10);
        this.displaycard = this.firstdigit.substr(0, 6) + "-" + this.firstdigit.substr(6, 4);
      
    
        this.cardArr.forEach((element: any) => {
          this.lastdigit.push((String(element).substr(-6)));
        });
      }
    });
   
  }
  hideData(){
    this.isShowTbl=false;
    this.carderrorrad=false;
    this.amtvalue= Number(this.topupForm.amt)
    this.amtvalue=0.00
    this.amtshow =false;
    this.dataSource=[];
    this.cardNoSelectedList.reset();
    this.uploadFile = "";
    this.uploadFileName = "";
    this.isUpload = false;
    this.fileSizeExceed = false;
    this.topupForm.amt="";
    this.showcarderror=false;
    this.drafttable=false;
   
  }
  getTotalCost() {
    return this.dataSource.reduce((a, b) => {
      return Number(a) + Number(b['topUpAmount']);
    }, 0);
  }
  private regex: RegExp = new RegExp(/^\d{0,12}\.?\d{0,2}$/g);
  // Allow key codes for special events. Reflect :
  // Backspace, tab, end, home
  private specialKeys: Array<string> = ['Backspace', 'Tab', 'End', 'Home', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];
  numberOnly(event:any) {
    let value = event.target.value;
    if (this.specialKeys.indexOf(event.key) !== -1) {
      return;
    }
     let current: string = value;
      const position = event.target.selectionStart;
      const next: string = [current.slice(0, position), event.key == 'Decimal' ? '.' : event.key, current.slice(position)].join('');
      if (next && !String(next).match(this.regex)) {
       event.preventDefault();
      }
   
  }
  onSubmit() {
    
  this.arr="";
    this.dataSource.forEach((el:any)=>{
//this.deci=this.decimal.transform(el.topUpAmount,'1.2-2');
el.topUpAmount=Number(el.topUpAmount);
 
      });
      if(this.selection.selected.length==0){
        this.notifyService.showError('Please select record to submit','Error')
      }
      else{
        this.dataSource.forEach(element => {
          if(this.selection.selected){
            this.dataSourcedata=this.selection.selected;
          }
        });
       // const data = this.dataSourcedata.filter((o: any) => !o.topupAmt)
        this.dataSourcedata.forEach(element => {
         if(!element.topUpAmount){
           this.arr=element.cardNo;
         }
          
        });
        if(this.arr )
        {
       
        this.isShowTopUpError = true;
       
        this.notifyService.showError('Top-up Amount Is Required', 'Error')
        
      }
      else {
        this.isShowTopUpError = false;
        const dialogConfig = new MatDialogConfig()
        dialogConfig.data = {
          title: "Confirmation",
          msg: "Are you sure you want to Submit?",
          btnClose: "Cancel",
          btnConfirm: "Ok",
          
        }
        const dialogRef = this.dialog.open(CommonModalComponent, dialogConfig);
        dialogRef.afterClosed().subscribe(result => {
          if (result) {
            let obj={
              "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "accountNo": this.accountnumber,
          "accountName" :  this.accountName,
          "customerNumber":this.customernumber,
          "prepaidValidDtoList": this.dataSourcedata,
          "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId
            }
            this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/submitTopupInfo', obj).subscribe
            ((response: any) => {
              console.log(response)
              if (response.status === 200) {
                 this.submitdata = response.body;
                 if(response.body.errorMessage===null){
                  this.notifyService.showSuccess(response.body.successMessage,'Success');
                  this.getDetails();
                  this.resetTopUpPage();
                    this.cdgService.upload.function = "";
                    this.uploadFileName = "";
                    this.topupForm.radioValue = 'cardNo';
                    this.isUpload = false; 
                     }
                     else{
                      this.notifyService.showError(response.body.errorMessage,'Error') 
                     }
              }
             } );
          }
        });
        
      }                                    
      }
     
  }
  
  // after successful submit resetting the page.
  resetTopUpPage(){
    this.isCardNoShow = false;
    this.isShowAddBtn = false;
    this.drafttable = false;
    this.dataSource = [];
    this.topupForm.productType="";
    this.cardNoSelectedList.reset();
  }

  onPrevious(){
    let obj={
  "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
  "accountNo": this.accountnumber,
  "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
  "cardNumberList" :  this.cardArr
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/previousTopupAmount', obj).subscribe
    ((response: any) => {
      console.log(response)
      if (response.status === 200) {
         this.datas = response.body;
       ;
          //   let data: any = this.personalTbl.find(o => o.cardNo.includes(element))
          //   if(data){
          //   data.topupAmt = Number(this.topupForm.amt)
          //   }
       this.datas.forEach((elements:any) => {
        this.dataSource.forEach(element => {
          if(element.cardNo===elements.cardNumber){
           
          element.topUpAmount = elements.topupAmount ;
         
          }
        });
       
       });
     this.dataSource.forEach(ele=>{

  if(!ele.topupAmount){
    // this.topuperror=true;
     this.elearray.push(ele.cardNo)
     if(this.elearray.length>0){
       this.notifyService.showError('No Previous Topup amount for records','Error')
     }
      }
      else{
       // this.topuperror=false;
      }

     });
      }
  });
}
change(){
  this.isDraftBtn=true;
}
Onsaveasdraft(){
  this.arr="";
  if(this.selection.selected.length==0){
    this.notifyService.showError('Please select record to save','Error')
  }
  else{
    this.dataSource.forEach(element => {
      if(this.selection.selected){
        this.dataSourcedata=this.selection.selected;
      }
    });
   // const data = this.dataSourcedata.filter((o: any) => !o.topupAmt)
    this.dataSourcedata.forEach(element => {
     if(!element.topUpAmount){
       this.arr=element.cardNo;
     }
      
    });
    if(this.arr )
    {
   
    this.isShowTopUpError = true;
   
    this.notifyService.showError('Top-up Amount Is Required', 'Error')
    
  }
  if(this.data.prepaidValidCardDtoList){
    if(this.data.prepaidValidCardDtoList.length>0){
      this.cdvalues=this.data.prepaidValidCardDtoList[0].productTypeId;
    }
  }
  let obj={
    "accountNo": this.accountnumber,
    "productTypeId":this.topupForm.productType ?this.topupForm.productType: this.cdvalues,
    "roleId":this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
    "role":this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
    "accessId":this.localStorage.localStorageGet("loginUserDetails").accessId,
    "prepaidValidDtoList":  this.dataSourcedata
  }
  this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/saveDraft', obj).subscribe
        ((response: any) => {
          console.log(response)
          if (response.status === 200) {
             this.data = response.body; 
             this.getDetails();
             if(response.body.errorMessage===null){
                this.notifyService.showSuccess(response.body.successMessage,'Success')
             }
             else{
              this.notifyService.showError(response.body.errorMessage,'Error') 
             }
             this.localStorage.localStorageSet('producttypeid',this.topupForm.productType);
          }
        });
  }
   
}
  routeToErrorCard(){
    // selftopup/errorCards
    this.localStorage.localStorageSet('errorCard',this.errorTbl)
    this.router.navigateByUrl('/layout/selftopup/errorCards');
  }
  carderror(val:any){
    if(val){
    this.showcarderror=false;
    }
  }
  ongo(isValid: boolean){
    this.topuperror=false;
    this.isDraftBtn=true;
    

    if(this.topupForm.radioValue === ""){
   this.carderrorrad=true;
    }
    else{
      this.carderrorrad=false; 
    }
    if(!this.cardNoSelectedList.value){
   this.showcarderror=true;
   
    }
    else{
      this.showcarderror=false; 
    }
    if(isValid){
      this.drafttable=false;
      if (this.topupForm.radioValue === 'cardNo' ){
        this.fullcardno=[];
        if( this.cardNoSelectedList.value){
        this.cardNoSelectedList.value.forEach((element:any) => {
        this.fullcardno.push( this.firstdigit + element);
        });
      
        let obj={
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
           "accountNo": this.accountnumber,
            "role":this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
             "cardNumberList" : this.fullcardno
        }
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/retrieveCardTopupDetails', obj).subscribe
        ((response: any) => {
          console.log(response)
          if (response.status === 200) {
             this.personalTbl = response.body;
             this.dataSource=this.personalTbl;
           if(this.dataSource) {
             this.amtshow =true;
           
           }
          
          }
        });  
      }
    }
     if (this.topupForm.radioValue === 'displayAll'){
       if(this.cardArr.length===0){
         this.notifyService.showError('Card Details not Found','Error');
       }
      let obj={
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
"accountNo": this.accountnumber,
"role":this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
"cardNumberList" : this.cardArr
      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/retrieveCardTopupDetails', obj).subscribe
      ((response: any) => {
        console.log(response)
        if (response.status === 200) {
           this.personalTbl = response.body;
           this.dataSource=this.personalTbl;
         if(this.dataSource) {
           this.amtshow =true;
         
         }
        
        }
      }); 
      }
    if (this.topupForm.radioValue === 'upload') {
      let formData = new FormData();
    if (this.uploadFile !== undefined) {
      formData.append('file', this.uploadFile);
       formData.append('attachmentName', this.uploadFileName);
    }
    if (this.uploadFile === undefined) {
    
      // || this.uploadFile === ''
      this.uploadFile = '';
      this.uploadFileName = '';
      formData.append('file', this.uploadFile);
      formData.append('attachmentName', this.uploadFileName);
    }
    if(this.uploadFile == ""){
      this.notifyService.showError('Please upload the file','Error');
    }

    formData.append('accessId',  this.localStorage.localStorageGet("loginUserDetails").accessId);
    formData.append('role', this.localStorage.localStorageGet("loginUserDetails").roles.roleName);
    formData.append('accountNo',this.accountnumber);
    formData.append('productTypeId',this.topupForm.productType);
    formData.append('roleId', this.localStorage.localStorageGet("loginUserDetails").roles.roleId);
    
   
    const options = {
      headers: new HttpHeaders().set('Accept','application/json').set("Authorization", this.localStorage.localStorageGet("auth"))
    }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/validateCards', formData,options).subscribe
      ((response: any) => {
        console.log(response)
       // if (response.status === 200) {
           this.data = response;
           this.localStorage.localStorageSet("tabledata",this.data.prepaidValidCardDtoList);
           this.localStorage.localStorageSet("prodtype",this.topupForm.productType);
           if(response.errorMessage){
             this.notifyService.showError(response.errorMessage,'Error');
           }
           else{
           this.errorTbl= this.data.selfServiceTopUpErrorCardDtoList;
           if(this.data.selfServiceTopUpErrorCardDtoList){
            const dialogConfig = new MatDialogConfig()
    dialogConfig.data = {
      title: "Confirmation",
      msg: "Do you want to see the Card Details?",
      btnClose: "Cancel",
      btnConfirm: "Proceed",
      btnerror: "View Error",
      func: 'edit'
    }
    const dialogRef = this.dialog.open(CommonModalComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (dialogConfig.data.btnConfirm ==="Proceed" && result===true) {
        if(this.data.prepaidValidCardDtoList.length===0){
      this.notifyService.showError('No Valid Records Found in Uploaded Excel','Error');
        }
        else{
        this.dataSource =this.data.prepaidValidCardDtoList;
        this.personalTbl=this.data.prepaidValidCardDtoList;
      
        this.amtshow =true;
        }
      }
      if(dialogConfig.data.btnerror ==="View Error" && result===false){
       
        this.routeToErrorCard(); 
      }
    });
  }
    // if(dialogConfig.data.btnConfirm ==="Ok") {
    //    this.routeToErrorCard();
    //        }
          }
        
       //}
     });
    
     
      // this.personalTbl.forEach(element => {
      //   element.topupAmt = Number(this.topupForm.amt)
      // });
      // this.notifyService.showSuccess('Submission Successfull', 'Success')
    }
  }
  else {
    this.notifyService.showError('Mandatory Fields cannot be blank', 'Error')
  }

}

  submit(isValid: boolean) {

    // console.log(isValid)
    // this.isShowTbl = false;
    this.dataSource = [];
  
    if (isValid) {
      // this.topupForm.cardNo = this.cardNoSelectedList.value;
      // console.log(this.topupForm, this.cardNoSelectedList)
      if (this.topupForm.radioValue === 'cardNo') {
        this.personalTbl.forEach(element => {
          element.topUpAmount = Number(this.topupForm.amt);
       
        });
        this.dataSource=this.personalTbl;
       
        // this.topupForm.cardNo.forEach(element => {
        //   let data: any = this.personalTbl.find(o => o.cardNo.includes(element))
        //   if(data){
        //   data.topupAmt = Number(this.topupForm.amt)
        //   }
        //   if (data) {
        //     this.notifyService.showSuccess('Submission Successfull', 'Success')
        //   }
        // });
      }
      if (this.topupForm.radioValue === 'displayAll') {
        this.personalTbl.forEach(element => {
          element.topUpAmount = Number(this.topupForm.amt)
        });
        this.dataSource=this.personalTbl;
       
      }
      if(this.cdgService.upload.function === "back"){
        this.dataSource.forEach(element => {
          element.topUpAmount = Number(this.topupForm.amt)
        });
        
      }
      if (this.topupForm.radioValue === 'upload') {
       // let formData = new FormData();
     
     

      // formData.append('accessId', this.localStorage.localStorageGet("loginUserDetails").accessId);
      // formData.append('role', this.localStorage.localStorageGet("loginUserDetails").roles.roleName);
      // formData.append('accountNo',this.accountnumber);
      // formData.append('productTypeId',this.topupForm.productType);
      // formData.append('roleId', this.localStorage.localStorageGet("loginUserDetails").roles.roleId);
     
      // const options = {
      //   headers: new HttpHeaders({ 'Accept': 'application/json' })
      // }
      //   this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/validateCards', formData,options).subscribe
      //   ((response: any) => {
      //     console.log(response)
      //     if (response.status === 200) {
      //        this.data = response.body;
      //        if(this.data.selfServiceTopUpErrorCardDtoList){

      //        }
          
      //     }
      //   });
       
      this.personalTbl.forEach(element => {
        element.topUpAmount = Number(this.topupForm.amt)
      });
      this.dataSource=this.personalTbl;
       // this.notifyService.showSuccess('Submission Successfull', 'Success')
      }
    }
   

  }
  onFileInput(event: any) {
    console.log(event.target.file)
    if (event.target.files.length > 0) {
      this.uploadFile = event.target.files[0];
      this.localStorage.localStorageSet("uploadFileName", this.uploadFile.name);
      console.log('size', this.uploadFile.size);
      this.uploadedFileSize = this.uploadFile.size;
      if (this.uploadedFileSize > 1000000) {
        this.fileSizeExceed = true;
        this.notifyService.showWarning("Uploaded File size exceeded", 'Warning!')
      }
      else {
        this.fileSizeExceed = false;
      }
      this.uploadFileName = this.uploadFile.name
      console.log(this.uploadFile)
      this.isUpload = true;
    }
  }


  removeFile() {
    this.uploadFile = "";
    this.uploadFileName = "";
    this.isUpload = false;
    this.fileSizeExceed = false;
  }

  Reset() {
    if(this.selection.selected.length==0){
      this.notifyService.showWarning('Please select record to reset','Warning');
    }
    else{
      this.dataSource.forEach(obj => {
        this.selection.selected.forEach(selectedElementObj => {
          if(obj.cardNo === selectedElementObj.cardNo){
            obj.topUpAmount = 0;
          }
        });      
      });
    }
  }
  
  //to check whether the number of rows selected matches the total number of rows
  isAllSelected(){
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.length;
    return numSelected === numRows;
  }
  //selects all the rows if they are not all selected or clears the selection
  masterToggle(){
    this.isAllSelected() ? 
    this.selection.clear() :
    this.dataSource.forEach(element => {
      this.selection.select(element);
    });
  }

  downloadExcelTemplate() {
    let fileUrl = "../assets/excel/Book_Empty.xlsx";
    let link = document.createElement('a');
    document.body.append(link);
    link.setAttribute("href", fileUrl);
    link.setAttribute("target", '_blank');
    link.setAttribute("download", "Book_Empty.xlsx");
    link.click();
    document.body.removeChild(link);
  }
}

