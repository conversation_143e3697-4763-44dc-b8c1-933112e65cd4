<div fxLayout="column" fxLayoutGap="50px">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">monetization_on</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">Self Service Top-Up</span>
        </div>
        
    </div>
    <div fxLayout="column" class="custom-padding-left sub-title">
        <span class="sub-header">INSTRUCTIONS:</span><br>
        <span>Items marked with <span style="color: red;font-weight: bold;">*</span>are mandatory.Please ensure all mandatory fields are filled</span>
</div>
    <form #f="ngForm">
       
        <div fxLayout="column" class="custom-padding-left" fxLayoutGap="30px" >
           
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">PRODUCT TYPE <span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%">
                    <mat-form-field appearance="outline" class="product-inp">
                        <mat-select name="prodType" [(ngModel)]="topupForm.productType"
                            placeholder="Select Product Type"   (selectionChange)="getDetails()" #prodType="ngModel"
                            required>
                            <mat-option *ngFor="let acl of productArr" [value]="acl.productId">
                                {{acl.productName}}
                            </mat-option>
                        </mat-select>
                        <mat-error *ngIf="f.submitted && prodType.invalid" class="pt15">
                            <div *ngIf="prodType.hasError('required')">
                                Product Type Cannot Be Blank
                            </div>
                        </mat-error>
                    </mat-form-field>
                </div>                
            </div>

            <div fxLayout="column" fxLayoutGap="30px" *ngIf="isCardNoShow">
                <mat-radio-group name="radioValue" [(ngModel)]="topupForm.radioValue" (ngModelChange)="hideData()" fxLayoutGap="30px">
                    <mat-radio-button *ngFor="let val of radioArr" color="primary" [value]="val.value"
                        [disabled]="val.isDisable" labelPosition="before">
                        {{val.viewValue}}
                    </mat-radio-button>
                    <mat-error *ngIf="this.carderrorrad" class="pt15 si">
                        Please select any option
                      </mat-error>
                </mat-radio-group>

                <div *ngIf="topupForm.radioValue == 'cardNo'">
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">CARD NO<span class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%" fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="start center" *ngIf="!showerror" >
                            <p class="pb10">{{this.displaycard}} </p>
                            
                            <mat-form-field appearance="outline" class="inp">      
                                <mat-select [formControl]="cardNoSelectedList"   (selectionChange)="carderror($event)" multiple>
                                    <mat-option *ngFor="let val of lastdigit" ng [value]="val">{{val}}</mat-option>
                                </mat-select>  
                            </mat-form-field>
                            <!-- <mat-error class="pt15" *ngIf="f.submitted">
                                <div *ngIf="showcarderror && !showerror"  fxLayoutAlign="start start" >
                                Card No cannot be Blank
                            </div>
                            </mat-error> --> 
                        </div>

                    </div>
                    <div fxLayout="row">
                        <div fxFlex="30%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                           
                        </div>
                        <div fxFlex="70%" fxFlex.lt-md="65%" fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="start center">
                            <mat-error class="error" *ngIf="showcarderror && !showerror" fxLayoutAlign="start start">
                                Card No cannot be blank
                            </mat-error>
                        </div>
                    </div>
                        
                        <mat-error *ngIf="showerror" class="pt15">
                            No Card Details Found
                          </mat-error>
                   
                </div>
               
                <!-- <div *ngIf="topupForm.radioValue == 'displayAll'">
                    displayAll
                </div> -->
                <div *ngIf="topupForm.radioValue == 'upload'" fxLayout="column">
                    <a style="color: blue;text-decoration: underline;cursor: pointer;padding-bottom: 15px;" (click)="downloadExcelTemplate()"> Download Excel Template</a>
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayout="column">
                        <span class="date-title">ATTACH DOCUMENTS<span class="asterisk"><sup>*</sup></span>:</span>
                        <!-- <span class="attach">Accepted file formats jpg,docx,pdf,<br/>xlsx,csv,zip,doc,xls</span> -->
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%" >
                        <div fxFlex fxLayoutGap="10px">
                            <button mat-raised-button class="upload-btn" (click)="fileInput.click()">
                                UPLOAD
                                <input #fileInput type="file" id="attachment" name="attachment"  onclick="this.value=null" accept=".xls" (change)="onFileInput($event)" style="display: none;"/>
                            </button>
                            <span >{{uploadFileName}}  <mat-icon *ngIf="isUpload" class="delete-icon" (click)="removeFile()">delete</mat-icon> </span>              
                        </div>
                        <mat-error *ngIf="fileSizeExceed" class="mt15">
                            File size should not exceed 1MB
                        </mat-error>                       
                    </div>                                      
                </div>

                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="amtshow">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">AMOUNT:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="product-inp">
                            <input matInput type="text" name="amount" [(ngModel)]="topupForm.amt"  (keydown)="numberOnly($event)" #amount="ngModel" >
                            <mat-error *ngIf="f.submitted && amount.invalid" class="pt15">
                                <div *ngIf="amount.hasError('required')">
                                    Amount Cannot Be Blank
                                </div>
                            </mat-error>
                        </mat-form-field>
                  
                        &nbsp;&nbsp;<button mat-raised-button class="update-btn" (click)="submit(f.form.valid)">APPLY ALL</button>
                       
                    </div>                 
                </div>
                
            </div>
       
            <div fxFlex *ngIf="isShowAddBtn">
             
                <button mat-raised-button class="update-btn" (click)="ongo(f.form.valid)">GO</button>
            </div>
        
            <div fxFlex fxLayoutAlign="start center" *ngIf="drafttable">
                <span style="font-weight: bold;" class="sub-header">Topup Table:</span>
            </div>
            <div>
                <div class="tbl-container mat-elevation-z8" *ngIf="this.dataSource.length > 0">
                    <mat-table #table [dataSource]="dataSource">
                        <ng-container matColumnDef="slNo">
                            <mat-header-cell *matHeaderCellDef> S No. </mat-header-cell>
                            <mat-cell *matCellDef="let element;let i=index"> {{i+1}} </mat-cell>
                            <mat-footer-cell *matFooterCellDef class="fwb font-11 pr14 fo" > </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="nameOnCard">
                            <mat-header-cell *matHeaderCellDef> Name On Card </mat-header-cell>
                            <mat-cell *matCellDef="let element" class="fwb font-11 pr14 fo" > {{element.nameOnCard}} </mat-cell>
                            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="productName">
                            <mat-header-cell *matHeaderCellDef> Product Name </mat-header-cell>
                            <mat-cell *matCellDef="let transaction"  class="fwb font-11 pr14 fo" > {{transaction.productName}} </mat-cell>
                            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="cardNo" class="pr15">
                            <mat-header-cell *matHeaderCellDef> Card No </mat-header-cell>
                            <mat-cell *matCellDef="let transaction"  class="fwb font-10 pr14 fo"> {{transaction.cardNo}} </mat-cell>
                            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="creditBalance" class="pr15">
                            <mat-header-cell *matHeaderCellDef> Credit Balance</mat-header-cell>
                            <mat-cell *matCellDef="let transaction"  class="fwb font-10 pr14 fo" > {{transaction.creditBalance | currency}} </mat-cell>
                            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="cardExpiry" class="pr15 fo">
                            <mat-header-cell *matHeaderCellDef> Expiry Date </mat-header-cell>
                            <mat-cell *matCellDef="let transaction"  > {{transaction.cardExpiry}} </mat-cell>
                            <mat-footer-cell *matFooterCellDef> Total </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="topUpAmount">
                            <mat-header-cell *matHeaderCellDef> Top-up Amount ($) </mat-header-cell>
                            <mat-cell *matCellDef="let transaction;let i=index;">
                                <div fxLayout="column">
                                    <mat-form-field appearance="outline" class="tbl-inp">
                                        <input matInput type="text"  name="topUpAmount{{i}}"
                                            [(ngModel)]="transaction.topUpAmount"  (keydown)="numberOnly($event)" (ngModelChange)="change()" [ngModelOptions]="{standalone:true}" required>
                                    </mat-form-field>
                                    <!-- <mat-error class="error" *ngIf="topuperror" fxLayoutAlign="start start">
                                        No previous Top-up record Found
                                    </mat-error> 
                                    <mat-error class="error" *ngIf="!topuperror" fxLayoutAlign="start start">
                                    
                                    </mat-error>  -->
                                </div>
                            </mat-cell>
                            <mat-footer-cell *matFooterCellDef> {{getTotalCost() | currency}} </mat-footer-cell>
                        </ng-container>
                        
                        <ng-container matColumnDef="select">
                            <mat-header-cell *matHeaderCellDef> 
                                <mat-checkbox (change)="$event ? masterToggle() : null" 
                                [checked]="selection.hasValue() && isAllSelected()" color="primary"
                                [indeterminate]="selection.hasValue() && !isAllSelected">
                                </mat-checkbox>
                            
                        </mat-header-cell>
                            <mat-cell *matCellDef="let transaction">
                                <div fxLayout="column">
                                    <mat-checkbox (click)="$event.stopPropagation()" 
                                    (change)="$event ? selection.toggle(transaction) : null" color="primary"
                                    [checked]="selection.isSelected(transaction)">
                                    </mat-checkbox> 
                                
                                </div>
                            </mat-cell>
                            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
                        </ng-container>

                        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
                        <mat-footer-row *matFooterRowDef="displayedColumns"></mat-footer-row>
                    </mat-table>
                </div>
           </div>
           <div>
                <div fxFlex *ngIf="this.dataSource.length > 0" fxLayout="row" class="mt-10" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutGap.gt-md="1%"
                    fxLayoutGap.lt-md="4%">
                    <button mat-raised-button class="submit-btn" (click)="onSubmit()">SUBMIT</button>
                    <button mat-raised-button class="submit-btn" (click)="onPrevious()">PREVIOUS TOPUP AMOUNT</button>
                    <button mat-raised-button class="submit-btn" *ngIf="isDraftBtn" (click)="Onsaveasdraft()">SAVE AS DRAFT</button>
                    <button mat-raised-button class="submit-btn" (click)="Reset()">RESET</button>
                </div>
                <!-- {{selection.selected | json}} -->
        </div>
        </div>
        <div></div><br>
       
        <div fxLayout="column" class="custom-padding-left sub-title">
            <br>
            <span  class="sub-header">[Terms & Condition]->S&M to highlight if needed,if needed what should be the content
            </span>
    </div>
    </form>
</div>