@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
// .font-12{ 
//     font-size:  $cdgsz-font-size-xs;
// }
// .sub-btn{
//         background: $prime;
//         color:$white;
//         border-radius: 3px;
//         height: 30px;
//         border: none;
// }
// .full-height {
//     height: 100vh !important;
//   }
//   .mt150{
//       margin-top:150px;
//   }
//   .fw-b{
//       font-weight: $cdgsz-font-weight-bold;
//   }
//    .invalid-entry
//   {
//       color:$red;
//   }
//   @media screen and (max-width: 960px) {
//     .fogot-pwd-img-container{
//        display:none;
//    }
//    .mt150{
//        margin-top:65px;
//    }

// }






.tool-bar-cls{
    width: 100%;
    display: flex;
    padding-top:60px !important;
    padding-bottom: 30px !important;
    
}
.top-menu-main-div{
    display: flex;
}
.login-title{
    font-size: $cdgsz-font-size-xl;
    font-weight: $cdgsz-font-weight-bold;
}
.top-menu{
    font-family: Roboto;
    font-style: $cdgsz-font-weight-normal;
    font-weight: $cdgsz-font-weight-normal;
    font-size: $cdgsz-font-size-lg;
    line-height: 23px;
    text-align: center;
}
.full-height {
  height: calc(100vh - 90px) !important;
//   background: $cdgc-bg-prime;
  flex: 1 1 auto !important;
}
.form-div{
  
    width: 375px !important;
    
}
.text-cls{
    color: $white;
}
.font-12{
    font-size: 12px !important;
}
@media screen and (max-width: 960px) {
    // 1919
.full-height {
  height: auto !important;
  padding-top: 20px !important;
//   background: $cdgc-bg-prime;
  flex: 1 1 auto !important;
}
}
.login-form-input-field{
  width:210px;
}
.icon{
  font-size: 75px !important;
  height: 95px !important;
  width:80px !important;
}
.login-type{
    font-size: $cdgsz-font-size-prime;
    font-weight: $cdgsz-font-weight-bold;
}
.btn{
    width: 375px !important;
}
.register-btn{
        border:1px solid $cdgc-accent !important;
        margin-bottom: 10px !important;
        margin-top: 10px !important;
        background-color:$hue !important;
        color: $cdgc-accent;
}

@media screen and (max-width: 650px) {
    .form {
    padding-left: 20px !important;
    padding-right: 20px !important;
}
.form-div{
  // border-radius: 15px;
  // margin-left: 115px;
  // background: #ccebff;
  // margin-bottom:10%;
  // width:300px;
  // padding:20px;
  // background-color: $cdgc-bg-prime;
  width: 275px !important;
  
}
}
.login-div{
  border-radius: 15px;
  // margin-left: 115px;
  // background: #ccebff;
  // margin-bottom:10%;
  // width:300px;
  padding:20px;
  background-color: $cdgc-bg-prime;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
::ng-deep .mat-form-field-appearance-outline .mat-mdc-form-field-prefix, .mat-form-field-appearance-outline .mat-mdc-form-field-suffix {
  top: unset !important;
  bottom: 63px !important;
  margin-right: 10px;
}


.dividerd{
  border: 1px solid #f2f2f2;
  height: 27px;
  margin: 0 14px 0 0 !important;
}
.heading {
  font-family: Roboto;
  font-style: normal;
  font-weight: bold !important;
  font-size: $cdgsz-font-size-xl !important; //22px
  line-height: 25px;
  color: #f2f2f2;
}