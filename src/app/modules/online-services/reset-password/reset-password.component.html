<!-- <div fxFlex fxLayout="row" class="full-height" fxLayoutAlign="start start">
    <div fxLayout="column" fxFlex=50% fxFlex.xs="100%" fxFlex.sm="100%" fxLayoutAlign="center center" >
        <div fxLayout="row" fxFlex=20%>
        <form [formGroup]="resetPwdForm">
            <div fxFlex fxLayout="column">
                <div>
                    <h3 class="heading pb20">Reset password</h3>
                </div>
               <p class="font-12 fw-b">Reset your password below and Password must be at least <br>
                    8 characters long and contain atleast one numerical <br>
                    number (e.g.0-9) and one special character (e.g. ~!@#$%^&*()_-+=) </p>
                <div fxFlex>
                    <mat-form-field appearance="outline" class="inp">
                        <input matInput placeholder="Current password*" required formControlName="currentPwd">
                    </mat-form-field>
                </div>
                <div *ngIf="submitted && f.currentPwd.errors" class="invalid-entry">
                    <div *ngIf="f.currentPwd.errors.required">Password is required</div>
                </div>
                <div fxFlex>
                    <mat-form-field appearance="outline" class="inp">
                        <input matInput placeholder="New password" required formControlName="newPwd">
                        <mat-icon matSuffix>visibility</mat-icon>
                    </mat-form-field>
                </div>
                <div *ngIf="submitted && f.newPwd.errors" class="invalid-entry">
                    <div *ngIf="f.newPwd.errors.required">New password is required</div>
                    <div *ngIf="f.newPwd.errors.pattern">Password should meet all the criteria mentioned above</div>
                </div>
                <div fxFlex>
                    <mat-form-field appearance="outline" class="inp">
                        <input matInput placeholder="Confirm password" required formControlName="confirmPwd">
                        <mat-icon matSuffix>visibility</mat-icon>
                    </mat-form-field>
                </div>
                <div *ngIf="submitted && f.confirmPwd.errors" class="invalid-entry">
                    <div *ngIf="f.confirmPwd.errors.required">confirm password is required</div>
                    <div *ngIf="f.confirmPwd.errors.passwordMatchValidator">Confirm password should match </div>
                </div>
                  <div fxFlex>
                    <button
                    style="width: 35%"
                    mat-raised-button
                    class="sub-btn"
                    (click)="onSubmit()">
                    Submit
                  </button>
            </div>
            </div>
        </form>
    </div>
    </div>
    <div fxFlex=50% class="fogot-pwd-img-container">
        <p>Image</p>
     </div>
 </div> -->










 <div fxLayout="column" class="container" style="background-color: rgb(199, 209, 209);" (window:resize)="onResize($event)" fxLayoutAlign="center center">
	<div class="login-div" fxLayout="column" fxLayoutGap="20px">
		<div fxLayout="row" fxLayoutGap="14px" fxLayoutAlign="center center" >
            <img src="/assets/images/Cabcharge_white.png"  width="84px" alt="">
        </div>
		
			<div class="form-div">
				<form [formGroup]="resetPwdForm" class="form">
                    <div fxFlex>
                        <p class="font-12 fw-b text-cls">Reset your password below and Password must be at least <br>
                            8 characters long and contain atleast one numerical <br>
                            number (e.g.0-9) and one special character (e.g. ~!&#64;#$%^&*()_-+=) </p>
                    </div>
                    
					<div fxFlex>
                        <!-- <mat-label fxFlex class="text-cls font-16"> Current Password </mat-label> -->
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput required placeholder="Current password" formControlName="currentPwd" >
                            <!-- style="background-color:white;color:black" -->
                            <mat-error *ngIf="submitted && resetPwdForm.controls.currentPwd.hasError('required')">
								Password Is Required
							</mat-error>
                        </mat-form-field>
                    </div>
                    
                    <div fxFlex>
                        <!-- <mat-label fxFlex class="text-cls font-16"> New Password </mat-label> -->
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput required placeholder="New password" formControlName="newPwd" >
                            <!-- style="background-color:white;color:black" -->
                            <mat-icon matSuffix>visibility</mat-icon>
                            <mat-error *ngIf="submitted && resetPwdForm.controls.newPwd.hasError('required')">
								New password Is Required
                            </mat-error>
                            <mat-error *ngIf="submitted && resetPwdForm.controls.newPwd.hasError('pattern')">
								Password Should Meet All The Criteria Mentioned Above
							</mat-error>
                        </mat-form-field>
                    </div>
                  
                    <div fxFlex>
                        <!-- <mat-label fxFlex class="text-cls font-16"> Current Password </mat-label> -->
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput required placeholder="Confirm Password" formControlName="confirmPwd" >
                            <!-- style="background-color:white;color:black" -->
                            <mat-icon matSuffix>visibility</mat-icon>
                            <mat-error *ngIf="submitted && resetPwdForm.controls.confirmPwd.hasError('required')">
								Confirm Password Is Required
                            </mat-error>
                            <mat-error *ngIf="submitted && resetPwdForm.controls.confirmPwd.hasError('passwordMatchValidator')">
								Confirm Password Should Match
							</mat-error>
                        </mat-form-field>
                    </div>
					<div fxFlex>
						<button mat-raised-button class="inp register-btn" (click)="onSubmit()">SUBMIT</button>
                    </div>
				</form>
			</div>
		</div> 
	
	</div>
