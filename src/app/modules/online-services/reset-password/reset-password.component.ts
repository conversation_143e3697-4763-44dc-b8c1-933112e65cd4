import { Component, OnInit, HostListener} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { PasswordMatchValidator } from 'src/app/shared/pipes/password-match.validator';
import { Router } from '@angular/router';
import { NotificationService } from 'src/app/shared/services/notification.service';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss']
})
export class ResetPasswordComponent implements OnInit {
  public isWebLayout = false;
  public isTabletLayout = false;
  public innerWidth: any;
  submitted = false;
 resetPwdForm:FormGroup;
  constructor(private formBuilder: FormBuilder, private router: Router,private notifyService: NotificationService) { 
    this.resetPwdForm = this.formBuilder.group({
      currentPwd: ['', Validators.required],
      newPwd: ['', [Validators.required, Validators.pattern("^(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$")]],
      confirmPwd: ['', Validators.required]
    }, {
      validator: PasswordMatchValidator("newPwd", "confirmPwd")
  });
  }
  ngOnInit(): void {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth < 1280) { // 768px portrait
      this.isWebLayout = false;
      this.isTabletLayout = true;
    }
    else {
      this.isWebLayout = true;
      this.isTabletLayout = false;

    }
  }
  get f() {
    return this.resetPwdForm.controls;
  }
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth < 1280) { // 768px portrait
      this.isWebLayout = false;
      this.isTabletLayout = true;
    }
    else {
      this.isWebLayout = true;
      this.isTabletLayout = false;
    }
  }
 onSubmit() {
        this.submitted = true;
        if (this.resetPwdForm.invalid) {
            return;
        }
       else{
          this.notifyService.showSuccess("Your password has been changed",'Reset Password');
          this.router.navigate(['/reset-success'])
       }
    }
}
