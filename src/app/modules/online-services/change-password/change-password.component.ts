import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { HttpClient, HttpHeaders } from '@angular/common/http';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss']
})
export class ChangePasswordComponent implements OnInit {

  hide = true;
  hideConfirm = true;
  hideCurrentPwd = true;
  isSubmitted: boolean = false;
  showMatchError = false;

  changePwdForm: FormGroup
  error: boolean;

  constructor(private fb: FormBuilder, private router: Router, private notifyService: NotificationService, private apiService: ApiServiceService, public cdgService: CdgSharedService) { 
    this.changePwdForm = this.fb.group({
      password: ['', Validators.required],
      newPassword: ['', [Validators.required, Validators.minLength(8), Validators.maxLength(20), Validators.pattern("(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[$@$!%*?&#^]).{8,20}") ]],
      confirmPassword: ['', Validators.required]
    });
  }

  ngOnInit(): void {
  }

  submit() {
    this.isSubmitted = true;

    if(this. changePwdForm.controls.newPassword.value !== this. changePwdForm.controls.confirmPassword.value){
      this.error=true;
      this.notifyService.showError('New Password and Confirm New Password Does Not Match',"Error" );
    }
    else{
     this.error=false; 
    }
       if (this.changePwdForm.valid && this.error===false) {
      this.isSubmitted = false;

      // this.notifyService.showSuccess('Password changed successfully','Success');
      // this.router.navigateByUrl('/layout/dashboard');
      let obj = {
        "accessid": this.cdgService.userLoginDetails,
        "oldpassword": this.changePwdForm.value.password.trim(),
        "newpassword": this.changePwdForm.value.confirmPassword.trim()
      } 
      // const options ={
      //   headers:new HttpHeaders({'Accept':'application/json'})
      //   }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.CHANGE_PASSWORD, obj).subscribe((response: any) => {
        if (response.status === 200) {
          console.log(response)
          let opt = response.body;
          if(opt.successResponse){
          this.notifyService.showSuccess(opt.successResponse, 'Success');
          }
          else{
          this.notifyService.showError(opt.errorResponse, 'Error');
          }
          this.changePwdForm.reset();
        }
      },
        e => {
          // console.log(e)
          this.notifyService.showError(e.error.message,"Error")
        })
    }
  }

  reset() {
    this.isSubmitted = false;
    this.changePwdForm.reset();
  }

  getPasswordInfo() {
    let val = '';
    val = 'Password should contain at least one capital letter and one small letter. \n Password should contain at least one number. \n Password should contain at least one special character like !,@,#,$,%,^,&,* \n Password length should contain a min of 8 and a maximum of 20 characters';
    return val;
  }
  passwordValidation() {
    console.log(this.changePwdForm.value.newPassword)
    if (this.changePwdForm.value.newPassword !== "") {
      this.cdgService.popupObj.function = "";
      this.cdgService.showRegMsgDiv = false;
      this.cdgService.passwordValidation = [
        {
          msg: "Password should contain at least one capital letter and one small letter",
          icon: "done"
        },
        {
          msg: "Password should contain at least one number",
          icon: "done"
        },
        {
          msg: "Password should contain at least one special character like !,@,#,$,%,^,&,*",
          icon: "done"
        },
        {
          msg: "Password length should contain a min of 8 and a maximum of 20 characters",
          icon: "done"
        }
      ]
      this.confirmPasswordValidation(); // check confirm password as same.
      const matches = this.changePwdForm.value.newPassword.match(/\d+/g);
      const format = /[`!@#$%^&*\\|,.<>\/?]+/
      const capsFormat=/[A-Z]/
      const smallLetterFormat=/[a-z]/
      console.log(format.test(this.changePwdForm.value.newPassword))
      
      if (!capsFormat.test(this.changePwdForm.value.newPassword) || !smallLetterFormat.test(this.changePwdForm.value.newPassword) || matches === null || format.test(this.changePwdForm.value.newPassword) === false || this.changePwdForm.value.newPassword.length <= 7 || this.changePwdForm.value.newPassword.length > 20) {
        if (!capsFormat.test(this.changePwdForm.value.newPassword) || !smallLetterFormat.test(this.changePwdForm.value.newPassword)) {
          this.cdgService.passwordValidation[0].icon = "close"
        }
        if (matches === null) {
          this.cdgService.passwordValidation[1].icon = "close"
        }
        if (!format.test(this.changePwdForm.value.newPassword)) {
          this.cdgService.passwordValidation[2].icon = "close"
        }
        if (this.changePwdForm.value.newPassword.length <= 7 || this.changePwdForm.value.newPassword.length > 20) {
          this.cdgService.passwordValidation[3].icon = "close"
        }
        this.cdgService.popupObj.function = "passwordValidation";
        this.cdgService.showRegMsgDiv = true;
        console.log(this.cdgService.passwordValidation)
      }


    }
  }
  passValidation() {
    this.cdgService.showRegMsgDiv = false;
    this.cdgService.passwordValidation = [
      {
        msg: "Password should contain at least one capital letter and one small letter",
        icon: "done"
      },
      {
        msg: "Password should contain at least one number",
        icon: "done"
      },
      {
        msg: "Password should contain at least one special character like !,@,#,$,%,^,&,*",
        icon: "done"
      },
      {
        msg: "Password length should contain a min of 8 and a maximum of 20 characters",
        icon: "done"
      }
    ]
  }

  confirmPasswordValidation(){
    if (this.changePwdForm.value.newPassword !== "" && this.changePwdForm.value.confirmPassword !== "") {

      if(this.changePwdForm.value.newPassword !== this.changePwdForm.value.confirmPassword){
        this.showMatchError = true;
      }else{
        this.showMatchError = false;
      }
    }
  }

}
