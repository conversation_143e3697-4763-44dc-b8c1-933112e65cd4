@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';

.head{
  margin: 0px !important;
  color: $cdgc-accent;
  font-weight: $cdgsz-font-weight-bold;
}
.msg{
  margin: 0px !important;
  color: $cdgc-accent;
}
.ok-btn{
  width: 60px;
  height: 34px;
  border:1px solid $cdgc-accent !important;
  color: $cdgc-accent !important;
  border-radius: 4px !important;
  font-size: $cdgsz-font-size-xs !important;
}
  .header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
::ng-deep .main-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
}
  .btn{
    width:205px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
  }

  .btn-cell{
    width:100%;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
  }

  .mat-icon-color{
    color: $cdgc-bg-prime !important;
}

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
  padding-bottom: 0px !important;
}

.error-container {
    margin: 14px 0 14px 0;
}

.align-icon-text{
    display: flex;
    align-items: center;
  }
  .validationBoxMaster{
    width: 375px !important;
    height: 272px !important;
    position: absolute;
    top: 33.1%;
    left: 50%;
    margin: 0;
    transform: translate(-50%,-50%);
    -ms-transform:translate(-50%,-50%);
    opacity: 1 !important;
    z-index: 9;
    border-radius: 10px;
    // background-color: $cdgc-bg-prime;
    box-shadow: 2px 2px 4px 2px rgba(243, 6, 6, 0.14);
    // box-shadow: 0 2px 4px 0 rgba(0,0,0,0.14);
    border: 4px double $cdgc-accent;
  
  }
  
  .customErr{
    margin-bottom: 0px !important;
    font-size: 75%;
    margin-left: 20%;
  }

  .marginBottom-zero{
    margin-bottom: 0px !important;
  }