<div style="position: relative" fxLayoutGap="50px" fxLayout="column">
    <div *ngIf="cdgService.showRegMsgDiv && cdgService.popupObj.function === 'passwordValidation'" class="validationBoxMaster" [ngStyle]="{'background-color': '#b9b9bb'}">
        <div  fxLayout="column" class="p5">
            <p class="head">Password Must Include</p>
            <ng-container *ngFor="let val of cdgService.passwordValidation; let i = index">
                <p class="msg" fxLayoutAlign="start start"> <mat-icon class="material-icons-outlined main-preview-icon" [ngStyle]="{'color': val.icon == 'done' ? ' #28a745' :  '#ff0000'}">{{val.icon}}</mat-icon>{{val.msg}}</p>
            </ng-container>
            <!-- 'Password should contain at least one capital letter. \n Password should contain at least one number. \n Password should contain at least one special character like @,!,%,&,* \n Password length should contain a min of 8 and a maximum of 20 characters' -->
            <div fxFlex fxLayoutAlign="end center" >
                <button mat-raised-button class="ok-btn" [ngStyle]="{'background-color': '#b9b9bb'}" (click)="passValidation()">Ok</button>
            </div>
        </div>
    </div>
<div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap.lt-md="30px" fxLayoutGap.lt-sm="10px">
    <div fxFlex fxLayout="row" class="mt5 ml5" fxLayoutGap="10px" fxLayoutGap.lt-md="20px">
        <div fxFlex="5%">
            <img src="/assets/images/edit profile.png" alt="" class="darkMode">
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">Change Password</span>
        </div>
    </div>
</div>


    <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
        <form [formGroup]="changePwdForm" fxLayout="column" fxLayoutGap="20px">
                <div fxLayout="row" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutAlign.gt-md="start center">
                    <!-- <p class="label" fxFlex="20%" [ngClass.lt-md]="'cell-label'">CURRENT PASSWORD:<span class="asterisk">*</span></p> -->
                    <p class="label" fxFlex="20%" >CURRENT PASSWORD:<span class="asterisk">*</span></p>

                        <mat-form-field  fxFlex.gt-md="30%" fxFlex.lt-md="100%" fxFlex.md="50%" style="width:100%;" appearance="outline">
                            <input  formControlName="password" [type]="hideCurrentPwd ? 'password' : 'text'"
                                type="password" matInput placeholder="Current Password" required>
                            <mat-error class="error-container" *ngIf="isSubmitted && changePwdForm.controls.password.hasError('required')">
                                Please Enter Current Password
                            </mat-error>
                            <button matSuffix mat-icon-button (click)="hideCurrentPwd = !hideCurrentPwd" [attr.aria-label]="'Hide password'"
                                [attr.aria-pressed]="hide">
                                <mat-icon class="mat-icon-color">{{hideCurrentPwd ? 'visibility_off' : 'visibility'}}</mat-icon>
                            </button> 
                        </mat-form-field>
                </div>
                
                <div fxLayout="row" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutAlign.gt-md="start center">
                    <!-- <p class="label align-icon-text" fxFlex="20%" [ngClass.lt-md]="'cell-label'">NEW PASSWORD:<span class="asterisk">*</span><mat-icon class="info cursor-pointer mat-icon-color" matTooltip="{{getPasswordInfo()}}" >info</mat-icon></p> -->
                    <p class="label align-icon-text" fxFlex="20%" >NEW PASSWORD:<span class="asterisk">*</span><mat-icon class="info cursor-pointer mat-icon-color" matTooltip="{{getPasswordInfo()}}" >info</mat-icon></p>

                    
                        <mat-form-field fxFlex.gt-md="30%" fxFlex.lt-md="100%" fxFlex.md="50%" style="width:100%;" appearance="outline">
                            <input #input [type]="hide ? 'password' : 'text'" formControlName="newPassword"
                                type="password" matInput placeholder="New Password" (focusout)="passwordValidation()" required>
                              
                            <mat-error class="error-container" *ngIf="isSubmitted && changePwdForm.controls.newPassword.hasError('required')">
                                Please Enter New Password
                            </mat-error>
                            <!-- <mat-error class="error-container" *ngIf="isSubmitted && changePwdForm.controls.newPassword.hasError('pattern')">
                                Password Does Not Meet The Criteria
                            </mat-error>  -->
                            <!-- <mat-error class="error-container" *ngIf="changePwdForm.controls.newPassword.hasError('minlength')">
                                Password should contain 6 characters.
                            </mat-error>  -->
                            <button matSuffix mat-icon-button (click)="hide = !hide" [attr.aria-label]="'Hide password'"
                                [attr.aria-pressed]="hide">
                                <mat-icon  class="mat-icon-color">{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
                            </button> 
                        </mat-form-field>
                </div>

                <div fxLayout="row" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutAlign.gt-md="start center" class="marginBottom-zero">
                    <p class="label" fxFlex="20%" >CONFIRM NEW PASSWORD:<span class="asterisk">*</span></p>

                    <!-- <p class="label" fxFlex="20%" [ngClass.lt-md]="'cell-label'">CONFIRM NEW PASSWORD:<span class="asterisk">*</span></p> -->
                        <mat-form-field fxFlex.gt-md="30%" fxFlex.lt-md="100%" fxFlex.md="50%" style="width:100%;" appearance="outline">
                            
                            <input #confirmPassword [type]="hideConfirm ? 'password' : 'text'" (input)="confirmPasswordValidation()"
                                formControlName="confirmPassword" matInput placeholder="Confirm New Password"
                                 required>
                               
                                <mat-error class="error-container" *ngIf="isSubmitted && changePwdForm.controls.confirmPassword.hasError('required')">
                                Please Enter Confirm New Password
                                </mat-error> 
                                                               
                            <button matSuffix mat-icon-button (click)="hideConfirm = !hideConfirm" [attr.aria-label]="'Hide password'"
                                [attr.aria-pressed]="hideConfirm">
                                <mat-icon class="mat-icon-color">{{hideConfirm ? 'visibility_off' : 'visibility'}}</mat-icon>
                            </button>
                        </mat-form-field>
                </div>
                <mat-error class="customErr"  *ngIf="showMatchError">
                    New Password and Confirm New Password Does Not Match
                </mat-error> 

              

                <div fxLayout="row" class="mt-10" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutGap.gt-md="1%"
                    fxLayoutGap.lt-md="4%">
                    <!-- <button [ngClass.gt-md]="'btn'"  [ngClass.lt-md]="'btn-cell'" mat-raised-button -->
                        <button mat-raised-button class="btn"
                        (click)="submit()">
                        SUBMIT
                    </button>
                   
                    <!-- <button [ngClass.gt-md]="'btn'" [ngClass.lt-md]="'btn-cell'" mat-raised-button -->
                        <button mat-raised-button class="btn"
                        (click)="reset()">
                        RESET
                    </button>
                </div>

          
        </form>
    </div>
</div>