@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
:host .mat-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
    height: 40px !important;
    width: 40px !important;
}
.inp{
    width: 260px !important;
}
.submit-btn{
    // width:205px;
    background-color:$cdgc-bg-hue;
    color: $cdgc-font-accent;
    // border-radius: 10px !important;
}
.fo{
    font-size: 15px; 
    text-align: left;
    justify-content: left;    
}
.foo{
    font-size: 15px; 
    text-align: left;
    justify-content: left;  
}
.sub-title {
    font-family: $cdgsz-font-family;
    font-style: normal;
    font-weight: bold;
    font-size: 12px;
    line-height: 16px;
}
.sub-header{
    font-size:$cdgsz-font-size-sm ;
}
.error {
    font-size: 11px;
    display: inline;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
 }
 .tbl-container{
    display: flex;
    flex-direction: column;
    // background-color: transparent !important;
    // max-width: 300px;
    // max-height: 500px;
    // max-height: 600px; commented for testing on 1/12/2022 neevika
}
.mat-mdc-header-row{
    border-radius: 10px;
    background-color:$cdgc-bg-prime !important;
    color: $cdgc-font-accent !important;
}
.mat-mdc-header-cell {
    color: $cdgc-font-accent !important;
    
}
.mat-mdc-row{
    border-radius: 12px;
    color: $lightGrey !important;

}
.si{
    font-size: 13px;   
 }
.sno{
    display: block;
}
.mobile-label{
    display: none;
}
 :host input[type=number]::-webkit-outer-spin-button{
    opacity: 0;
}
:host input[type=number]::-webkit-inner-spin-button{
    opacity: 0;
}
 .tbl-inp{
    width: 90px !important;
}
@media screen and (max-width: 600px) {
    .inp{
        width: 183px !important;
    }
    .mobile-label{
        display: inline-block;
        width: 165px;
        font-weight: $cdgsz-font-weight-bold;
    }
    .mat-mdc-header-row{
        display: none;
    }
    .mat-mdc-row{
        flex-direction: column;
        align-items: start;
        padding: 8px 24px;
        border-radius: 12px !important;
        border: 1px solid $cdgc-border-prime;
        min-height: 28px !important;
        // margin-bottom: 10px !important;
    }
    .mat-mdc-cell:first-of-type {
        padding-left: 0px !important; 
    }
    .sno{
        display: none !important;
    }
 }

 .attach{
    font-size: 11px;
    font-weight: 400;
 }
 .upload-btn{
    background: #006BA8;
    color: #fff;
    height: 50px;
    border-radius: 10px !important;
    font-size: 1.25rem !important;
    padding-right: 24px !important;
    padding-left: 24px !important;
 }
 .delete-icon{
    margin: 7px 5px;
    cursor: pointer;
 }
 
 [hidden]{
    display: none;
}