import { Component, OnInit, ViewChild } from '@angular/core';
import { Validators, FormBuilder, FormControl } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { ActivatedRoute, Router } from '@angular/router';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { SelectionModel } from '@angular/cdk/collections';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { CommonModalComponent } from 'src/app/shared/shared-modules/common-modal/common-modal.component';
import { DecimalPipe } from '@angular/common';
import * as XLSX from 'xlsx';

import { distinct } from 'rxjs/operators';
import { threadId } from 'worker_threads';
// import { GridAlignColumnsDirective } from '@angular/flex-layout/grid/typings/align-columns/align-columns';


@Component({
  selector: 'app-self-service-top-up-draft',
  templateUrl: './self-service-top-up-draft.component.html',
  styleUrls: ['./self-service-top-up-draft.component.scss']
})
export class SelfServiceTopUpDraftComponent implements OnInit {
  topupForm = {
    productType: "ALL",
    radioValue: "cardNo",
    cardNo: [],
    amt: "",
  }
  isCardNoShow = false;
  data: any;
  isShowAddBtn: boolean = true;
  isDraftBtn: boolean = false;
  displayedColumns: any[] = ['slNo', 'nameOnCard', 'productName', 'cardNo', 'creditBalance', 'cardExpiry', 'topUpAmount', 'select'];
  radioArr = [
    { value: "cardNo", viewValue: "CARD NUMBER", isDisable: false },
    { value: "displayAll", viewValue: "DISPLAY ALL", isDisable: false },
    { value: "upload", viewValue: "UPLOAD", isDisable: false }
  ]
  cardNoSelectedList = new FormControl();
  fileName = 'SelfserviceTopup.xlsx'
  errorTbl = [
    {
      nameOnCard: 'CHRIST CARD THREE',
      productName: 'CONTACTLESS GIFT CARD(C3)',
      cardNo: '6010896517000036',
      creditBalance: 4328.18,
      expDate: '20-03-2021',
      topupAmt: 5,
      select: true
    },
    {
      nameOnCard: 'CHRIST CARD FIVE',
      productName: 'CONTACTLESS GIFT CARD(C3)',
      cardNo: '60108965111235',
      creditBalance: 4328.18,
      expDate: '20-03-2021',
      topupAmt: 5,
      select: true
    }
  ]
  selfServiceDraftList: any[] = [];
  dataSource = new MatTableDataSource<any>(this.selfServiceDraftList);
  @ViewChild(MatPaginator) paginator: MatPaginator;
  fullcardno: any[] = [];
  personalTbl: any[] = [];
  productArr: any[] = [];
  cardArr: any[] = [];
  isShowTbl: boolean = false;
  amtshow: boolean = false;
  isShowTopUpError: boolean = false;
  lastdigit: any[] = [];
  dataSourcedata: any[] = [];
  firstdigit: string;
  displaycard: string;
  uploadFile: any;
  uploadedFileSize: any;
  fileSizeExceed: boolean;
  topuperror: boolean = false;
  showcarderror: boolean = false;
  fielddisable: boolean = false;
  showerror: boolean = false;
  carderrorrad: boolean = false;
  uploadFileName: any;
  accountnumber: any;
  amount: [];
  count: [];
  isUpload: boolean;
  selection = new SelectionModel<any>(true, []);
  datas: any;
  accountName: any;
  customerName: any;
  submitdata: any;
  prod: boolean;
  customernumber: any;
  selectedvalue: string;
  cdvalue: string;
  valuede: any;
  deci: any;
  arr: any;
  amtvalue: number;
  uploadback: any;
  cdvalues: any;
  elearray: any = [];
  tempObj: any;
  selectedDeleteList: any;
  selectedSaveDraftList: any;
  constructor(private route: ActivatedRoute, public decimal: DecimalPipe, public dialog: MatDialog, public apiService: ApiServiceService, private router: Router, public localStorage: LocalStorageService, private fb: FormBuilder, public cdgService: CdgSharedService, private notifyService: NotificationService) { }

  ngOnInit(): void {
    if (this.cdgService.upload) {
      if (this.cdgService.upload.function === "back") {
        this.selfServiceDraftList = this.localStorage.localStorageGet('tabledata');
        if (this.selfServiceDraftList) {
          if (this.selfServiceDraftList.length > 0) {
            this.fielddisable = true;
            this.prod = true;
            this.amtshow = true;
            this.isDraftBtn = true;
            this.cdvalue = this.localStorage.localStorageGet('prodtype');
            this.selectedvalue = this.localStorage.localStorageGet('prodtype');
          }
        }
        else {
          this.fielddisable = false;
          this.prod = false;
          this.amtshow = false;
        }

      }
    }

    if (this.cdgService.upload.function !== "back") {
      this.isDraftBtn = false;
    }
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        if (this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0]) {
          this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
          this.customerName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name
        }
      }
      if (this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails) {
        this.customernumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number;
        this.customerName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name

      }
      else {
        console.log(this.localStorage.localStorageGet('customerviewvalue'), this.localStorage.localStorageGet('viewvalueonload'));

        if (this.localStorage.localStorageGet('customerviewvalue') === null) {
          this.customernumber = this.localStorage.localStorageGet('viewvalueonload').substr(0, 6);
          this.customerName = this.localStorage.localStorageGet('viewvalueonload').substr(9)
        }
        else {
          this.customernumber = this.localStorage.localStorageGet('customerviewvalue').substr(0, 6);
          this.customerName = this.localStorage.localStorageGet('customerviewvalue').substr(9)
        }
      }
    }
    this.accountName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails ? this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name : this.customerName,
      this.isShowAddBtn = false;
    this.getDetails();
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "accountNo": this.accountnumber,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/topupProductType', obj).subscribe
      ((response: any) => {
        console.log(response)
        if (response.status === 200) {
          this.data = response.body;
          this.productArr = this.data;
          this.productArr.unshift({
            oneTimeUsage: "N",
            productId: "ALL",
            productName: "ALL"
          });
        }
      });
  }
  add(event: any) {
    console.log(event)
  }

  getDetails() {
    let obj = {
      "accountNo": this.accountnumber,
      "productTypeId": this.topupForm.productType,
      "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
    }

    console.log('Default/specific data view api', obj);

    this.selection = new SelectionModel<any>(true, []); // resetting the selection values

    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/retrieveCardDraftdetail', obj).subscribe
      ((response: any) => {
        var responseData = response.body;
        if (response.status === 200) {
          this.selfServiceDraftList = responseData;
          this.selfServiceDraftList.forEach((element: any) => {
            this.cardArr.push(element.cardNo);
          });
          this.dataSource = new MatTableDataSource<any>(this.selfServiceDraftList);
          this.dataSource.paginator = this.paginator;
        }
      });

  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  hideData() {
    this.isShowTbl = false;
    this.carderrorrad = false;
    this.amtvalue = Number(this.topupForm.amt)
    this.amtvalue = 0.00
    this.amtshow = false;
    this.selfServiceDraftList = [];
    this.cardNoSelectedList.reset();
    this.uploadFile = "";
    this.uploadFileName = "";
    this.isUpload = false;
    this.fileSizeExceed = false;
    this.topupForm.amt = "";
    this.showcarderror = false;
  }

  getTotalCost() {
    return this.selfServiceDraftList.reduce((a, b) => {
      return Number(a) + Number(b['topUpAmount']);
    }, 0);
  }
  private regex: RegExp = new RegExp(/^\d{0,12}\.?\d{0,2}$/g);
  // Allow key codes for special events. Reflect :
  // Backspace, tab, end, home
  private specialKeys: Array<string> = ['Backspace', 'Tab', 'End', 'Home', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];
  numberOnly(event: any) {
    let value = event.target.value;
    if (this.specialKeys.indexOf(event.key) !== -1) {
      return;
    }
    let current: string = value;
    const position = event.target.selectionStart;
    const next: string = [current.slice(0, position), event.key == 'Decimal' ? '.' : event.key, current.slice(position)].join('');
    if (next && !String(next).match(this.regex)) {
      event.preventDefault();
    }

  }
  onSubmit() {

    this.arr = "";
    this.selfServiceDraftList.forEach((el: any) => {
      el.topUpAmount = Number(el.topUpAmount);
    });
    if (this.selection.selected.length == 0) {
      this.notifyService.showError('Please select record to submit', 'Error')
    }
    else {
      this.selfServiceDraftList.forEach(element => {
        if (this.selection.selected) {
          this.dataSourcedata = this.selection.selected;
        }
      });
      this.dataSourcedata.forEach(element => {
        if (!element.topUpAmount) {
          this.arr = element.cardNo;
        }
      });
      if (this.arr) {
        this.isShowTopUpError = true;
        this.notifyService.showError('Top-up Amount Is Required', 'Error')
      }
      else {
        this.isShowTopUpError = false;
        const dialogConfig = new MatDialogConfig()
        dialogConfig.data = {
          title: "Confirmation",
          msg: "Are you sure you want to Submit?",
          btnClose: "Cancel",
          btnConfirm: "Ok",
        }
        const dialogRef = this.dialog.open(CommonModalComponent, dialogConfig);
        dialogRef.afterClosed().subscribe(result => {
          if (result) {
            let obj = {
              "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
              "accountNo": this.accountnumber,
              "accountName": this.accountName,
              "customerNumber": this.customernumber,
              "prepaidValidDtoList": this.dataSourcedata,
              "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
              "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId
            }
            this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/submitDraftedTopupInfo', obj).subscribe
              ((response: any) => {
                console.log(response)
                if (response.status === 200) {
                  this.submitdata = response.body;
                  if (response.body.errorMessage === null) {
                    this.notifyService.showSuccess(response.body.successMessage, 'Success');
                    this.getDetails();
                  }
                  else {
                    this.notifyService.showError(response.body.errorMessage, 'Error')
                  }
                }
              });
          }
        });

      }

    }

  }
  onPrevious() {
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "accountNo": this.accountnumber,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "cardNumberList": this.cardArr
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/previousTopupAmount', obj).subscribe
      ((response: any) => {
        console.log(response)
        if (response.status === 200) {
          this.datas = response.body;
          this.datas.forEach((elements: any) => {
            this.selfServiceDraftList.forEach(element => {
              if (element.cardNo === elements.cardNumber) {

                element.topUpAmount = elements.topupAmount;

              }
            });

          });
          this.selfServiceDraftList.forEach(ele => {

            if (!ele.topupAmount) {
              this.elearray.push(ele.cardNo)
              if (this.elearray.length > 0) {
                this.notifyService.showError('No Previous Topup amount for records', 'Error')
              }
            }

          });
        }
      });
  }
  change() {
    this.isDraftBtn = true;
  }
  Onsaveasdraft() {
    this.arr = "";
    if (this.selection.selected.length == 0) {
      this.notifyService.showError('Please select record to save', 'Error')
    }
    else {
      this.selfServiceDraftList.forEach(element => {
        element.topUpAmount = Number(element.topUpAmount);
        if (this.selection.selected) {
          this.dataSourcedata = this.selection.selected;
        }
      });
      this.dataSourcedata.forEach(element => {
        if (!element.topUpAmount) {
          this.arr = element.cardNo;
        }
      });

      if (this.arr) {
        this.isShowTopUpError = true;
        this.notifyService.showError('Top-up Amount Is Required', 'Error')
      }

      let obj = {
        "accountNo": this.accountnumber,
        "productTypeId": this.topupForm.productType,
        "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "prepaidValidDtoList": this.dataSourcedata
      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/draftUpdate', obj).subscribe
        ((response: any) => {
          console.log(response)
          if (response.status === 200) {
            this.data = response.body;
            this.getDetails();
            if (response.body.errorMessage === null) {
              this.notifyService.showSuccess(response.body.successMessage, 'Success')
            }
            else {
              this.notifyService.showError(response.body.errorMessage, 'Error')
            }
            this.localStorage.localStorageSet('producttypeid', this.topupForm.productType);
          }
        });
    }

  }
  routeToErrorCard() {
    this.localStorage.localStorageSet('errorCard', this.errorTbl)
    this.router.navigateByUrl('/layout/selftopup/errorCards');
  }
  carderror(val: any) {
    if (val) {
      this.showcarderror = false;
    }
  }
  ongo(isValid: boolean) {
    this.topuperror = false;
    this.isDraftBtn = true;

    if (this.topupForm.radioValue === "") {
      this.carderrorrad = true;
    }
    else {
      this.carderrorrad = false;
    }
    if (!this.cardNoSelectedList.value) {
      this.showcarderror = true;

    }
    else {
      this.showcarderror = false;
    }
    if (isValid) {
      if (this.topupForm.radioValue === 'cardNo') {
        this.fullcardno = [];
        if (this.cardNoSelectedList.value) {
          this.cardNoSelectedList.value.forEach((element: any) => {
            this.fullcardno.push(this.firstdigit + element);
          });

          let obj = {
            "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
            "accountNo": this.accountnumber,
            "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
            "cardNumberList": this.fullcardno
          }
          this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/retrieveCardTopupDetails', obj).subscribe
            ((response: any) => {
              console.log(response)
              if (response.status === 200) {
                this.personalTbl = response.body;
                this.selfServiceDraftList = this.personalTbl;
                if (this.selfServiceDraftList) {
                  this.amtshow = true;

                }

              }
            });
        }
      }
      if (this.topupForm.radioValue === 'displayAll') {
        if (this.cardArr.length === 0) {
          this.notifyService.showError('Card Details not Found', 'Error');
        }
        let obj = {
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "accountNo": this.accountnumber,
          "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          "cardNumberList": this.cardArr
        }
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/retrieveCardTopupDetails', obj).subscribe
          ((response: any) => {
            console.log(response)
            if (response.status === 200) {
              this.personalTbl = response.body;
              this.selfServiceDraftList = this.personalTbl;
              if (this.selfServiceDraftList) {
                this.amtshow = true;

              }

            }
          });
      }
      if (this.topupForm.radioValue === 'upload') {
        let formData = new FormData();
        if (this.uploadFile !== undefined) {
          formData.append('file', this.uploadFile);
          formData.append('attachmentName', this.uploadFileName);
        }
        if (this.uploadFile === undefined) {
          this.uploadFile = '';
          this.uploadFileName = '';
          formData.append('file', this.uploadFile);
          formData.append('attachmentName', this.uploadFileName);
        }
        if (this.uploadFile == "") {
          this.notifyService.showError('Please upload the file', 'Error');
        }

        formData.append('accessId', this.localStorage.localStorageGet("loginUserDetails").accessId);
        formData.append('role', this.localStorage.localStorageGet("loginUserDetails").roles.roleName);
        formData.append('accountNo', this.accountnumber);
        formData.append('productTypeId', this.topupForm.productType);
        formData.append('roleId', this.localStorage.localStorageGet("loginUserDetails").roles.roleId);


        const options = {
          headers: new HttpHeaders().set('Accept', 'application/json').set("Authorization", this.localStorage.localStorageGet("auth"))
        }
        this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/validateCards', formData, options).subscribe
          ((response: any) => {
            console.log(response)
            this.data = response;
            this.localStorage.localStorageSet("tabledata", this.data.prepaidValidCardDtoList);
            this.localStorage.localStorageSet("prodtype", this.topupForm.productType);
            if (response.errorMessage) {
              this.notifyService.showError(response.errorMessage, 'Error');
            }
            else {
              this.errorTbl = this.data.selfServiceTopUpErrorCardDtoList;
              if (this.data.selfServiceTopUpErrorCardDtoList) {
                const dialogConfig = new MatDialogConfig()
                dialogConfig.data = {
                  title: "Confirmation",
                  msg: "Do you want to see the Card Details?",
                  btnClose: "Cancel",
                  btnConfirm: "Proceed",
                  btnerror: "View Error",
                  func: 'edit'
                }
                const dialogRef = this.dialog.open(CommonModalComponent, dialogConfig);
                dialogRef.afterClosed().subscribe(result => {
                  if (dialogConfig.data.btnConfirm === "Proceed" && result === true) {
                    if (this.data.prepaidValidCardDtoList.length === 0) {
                      this.notifyService.showError('No Valid Records Found in Uploaded Excel', 'Error');
                    }
                    else {
                      this.selfServiceDraftList = this.data.prepaidValidCardDtoList;
                      this.personalTbl = this.data.prepaidValidCardDtoList;

                      this.amtshow = true;
                    }
                  }
                  if (dialogConfig.data.btnerror === "View Error" && result === false) {

                    this.routeToErrorCard();
                  }
                });
              }
            }
          });

      }
    }
    else {
      this.notifyService.showError('Mandatory Fields cannot be blank', 'Error')
    }

  }

  submit(isValid: boolean) {
    this.selfServiceDraftList = [];

    if (isValid) {
      if (this.topupForm.radioValue === 'cardNo') {
        this.personalTbl.forEach(element => {
          element.topUpAmount = Number(this.topupForm.amt);

        });
        this.selfServiceDraftList = this.personalTbl;
      }
      if (this.topupForm.radioValue === 'displayAll') {
        this.personalTbl.forEach(element => {
          element.topUpAmount = Number(this.topupForm.amt)
        });
        this.selfServiceDraftList = this.personalTbl;

      }
      if (this.cdgService.upload.function === "back") {
        this.selfServiceDraftList.forEach(element => {
          element.topUpAmount = Number(this.topupForm.amt)
        });

      }
      if (this.topupForm.radioValue === 'upload') {
        this.personalTbl.forEach(element => {
          element.topUpAmount = Number(this.topupForm.amt)
        });
        this.selfServiceDraftList = this.personalTbl;
      }
    }


  }
  onFileInput(event: any) {
    console.log(event.target.file)
    if (event.target.files.length > 0) {
      this.uploadFile = event.target.files[0];
      console.log('size', this.uploadFile.size);
      this.uploadedFileSize = this.uploadFile.size;
      if (this.uploadedFileSize > 1000000) {
        this.fileSizeExceed = true;
        this.notifyService.showWarning("Uploaded File size exceeded", 'Warning!')
      }
      else {
        this.fileSizeExceed = false;
      }
      this.uploadFileName = this.uploadFile.name
      console.log(this.uploadFile)
      this.isUpload = true;
    }
  }


  removeFile() {
    this.uploadFile = "";
    this.uploadFileName = "";
    this.isUpload = false;
    this.fileSizeExceed = false;
  }

  Reset() {
    if(this.selection.selected.length==0){
      this.notifyService.showWarning('Please select record to reset','Warning');
    }
    else{
      this.selfServiceDraftList.forEach(obj => {
        this.selection.selected.forEach(selectedElementObj => {
          if(obj.cardNo === selectedElementObj.cardNo){
            obj.topUpAmount = 0;
          }
        });
      });
    }    
  }
  //to check whether the number of rows selected matches the total number of rows
  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.selfServiceDraftList.length;
    return numSelected === numRows;
  }
  //selects all the rows if they are not all selected or clears the selection
  masterToggle() {
    this.isAllSelected() ?
      this.selection.clear() :
      this.selfServiceDraftList.forEach(element => {
        this.selection.select(element);
      });
  }

  onSingleDltClick(transaction: any) {
    console.log('single delete fun added', transaction);
    let singleDltDbObj = {
      "accountNo": this.accountnumber,
      "productTypeId": this.topupForm.productType,
      "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "prepaidValidDtoList": [{
        "cardNo": transaction.cardNo,
        "nameOnCard": transaction.nameOnCard,
        "productName": transaction.productName,
        "creditBalance": transaction.creditBalance,
        "cardExpiry": transaction.cardExpiry,
        "topUpAmount": transaction.topUpAmount,
        "productTypeId": transaction.productTypeId
      }]
    }

    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/deleteByRow', singleDltDbObj).subscribe
      ((response: any) => {
        console.log(response);
        if (response.status === 200) {
          this.notifyService.showSuccess("Data Removed Successfully", 'Success');
          this.getDetails();
        }
        else {
          this.notifyService.showError("Err occured in removing data", 'Error');
        }
      });
  }

  onBulkDltClick() {
    this.selectedDeleteList = [];
    this.arr = "";
    if (this.selection.selected.length == 0) {
      this.notifyService.showError('Please select record to delete', 'Error')
    }
    else {
      this.selfServiceDraftList.forEach(element => {
        if (this.selection.selected) {
          this.dataSourcedata = this.selection.selected;
        }
      });

      this.dataSourcedata.forEach(element => {
        this.tempObj = {};
        this.tempObj.cardNo = element.cardNo,
          this.tempObj.nameOnCard = element.nameOnCard,
          this.tempObj.productName = element.productName,
          this.tempObj.creditBalance = element.creditBalance,
          this.tempObj.cardExpiry = element.cardExpiry,
          this.tempObj.topUpAmount = element.topUpAmount,
          this.tempObj.productTypeId = element.productTypeId
        this.selectedDeleteList.push(this.tempObj);
      });


      let obj = {
        "accountNo": this.accountnumber,
        "productTypeId": this.topupForm.productType,
        "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "prepaidValidDtoList": this.selectedDeleteList,
      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/deleteByBulk', obj).subscribe
        ((response: any) => {
          console.log(response);
          if (response.status === 200) {
            this.notifyService.showSuccess("Data's Removed Successfully", 'Success');
            this.getDetails();
          }
          else {
            this.notifyService.showError("Err occured in removing data", 'Error');
          }
        });
    }

  }
}

