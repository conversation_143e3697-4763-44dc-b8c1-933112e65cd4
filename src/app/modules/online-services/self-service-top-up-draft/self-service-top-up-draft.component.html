<div fxLayout="column" fxLayoutGap="50px">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">monetization_on</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">Self Service Top-Up Draft</span>
        </div>
        
    </div>
    <div fxLayout="column" class="custom-padding-left sub-title">
        <span class="sub-header">INSTRUCTIONS:</span><br>
        <span>Items marked with <span style="color: red;font-weight: bold;">*</span>are mandatory.Please ensure all mandatory fields are filled</span>
</div>
    <form #f="ngForm">
       
        <div fxLayout="column" class="custom-padding-left" fxLayoutGap="30px" >

            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">PRODUCT TYPE <span class="asterisk"><sup>*</sup></span>:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%">
                    <mat-form-field appearance="outline" class="product-inp">
                        <mat-select name="prodType" [(ngModel)]="topupForm.productType"
                            placeholder="Select Product Type"   (selectionChange)="getDetails()" #prodType="ngModel"
                            required>
                            <mat-option *ngFor="let acl of productArr" [value]="acl.productId">
                                {{acl.productName}}
                            </mat-option>
                        </mat-select>
                        <mat-error *ngIf="f.submitted && prodType.invalid" class="pt15">
                            <div *ngIf="prodType.hasError('required')">
                                Product Type Cannot Be Blank
                            </div>
                        </mat-error>
                    </mat-form-field>
                </div>
                
            </div>
        
            <div fxFlex fxLayoutAlign="start center">
                <span style="font-weight: bold;" class="sub-header">Draft Table:</span>
            </div>
            <div>
                <div class="tbl-container mat-elevation-z8" [hidden]="selfServiceDraftList.length === 0">
                    <mat-table #table [dataSource]="dataSource">
                        <ng-container matColumnDef="slNo">
                            <mat-header-cell *matHeaderCellDef> S No. </mat-header-cell>
                            <mat-cell *matCellDef="let element;let i=index"> {{i+1}} </mat-cell>
                            <mat-footer-cell *matFooterCellDef class="fwb font-11 pr14 fo" > </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="nameOnCard">
                            <mat-header-cell *matHeaderCellDef> Name On Card </mat-header-cell>
                            <mat-cell *matCellDef="let element" class="fwb font-11 pr14 fo" > {{element.nameOnCard}} </mat-cell>
                            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="productName">
                            <mat-header-cell *matHeaderCellDef> Product Name </mat-header-cell>
                            <mat-cell *matCellDef="let transaction"  class="fwb font-11 pr14 fo" > {{transaction.productName}} </mat-cell>
                            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="cardNo" class="pr15">
                            <mat-header-cell *matHeaderCellDef> Card No </mat-header-cell>
                            <mat-cell *matCellDef="let transaction"  class="fwb font-10 pr14 fo"> {{transaction.cardNo}} </mat-cell>
                            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="creditBalance" class="pr15">
                            <mat-header-cell *matHeaderCellDef> Credit Balance</mat-header-cell>
                            <mat-cell *matCellDef="let transaction"  class="fwb font-10 pr14 fo" > {{transaction.creditBalance | currency}} </mat-cell>
                            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="cardExpiry" class="pr15 fo">
                            <mat-header-cell *matHeaderCellDef> Expiry Date </mat-header-cell>
                            <mat-cell *matCellDef="let transaction"  > {{transaction.cardExpiry}} </mat-cell>
                            <mat-footer-cell *matFooterCellDef> Total </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="topUpAmount">
                            <mat-header-cell *matHeaderCellDef> Top-up Amount ($) </mat-header-cell>
                            <mat-cell *matCellDef="let transaction;let i=index;">
                                <div fxLayout="column">
                                    <mat-form-field appearance="outline" class="tbl-inp">
                                        <input matInput type="text"  name="topUpAmount{{i}}"
                                            [(ngModel)]="transaction.topUpAmount"  (keydown)="numberOnly($event)" (ngModelChange)="change()" [ngModelOptions]="{standalone:true}" required>
                                    </mat-form-field>
                                    <!-- <mat-error class="error" *ngIf="topuperror" fxLayoutAlign="start start">
                                        No previous Top-up record Found
                                    </mat-error> 
                                    <mat-error class="error" *ngIf="!topuperror" fxLayoutAlign="start start">
                                    
                                    </mat-error>  -->
                                </div>
                            </mat-cell>
                            <mat-footer-cell *matFooterCellDef> {{getTotalCost() | currency}} </mat-footer-cell>
                        </ng-container>
                        
                        <ng-container matColumnDef="select">
                            <mat-header-cell *matHeaderCellDef> 
                                <mat-checkbox (change)="$event ? masterToggle() : null" 
                                [checked]="selection.hasValue() && isAllSelected()" color="primary"
                                [indeterminate]="selection.hasValue() && !isAllSelected">
                                </mat-checkbox>
                            
                        </mat-header-cell>
                            <mat-cell *matCellDef="let transaction">
                                <div fxLayout="column">
                                    <mat-checkbox (click)="$event.stopPropagation()" 
                                    (change)="$event ? selection.toggle(transaction) : null" color="primary"
                                    [checked]="selection.isSelected(transaction)">
                                    </mat-checkbox>
                                </div>
                                <div fxLayout="column">                               
                                    <span><mat-icon style="font-size: 1.5rem !important;" (click)="onSingleDltClick(transaction)" class="delete-icon">delete</mat-icon> </span>
                                </div>
                            </mat-cell>
                            
                            <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
                        </ng-container>

                        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>                        
                        <mat-footer-row *matFooterRowDef="displayedColumns"></mat-footer-row>
                    </mat-table>
                    <mat-paginator [pageSize]="5" [pageSizeOptions]="[5, 10, 20, 50, 100]" showFirstLastButtons></mat-paginator>
                </div>

                <div *ngIf="selfServiceDraftList.length === 0">
                    <h2 style="color: red;text-align: center;">No Data Found</h2>
                </div>
            </div>
           <div>
            <div fxFlex *ngIf="selfServiceDraftList.length > 0" fxLayout="row" class="mt-10" fxLayout.lt-md="column" fxLayout.md="column" fxLayoutGap.gt-md="1%"
                fxLayoutGap.lt-md="4%">
                <button mat-raised-button class="submit-btn" (click)="onSubmit()">SUBMIT</button>
                <button mat-raised-button class="submit-btn" (click)="Onsaveasdraft()">SAVE DRAFT</button>
                <button mat-raised-button class="submit-btn" (click)="onPrevious()">PREVIOUS TOPUP AMOUNT</button>
                <button mat-raised-button class="submit-btn" (click)="Reset()">RESET</button>
                <button mat-raised-button class="submit-btn" (click)="onBulkDltClick()">DELETE ALL</button>
            </div>
        </div>
        </div>
        <div></div><br>
       
        <div fxLayout="column" class="custom-padding-left sub-title">
            <br>
            <span  class="sub-header">[Terms & Condition]->S&M to highlight if needed,if needed what should be the content
            </span>
        </div>
    </form>
</div>