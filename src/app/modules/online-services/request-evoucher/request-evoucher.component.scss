@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';

.title{
    line-height: 14.2px;
    color: $lightGrey;
}
.header{
  color: $lightGrey !important;
  font-size: 28px;
  font-weight:$cdgsz-font-weight-normal ;
}
.sub-title {
  font-family: $cdgsz-font-family;
  font-style: normal;
  font-weight: bold;
  font-size: 12px;
  line-height: 16px;
}
.sub-header{
  font-size:$cdgsz-font-size-sm ;
}
.full-height {
    height: 100vh !important; 
  }
  .mt50{
      margin-top: 50px;
  }
 
  .reset-btn{
    width:205px;
    border-radius: 10px !important;  
}
.submit-btn{
  width:205px;
  background-color:$cdgc-bg-hue !important;
  color: $cdgc-font-accent !important;
  border-radius: 10px !important;
}
.inp{
  width: auto !important;
}
//desktops
@media only screen and (min-width:992px){
    .title{
      font-size: 24px;
    } 
    .submit-btn, .reset-btn{
      width:190px;
    }
  }
  .ml20{
    margin-left: 20px;
  }
  //larger screens than desktop
  @media only screen and (min-width:1200px){
    .title{
      font-size: 24px;
    }
    .submit-btn, .reset-btn{
      width:190px;
    }
  }
  //for mobile
  @media only screen and (max-width:768px){
    .title{
      font-size:18px;
    }
    .submit-btn, .reset-btn{
      width:110px;
    }
    .inp{
      width: 156px !important;
    }
  }
  /*Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  ::ng-deep .mat-mdc-form-field-text-infix {
   width: 300px !important;
}
  /*Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/

:host ::ng-deep .mat-mdc-text-field-wrapper {
  padding-bottom: 0px !important;
}
:host ::ng-deep .mat-mdc-text-field-wrapper {
  width: 250px;
}