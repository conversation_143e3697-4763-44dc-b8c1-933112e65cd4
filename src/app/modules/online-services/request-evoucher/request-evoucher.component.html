<div fxFlex fxLayout="column"> 
  <!-- class="full-height" -->
    <!-- <div fxLayout="row" class="mt50 ml20">
      <div fxFlex="5%">
        <img src="/assets/images/recent transactions.svg" alt="" width="40px">
    </div>
      <div class="title">REQUEST eVOUCHERS</div>
     </div> -->
     <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
      <div fxFlex="5%">
        <img src="/assets/images/recent transactions.svg" alt="" width="40px">
      </div>
      <div fxFlex="95%" fxLayoutAlign="start center">
          <span class="header pb5">REQUEST eVOUCHERS</span> 
      </div>
  </div>
     <br>
     <div fxLayout="column" class="custom-padding-left sub-title">
      <span class="sub-header">INSTRUCTIONS:</span><br>
      <span>Items marked with <span style="color: red;font-weight: bold;">*</span>are mandatory.Please ensure all mandatory fields are filled</span>
</div><br>
     <div fxLayout="row" class="ml20"> 
      <form [formGroup]="eVoucherForm" fxFlex="100%">
        <div fxLayout="column" fxLayoutGap="20px" class="custom-padding-left">
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px">
          <div fxFlex="21%" fxFlex.lt-md="35%" fxFlex.md="35%" fxLayoutAlign="start center">
            <span class="date-title">DIVISION<span class="asterisk"><sup>*</sup></span>:</span>
        </div>
        <div fxFlex="91%" fxFlex.lt-md="65%" fxFlex.md="75%">
           
                <mat-form-field appearance="outline" class="inp">
                    <!-- <mat-select [(ngModel)]="divisionSelected" placeholder="Select cost center name"  formControlName="productType">  -->
                      <!-- (selectionChange)="getCardDetails($event)"              -->
                      <!-- [(ngModel)]="selectedValue" -->
                      <!-- <mat-option *ngFor = "let type of products;" [value]="type.value" >
                        {{type.viewValue}}
                      </mat-option>
                    </mat-select> -->


                    <mat-select placeholder="PLEASE SELECT ONE" [(ngModel)]="frstDivSelection"  formControlName="productType"
                    (selectionChange)="selectDept(frstDivSelection)" >
                    <mat-option  *ngFor="let div of divArray" [value]="div.nameToDisplayOnDropdown">
                        {{div.nameToDisplayOnDropdown}}
                    </mat-option>
                </mat-select>

                    <mat-error *ngIf="submitted && eVoucherForm.controls.productType.hasError('required')" class="mt5">
                      Please Select One
                  </mat-error>
                  
                  </mat-form-field>
            </div>
        </div>
         <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px" 
         *ngIf="isShowDepartment">
          <div fxFlex="21%" fxFlex.lt-md="35%" fxFlex.md="35%" fxLayoutAlign="start center">
            <span class="date-title">DEPARTMENT:
              </span>
         </div>
            <div fxFlex="91%" fxFlex.lt-md="65%" fxFlex.md="75%">
                <mat-form-field appearance="outline" class="inp">    
                  <mat-select [(ngModel)]="defaultDept" name="defaultDept"  [ngModelOptions]="{standalone:true}">
                      <mat-option *ngFor="let dept of deptArray" [value]="dept.nameToDisplayOnDropdown">
                          {{dept?.nameToDisplayOnDropdown}}
                      </mat-option>
                  </mat-select>
                  </mat-form-field>
            </div>   
         </div>
          
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px">
          <div fxFlex="21%" fxFlex.lt-md="35%" fxFlex.md="25%" fxLayoutAlign="start center">
            <span class="date-title">QUANTITY REQUIRED<span class="asterisk"><sup>*</sup></span>:</span>
        </div>
        
        <div fxFlex="91%" fxFlex.lt-md="65%" fxFlex.md="75%">
           
                <mat-form-field appearance="outline" class="inp"> 
                    <input matInput type="text" placeholder="Quantity Required" maxlength="5" pattern="[0-9\-]+"  autocomplete="off" formControlName="quantityReq" onkeypress="return event.charCode >= 48 && event.charCode <= 57" [(ngModel)]="data.quantity"  >
                    <mat-error *ngIf="submitted && eVoucherForm.controls.quantityReq.hasError('required')" class="mt5">
                        Please Fill In Quantity Required
                    </mat-error>
                    <mat-error *ngIf="submitted && eVoucherForm.controls.quantityReq.hasError('pattern')" class="mt5">
                      Please Fill In Numbers 
                  </mat-error>
                  <mat-error *ngIf="submitted && eVoucherForm.controls.quantityReq.hasError('min')" class="mt5">
                    Minimum Value Should be 50
                </mat-error>
                    <mat-hint>min. 50 pcs and in multiple of 50</mat-hint>
                </mat-form-field>
            </div>
         
        </div>
      
        <!-- <div fxFlex  class="pt5" fxLayoutAlign="start start" fxLayout="row" fxLayoutGap="2%" class="mt10">
          <div  *ngIf="this.cdgService.eVoucherDetails.mode !== 'edit'"  fxFlex.gt-md="15%">
            <button
            mat-raised-button class="reset-btn" 
              (click)="onSubmit()">
              SUBMIT
            </button>
          </div>
         
          <div  fxFlex fxFlex.gt-md="20%">
            <button
            mat-raised-button class="reset-btn" 
              (click)="reset()">
              RESET
            </button>
          </div>
        </div> -->
        <div fxFlex class="mt20" fxLayoutGap="20px" fxLayoutAlign="start start">
          <button mat-raised-button class="reset-btn"
          (click)="reset()">RESET</button>
          <button mat-raised-button class="submit-btn"
          (click)="onSubmit()">SUBMIT</button>
      </div>
        </div>
    </form>
     </div>
 </div>







