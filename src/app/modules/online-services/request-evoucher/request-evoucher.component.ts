import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NotificationService } from '../../../shared/services/notification.service';
import { Router } from '@angular/router';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';

@Component({
  selector: 'app-request-evoucher',
  templateUrl: './request-evoucher.component.html',
  styleUrls: ['./request-evoucher.component.scss']
})
export class RequestEvoucherComponent implements OnInit {
  selectedValue: any;
  isDivision: boolean = false;
  addMode: boolean = false;
  divArray: any[] = [];
  deptArray: any[] = [];
  isShowDepartment: boolean = false;
  frstDivSelection: any;
  defaultDept: any;
  fetchDept: any[] = [];
  data = {
    quantity:"",
  }
  // products = [
  //   {value:'corporate',viewValue:'CORPORATE'},
  //   {value:'cabincrew',viewValue:'CABIN CREW (1003)'},
  //   {value:'corporateoffice',viewValue:'CORPORATE OFFICE(1005)'},
  //   {value:'csm1',viewValue:'CSM1(1004)'},

  // ]
  // division = [
  //   {viewValue:'DIVISION 1'},
  //   {viewValue:'DIVISION 12'}
  // ]
  divisionSelected: any;

  isCardNoShow: boolean = false;
  submitted = false;
  eVoucherForm:FormGroup
  divisions: never[];
  accountnumber: any;
  value: any;
  quan: number;
  division: any;
  dept: any;
  department: any;
  constructor(public apiService: ApiServiceService, public localStorage: LocalStorageService, private formBuilder: FormBuilder, private notifyService: NotificationService,
    private router: Router, public cdgService: CdgSharedService, private fb:FormBuilder) {
     this.eVoucherForm = this.fb.group({
        productType: ['', Validators.required],
        division: [''],
        quantityReq: ['', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(50)]]
      });
    }

  ngOnInit(): void {
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }
    }
    if (this.cdgService.eVoucherDetails.mode == 'edit') {
      console.log(this.cdgService.eVoucherDetails)
      if (this.cdgService.eVoucherDetails.division) {
        this.isShowDepartment = true
      }
      else {
        this.isShowDepartment = false
      }
      this.setValue();
      this.showCard(this.cdgService.eVoucherDetails.productType);
    }
    if (this.cdgService.eVoucherDetails.mode == 'confirm') {
      this.eVoucherForm.reset();
    }
    this.getDivisionData()
  }

  getCardDetails(selected: any) {
    if (selected.value === 'cabincrew') {
      this.isCardNoShow = true;
    }
    else {
      this.isCardNoShow = false;
    }
  }

  showCard(productValue: any) {

    if (productValue === 'cabincrew') {
      this.isCardNoShow = true;
    }
    else {
      this.isCardNoShow = false;
    }
  }


  keyPress() {


    if (this.data.quantity) {
      if (Number(this.data.quantity) % 50 !== 0) {
        this.data.quantity = (Math.round(Number(this.data.quantity) / 50) * 50).toString();
        this.notifyService.showInfo('The Quantity is rounded to the nearest multiple 50', 'Request E-voucher')
      }

    }
  }
  setValue() {
    // this.value=this.cdgService.eVoucherDetails.productType;
    // this.division = this.value;
    // console.log(this.division)
    // this.frstDivSelection = this.cdgService.eVoucherDetails.productType;
    // this.dept = this.cdgService.eVoucherDetails.division;
    // this.department = JSON.parse(this.dept);
    // console.log(this.department);
    // this.defaultDept = this.cdgService.eVoucherDetails.productType;
    this.data.quantity = this.cdgService.eVoucherDetails.quantityReq;
    // if(this.cdgService.eVoucherDetails.division){
    //   this.eVoucherForm.get('division')?.setValue(this.cdgService.eVoucherDetails.division)
    // }
    // this.eVoucherForm.get('productType')?.setValue(this.value.nameToDisplayOnDropdown)
    // this.eVoucherForm.get('quantityReq')?.setValue(this.cdgService.eVoucherDetails.quantityReq)
  }
  getDivisionData() {
    this.divisions = [];
    let obj;
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPADMIN) {
      obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "accountNo": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no
      }
    }
    else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_MASTERUSER) {
      obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "accountNo": this.accountnumber
      }
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + '/cabcharge/onlineapp/divDeptDropdownEvoucher', obj).subscribe
      ((response: any) => {
        console.log(response)
        if (response.status === 200) {
          let data = response.body;
          console.log(data)
          this.divisions = [];

          if (data.corpAccountDto == null) {
            if (data.divAccountList.length > 1) {
              var obj = {
                // "deptList": [],
                // "divAccount": {
                "accountCategory": "",
                "accountCode": "",
                "accountName": "ALL",
                "accountNo": '',
                "nameToDisplayOnDropdown": "ALL"
                // }

              }
              // this.divArray.push(obj)
              console.log(this.divArray);

              // data.divAccountList.forEach((element: any) => {
              //   console.log(element);
              //   this.divArray.push(element.divAccount);
              //   console.log(this.divArray);
              //    this.frstDivSelection = this.divArray[0];

              // });

              // console.log(this.divArray)
            }
          }
          if (data.corpAccountDto) {
            if (Object.keys(data.corpAccountDto).length > 0) {
              let obj = {
                accountCategory: data.corpAccountDto.accountCategory,
                accountCode: data.corpAccountDto.accountCode,
                accountName: data.corpAccountDto.accountName,
                accountNo: data.corpAccountDto.accountNo,
                nameToDisplayOnDropdown: data.corpAccountDto.nameToDisplayOnDropdown
              }
              // this.divArray.push(obj);

            }

          }

          if (data.divAccountList) {
            if (data.divAccountList.length > 0) {
              var obj = {
                // "deptList": [],
                // "divAccount": {
                "accountCategory": "",
                "accountCode": "",
                "accountName": "ALL",
                "accountNo": '',
                "nameToDisplayOnDropdown": "CORPORATE"
                //  }

              }
              this.divArray.push(obj)
              console.log("dataa",data.divAccountList)
              data.divAccountList.forEach((element: any) => {

                this.fetchDept.push(element);
                this.divArray.push(element.divAccount);
                // this.frstDivSelection = this.divArray[0];
                // console.log(this.frstDivSelection);
                if (this.cdgService.eVoucherDetails.mode === 'edit') {
                  this.frstDivSelection = this.cdgService.eVoucherDetails.productType;
                }
                if (element.deptList.length > 0) {
                  if (this.cdgService.eVoucherDetails.mode !== 'edit') {
                    this.selectDept(this.divArray[0].nameToDisplayOnDropdown);
                  }
                  else {
                    this.selectDept(this.cdgService.eVoucherDetails.productType);

                  }
                }
                console.log(element);
              });
              console.log(this.divArray);

            }
          }

        }
        // console.log(this.divisions)
      })
  }
  selectDept(value: any) {
    console.log(value);
    this.isShowDepartment = false;
    this.defaultDept = ""
    // this.isCabinCrew = false;
    console.log(this.divArray)
    console.log(value);
    // if (value.nameToDisplayOnDropdown === "-CORPORATE INVOICE-") {
    //   this.isCorpInvoice = true;
    // }
    if (value !== undefined) {
      // value.nameToDisplayOnDropdown !== "ALL" && value.nameToDisplayOnDropdown !== "-CORPORATE INVOICE-" && 

      if (value !== "CORPORATE") {
        console.log(this.fetchDept,value)
        const obj = this.fetchDept.find(x => x.divAccount.nameToDisplayOnDropdown === value)
        console.log(obj)
        this.deptArray = [];

        if (obj) {

          if (obj.deptList.length > 0) {
            this.isShowDepartment = true;
            // this.isCabinCrew = true;
            // this.deptArray.push({
            //   accountCategory: "",
            //   accountCode: "",
            //   accountName: "",
            //   accountNo: "",
            //   nameToDisplayOnDropdown: "-DIVISION-"
            // })
            obj.deptList.forEach((val: any) => {
              this.deptArray.push(val);
            });
            this.isShowDepartment = true;
            if (this.cdgService.eVoucherDetails.mode !== 'edit') {
              this.defaultDept = this.deptArray[0].nameToDisplayOnDropdown;
            }
            else {
              this.defaultDept = this.cdgService.eVoucherDetails.division ? this.cdgService.eVoucherDetails.division : this.deptArray[0].nameToDisplayOnDropdown;
            }
            console.log("depttttt", this.defaultDept)
          }
        }
        console.log(obj);
      }
    }
  }
  onSubmit() {
    this.submitted = true;
      if (this.eVoucherForm.invalid) {
          return;
      }
     else{
    this.keyPress();
    this.cdgService.eVoucherDetails.productType = this.frstDivSelection;
    // this.eVoucherForm.controls['productType'].value
    this.cdgService.eVoucherDetails.division = this.defaultDept;
    // this.eVoucherForm.controls['division'].value
    this.cdgService.eVoucherDetails.quantityReq = this.data.quantity.toString();
    this.cdgService.eVoucherDetails.prodArr = this.divArray;
    this.cdgService.eVoucherDetails.divArr = this.deptArray;
    this.cdgService.eVoucherDetails.mode = 'add';
    if (this.data.quantity) {
      this.router.navigate(['layout/confirmRequestEvoucher']);
       }
    }
  }
  onUpdate() {
    this.cdgService.eVoucherDetails.productType = this.eVoucherForm.controls['productType'].value;
    this.cdgService.eVoucherDetails.division = this.eVoucherForm.controls['division'].value;
    this.cdgService.eVoucherDetails.quantityReq = this.eVoucherForm.controls['quantityReq'].value;
    this.cdgService.eVoucherDetails.mode = 'edit';
    this.router.navigate(['layout/confirmRequestEvoucher']);
  }
  reset() {
    this.frstDivSelection = "";
    this.defaultDept = "";
    this.eVoucherForm.reset();
    this.isShowDepartment=false;
  }

}






