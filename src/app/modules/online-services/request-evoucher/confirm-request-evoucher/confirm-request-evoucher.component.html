<div fxFlex fxLayout="column" class="full-height"> 
    <!-- <div fxLayout="row" class="mt50 ml20">
      <div fxFlex="5%">
        <img src="/assets/images/recent transactions.svg" alt="" width="40px">
    </div>
      <div class="title">CONFIRM REQUEST eVOUCHER</div>
     </div> -->
     <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
      <div fxFlex="5%">
        <img src="/assets/images/recent transactions.svg" alt="" width="40px">
      </div>
      <div fxFlex="95%" fxLayoutAlign="start center">
          <span class="header pb5">CONFIRM REQUEST eVOUCHER</span>
      </div>
      </div>
     <br>
     
      <form [formGroup]="confirmEVoucherForm" fxFlex="100%">
        <div fxLayout="column" fxLayoutGap="20px" class="custom-padding-left">
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px">
          <div fxFlex="21%" fxFlex.lt-md="35%" fxFlex.md="35%" fxLayoutAlign="start center">
            <span class="date-title">DIVISION<span class="asterisk"><sup>*</sup></span>:</span>
        </div>
        <div fxFlex="91%" fxFlex.lt-md="65%" fxFlex.md="75%">
        <!-- <div fxLayout="row" fxLayout.xs="column">
            <div fxFlex="15%">
                <p class="cd">COST CENTRE NAME/CODE:</p>
            </div>
            <div fxFlex="40%"> -->
                <span class="date-title">{{this.vaue}}</span>
            </div>
         
        </div>
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px" *ngIf="this.divsion">
          <!-- *ngIf="(this.cdgService.eVoucherDetails.division != '') && (this.cdgService.eVoucherDetails.productType !== 'CORPORATE OFFICE(1005)') && (this.cdgService.eVoucherDetails.productType !== 'CORPORATE') && (this.cdgService.eVoucherDetails.productType !== 'CSMI(1004)')"  -->
            
              <div fxFlex="21%" fxFlex.lt-md="35%" fxFlex.md="35%" fxLayoutAlign="start center">
                <span class="date-title">DEPARTMENT:</span>
            </div>
            <div fxFlex="91%" fxFlex.lt-md="65%" fxFlex.md="75%">
                <span class="date-title">{{this.divsion}}</span>
            </div>
            
        </div>
        <div fxLayout="column">
        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" fxLayoutGap.md="10px">
          <div fxFlex="21%" fxFlex.lt-md="35%" fxFlex.md="35%" fxLayoutAlign="start start" >
            <span class="date-title">QUANTITY REQUIRED<span class="asterisk"><sup>*</sup></span>:</span>
        </div>
        <div fxFlex="91%" fxFlex.lt-md="65%" fxFlex.md="75%" >
                <span class="date-title">{{this.cdgService.eVoucherDetails.quantityReq}}</span>
            </div>
        </div>
        <mat-hint class="fs">Please wait for the acknowledgement to ensure we recive your submission . If you do not see any acknowledgement <br/>message, Please resubmit again.</mat-hint>
        </div>
      
        <div fxFlex fxLayout="row" fxLayout.xs="column" fxLayoutGap="30px" fxLayoutGap.xs="10px">
          <button mat-raised-button class="reset-btn" (click)="onCancel()">CANCEL</button>
          <button mat-raised-button class="edit-btn" (click)="onEdit()">EDIT</button>
          <button mat-raised-button class="update-btn"  (click)="onConfirm()">CONFIRM</button>
        </div>
          <!-- <div  fxLayout="row" fxLayoutGap="8%" fxLayoutGap.xs="5%">
              <div fxFlex>
                <button
                class="confirm-btn"
                (click)="onConfirm()">
                CONFIRM
              </button>
              </div>
              <div fxFlex>-->
            
             
      </div>
    
    </form>
   
 </div>







