@import '@angular/material/theming';
@import '../../../../../styles/colors.scss';
@import '../../../../../styles/sizing.scss';
@import '../../../../../styles/main.scss';

.title{
    line-height: 14.2px;
    color: $lightGrey;
}
.header{
  color: $lightGrey !important;
  font-size: 28px;
  font-weight:$cdgsz-font-weight-normal ;
}
.full-height {
    height: 100vh !important; 
  }
  .mt50{
      margin-top: 50px;
  }
//   .sub-btn{
//     background:$hue; //#DA6C2A;
//     color:$white;
//     font-size:14px;
//     height: 36px;
//     width:120px;
//     border: none;
// }
.cd{
    font-size: 14px;
    font-weight: $cdgsz-font-weight-normal;
}
.edit-btn{
    background-color: $cdgc-hue !important;
}
.confirm-btn{
    background-color: #10A2F5 !important;
}
.cancel-btn{
    background-color:$cdgc-warn !important;   
}
.cancel-btn , .confirm-btn, .edit-btn{
    color:$white;
    font-size:14px;
    height: 36px;
    border:none;
    cursor: pointer;
}
//desktops
@media only screen and (min-width:992px){
    .title{
      font-size: 24px;
    } 
    .cancel-btn, .confirm-btn, .edit-btn{
        width:170px !important;
    }
    
  }
  .ml20{
    margin-left: 20px;
  }
  .fs{
    font-size: 10px !important;
  }
  //larger screens than desktop
  @media only screen and (min-width:1200px){
    .title{
      font-size: 24px;
    }
    .cancel-btn, .confirm-btn, .edit-btn{
        width:170px !important;
    }
  }
  //for mobile
  @media only screen and (max-width:768px){
    .title{
      font-size:18px;
    }
    .cancel-btn, .confirm-btn, .edit-btn{
        width:100px !important;
    }
  }


  .edit-btn{
    background-color: #519585 !important;
    width: 170px !important;
    border-radius: 10px !important;
    cursor: pointer;

    // font-size: 12px !important;
}
.update-btn{
    width:170px;
    background-color:$cdgc-bg-hue;
    color: $cdgc-font-accent;
    border-radius: 10px !important;
    cursor: pointer;

}
.reset-btn{
    width:170px;
    border-radius: 10px !important;  
    cursor: pointer;

}