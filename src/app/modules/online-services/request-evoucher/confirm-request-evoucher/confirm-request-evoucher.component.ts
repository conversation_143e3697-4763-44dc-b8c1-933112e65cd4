import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NotificationService } from '../../../../shared/services/notification.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { Router } from '@angular/router';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';

@Component({
  selector: 'app-confirm-request-evoucher',
  templateUrl: './confirm-request-evoucher.component.html',
  styleUrls: ['./confirm-request-evoucher.component.scss']
})
export class ConfirmRequestEvoucherComponent implements OnInit {
  mode:any;
  // isDivision:boolean = false;
 
  submitted = false;
  confirmEVoucherForm:FormGroup
  prducttype: any;
  vaue: any;
  accountcode: any;
  customernumber: any;
  customername: any;
  divi: any;
  divsion: any;
  customerName: any;
  constructor(private formBuilder: FormBuilder,private notifyService : NotificationService,
    private router:Router,public cdgService:CdgSharedService, private apiService: ApiServiceService,public localStorage: LocalStorageService) { 
      this.confirmEVoucherForm = this.formBuilder.group({
        productType: ['', Validators.required],
        division: [''],
        quantityReq: ['', Validators.required]
      });
    }

  ngOnInit(): void {
    if(!this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails){
    if(this.localStorage.localStorageGet('customerviewvalue')===null){
      this.customername =this.localStorage.localStorageGet('viewvalueonload').substr(9,50);
    }
    else{
      this.customername = this.localStorage.localStorageGet('customerviewvalue').substr(9,50);
    }
  }
  else{
    this.customername =this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].main_account_name;
  }
  // if (this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails) {
  //   this.customernumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number;
  // }
  // else {
  //   console.log(this.customernumber = this.localStorage.localStorageGet('customerviewvalue'));
   
  //   if(this.localStorage.localStorageGet('customerviewvalue')===null){
  //     this.customernumber =this.localStorage.localStorageGet('viewvalueonload').substr(0, 6);
  //   }
  //   else{
  //     this.customernumber = this.localStorage.localStorageGet('customerviewvalue').substr(0, 6);
  //   }
  if (this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails) {
    this.customernumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number;
    this.customerName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name

  }
  else {
    console.log(this.localStorage.localStorageGet('customerviewvalue'), this.localStorage.localStorageGet('viewvalueonload'));

    if (this.localStorage.localStorageGet('customerviewvalue') === null) {
      this.customernumber = this.localStorage.localStorageGet('viewvalueonload').substr(0, 6);
      this.customerName = this.localStorage.localStorageGet('viewvalueonload').substr(9)
    }
    else {
      this.customernumber = this.localStorage.localStorageGet('customerviewvalue').substr(0, 6);
      this.customerName = this.localStorage.localStorageGet('customerviewvalue').substr(9)
    }
  }
  
    this.setValue();
    this.prducttype=this.cdgService.eVoucherDetails.productType; 
    // this.dept = this.cdgService.eVoucherDetails.division;
    // console.log(this.dept);
    this.vaue= this.prducttype;
    console.log(this.cdgService.eVoucherDetails)
    // this.department = this.dept.nameToDisplayOnDropdown;
    // console.log(this.department);
    this.accountcode = this.prducttype.accountCode;
    this.divi= this.cdgService.eVoucherDetails.division;
    this.divsion=this.divi;
   
  }
  setValue() {
    this.confirmEVoucherForm.get('productType')?.setValue(this.cdgService.eVoucherDetails.productType)
   // this.confirmEVoucherForm.get('division')?.setValue(this.department)
    this.confirmEVoucherForm.get('quantityReq')?.setValue(this.cdgService.eVoucherDetails.quantityReq)
  }
 

  get formControls() {
    return this.confirmEVoucherForm.controls;
  }
  onConfirm(){
    this.cdgService.eVoucherDetails.mode = 'confirm';
    const companyName=this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails ? this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name : this.customerName
    let obj={
      "accountName":companyName+ "(" +"ID:"+this.customernumber +")",
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "quantityNo":this.cdgService.eVoucherDetails.quantityReq,
      "divCode":this.vaue,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.REQUESTEVOUCHER, obj).subscribe
    ((response: any) => {
      if(response.status === 200){
        if(response.body.successMessage!==null){
      this.notifyService.showSuccess(response.body.successMessage,'Thank You');
      this.router.navigate(['layout/requestevoucher']);
        }
        else{
          this.notifyService.showError(response.body.errorMessage,'Error');  
        }
      }
    });
   
  }
  onEdit(){
    this.cdgService.eVoucherDetails.mode = 'edit';
    this.cdgService.eVoucherDetails.productType = this.confirmEVoucherForm.controls['productType'].value;

    this.router.navigate(['layout/requestevoucher']);
  }
  onCancel(){
    this.router.navigate(['layout/requestevoucher']);
  }
}






