import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { RoleConstants } from 'src/assets/url-constants/url-constants';
import { NotificationService } from '../../../shared/services/notification.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';

@Component({
  selector: 'app-report-faulty-card',
  templateUrl: './report-faulty-card.component.html',
  styleUrls: ['./report-faulty-card.component.scss']
})
export class ReportFaultyCardComponent implements OnInit {
  // faultyCardForm: FormGroup;
  submitted:boolean = false;
  isDisable: boolean = false;
  products:any=[];
  cards:any[]=[];
  faultyCardForm:FormGroup;
  isDisableRadioButton: boolean = false;
  accountnumber: any;
  requestvalue: any;
  firstdigit: string;
  displaycard: string;
  cardsList: any;
  showDisplayErr: boolean=false;
  hideDis: boolean=false;
  constructor(public localStorage: LocalStorageService,private apiService: ApiServiceService,private formBuilder: FormBuilder,private notifyService : NotificationService, public cdgService: CdgSharedService) { 
    this.faultyCardForm= this.formBuilder.group({
      productType: ['', Validators.required],
      cardNo: ['', Validators.required],
      reqCancel: ['']
    });
  }

  ngOnInit(): void {
    this.submitted = false;
    // if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER) {
    //   this.products.push({ value: 'personalCard', viewValue: 'PERSONAL CARD'});
    //   this.faultyCardForm.get('productType')?.setValue('personalCard');
    //   this.faultyCardForm.get('cardNo')?.setValue('6100-1234-7890')
    //   this.isDisable = true;
    // }
    // else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
    //   this.faultyCardForm.get('productType')?.setValue('corporatecard');
    //   this.faultyCardForm.get('cardNo')?.setValue('6100-1234-7891')
    //   this.isDisable = true;
    // }
    // else if(this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUBAPPLICANT){
    //   this.products.splice(0,this.products.length);
    //   this.products.push({value: 'personalCard', viewValue: 'PERSONAL CARD'});
    //   this.products.push({value: 'priorityCard', viewValue: 'PRIORITY CARD'});
    // }
    if(this.localStorage.localStorageGet("customerNoSelected")!=null){
      this.accountnumber=this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if(this.accountnumber=this.localStorage.localStorageGet("custNo")!=null){
      this.accountnumber=this.localStorage.localStorageGet("custNo").view;
    }
    else {
      this.accountnumber= this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
    }
    }
let prodtypeobj={
  "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
  "role":  this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
  "accountNo":this.accountnumber
}
    
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_PRODUCTTYPE,prodtypeobj).subscribe((response: any) => {
      if (response.status === 200) {
        console.log(response)
        let opt = response.body.productType;
        opt.forEach((element: any) => {
          this.products.push(element);
          console.log(this.products);
        });
         }      
  });

  }
  get formControls() {
    return this.faultyCardForm.controls;
  }

  onSelect(item: any) {
    this.faultyCardForm.controls['cardNo'].reset();
    if (item.value === "OV") {
      this.isDisableRadioButton = true;
      this.faultyCardForm.controls.reqCancel.reset();
    }
    else{
      this.isDisableRadioButton = false; 
      this.faultyCardForm.get('reqCancel')?.setValue('No')
    }
    this.showDisplayErr=false
    this.hideDis=false;

    let obj={
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role":  this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "roleId":this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      "productType" :item.value,
      "accountNo":this.accountnumber,
      "masterAccountNo":Number(this.accountnumber)
    }
    if (item.value !== "OV") {
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_CARDLIST, obj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body.cardNo;
          this.cards = []; this.cardsList = [];
            this.firstdigit = String(opt[0]).substr(0, 10);
            this.displaycard = this.firstdigit.substr(0, 6) + "-" + this.firstdigit.substr(6, 4);
            opt.forEach((element: any) => {
              this.cards.push((String(element).substr(-6)));
              this.cardsList.push((String(element).substr(-6)));
            });
            if (response.body.cardNo.length > 0) {
              this.showDisplayErr = false;
            }
            else {
              this.showDisplayErr = true;
            }
            this.hideDis = false;
        }
      });
    } else {
      this.hideDis = true;
    }
}

  onSubmit(){
    this.submitted = true;
    if (this.faultyCardForm.invalid) {
        return;
    }
   else{
    this.submitted = false;
    if(this.faultyCardForm.value.reqCancel === 'Yes'){
      this.requestvalue="true";
    }
    else{
     this.requestvalue="false";
    }
    let obj={
     "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
     "role":  this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
     "cardNo" :this.faultyCardForm.value.productType !=="OV" ? this.firstdigit+this.faultyCardForm.value.cardNo:this.faultyCardForm.value.cardNo,
     "replacementOfCard":this.requestvalue,
     "productTypeId" :this.faultyCardForm.value.productType,
     "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
     "accountNo": this.accountnumber,
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.GET_FAULTYCARD,obj).subscribe((response: any) => {
      if (response.status === 200) {
        console.log(response)
        let opt = response.body;
        if(response.body.successMessage!== null){
        this.notifyService.showSuccess(response.body.successMessage,'Report faulty card');

        }
        else{
          this.notifyService.showError(response.body.errorMessage,'Report faulty card');
        }
        this.faultyCardForm.reset();
        this.displaycard = '';
        this.faultyCardForm.get('reqCancel')?.setValue('No')
        this.submitted = false;
         }      
  });
   }
  }
}





