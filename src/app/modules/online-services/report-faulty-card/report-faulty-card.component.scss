@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';

/* Change_by_navani (mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
// ::ng-deep .mat-mdc-radio-button.mat-accent.mat-mdc-radio-checked .mat-mdc-radio-outer-circle {
//   /* border-color: #ff4081; */
//   border-color: rgba(0, 0, 0, 0.54);
// }
// ::ng-deep .mat-mdc-radio-button.mat-accent .mat-mdc-radio-inner-circle, .mat-radio-button.mat-accent .mat-radio-ripple .mat-mdc-ripple-element:not(.mat-radio-persistent-ripple), .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-persistent-ripple, .mat-radio-button.mat-accent:active .mat-radio-persistent-ripple {
//   // background-color: #DA6C2A;
//   background-color: #000000;
// }
.ml20{
  margin-left: 20px;
}
.astriek{
  color:red;
}
.cardSize{
    // background: $cdgc-bg-mild;
    background: #ccebff;
    max-width: 500px;
    margin: 4px
}
.content-class{
    padding:10px;
}

.button-width{
   padding-top:5px;
}
.align-label{
  line-height: 50px;
}
 
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
:host ::ng-deep .mat-form-field-appearance-outline .mdc-notched-outline {
  display: none !important;
  }
/* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version. */
:host ::ng-deep .mat-mdc-select-arrow-wrapper{
      padding-top: 5px !important;
    }
:host ::ng-deep  .mat-mdc-select{
      width:100% !important;
    }
    .mt50{
      margin-top:50px;
    }
    .sub-btn{
        background:$hue !important; //#DA6C2A;
        color:$white;
        font-size:14px;
        border-radius: 5px;
        height: 36px;
        width:220px;
        border: none;
    }
      .full-height {
        height: 100vh !important;
        // flex: 1 1 auto !important;
      }
  // .report-card-container{
  //   margin:0 50px 0 50px;
  // }
  .inp{
    width: 260px !important;
}


/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
// :host ::ng-deep .mat-mdc-text-field-wrapper {
//   padding-bottom: 1.34375em !important;
// }
:host ::ng-deep .mat-mdc-form-field-infix{
  min-height: 10px !important;
}

:host ::ng-deep .mat-form-field-appearance-outline .mat-mdc-form-field-flex{
  border: 2px solid #dedede!important;
  background: #fff!important;
  width: 150%!important;
}

:host ::ng-deep .mat-mdc-select-arrow{
  height: 1px!important;
  width: 1px!important;
  margin-right: 15px!important;
  margin-top: 0px!important;
}