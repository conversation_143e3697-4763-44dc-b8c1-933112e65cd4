@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
// @media screen and (max-width: 960px) {
//     .report-card-container{
//       margin:0 50px 0 50px;
//     }
// }

//desktops
@media only screen and (min-width:992px){
  // .toggle-btn{
  //   width:268px;
  //   height:44px;
  // }
  .title{
    font-size: 24px;
  }
  
}
.ml20{
  margin-left: 20px;
}

.toggle-btn{
  padding: 5px 60px;
}
//larger screens than desktop
@media only screen and (min-width:1200px){
  // .toggle-btn{
  //   width:268px;
  //   height:44px;
  // }
  .title{
    font-size: 24px;
  }
}
//for mobile
@media only screen and (max-width:600px){
  .toggle-btn{
    padding: 5px 15px;
  }
  .title{
    font-size:18px;
  }
}
.full-height {
    height: 100vh !important;
    // flex: 1 1 auto !important;
  }
  .title{
    // font-weight: bold;
    
    line-height: 14.2px;
    color: #4d4d4d;
}
.mb40{
  margin-bottom:40px;
}
.icon_report{
  width:32px;
  height:32px;
  color:#4d4d4d;
  margin-top:2px
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
::ng-deep .mat-mdc-button-toggle-button {
  margin-top: 5px !important;
}


:host ::ng-deep .main-icon{
  color: $lightGrey !important;
  font-size: $cdgsz-font-size-xxl !important;
  padding-right: 15px !important;
  height: 40px !important;
  width: 40px !important;
}
// :host ::ng-deep .mat-mdc-form-field-infix{
//   min-height: 10px !important;
// }

.mat-button-toggle-appearance-standard {
  color: #b71d1d!important;
  border-radius: 5px!important;
  border: 1px solid #DA6C2A !important;
  margin-right: 25px;
}

// :host ::ng-deep .mat-button-toggle-appearance-standard .mat-button-toggle-label-content {
//   padding: 0px !important;
// }

.mat-button-toggle-appearance-standard.mat-button-toggle-checked {
  background-color: #DA6C2A;
  color: white !important;
}

// :host ::ng-deep .mat-button-toggle-appearance-standard {
//   color: #1a1919!important;
//   border-radius: 5px!important;
//   border: 1px solid #DA6C2A!important;
//   margin-right: 25px;
// }

//change 
@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
::ng-deep .main-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
    height: 40px !important;
    width: 40px !important;
}
.login-type{
    font-size: $cdgsz-font-size-prime !important;
    font-weight: $cdgsz-font-weight-normal !important;
    margin-bottom: 0px !important;
    // padding-top: 10px;
}
.toggle-div{
    padding: 5px 60px;
}
.toggle-icon{
    font-size: 35px !important;
}
// ::ng-deep .mat-button-toggle-group-appearance-standard .mat-button-toggle + .mat-button-toggle {
//     border-radius: 10px;
//     border: 1px solid $cdgc-border-hue;
// }
.header-div{
 background-color: $cdgc-bg-prime !important;
 color: $cdgc-font-accent !important;
 border-radius: 5px !important;
 padding: 6px 25px !important;
 font-size: 19px;
 font-weight: $cdgsz-font-weight-bold !important;
}
.cd{
    font-size: 19px;
    font-weight: $cdgsz-font-weight-normal;
}
.inp{
    width: 310px !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
   padding-bottom: 0px !important;
}
.error{
    padding-top: 15px;
}
@media screen and (max-width: 600px) {
    .toggle-div{
        padding: 5px 15px;
    }
 }

.mat-button-toggle-appearance-standard {
    color: #4d4d4d!important;
    border-radius: 5px!important;
    border: 1px solid #DA6C2A;
    margin-right: 20px;
}

.mat-button-toggle-checked {
    background-color: #DA6C2A !important;
    color: #fff !important;
}

