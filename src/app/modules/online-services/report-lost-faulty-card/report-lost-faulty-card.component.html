<!-- <div fxFlex fxLayout="column" class="full-height" fxLayoutAlign="center center">
    <div class="report-card-container" fxLayout="row">
        <div fxLayout="row" fxFlex=10% class="card-header" fxLayoutAlign="center center">
            <h3 class="heading">Report Lost/Faulty Card</h3>
          </div>
          <div  fxFlex fxLayout="row" fxFlex=10%  fxLayoutAlign="center center">
            <mat-button-toggle-group #toggleGroup = "matButtonToggleGroup" style="height:50px;">
                <mat-button-toggle value="lostCard">
                <p>Report Lost Card</p>
                </mat-button-toggle>
                <mat-button-toggle value="faultyCard">
                <p>Report Faulty Card</p> 
                </mat-button-toggle>
            </mat-button-toggle-group>
          </div>
    </div>
    <div *ngIf="toggleGroup.value == 'lostCard'" fxLayout="row"  fxLayoutAlign="center center">
        <app-report-lost-card></app-report-lost-card>
      </div>
        <div  fxFlex=80%  *ngIf="toggleGroup.value == 'faultyCard'" fxLayout="row"   
        fxLayoutAlign="center center">
            <app-report-faulty-card></app-report-faulty-card>
        </div>
</div> -->









<!-- <div fxFlex fxLayout="column" class="full-height">
      <div fxLayout="row" fxLayoutAlign="start start" >     
          <div fxFlex fxLayout="row" fxLayoutGap="5px" 
          class="mb40 mt10 ml20">
          <div>
            class="icon_report"
             <mat-icon class="material-icons-outlined main-icon">report</mat-icon>
         </div>
          <div class="title" fxLayoutAlign="start center">
            Report Lost/Faulty Card
          </div>
          </div>
        </div>

        

        <div fxFlex fxLayout="row" fxLayout.lt-md="row"  fxLayoutAlign="start start"  
        class="cursor-pointer" class="ml20">
            <mat-button-toggle-group #toggleGroup = "matButtonToggleGroup" style="border-color: #DA6C2A ;" value="lostCard">
                <mat-button-toggle value="lostCard" class="toggle-btn" >
                <p>Report Lost Card</p>
                </mat-button-toggle>
                <mat-button-toggle value="faultyCard"  class="toggle-btn" >
                <p>Report Faulty Card</p> 
                </mat-button-toggle>
            </mat-button-toggle-group>
        </div> 



        <div *ngIf="toggleGroup.value == 'lostCard'" >
          <app-report-lost-card></app-report-lost-card>
        </div>
          <div *ngIf="toggleGroup.value == 'faultyCard'">
              <app-report-faulty-card></app-report-faulty-card>
          </div>
  </div> -->


<div fxLayout="column" fxLayoutGap="50px">
  <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
    <div fxFlex="5%">
      <mat-icon class="material-icons-outlined main-icon">report</mat-icon>
    </div>
    <div fxFlex="95%" fxLayoutAlign="start center">
      <span class="header pb5">Report Lost/Faulty Card</span>
    </div>
  </div>


  <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
    <div fxFlex>
      <mat-button-toggle-group fxFlex fxLayoutAlign="start center" #toggleGroup="matButtonToggleGroup"
        value="lostCard">
        <mat-button-toggle value="lostCard">
          <div fxLayout="row" fxLayoutGap="15px" class="toggle-div">
            <p class="login-type">Report Lost Card</p>
          </div>

        </mat-button-toggle>
        <mat-button-toggle value="faultyCard" class="toggle-div">
          <div fxLayout="row" fxLayoutGap="15px">
            <p class="login-type">Report Faulty Card</p>
          </div>
        </mat-button-toggle>
      </mat-button-toggle-group>
    </div>
    <div *ngIf="toggleGroup.value == 'lostCard'">
      <app-report-lost-card></app-report-lost-card>
    </div>
    <div *ngIf="toggleGroup.value == 'faultyCard'">
      <app-report-faulty-card></app-report-faulty-card>
    </div>
  </div>
</div>