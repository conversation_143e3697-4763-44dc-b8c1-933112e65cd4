// import { Component, OnInit } from '@angular/core';
// import { Router } from '@angular/router';
// import { NgForm, FormGroup, FormControl, FormBuilder, FormArray, Validators } from '@angular/forms'
// import {ChangeDetectorRef,AfterViewInit} from '@angular/core';
// @Component({
//   selector: 'app-public-pages',
//   templateUrl: './public-pages.component.html',
//   styleUrls: ['./public-pages.component.scss']
// })
// export class PublicPagesComponent implements  AfterViewInit{
 
//   editRow:boolean = false;
//   uploadImage:boolean = false;
//   selectedFileImage:any;
//   newPage:boolean=false;
//   imageUrl:any;
  
//   firstOpenItemIndex = -1;
//   items = [
//     {id: 1,title:'Home',
//     content:'Type', 
//     pageType:'Landing Page',
//     image:'Cover image',
//     imageDesc:'svg',
//     header:'Header', 
//     headerDesc:'Lorem Ipsum Dolor',
//     text:'Text',
//     textDesc:'Lorem ipsum sjhgjhfghfg hfhgfgh ghfghfhg gjhghjg ghfhgf werteyt adf'
//   },
//     {id: 2, title:'About Us',
//     content:'Type',
//     pageType:'About Page',
//      image:'Cover image',
//      imageDesc:'svg',
//      header:'Header',
//      headerDesc:'Lorem Ipsum Dolor',
//      text:'Text',
//      textDesc:'Lorem ipsum sjhgjhfghfg hfhgfghfghfghfhg gjhghjg ghfhgf werteyt adf hjlk'
//     },
//     {id: 3,title:'Products',
//     content:'Type',
//     pageType:'Products Page',
//      image:'Cover image',
//      imageDesc:'svg',
//      header:'Header',
//      headerDesc:'Lorem Ipsum Dolor',
//      text:'Text',
//      textDesc:'Lorem ipsum sjhgjhfghfg hfhgfghfghfghfhg gjhghjg ghfhgf werteyt adf hjlk'
//     },
//     { id: 4,
//       title:'Contact Us',
//       content:'Type',
//       pageType:'Contacts Page',
//       image:'Cover image',
//       imageDesc:'svg',
//       header:'Header',
//       headerDesc:'Lorem Ipsum Dolor',
//       text:'Text',
//       textDesc:'Lorem ipsum sjhgjhfghfg hfhgfghfghfghfhg gjhghjg ghfhgf werteyt adf hjlk'},
//   ]
//   publicForm = new FormGroup({
//     formItems:new FormArray([])
//   });
//   formItems:FormArray = this.publicForm.get('formItems')as FormArray;
  
//     constructor(private router: Router, private fb: FormBuilder, private cdr:ChangeDetectorRef) {
//       this.pages();
//       }
//       ngAfterViewInit(){
//        this.cdr.detectChanges();
//      }
  
  
  
//   ngOnInit(): void {
//   }
//   setOpened(itemIndex:any){
//     this.firstOpenItemIndex = itemIndex;
//   }
//   setClosed(itemIndex:any){
//     if(this.firstOpenItemIndex === itemIndex){
//       this.firstOpenItemIndex = -1;
//     }
//   }
//   onEdit(row:any){
   
//     console.log(row);
//     row.value.isEdit = true;
   
//   }
//   onTextChange(id:any){
//     var newItem = this.items.find((item:any) => item.id === id)
//     console.log(newItem);
//     this.editRow  = false;
//   }
//   onBackButtonClick(){
//     this.router.navigate(['layout/dashboard']);
//   }
//   onChangeImage(id:any){
//  var newImage = this.items.find((item:any)=>item.id === id);
//  this.uploadImage = true;
//   }
//   onChoosingImage(event:any){
// const reader = new FileReader();
// if(event.target.files && event.target.files.length){
//   const [file] = event.target.files;
//   reader.readAsDataURL(file);
//   reader.onload = ()=>{
//     this.imageUrl = reader.result as string;
//     console.log(this.imageUrl);
//   }
// }
//   }
//   goToLanding(){
//     this.router.navigate(['']);
//   }
  
//   pages(){
//     if(this.items.length > 0 ){
//       this.items.forEach(obj => {
//         const frmgrp = this.updateFormValues(obj);
//         this.formItems.push(frmgrp);
//       });
//     }
//   }
//   addNewPage():void{
//     this.formItems.push(this.createNewPage())
//   }
//   createNewPage():FormGroup{
//   return this.fb.group({
//     title: '',
//     content: '',
//     pageType:'',
//     image:'',
//     header:'',
//     headerDesc:'',
//     text:'',
//     textDesc:'',
//     isEdit:false,
//     isNew:true
// })
// }
//    updateFormValues(obj:any){
//     return this.fb.group({
//       title: obj?.title,
//       content: obj?.content,
//       pageType:obj?.pageType,
//       image:obj?.image,
//       header:obj?.header,
//       headerDesc:obj?.headerDesc,
//       text:obj?.text,
//       textDesc:obj?.textDesc,
//       isEdit:false,
//       isNew:true
//   })
//    }
  

// }



































import { Component, OnInit } from '@angular/core';
import { NgForm, FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms'
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { Router } from '@angular/router';


@Component({
  selector: 'app-public-pages',
  templateUrl: './public-pages.component.html',
  styleUrls: ['./public-pages.component.scss']
})
export class PublicPagesComponent implements OnInit {
  editRow:boolean = false;
  uploadImage:boolean = false;
  selectedFileImage:any;
  newPage:boolean=false;
  imageUrl:any;
  firstOpenItemIndex = -1;

  publicPages: any[] = [];

    items = [
    {id: 1,title:'Home',    
    pageType:'Landing Page',   
    imageDesc:'svg',     
    headerDesc:'Lorem Ipsum Dolor',   
    textDesc:'Lorem ipsum sjhgjhfghfg hfhgfgh ghfghfhg gjhghjg ghfhgf werteyt adf',
    isEdit: false,
    isChangeDesc:false
    // content:'Type',
    // text:'Text',
    // header:'Header',
    // image:'Cover image',
  },
    {id: 2, title:'About Us',   
    pageType:'About Page',   
     imageDesc:'svg',     
     headerDesc:'Lorem Ipsum Dolor',    
     textDesc:'Lorem ipsum sjhgjhfghfg hfhgfghfghfghfhg gjhghjg ghfhgf werteyt adf hjlk',
     isEdit: false,
     isChangeDesc:false
    //  content:'Type',
    // image:'Cover image',
    // header:'Header',
    // text:'Text',
    },
    {id: 3,title:'Products',  
    pageType:'Products Page',   
     imageDesc:'svg',  
     headerDesc:'Lorem Ipsum Dolor', 
     textDesc:'Lorem ipsum sjhgjhfghfg hfhgfghfghfghfhg gjhghjg ghfhgf werteyt adf hjlk',
     isEdit: false,
     isChangeDesc:false
    //  content:'Type',
    // text:'Text',
    // header:'Header',
    // image:'Cover image',
    },
    { id: 4,
      title:'Contact Us',
      pageType:'Contacts Page',
      imageDesc:'svg',
      headerDesc:'Lorem Ipsum Dolor',
      textDesc:'Lorem ipsum sjhgjhfghfg hfhgfghfghfghfhg gjhghjg ghfhgf werteyt adf hjlk',
      isEdit: false,
      isChangeDesc:false
      // content:'Type',
      // image:'Cover image',
      // header:'Header',
      // text:'Text',
      },
  ]
  isEdit: boolean = false
  isNewPage: boolean=false;
  isChangeDesc:boolean = false;

  constructor(private fb: FormBuilder, public cdgService: CdgSharedService, private router: Router, private notifyService: NotificationService) { }

  ngOnInit(): void {
    
    this.items.forEach((data:any)=>{
      let obj = {
        title:data.title,
        pageType:data.pageType,
        imageDesc:data.imageDesc,
        headerDesc:data.headerDesc,
        textDesc:data.textDesc,
        isEdit: false
      // content:data.content,
      // image:data.image,
      // header:data.header,
      // text:data.text,
      }
      this.publicPages.push(obj)
    })
  }
 

    setOpened(itemIndex:any){
    this.firstOpenItemIndex = itemIndex;
    }
    setClosed(itemIndex:any){
    if(this.firstOpenItemIndex === itemIndex){
      this.firstOpenItemIndex = -1;
    }
  }
  // onEdit(row:any){
   
  //   console.log(row);
  //   row.value.isEdit = true;
   
  // }
  onTextChange(id:any){
    var newItem = this.items.find((item:any) => item.id === id)
    console.log(newItem);
    this.editRow  = false;
  }
  onBackButtonClick(){
    this.router.navigate(['layout/dashboard']);
  }
  onChangeImage(id:any){
 var newImage = this.items.find((item:any)=>item.id === id);
 this.uploadImage = true;
  }
  onChoosingImage(event:any){
const reader = new FileReader();
if(event.target.files && event.target.files.length){
  const [file] = event.target.files;
  reader.readAsDataURL(file);
  reader.onload = ()=>{
    this.imageUrl = reader.result as string;
    console.log(this.imageUrl);
  }
}
  }
  goToLanding(){
    this.router.navigate(['']);
  }

  addNewPage() {
    this.publicPages.push({
      title:"",
      content:"",
      pageType:"",
      image:"",
      imageDesc:"",
      header:"",
      headerDesc:"",
      text:"",
      textDesc:"",
      isEdit:true
    })
this.isNewPage=true;
  }
  deletePages(index: number) {
    this.publicPages.splice(index, 1)

  }
  onEdit(index: number) {
    console.log("edit")
    this.publicPages.forEach((element, i) => {
      if (i === index) {
        element.isEdit = true;
        // element.isChangeDesc = true;
      }
        else {
          element.isEdit = false;
         
        }
      
    });

  }
  onSubmit() {
      this.notifyService.showSuccess('Details Added Successfully', 'Success')
  }
}
