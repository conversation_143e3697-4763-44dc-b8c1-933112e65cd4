<div fxFlex fxLayout="column" class="full-height">
  <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="center center">
    <div fxFlex fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="start center" fxFlex.xs=61% fxFlex.md=61%>
      <div class="cursor-pointer">
        <mat-icon (click)="onBackButtonClick()">arrow_back</mat-icon>
      </div>
      <div>
        <div class="title">Public Pages</div>
      </div>
    </div>
    <div fxFlex fxLayout="row" fxLayout.lt-md="row" fxLayoutAlign="end center">
      <button mat-raised-button class="add-btn" (click)="addNewPage()">+ New Page</button>
    </div>
  </div><br>
  <div fxLayout="row" fxFlex=5% fxFlex.sm=10% fxFlex.md=10% class="ml5 mr5">
    <p class="sub-title">Manage and edit cabcharge Public pages</p>
  </div>
  <div fxLayout="row" fxLayoutAlign="start center">
    <div fxFlex=100%>
      <form #f="ngForm" name="publicForm">
        <mat-accordion>
          <div *ngFor="let control of publicPages; let i= index">
            <!-- {{control[i].title}} -->
            <mat-expansion-panel (opened)="setOpened(i)" (closed)="setClosed(i)">
               <mat-expansion-panel-header>
                 <mat-panel-title >
                   <div *ngIf="!publicPages[i].isEdit">{{publicPages[i].title}}
                    
                    &nbsp;
                    <mat-icon (click)="onEdit(i)" style="cursor: arrow;color: white;">mode_edit</mat-icon>
                    <!-- (click)="onEdit(i)" -->
                    </div>
                   <mat-form-field  *ngIf="publicPages[i].isEdit">
                    <input matInput name="title{{i}}" [(ngModel)]="publicPages[i].title"  #title="ngModel" required> 
                   </mat-form-field>   
                 </mat-panel-title>
               </mat-expansion-panel-header>
            <div>

            <div fxLayout="row" fxLayoutAlign="start center">
              <div fxFlex="50%" fxLayoutAlign="start center">
                <!-- <mat-form-field > -->
                  <!-- <input matInput name="content{{i}}" [(ngModel)]="publicPages[i].content"  #content="ngModel" required>  -->
                  <!-- </mat-form-field> -->
                  <span>Type</span>
              </div>
              <div fxFlex="50%" fxLayoutAlign="end end" class="cursor-pointer">
                <!-- <mat-form-field > -->
                  <div *ngIf="!publicPages[i].isEdit">
                    {{publicPages[i].pageType}}
                  </div>
                  <div *ngIf="publicPages[i].isEdit">
                    <mat-form-field appearance="outline">
                      <input matInput name="pageType{{i}}" [(ngModel)]="publicPages[i].pageType"  #pageType="ngModel" required> 
                    </mat-form-field>
                    
                  </div>
                  <!-- </mat-form-field>  -->
                <mat-icon (click)="goToLanding()">
                  keyboard_arrow_right
                </mat-icon>
              </div>
            </div>
            <mat-divider></mat-divider>
            <div fxLayout="row" fxLayoutAlign="start center">
              <div fxFlex="50%" fxLayoutAlign="start start">
                <!-- <mat-form-field > -->
                  <!-- <input matInput name="image{{i}}" [(ngModel)]="publicPages[i].image"  #image="ngModel" required>  -->
                  <!-- </mat-form-field> -->
                  <span>Cover Image</span>
              </div>
              <div fxFlex="50%" fxLayoutAlign="end end">
                <div *ngIf="!publicPages[i].isEdit">
                  {{publicPages[i].imageDesc}}
                </div>

                <div *ngIf="publicPages[i].isEdit">
                  <span style="text-decoration:underline" (click)="onChangeImage(i)">Change</span>
                  <span *ngIf="!uploadImage">
                    <img src="/assets/images/cabcharge_logo.png" alt="" width="25px" height="25px">
                  </span>
                  <span *ngIf="uploadImage">
                    <input type="file" id="imageUpload" accept="image/*" (change)="onChoosingImage($event)"
                    name="imageDesc{{i}}" [(ngModel)]="publicPages[i].imageDesc"  #imageDesc="ngModel">
                  </span>
                </div>


              
              </div>
            </div>
            <mat-divider></mat-divider>
            <div fxLayout="row" fxLayoutAlign="start center">
              <div fxFlex="50%" fxLayoutAlign="start start">
                <!-- <mat-form-field > -->
                  <!-- <input matInput name="header{{i}}" [(ngModel)]="publicPages[i].header"  #header="ngModel" required>  -->
                  <!-- </mat-form-field> -->
                  <span>Header</span>
              </div>
              <div fxFlex="50%" fxLayoutAlign="end end">
                <div *ngIf="!publicPages[i].isEdit">
                  {{publicPages[i].headerDesc}}
                </div>
                <div *ngIf="publicPages[i].isEdit" appearance="outline">
                  <mat-form-field appearance="outline">
                    <input matInput  name="headerDesc{{i}}" [(ngModel)]="publicPages[i].headerDesc"  #headerDesc="ngModel" required> 
                  </mat-form-field>
                </div>
              </div>
            </div>
            <mat-divider></mat-divider>
            <div fxLayout="column">
              <div fxFlex="20%" fxLayoutAlign="start start">
               
                  <span>Text</span>
               
                <!-- <mat-icon (click)="onEdit(i)" style="cursor: arrow;">mode_edit</mat-icon> -->
              </div>
              <div *ngIf="!publicPages[i].isEdit">
                {{publicPages[i].textDesc}}
              </div>
              <div *ngIf="publicPages[i].isEdit">
                <mat-form-field style="width:280px" appearance="outline">
                  <textarea matInput rows="5" style="width:280px" name="textDesc{{i}}" [(ngModel)]="publicPages[i].textDesc"  #textDesc="ngModel">
                        {{publicPages[i].textDesc}}
                      </textarea>
                </mat-form-field>
             
              </div>
              
            </div>
            </div>
          </mat-expansion-panel>
          </div>
        </mat-accordion>
        <br>
        <div fxFlex>
          <button mat-raised-button class="inp signin-btn warning" (click)="onSubmit()">Save and Submit</button>
        </div>
      </form>
    </div>
  </div>
</div>













<!-- <mat-accordion> -->
        <!-- <mat-expansion-panel (opened)="setOpened(i)" (closed)="setClosed(i)" *ngFor="let control of formItems.controls; let i= index">
         <ng-container [formGroupName]="i">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{control.value.title}}
            </mat-panel-title>
          </mat-expansion-panel-header>
         </ng-container>
        </mat-expansion-panel> -->
        <!-- <ng-template *ngFor="let item of items; let i=index">
          {{item.title}}
         
            <div *ngIf="newPage">
           
              <mat-expansion-panel (opened)="setOpened(i)" (closed)="setClosed(i)">
  
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    {{item.title}}
                  </mat-panel-title>
                </mat-expansion-panel-header>
  
                <div fxLayout="row" fxLayoutAlign="start center">
                  <div fxFlex="50%" fxLayoutAlign="start center">
                    <p>{{item.content}}</p>
                  </div>
                  <div fxFlex="50%" fxLayoutAlign="end end" class="cursor-pointer">
                    {{item.pageType}} <mat-icon (click)="goToLanding()">keyboard_arrow_right</mat-icon>
                  </div>
                </div>
                <mat-divider></mat-divider>
                <div fxLayout="row" fxLayoutAlign="start center">
                  <div fxFlex="50%" fxLayoutAlign="start start">
                    <p>{{item.image}}</p>
                  </div>
                  <div fxFlex="50%" fxLayoutAlign="end end">
                    <span style="text-decoration:underline" (click)="onChangeImage(item.id)">Change</span>
  
                    <span *ngIf="!uploadImage">
                      <img src="/assets/images/cabcharge_logo.png" alt="" width="25px" height="25px">
                    </span>
                    <span *ngIf="uploadImage">
                      <input type="file" id="imageUpload" accept="image/*" (change)="onChoosingImage($event)"
                        formControlName="image">
                    </span>
                  </div>
                </div>
                <mat-divider></mat-divider>
                <div fxLayout="row" fxLayoutAlign="start center">
                  <div fxFlex="50%" fxLayoutAlign="start start">
                    <p>{{item.header}}</p>
                  </div>
                  <div fxFlex="50%" fxLayoutAlign="end end">
                    {{item.headerDesc}}
                  </div>
                </div>
                <mat-divider></mat-divider>
                <div fxLayout="column" *ngIf="item.text">
                  <div fxFlex="20%" fxLayoutAlign="start start">
                    <p>{{item.text}}</p> &nbsp;
                    <mat-icon (click)="onEdit()" style="cursor: arrow;">mode_edit</mat-icon>
                  </div>
                  <div fxFlex="80%" fxLayoutAlign="start start" *ngIf="!editRow">
                    {{item.textDesc}}
                  </div>
                  <div fxFlex="90%" fxLayoutAlign="start start" *ngIf="editRow">
                    <mat-form-field style="width:280px">
                      <textarea matInput rows="5" style="width:280px" (change)="onTextChange(item.id)"
                        formControlName="textDesc">
                            {{item.textDesc}}
                          </textarea>
                    </mat-form-field>
                  </div>
                </div>
  
              </mat-expansion-panel>
            </div>
         
          
          <div *ngIf="!newPage">
            <mat-expansion-panel (opened)="setOpened(i)" (closed)="setClosed(i)">

              <mat-expansion-panel-header>
                <mat-panel-title>
                  {{item.title}}
                </mat-panel-title>
              </mat-expansion-panel-header>

              <div fxLayout="row" fxLayoutAlign="start center">
                <div fxFlex="50%" fxLayoutAlign="start center">
                  <p>{{item.content}}</p>
                </div>
                <div fxFlex="50%" fxLayoutAlign="end end" class="cursor-pointer">
                  {{item.pageType}} <mat-icon (click)="goToLanding()">keyboard_arrow_right</mat-icon>
                </div>
              </div>
              <mat-divider></mat-divider>
              <div fxLayout="row" fxLayoutAlign="start center">
                <div fxFlex="50%" fxLayoutAlign="start start">
                  <p>{{item.image}}</p>
                </div>
                <div fxFlex="50%" fxLayoutAlign="end end">
                  <span style="text-decoration:underline" (click)="onChangeImage(item.id)">Change</span>

                  <span *ngIf="!uploadImage">
                    <img src="/assets/images/cabcharge_logo.png" alt="" width="25px" height="25px">
                  </span>
                  <span *ngIf="uploadImage">
                    <input type="file" id="imageUpload" accept="image/*" (change)="onChoosingImage($event)"
                      formControlName="image">
                  </span>
                </div>
              </div>
              <mat-divider></mat-divider>
              <div fxLayout="row" fxLayoutAlign="start center">
                <div fxFlex="50%" fxLayoutAlign="start start">
                  <p>{{item.header}}</p>
                </div>
                <div fxFlex="50%" fxLayoutAlign="end end">
                  {{item.headerDesc}}
                </div>
              </div>
              <mat-divider></mat-divider>
              <div fxLayout="column" *ngIf="item.text">
                <div fxFlex="20%" fxLayoutAlign="start start">
                  <p>{{item.text}}</p> &nbsp;
                  <mat-icon (click)="onEdit()" style="cursor: arrow;">mode_edit</mat-icon>
                </div>
                <div fxFlex="80%" fxLayoutAlign="start start" *ngIf="!editRow">
                  {{item.textDesc}}
                </div>
                <div fxFlex="90%" fxLayoutAlign="start start" *ngIf="editRow">
                  <mat-form-field style="width:280px">
                    <textarea matInput rows="5" style="width:280px" (change)="onTextChange(item.id)"
                      formControlName="textDesc">
                          {{item.textDesc}}
                        </textarea>
                  </mat-form-field>
                </div>
              </div>
            </mat-expansion-panel>
          </div>
        </ng-template> -->
      <!-- </mat-accordion> -->






















      <!-- neeha -->

      <!-- <div fxLayout="column" fxLayoutGap="50px">
        <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
            <div fxFlex="5%">
                <mat-icon class="material-icons-outlined main-icon">card_membership</mat-icon>
            </div>
            <div fxFlex="95%" fxLayoutAlign="start center">
                <span class="header pb5">Departmental Card Nomination Form</span>
            </div>
        </div>
        <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
            <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="end center">
                <div fxLayoutGap="5px">
                    <button mat-raised-button class="add-btn" (click)="addNominee()">Add Nominees<mat-icon>group_add</mat-icon>
                        </button>
                </div>
            </div>
            <form #f="ngForm" name="form">
                <ng-container *ngFor="let val of deparmentCard; let i = index" >
                    <mat-card fxLayout="column" fxLayoutGap="20px" style="margin-bottom: 15px;border-radius: 10px;">
                    <div fxFlex class="header-div">
                        <span>Nominees Details</span>
                       </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Cost Center/Division:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            <mat-form-field appearance="outline" class="inp">
                                    <mat-select name="costCenter{{i}}" [(ngModel)]="deparmentCard[i].costCenter" placeholder="Please Select"  #costCenter="ngModel"  (selectionChange)="onSelection(deparmentCard[i].costCenter,i)">
                                        
                                        <mat-option *ngFor="let acl of costcenter" [value]="acl.viewValue">
                                            {{acl.viewValue}}
                                        </mat-option>
                                    </mat-select>
                            </mat-form-field>
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="val.isShowDept">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Department:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            <mat-form-field appearance="outline" class="inp">
                                    <mat-select name="dept{{i}}" [(ngModel)]="deparmentCard[i].dept" placeholder="Please Select"  #dept="ngModel">
                                        
                                        <mat-option *ngFor="let acl of departmentList" [value]="acl.viewValue">
                                            {{acl.viewValue}}
                                        </mat-option>
                                    </mat-select>
                            </mat-form-field>
                        </div>
                    </div>
                    <div fxLayout="column" fxLayoutGap="20px" *ngIf="val.isShowShippingDetails">
                        <div fxFlex fxLayoutAlign="start center">
                            <span class="date-title">Shipping Contact Details:</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                                <span class="date-title">Contact Person:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                {{this.cdgService.loggedInUserDetails.userName}}
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                                <span class="date-title">Contact Telephone:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                {{this.cdgService.loggedInUserDetails.telephone}}
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                                <span class="date-title">Contact Mobile:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                {{this.cdgService.loggedInUserDetails.mobNo}}
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                                <span class="date-title">Contact Address:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                               <span> {{this.cdgService.loggedInUserDetails.blockNo}}<br/>
                                {{this.cdgService.loggedInUserDetails.unitNo}},{{this.cdgService.loggedInUserDetails.streetName}}<br/>
                                {{this.cdgService.loggedInUserDetails.buildingName}},{{this.cdgService.loggedInUserDetails.area}}<br/>
                                {{this.cdgService.loggedInUserDetails.country}},{{this.cdgService.loggedInUserDetails.postalCode}}
                            </span>
                            </div>
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Div/Dept Full Name<span class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            <mat-form-field appearance="outline" class="inp">
                            <input matInput name="deptName{{i}}" [(ngModel)]="deparmentCard[i].deptName"  #deptName="ngModel" required>
    
                                  
                                <mat-error *ngIf="f.submitted && deptName.invalid" class="pt15">
                                    <div *ngIf="deptName.hasError('required')">
                                        Div/Dept Full Name is required
                                    </div>
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Name On Card<span class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            <mat-form-field appearance="outline" class="inp">
                            <input matInput name="nameOnCard{{i}}" [(ngModel)]="deparmentCard[i].nameOnCard"  #nameOnCard="ngModel" required>
                                <mat-error *ngIf="f.submitted && nameOnCard.invalid" class="pt15">
                                    <div *ngIf="nameOnCard.hasError('required')">
                                       Name is required
                                    </div>
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Remarks:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            <mat-form-field appearance="outline" class="inp">
                        <textarea matInput placeholder="Message" rows="6" name="remarks{{i}}" [(ngModel)]="deparmentCard[i].remarks"  #remarks="ngModel" ></textarea>
                            </mat-form-field>
                        </div>
                    </div>
                    <div fxLayoutGap="5px" fxLayoutAlign="end center" *ngIf="i > 0">
                        <button mat-raised-button class="delete-btn" (click)="deleteNominee(i)">Delele Nominee<mat-icon>delete_sweep
                            </mat-icon></button>
                    </div>
            </mat-card>
                </ng-container>
                <div fxFlex class="mt20">
                    <button mat-raised-button class="update-btn"
                        (click)="submit(f.form.valid)">SUBMIT</button>
                </div>
            </form>
        </div>
    </div> -->