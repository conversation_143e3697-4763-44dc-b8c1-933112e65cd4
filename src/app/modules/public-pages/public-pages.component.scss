@import '@angular/material/theming';
@import './../../../styles/colors.scss';
@import './../../../styles/sizing.scss';
@import './../../../styles/main.scss';
.mat-expansion-panel-header{ 
    background-color: $prime; 
    border-bottom: 2px solid #CBECFF;
}
.mat-expansion-panel-header-title {
    color:$white !important;
}
::ng-deep .mat-expansion-panel-header:hover{ 
    background-color: $prime !important;
    // 006BA8
}
.mat-expanded:focus, .mat-expanded:hover{
    background: $prime !important;
}

::ng-deep.mat-expansion-panel-body{
    background-color: #CBECFF !important;
}
.mat-icon{
    color: $cdgc-font-prime;
}
::ng-deep .mat-expansion-indicator::after{
    color: white !important;
}
.warning{
    background-color:$hue;
}
// .mat-expansion-panel-header  {
//  pointer-events:none;
// }
// ::ng-deep .mat-expansion-indicator{
//     pointer-events:visibleFill !important;
// }


// .mat-expansion-panel-header-description, .mat-expansion-indicator::after {
//     color: rgba(0, 0, 0, 0.54);
//     color: white;
// }