@import '@angular/material/theming';
@import './../../../styles/colors.scss';
@import './../../../styles/sizing.scss';
@import './../../../styles/main.scss';

    .title {
        font-family: $cdgsz-font-family;
        font-style: normal;
        font-weight: bold;
        font-size: $cdgsz-font-size-lg;
        line-height: 23px;
    }
    /* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
    .mat-mdc-button-toggle-checked{
        background-color:$cdgc-bg-prime !important;
        color: $cdgc-font-accent !important;
       
    }
    /* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
    .mat-mdc-button-toggle{
        height:30px;
    }
    // .isMobile{
    //     display: none;
    // }
    // @media screen and (max-width: 600px) {
    //     .isWeb{
    //         display: none;
    //     }
    // }
    // @media screen and(min-width: 768px) and (max-width: 1240px) {
    // .isMobile{
    //     display: none;
    // }
    // }
    // @media screen and(min-width: 1030px) and (max-width: 1366px) {
    // .isMobile{
    //     display: none;
    // }
    // } 