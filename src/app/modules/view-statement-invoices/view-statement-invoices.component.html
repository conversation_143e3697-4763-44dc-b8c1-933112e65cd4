<div fxFlex fxLayout="column" class="full-height">
  <!-- fxShow.gt-md fxHide.xs -->
    <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="center center" >     
        <div fxFlex fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="start center" fxFlex.xs=70% fxFlex.md=70%>
          <div>
            <div class="title" *ngIf="toggleGroup.value == 'statements'">View Statement of Accounts</div>
            <div class="title" *ngIf="toggleGroup.value == 'invoices'">View / Download Invoices</div>
          </div>
        </div>
        <div fxFlex fxLayout="row" fxLayout.lt-md="row"  fxLayoutAlign="end center" fxLayoutAlign.xs="start center" fxLayoutAlign.md="start center" class="cursor-pointer">
            <mat-button-toggle-group #toggleGroup = "matButtonToggleGroup" style="height:50px;" value="statements">
                <mat-button-toggle value="statements">
                <p>View Statements</p>
                </mat-button-toggle>
                <mat-button-toggle value="invoices">
                <p>View Invoices</p> 
                </mat-button-toggle>
            </mat-button-toggle-group>
        </div>
      </div> 
      <div *ngIf="toggleGroup.value == 'statements'">
        <app-account-statements></app-account-statements>
      </div>
        <div *ngIf="toggleGroup.value == 'invoices'">
            <app-invoices></app-invoices>
        </div>
</div>


<!-- <div fxFlex fxLayout="column" class="full-height" fxHide.gt-md fxShow.xs>
  <div fxLayout="column" fxLayoutGap="15px" fxLayoutAlign="center center"> 
      
      <div fxFlex fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="center center">
        
        <div>
          <div class="title" *ngIf="toggleGroup.value == 'statements'">View Statement of Accounts</div>
          <div class="title" *ngIf="toggleGroup.value == 'invoices'">View / Download Invoices</div>
        </div>
      </div>
      <div fxFlex fxLayout="row" fxLayout.lt-md="row"  fxLayoutAlign="center center" fxLayoutAlign.xs="start center" fxLayoutAlign.md="start center" class="cursor-pointer">
        <mat-button-toggle-group #toggleGroup = "matButtonToggleGroup" style="height:50px;" value="statements">
            <mat-button-toggle value="statements">
            <p>View Statements</p>
            </mat-button-toggle>
            <mat-button-toggle value="invoices">
            <p>View Invoices</p> 
            </mat-button-toggle>
        </mat-button-toggle-group>
    </div>  
     
    </div> 
    <div *ngIf="toggleGroup.value == 'statements'">
      <app-account-statements></app-account-statements>
    </div>
      <div *ngIf="toggleGroup.value == 'invoices'">
          <app-invoices></app-invoices>
      </div>
</div>  -->
