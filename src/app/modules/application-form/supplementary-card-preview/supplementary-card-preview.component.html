<div fxLayout="column" fxLayoutGap="50px" *ngIf="this.supplemetaryCard.length > 0 && !isShowErrorPage">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">preview</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">{{header}}</span>
        </div>
    </div>
    <div fxLayout="column" fxLayoutGap="10px" class="custom-padding-left" >
        <!-- <div fxLayout="row" fxLayoutGap="5px">
            <span class="subHeading">Account Name (Account No) :</span>
            <span class="subHeadingLite">{{accountName}} ({{accNo}})</span>
        </div> -->

        <ng-container *ngFor="let val of supplemetaryCard; let i = index">
            <!-- <div fxLayout="column" fxLayoutGap="20px"> -->
                <mat-card appearance="raised" fxLayout="column" fxLayoutGap="20px" style="margin-bottom: 10px;border-radius: 10px;">
                    <div fxFlex class="header-div">
                        <span>Nominees Details</span>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Salutation:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.salutation}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Full Name<span class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.name}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Name On Card<span class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.nameOnCard}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">NRIC / Passport Number<span
                                class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.passportNo}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Date Of Birth<span class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.dob | date: 'dd-MM-yyyy'}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Relation To Card Holder<span
                                class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.relationToCardHolder}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Telephone No:</span>
                               
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.telephone}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Office No:</span>
                               
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.officeNo}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Mobile No:</span>
                               
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.mobNo}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Email Address:</span>
                               
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.email}}
                        </div>

                    </div>
                    <div fxFlex class="header-div">
                        <span>Address Details</span>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Block No:</span>
                               
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.blockNo}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Unit No:</span>
                               
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.unitNo}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Street Name<span class="asterisk"><sup>*</sup></span>:</span>
                               
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.streetName}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Building Name:</span>
                               
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.buildingName}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Area:</span>
                               
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.area}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Country<span class="asterisk"><sup>*</sup></span>:</span>
                               
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.country}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">State:</span>
                               
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.state}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">City</span><span>:</span>
                               
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.city}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;" >
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Postal Code<span class="asterisk"><sup>*</sup></span>:</span>
                               
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.postalCode}}
                        </div>

                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 25px;">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Remarks:</span>
                               
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.remarks}}
                        </div>

                    </div>
                
                </mat-card>
            <!-- </div> -->
        </ng-container>
    </div>
    <div fxFlex class="mt20" fxLayoutGap="20px" fxLayoutAlign="end center">
        <button mat-raised-button class="reset-btn" (click)="cancel()">CANCEL</button>
        <button mat-raised-button class="edit-btn" (click)="edit()">EDIT</button>
        <button mat-raised-button class="update-btn" (click)="submit()">CONFIRM</button>
    </div>
</div>
<div fxLayout="column" fxLayoutGap="10px" *ngIf="isShowErrorPage" fxLayoutAlign="center center"
    style="height: 100% !important;">
    <div class="noPreviewMsg">Sorry No Preview is</div>
    <div fxLayout="row" fxLayoutAlign="center center" class="noPreviewMsg"><span>Available </span> <span>
            <mat-icon class="material-icons-outlined main-preview-icon">sentiment_dissatisfied</mat-icon>
        </span></div>

</div>
