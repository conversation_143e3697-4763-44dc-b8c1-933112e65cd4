import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { timeStamp } from 'console';
import { DatePipe } from '@angular/common';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';

@Component({
  selector: 'app-supplementary-card-preview',
  templateUrl: './supplementary-card-preview.component.html',
  styleUrls: ['./supplementary-card-preview.component.scss'],
  providers: [DatePipe]
})
export class SupplementaryCardPreviewComponent implements OnInit {
  accountnumber: any;
  isShowErrorPage: boolean;
  supplemetaryCard: any[] = [];
  datavalue: any = [];
  header: any;
  objdata: any;

  constructor(private router: Router, private datePipe: DatePipe, public dialog: MatDialog, private fb: FormBuilder, public localStorage: LocalStorageService, private apiService: ApiServiceService, public cdgService: CdgSharedService, private notifyService: NotificationService) { }

  ngOnInit(): void {
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        if(this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0]){
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
        }
      }
    }
    this.isShowErrorPage = false;
    console.log(this.localStorage.localStorageGet('supplemetarycard'))
    if (this.localStorage.localStorageGet('supplemetarycard').length > 0) {
      this.header = "Supplementary Card Nomination Form Preview"
      this.supplemetaryCard = this.localStorage.localStorageGet("supplemetarycard")
      console.log(this.supplemetaryCard);
    }
    else {
      this.isShowErrorPage = true;
    }
  }
  cancel() {
    this.cdgService.supplementaryPreview.supplementarypreview = true;
    this.cdgService.supplementaryPreview.function = "cancel"

    if (this.localStorage.localStorageGet("supplemetarycard")) {
      this.router.navigate(['layout/supplementarycard']);
    }

  }

  edit() {
    this.cdgService.supplementaryPreview.supplementarypreview = true;
    this.cdgService.supplementaryPreview.function = "edit"
    if (this.localStorage.localStorageGet("supplemetarycard")) {
      this.cdgService.supplementarypreview = true;
      this.router.navigate(['layout/supplementarycard']);
    }
  }

  submit() {
    // this.cdgService.supplementarypreview=true;
    this.supplemetaryCard.forEach((item: any) => {

      let obj = {

        "nomineeSalutation": item.salutation,
        "nomineeName": item.name,
        "nomineeNameOnCard": item.nameOnCard,
        "nomineePassportNumber": item.passportNo,
        "nomineeDateOfBirth": this.datePipe.transform(item.dob, 'dd-MM-YYYY'),
        "nomineeRelationWithCardHolder": item.relationToCardHolder,
        "nomineeTelephoneNo": item.telephone,
        "nomineeOfficeNo": item.officeNo,
        "nomineeMobileNo": item.mobNo,
        "nomineeEmailId": item.email,
        "nomineeAddressBlock": item.blockNo,
        "nomineeAddressUnit": item.unitNo,
        "nomineeAddressStreet": item.streetName,
        "nomineeAddressBuilding": item.buildingName,
        "nomineeAddressArea": item.area,
        "nomineeCountryName": item.country,
        "nomineeStateName": item.state,
        "nomineeCityName": item.city,
        "nomineeAddressPostal": item.postalCode,
        "nomineeRemarks": item.remarks
      }
      this.datavalue.push(obj);
      console.log(obj)

    });


    this.objdata = {

      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "accountName": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].main_account_name,
      "accountNo": this.accountnumber,
      "customerNo": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,

      "nomineeSupplementaryCardRequestDtoArr": this.datavalue
    }

    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.SUPPLY_SUBMIT, this.objdata).subscribe((response: any) => {
      if (response.status === 200) {
        this.cdgService.supplementaryPreview.supplementarypreview = true;
        this.cdgService.supplementaryPreview.function = "cancel"
        this.notifyService.showSuccess(response.body.successMessage, 'Supplementary Card')
        this.router.navigate(['layout/supplementarycard']);

      }
    });
  }


}
