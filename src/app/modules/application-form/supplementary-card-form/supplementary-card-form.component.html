<div fxLayout="column" fxLayoutGap="50px"> 
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">card_membership</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">Supplementary Card Form</span>
        </div>
    </div>
    <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
       
        <form #f="ngForm" name="form">
            <ng-container *ngFor="let val of nomineeCard; let i = index" >
                <mat-card appearance="raised" fxLayout="column" fxLayoutGap="20px" style="margin-bottom: 15px;border-radius: 10px;">
                <div fxFlex class="header-div">
                    <span>NOMINEES DETAILS</span>
                   </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center" style="max-height: 55px;">
                        <span class="date-title">SALUTATION:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <mat-select name="salutation{{i}}" [(ngModel)]="nomineeCard[i].salutation" placeholder="Please Select" #salutation="ngModel">

                                <mat-option *ngFor="let acl of nameTitle" [value]="acl.sMasterValue">
                                    {{acl.sMasterValue}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center" style="max-height: 55px;">
                        <span class="date-title">FULL NAME<span class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput name="name{{i}}" autocomplete="off" onkeypress="return event.charCode >= 65 && event.charCode <=90 || event.charCode >= 95 && event.charCode <=122 || event.charCode === 32" [(ngModel)]="nomineeCard[i].name" #name="ngModel" required>
                            <mat-error *ngIf="f.submitted && name.invalid" class="pt15">
                                <div *ngIf="name.hasError('required')">
                                   Name Is Required
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center" style="max-height: 55px;">
                        <span class="date-title">NAME ON CARD<span class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput name="nameOnCard{{i}}" onkeypress="return event.charCode >= 65 && event.charCode <=90 || event.charCode >= 95 && event.charCode <=122 || event.charCode === 32" [(ngModel)]="nomineeCard[i].nameOnCard" #nameOnCard="ngModel" required>
                            <mat-error *ngIf="f.submitted && nameOnCard.invalid" class="pt15">
                                <div *ngIf="nameOnCard.hasError('required')">
                                   Name On card is required
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">NRIC / PASSPORT NUMBER<span
                                class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%" fxLayout="column">
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput name="passportNo{{i}}" autocomplete="off" [(ngModel)]="nomineeCard[i].passportNo" #passportNo="ngModel" required>
                            <mat-error *ngIf="f.submitted && passportNo.invalid" class="pt15">
                                <div *ngIf="passportNo.hasError('required')">
                                   Passport number is required
                                </div>
                            </mat-error>
                        </mat-form-field>
                        <span class="label">Please key in only the first letter and last 4 characters of NRIC / Passport Number e.g.PXXXXX234G</span>
                    </div>
                </div>
                
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">DATE OF BIRTH<span class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput [matDatepicker]="picker1" readonly name="dob{{i}}" [max]="currDate" data-date-format="dd/mm/yyyy" [(ngModel)]="nomineeCard[i].dob" #dob="ngModel" (dateChange)="formatChange($event)"
                                placeholder="dd-mm-yyyy" required>
                            <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                            <mat-datepicker #picker1></mat-datepicker>
                            <mat-error *ngIf="f.submitted && dob.invalid" class="pt15">
                                <div *ngIf="dob.hasError('required')">
                                   Date of birth number is required
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">RELATION TO CARD HOLDER<span
                                class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <mat-select name="relationToCardHolder{{i}}" [(ngModel)]="nomineeCard[i].relationToCardHolder" #relationToCardHolder="ngModel"
                                placeholder="Please Select" required >

                                <mat-option *ngFor="let acl of relationToHolder" [value]="acl.rMasterValue">
                                    {{acl.rMasterValue}}
                                </mat-option>
                            </mat-select>
                            <mat-error *ngIf="f.submitted && relationToCardHolder.invalid" class="pt15">
                                <div *ngIf="relationToCardHolder.hasError('required')">
                                    Relation To Card Holder Is Required
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxFlex class="header-div">
                    <span>CONTACT DETAILS</span>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">TELEPHONE NO:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput type="number"  onkeypress="if(event.charCode===69 || event.charCode===101)return false;" name="telephone{{i}}" autocomplete="off" [(ngModel)]="nomineeCard[i].telephone" #telephone="ngModel">
                            <mat-error *ngIf="f.submitted && relationToCardHolder.invalid" class="pt15">
                                <div *ngIf="relationToCardHolder.hasError('maxLength')">
                                    Telephone Number Cannot Be More Than 10 Digits
                                </div>
                                <div *ngIf="relationToCardHolder.hasError('pattern')">
                                    Please Enter Valid Telephone Number Containing 10 Digits Only
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">OFFICE NO:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field  appearance="outline" class="inp">
                            <input matInput type="number"  onkeypress="if(event.charCode===69 || event.charCode===101)return false;" autocomplete="off" name="officeNo{{i}" [(ngModel)]="nomineeCard[i].officeNo" #officeNo="ngModel">
                           
                            <mat-error *ngIf="f.submitted && officeNo.invalid" class="pt15">
                                <div *ngIf="officeNo.hasError('maxLength')">
                                    Office No. Cannot Be More Than 10 Digits
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">MOBILE NO:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput  type="number" autocomplete="off" name="mobNo{{i}}" onkeypress="if(event.charCode===69 || event.charCode===101)return false;" [(ngModel)]="nomineeCard[i].mobNo" #mobNo="ngModel">
                        
                            <mat-error *ngIf="f.submitted && officeNo.invalid" class="pt15">
                                <div *ngIf="officeNo.hasError('maxLength')">
                                    Mobile Number Cannot Be More Than 8 Digits
                                </div>
                            </mat-error>
                            <mat-error *ngIf="f.submitted && officeNo.invalid" class="pt15">
                                <div *ngIf="officeNo.hasError('pattern')">
                                    Please Enter Valid Mobile Number Containing 8 Digits Only
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">EMAIL ADDRESS:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput type="email" pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-.]+$" autocomplete="off" name="email{{i}}" [(ngModel)]="nomineeCard[i].email" #email = "ngModel">
                            <!-- pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$" -->
                            <mat-error *ngIf="f.submitted && email.invalid" class="pt15">
                                <div *ngIf="email.hasError('pattern')">
                                    Please Enter Valid Email
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxFlex class="header-div">
                    <span>ADDRESS DETAILS</span>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">BLOCK NO:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput type="tel" pattern="[0-9\-]+" autocomplete="off" name="blockNo{{i}}" [(ngModel)]="nomineeCard[i].blockNo" #blockNo="ngModel">
                        </mat-form-field>
                        <mat-error *ngIf="blockNo.invalid" class="pt15">
                            <div *ngIf="blockNo.hasError('pattern')">
                                Block no should be a number
                            </div>
                        </mat-error>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">UNIT NO:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput type="tel" pattern="[0-9\-]+" autocomplete="off" name="unitNo{{i}}" [(ngModel)]="nomineeCard[i].unitNo" #unitNo="ngModel">
                            <!-- pattern="[0-9]+([-\,][0-9]+)?"  -->
                           
                        </mat-form-field>
                        <mat-error *ngIf="unitNo.invalid" class="pt15">
                        <div *ngIf="unitNo.hasError('pattern')">
                            Unit no should be a number
                        </div>
                    </mat-error>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">STREET NAME<span class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput name="streetName{{i}}" autocomplete="off" [(ngModel)]="nomineeCard[i].streetName" #streetName="ngModel" required>
                            <mat-error *ngIf="f.submitted && streetName.invalid" class="pt15">
                                <div *ngIf="streetName.hasError('required')">
                                    Street Name Is Required
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">BUILDING NAME:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput name="buildingName{{i}}"  autocomplete="off" [(ngModel)]="nomineeCard[i].buildingName" #buildingName="ngModel">
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">AREA:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput name="area{{i}}" autocomplete="off" [(ngModel)]="nomineeCard[i].area" #area="ngModel">
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">COUNTRY<span class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <mat-select name="country{{i}}" [(ngModel)]="nomineeCard[i].country" placeholder="Please Select" #country="ngModel" (selectionChange)="onChangeCountry(country)">
                                <mat-option *ngFor="let acl of countryList" [value]="acl.masterValue" required>
                                    {{acl.masterValue}}
                                </mat-option>
                            </mat-select>
                            <mat-error *ngIf="f.submitted && country.invalid" class="pt15">
                                <div *ngIf="country.hasError('required')">
                                    Country Is Required
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                  
                   
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25" *ngIf="this.nomineeCard[i].country == 'OTHERS'">
                    <!-- showCountryTxtBox{{i}} -->
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <!-- <span class="date-title">Country<span class="asterisk"><sup>*</sup></span>:</span> -->
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput placeholder="Enter Country Name" name="countryName{{i}}" autocomplete="off" [(ngModel)]="nomineeCard[i].countryName" #countryName="ngModel">
                        </mat-form-field>
                    </div>
                </div>
                
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25" *ngIf="showstateCities">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">STATE<span class="asterisk"></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <!-- <mat-form-field appearance="outline" class="inp">
                            <mat-select name="state{{i}}" [(ngModel)]="nomineeCard[i].state" placeholder="Please Select" #state="ngModel">
                                <mat-option *ngFor="let acl of states" [value]="acl">
                                    {{acl.name}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field> -->
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput name="state{{i}}"  autocomplete="off" [(ngModel)]="nomineeCard[i].state" #state="ngModel">
                            <!-- <mat-error *ngIf="f.submitted && state.invalid" class="pt15">
                                <div *ngIf="state.hasError('required')">
                                    State Is Required
                                </div>
                            </mat-error> -->
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25" *ngIf="showstateCities">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">CITY<span class="asterisk"></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <!-- <mat-form-field appearance="outline" class="inp">
                            <mat-select name="city{{i}}" [(ngModel)]="nomineeCard[i].city" placeholder="Please Select" #city="ngModel">
                                <mat-option *ngFor="let acl of cities" [value]="acl">
                                    {{acl}}
                                </mat-option>
                            </mat-select>
                           
                        </mat-form-field> -->
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput name="city{{i}}" autocomplete="off" [(ngModel)]="nomineeCard[i].city" #city="ngModel">
                            <!-- <mat-error *ngIf="f.submitted && city.invalid" class="pt15">
                                <div *ngIf="city.hasError('required')">
                                    City Is Required
                                </div>
                            </mat-error> -->
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">POSTAL CODE<span class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput type="number" name="postalCode{{i}}" autocomplete="off"  onkeypress="if(event.charCode===69 || event.charCode===101)return false;" [(ngModel)]="nomineeCard[i].postalCode" #postalCode="ngModel" required>
                            <mat-error *ngIf="f.submitted && postalCode.invalid" class="pt15">
                                <div *ngIf="postalCode.hasError('required')">
                                    Postal Code Is Required
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl25">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">REMARKS:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <textarea matInput placeholder="Message" rows="6"name="remarks{{i}}" 
                            [(ngModel)]="nomineeCard[i].remarks"  #remarks="ngModel">
                            </textarea>
                            </mat-form-field>
                    </div>
                </div>
                <div fxLayoutGap="5px" fxLayoutAlign="end center" *ngIf="nomineeCard.length>1">
                    <button mat-raised-button class="delete-btn" (click)="deleteNominee(i)">Delete Nominee<mat-icon>delete_sweep
                        </mat-icon></button>
                </div>
        </mat-card>
            </ng-container>
            <ng-container>
            <!-- <div fxFlex class="mt20">
                <button mat-raised-button class="update-btn"
                    (click)="submitNominee(f.form.valid)">SUBMIT</button>
           
            &nbsp;
                <button mat-raised-button class="update-btn"
                    (click)="resetNominee()">RESET</button>
            </div> -->
            <div fxFlex class="mt20" fxLayoutGap="20px" fxLayoutAlign="start start">
                <button mat-raised-button class="reset-btn"
                (click)="resetNominee()">RESET</button>
                <button mat-raised-button class="update-btn"
                (click)="submitNominee(f.form.valid)">SUBMIT</button>
            </div>
        </ng-container>
            <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="end center">
                <div fxLayoutGap="5px">
                    <button mat-raised-button class="add-btn" (click)="addNominee(f.form.valid)" [disabled]="disableAddBtn">Add Nominees<mat-icon>group_add</mat-icon>
                        </button>
                </div>
            </div>
        </form>
    </div>
</div> 
<!-- add nominee form ends -->