import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>, FormBuilder, FormGroup } from '@angular/forms';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as _moment from 'moment';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core'
import { DatePipe } from '@angular/common';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { Router } from '@angular/router';


export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
}
@Component({
  selector: 'app-supplementary-card-form',
  templateUrl: './supplementary-card-form.component.html',
  styleUrls: ['./supplementary-card-form.component.scss'],
  providers: [DatePipe, { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] }, { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]
})
export class SupplementaryCardFormComponent implements OnInit {
  supplementaryForm:FormGroup
  nomineeCard: any[] = [];
  datasubmit: any;
  objdata: any;
  data = {
    salutation: "",
    name: "",
    nameOnCard: "",
    passportNo: "",
    dob: "",
    relationToCardHolder: "",
    telephone: "",
    officeNo: "",
    mobNo: "",
    email: "",
    blockNo: "",
    unitNo: "",
    streetName: "",
    buildingName: "",
    area: "",
    country: "",
    countryName:"",
    postalCode: "",
    remarks: ""
  }
  nameTitle: any = [];
  relationToHolder: any = [];
  datavalue: any = [];
  countryList: Array<any> = [
    {value:'singapore',masterValue:'SINGAPORE'},
    {value:'others',masterValue:'OTHERS'}
  ]

  masterdata: any;
  showNomineeForm: boolean = false;
  states: Array<any> = [];
  cities: Array<any> = [];
  showstateCities: boolean = false;
  disableAddBtn: boolean = false;
  accountnumber: any;
  temp: any;
  currDate: Date;
  showCountryTxtBox: boolean = false;
  selectedCountry: any;
  constructor(public localStorage: LocalStorageService, private router: Router, private datePipe: DatePipe, private apiService: ApiServiceService, private fb: FormBuilder, public cdgService: CdgSharedService, private notifyService: NotificationService) { 
    this.supplementaryForm = this.fb.group({
      salutation: [''],
      name: ['', [Validators.required]],
      nameOnCard: ['', [Validators.required]],
      passportNo: ['', [Validators.required]],
      dob: ['', [Validators.required]],
      relationToCardHolder: ['', [Validators.required]],
      telephone: ['', [Validators.pattern(/^[0-9]{8}$/)]],
      officeNo: ['', [Validators.minLength(8)]],
      mobNo: ['', [Validators.pattern(/^[0-9]{8}$/)]],
      email: [''],
      blockNo: [''],
      unitNo: [''],
      streetName: ['', [Validators.required]],
      buildingName: [''],
      area: [''],
      country: ['', [Validators.required]],
      state: [''],
      city: [''],
      postalCode: ['', [Validators.required]],
      remarks: ['']
    });
  }

  ngOnInit(): void {
   this.supplementaryForm.reset();
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        if(this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0]){
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
        }
      }
    }


    this.currDate = new Date();
    this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.GET_SUPPLEMENTARY).subscribe((response: any) => {
      if (response.status === 200) {
        this.masterdata = response.body;
        this.nameTitle = this.masterdata.salutationListDto;
        this.relationToHolder = this.masterdata.relationShipListDto;
        // this.countryList = this.masterdata.countryListDto;
      }
    });
    if (this.cdgService.supplementaryPreview.supplementarypreview) {
      if (this.cdgService.supplementaryPreview.function === 'edit') {
        this.nomineeCard = this.localStorage.localStorageGet('supplemetarycard');
       console.log(this.nomineeCard[0].country)
        if(this.nomineeCard[0].country !== 'SINGAPORE'){
          this.nomineeCard[0].country = 'OTHERS';
        }
        else{
          this.nomineeCard[0].country = 'SINGAPORE';
        }
        if(this.nomineeCard[1]){
          if(this.nomineeCard[1].country !== 'SINGAPORE'){
            this.nomineeCard[1].country = 'OTHERS';
          }
          else{
            this.nomineeCard[1].country = 'SINGAPORE';
          }
        }
       
      } else {
        this.localStorage.localStorageSet("supplemetarycard", []);
        this.resetNominee();
        console.log(this.nomineeCard)
      }
    }
    else {
      this.nomineeCard.push(this.data)
    }
  }
  addNominee(valid: any) {
    // this.onChangeCountry('');
//  if(this.nomineeCard[0].country !== 'OTHERS') {
//     this.showCountryTxtBox = false;
//   }
//   else{
//     this.showCountryTxtBox = true;
//   }
    
    if (valid && this.nomineeCard.length < 2) {
     
      this.nomineeCard.push({
        salutation: "",
        name: "",
        nameOnCard: "",
        passportNo: "",
        dob: "",
        relationToCardHolder: "",
        telephone: "",
        officeNo: "",
        mobNo: "",
        email: "",
        blockNo: "",
        unitNo: "",
        streetName: "",
        buildingName: "",
        area: "",
        country: "",
        countryName:"",
        city: "",
        state: "",
        postalCode: "",
        remarks: ""
      })
      // if(this.nomineeCard[1].country === ''){
      //   this.showCountryTxtBox = false;
      // }
    }
    else {
      if (!valid) {
        this.notifyService.showWarning("Please Fill Mandatory fields", "Warning");
      }
      else {
        this.disableAddBtn = true;
        this.notifyService.showWarning("Only two nominees can be added", "Warning");
      }


    }


  }
  deleteNominee(index: number) {
    this.disableAddBtn = false;
    this.nomineeCard.splice(index, 1)
  }
  onSelect(value: any) {
    this.showstateCities = true;
    // this.states = this.countryList.find(con => con.value === value).states;
  }
  //   onStateSelect(state:any){
  // this.cities = this.countryList.find(con => con.value === value).states
  //   }
  // onStateSelect(state:any){
  // this.cities = this.countryList.find(state => state == state)?.cities;
  // }
  onChangeCountry(conValue: any) {
    console.log(conValue.value);
    this.selectedCountry = conValue.value;
    this.showCountryTxtBox = false;
    if(this.selectedCountry == 'OTHERS' && this.selectedCountry !== ''){
      this.showCountryTxtBox = true;
    }
    else{
      this.showCountryTxtBox = false;
    }
    this.showstateCities = true;
  }
  addNewNominee() {
    this.showNomineeForm = true;
    
  }
  resetNominee() {
    // console.log("reset");
    this.nomineeCard=[{
        salutation: "",
        name: "",
        nameOnCard: "",
        passportNo: "",
        dob: "",
        relationToCardHolder: "",
        telephone: "",
        officeNo: "",
        mobNo: "",
        email: "",
        blockNo: "",
        unitNo: "",
        streetName: "",
        buildingName: "",
        area: "",
        country: "",
        postalCode: "",
        remarks: ""
    }]

  }
  formatChange(event: any) {
    const data: any = this.datePipe.transform(event.value, 'dd/MM/YYYY')
    this.supplementaryForm.value.issueDate = data;
  }
  submitNominee(valid: any) {
    if(this.nomineeCard[0]){
      let value = this.nomineeCard[0].country;
      console.log(value,'country')
      if(value == 'OTHERS'){
        this.nomineeCard[0].country = this.nomineeCard[0].countryName;
        console.log(this.nomineeCard[0].country,'final country')
      }
    }
    if(this.nomineeCard[1]){
      let value = this.nomineeCard[1].country;
      if(value == 'OTHERS'){
        this.nomineeCard[1].country = this.nomineeCard[1].countryName;
      }
    }
   
   
   
    // let value = this.supplementaryForm.controls.countryName.value;
    // console.log(value)
    this.datavalue = [];
    if (valid) {
      this.showNomineeForm = false;
      if (this.nomineeCard[1]) {
        if (this.nomineeCard[1].passportNo) {
          if (this.nomineeCard[0].passportNo === this.nomineeCard[1].passportNo) {

            this.notifyService.showWarning('Both nominee cannot have same passport No', 'Supplementary Card')
            return;
          }
        }
      }
      // this.nomineeCard.forEach((item: any) => {
      //   item.dob = this.datePipe.transform(item.dob, 'dd-MM-YYYY');
      // });
      console.log(this.nomineeCard)
      this.localStorage.localStorageSet('supplemetarycard', this.nomineeCard);
      // if (this.nomineeCard) {
        this.router.navigate(['layout/supplementarycard/preview']);
      // }
      //    this.nomineeCard.forEach((item:any)=>{

      //  let obj={ 

      //   "nomineeSalutation":item.salutation,
      //   "nomineeName": item.name,
      //   "nomineeNameOnCard":item.nameOnCard,
      //   "nomineePassportNumber":item.passportNo,
      //   "nomineeDateOfBirth":this.datePipe.transform(item.dob, 'dd-MM-YYYY'),
      //   "nomineeRelationWithCardHolder":item.relationToCardHolder,
      //   "nomineeTelephoneNo":item.telephone,
      //   "nomineeOfficeNo":item.officeNo,
      //   "nomineeMobileNo":item.mobNo,
      //   "nomineeEmailId":item.email,
      //  "nomineeAddressBlock":item.blockNo,
      //  "nomineeAddressUnit":item.unitNo,
      //  "nomineeAddressStreet":item.streetName,
      //   "nomineeAddressBuilding":item.buildingName,
      //   "nomineeAddressArea" : item.area,
      //   "nomineeCountryName":item.country,
      //   "nomineeStateName" :item.state,
      //     "nomineeCityName" :item.city,
      //   "nomineeAddressPostal":item.postalCode,
      //   "nomineeRemarks":item.remarks
      // }
      // this.datavalue.push(obj);


      //    });
      //    

      //      this.objdata={

      //         "accessId" : this.localStorage.localStorageGet("loginUserDetails").accessId,
      //         "accountName" : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].main_account_name,
      //         "accountNo" : this.accountnumber,
      //         "customerNo" : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number,
      //         "role" :  this.localStorage.localStorageGet("loginUserDetails").roles.roleName,

      //         "nomineeSupplementaryCardRequestDtoArr" :this.datavalue
      //       }

      // this.apiService.post(this.cdgService.localhostMicroServiceUrl + UrlConstants.SUPPLY_SUBMIT,this.objdata).subscribe((response: any) => {
      //   if (response.status === 200) {
      //     this.notifyService.showSuccess(response.body.successMessage, 'Supplementary Card')


      //   }});
    }
    else {
      this.notifyService.showError('Mandatory Fields cannot be blank', 'Error')
      this.showNomineeForm = true;
    }
  }
  submit() {
    
    if (this.supplementaryForm.invalid) {
      const invalid = [];
      const controls = this.supplementaryForm.controls;
      for (const name in controls) {
        if (controls[name].invalid) {
          invalid.push(name);
        }
      }
      this.notifyService.showError('Mandatory Fields cannot be blank', 'Error')
      return;
    }

    else {
     
      let obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "accountName": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].main_account_name,
        "accountNo": this.accountnumber,
        "customerNo": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,

        "nomineeSupplementaryCardRequestDtoArr":
          [
            {
              
              "nomineeSalutation": this.supplementaryForm.controls.salutation.value,
              "nomineeName": this.supplementaryForm.controls.name.value,
              "nomineeNameOnCard": this.supplementaryForm.controls.nameOnCard.value,
              "nomineePassportNumber": this.supplementaryForm.controls.passportNo.value,
              "nomineeDateOfBirth": this.supplementaryForm.controls.dob.value,
              "nomineeRelationWithCardHolder": this.supplementaryForm.controls.relationToCardHolder.value,
              "nomineeTelephoneNo": this.supplementaryForm.controls.telephone.value,
              "nomineeOfficeNo": this.supplementaryForm.controls.officeNo.value,
              "nomineeMobileNo": this.supplementaryForm.controls.mobNo.value,
              "nomineeEmailId": this.supplementaryForm.controls.email.value,
              "nomineeAddressBlock": this.supplementaryForm.controls.blockNo.value,
              "nomineeAddressUnit": this.supplementaryForm.controls.unitNo.value,
              "nomineeAddressStreet": this.supplementaryForm.controls.streetName.value,
              "nomineeAddressBuilding": this.supplementaryForm.controls.buildingName.value,
              "nomineeAddressArea": this.supplementaryForm.controls.area.value,
              // "nomineeCountryName": this.supplementaryForm.controls.country.value,
              "nomineeCountryName": this.supplementaryForm.controls.countryName.value,
              "nomineeStateName": this.supplementaryForm.controls.state.value,
              "nomineeCityName": this.supplementaryForm.controls.city.value,
              "nomineeAddressPostal": this.supplementaryForm.controls.postalCode.value,
              "nomineeRemarks": this.supplementaryForm.controls.remarks.value
            },

          ]
      }

      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.ENQUIRYFORM, obj).subscribe((response: any) => {
        if (response.status === 200) {

        }
      });

    }
  }
}

