import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';

import { ModulesRoutingModule } from './modules-routing.module';
import { ReportFaultyCardComponent } from './online-services/report-faulty-card/report-faulty-card.component';
import { ReportLostCardComponent } from './online-services/report-lost-card/report-lost-card.component';
import { FeedbackFormComponent } from './online-services/feedback-form/feedback-form.component';
import { CreditlimitandbalanceComponent } from './online-services/creditlimitandbalance/creditlimitandbalance.component';
import { UsagereportComponent } from './view-statement-usage/usagereport/usagereport.component';
import { ChangePasswordComponent } from './online-services/change-password/change-password.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './online-services/reset-password/reset-password.component';
import { VerifyOtpComponent } from './verify-otp/verify-otp.component';
import { ResetSuccessComponent } from './reset-success/reset-success.component';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatCardModule } from '@angular/material/card';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatSortModule } from '@angular/material/sort';
import { MatPaginatorModule } from '@angular/material/paginator';

import { DashboardComponent } from './dashboard/dashboard.component';
import { MatButtonModule } from '@angular/material/button';
import { AccessControlComponent } from './access-control/access-control.component';
import { AccessIdComponent } from './access-id/access-id.component';
// import { EmailConfigurationComponent } from './email-configuration/email-configuration.component';
import { PublicPagesComponent } from './public-pages/public-pages.component';
import { PhConfigComponent } from './ph-config/ph-config.component';
import { RetrieveOtpComponent } from './retrieve-otp/retrieve-otp.component';
// import { AddPhConfigComponent } from './add-ph-config/add-ph-config.component';
import { EmailConfigComponent } from './email-config/email-config.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { AccessRightsComponent } from './access-rights/access-rights.component';
import { EmailConfigEditComponent } from './email-config/email-config-edit/email-config-edit.component';

import { MatChipsModule } from '@angular/material/chips';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatListModule } from '@angular/material/list';
import { SharedModulesModule } from '../shared/shared-modules/shared-modules.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
// import { ToastrModule } from 'ngx-toastr';
import { PublicProductsComponent } from './public-products/public-products.component';
import { CabchargePersonalCardComponent } from './public-products/cabcharge-personal-card/cabcharge-personal-card.component';
import { AccountStatementsComponent } from './view-statement-usage/account-statements/account-statements.component';
import { InvoicesComponent } from './view-statement-usage/invoices/invoices.component';
import { ViewStatementInvoicesComponent } from './view-statement-invoices/view-statement-invoices.component';
import { CabchargeEVoucherComponent } from './public-products/cabcharge-e-voucher/cabcharge-e-voucher.component';
import { TaxiVouchersComponent } from './public-products/taxi-vouchers/taxi-vouchers.component';

import { AccessIdEditComponent } from './access-id/access-id-edit/access-id-edit.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete'
import { MatBadgeModule } from '@angular/material/badge'
import { MatTooltipModule } from '@angular/material/tooltip'
import { MyAccountComponent } from './my-account/my-account.component';
import { ToastrModule } from 'ngx-toastr';
// import { SlickCarouselModule } from 'ngx-slick-carousel';
import { RecentTransactionsComponent } from './my-account/recent-transactions/recent-transactions.component';
import { DownloadInvoicesComponent } from './view-statement-usage/download-invoices/download-invoices.component';
import { AddEditPhConfigComponent } from './ph-config/add-edit-ph-config/add-edit-ph-config.component';
import { DownloadTripReportComponent } from './view-statement-usage/download-trip-report/download-trip-report.component';
import { EditProfileComponent } from './online-services/edit-profile/edit-profile.component';
import { TransactionSummaryComponent } from './view-statement-usage/transaction-summary/transaction-summary.component';
import { ViewCardStatusUsageComponent } from './view-statement-usage/view-card-status-usage/view-card-status-usage.component';
import { DownloadCardHolderListComponent } from './view-statement-usage/download-card-holder-list/download-card-holder-list.component';
import { EnquiryComponent } from './enquiry/enquiry.component';
import { ReportLostFaultyCardComponent } from './online-services/report-lost-faulty-card/report-lost-faulty-card.component'
import { AssignCardToUserComponent } from './online-services/assign-card-to-user/assign-card-to-user.component';
import { AssignCardComponent } from './online-services/assign-card-to-user/assign-card/assign-card.component';
import { ReturnCardComponent } from './online-services/assign-card-to-user/return-card/return-card.component';
import { RequestEvoucherComponent } from './online-services/request-evoucher/request-evoucher.component';
import { ConfirmRequestEvoucherComponent } from './online-services/request-evoucher/confirm-request-evoucher/confirm-request-evoucher.component';
import { TopUpHistoryComponent } from './view-statement-usage/top-up-history/top-up-history.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { SelfServiceTopUpComponent } from './online-services/self-service-top-up/self-service-top-up.component';
import { SelfServiceTopUpDraftComponent } from './online-services/self-service-top-up-draft/self-service-top-up-draft.component';
import { DepartmentalCardComponent } from './online-application/departmental-card/departmental-card.component';
import { IndividualCardComponent } from './online-application/individual-card/individual-card.component';
import { LoyaltyRewardsRedemptionComponent } from './online-services/loyalty-rewards-redemption/loyalty-rewards-redemption.component';
import { CorporateSetupRulesComponent } from './corporate-setup-rules/corporate-setup-rules.component';
import { ViewTransactionCardEvoucherComponent } from './view-statement-usage/view-transaction-card-evoucher/view-transaction-card-evoucher.component';
import { VirtualCardComponent } from './online-application/virtual-card/virtual-card.component';
import { OnlineApplicationComponent } from './online-application/online-application.component';
import { OnlineServicesComponent } from './online-services/online-services.component';
import { ViewStatementUsageComponent } from './view-statement-usage/view-statement-usage.component';
import { ApplicationFormComponent } from './application-form/application-form.component';
import { SupplementaryCardFormComponent } from './application-form/supplementary-card-form/supplementary-card-form.component';
import { MySettingsComponent } from './view-statement-usage/view-transaction-card-evoucher/my-settings/my-settings.component';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { EReceiptRequestComponent } from './view-statement-usage/e-receipt-request/e-receipt-request.component';
import { MonthlySummaryComponent } from './view-statement-usage/usagereport/monthly-summary/monthly-summary.component';
import { DivisionMonthlySummaryComponent } from './view-statement-usage/usagereport/division-monthly-summary/division-monthly-summary.component';
import { MatDialogModule } from '@angular/material/dialog';
import { SetupPoliciesComponent } from './setup-policies/setup-policies.component';
import { SetupPoliciesListComponent } from './setup-policies/setup-policies-list/setup-policies-list.component';
import { CorpSetupRulesComponent } from './setup-policies/corp-setup-rules/corp-setup-rules.component';
import { VirtualIndDeptCardComponent } from './online-application/virtual-ind-dept-card/virtual-ind-dept-card.component';
import { NgxMatDatetimePickerModule, NgxMatTimepickerModule } from '@angular-material-components/datetime-picker';
import { VirtualIndDeptPreviewComponent } from './online-application/virtual-ind-dept-preview/virtual-ind-dept-preview.component';
import { SupplementaryCardPreviewComponent } from './application-form/supplementary-card-preview/supplementary-card-preview.component'
import { SelfServiceErrorCardComponent } from './online-services/self-service-top-up/self-service-error-card/self-service-error-card.component';
import { MatCarouselModule } from '@magloft/material-carousel';
import {MtxDatetimepickerModule} from '@ng-matero/extensions/datetimepicker' //Date and time picker
import { MtxNativeDatetimeModule } from '@ng-matero/extensions/core';
// import { MatTableDataSource } from '@angular/material/table';
// import { MatPaginator } from '@angular/material/paginator';
@NgModule({
  declarations: [ReportFaultyCardComponent,
    ReportLostCardComponent, FeedbackFormComponent, CreditlimitandbalanceComponent,
    UsagereportComponent, ChangePasswordComponent,
    ForgotPasswordComponent, ResetPasswordComponent, VerifyOtpComponent, ResetSuccessComponent,
    DashboardComponent, AccessControlComponent, AccessIdComponent, EmailConfigComponent,
    PublicPagesComponent, PhConfigComponent, AccessRightsComponent,
    EmailConfigEditComponent, PublicProductsComponent, CabchargePersonalCardComponent,
    AccountStatementsComponent, InvoicesComponent,
    ViewStatementInvoicesComponent, MyAccountComponent, AccessIdEditComponent,
    CabchargeEVoucherComponent, TaxiVouchersComponent, RecentTransactionsComponent,
    DownloadInvoicesComponent, AddEditPhConfigComponent, DownloadTripReportComponent,
    EditProfileComponent, TransactionSummaryComponent, ViewCardStatusUsageComponent,
    DownloadCardHolderListComponent, EnquiryComponent, ReportLostFaultyCardComponent,
    AssignCardToUserComponent, AssignCardComponent, TopUpHistoryComponent, ReturnCardComponent, RequestEvoucherComponent, ConfirmRequestEvoucherComponent, SelfServiceTopUpComponent, SelfServiceTopUpDraftComponent, DepartmentalCardComponent, IndividualCardComponent, LoyaltyRewardsRedemptionComponent, CorporateSetupRulesComponent, ViewTransactionCardEvoucherComponent, VirtualCardComponent, OnlineApplicationComponent, OnlineServicesComponent, ViewStatementUsageComponent, ApplicationFormComponent, SupplementaryCardFormComponent, MySettingsComponent, EReceiptRequestComponent, MonthlySummaryComponent, DivisionMonthlySummaryComponent, SetupPoliciesComponent, SetupPoliciesListComponent, CorpSetupRulesComponent, VirtualIndDeptCardComponent, VirtualIndDeptPreviewComponent, SupplementaryCardPreviewComponent, SelfServiceErrorCardComponent, RetrieveOtpComponent],

  // import { MatTableDataSource } from '@angular/material/table';
  // import { MatPaginator } from '@angular/material/paginator';
  // @NgModule({
  //   declarations: [ReportFaultyCardComponent, ReportLostCardComponent, FeedbackFormComponent, CreditlimitandbalanceComponent, UsagereportComponent, VoucherComponent, ChangePasswordComponent, ContactusComponent, ForgotPasswordComponent, ResetPasswordComponent, VerifyOtpComponent, ResetSuccessComponent, DashboardComponent, AccessControlComponent, AccessIdComponent, EmailConfigComponent,PublicPagesComponent,PhConfigComponent,AddPhConfigComponent, AccessRightsComponent, EmailConfigEditComponent, PublicProductsComponent, CabchargePersonalCardComponent, CabchargeEVoucherComponent,AccessIdEditComponent],
  imports: [
    BrowserModule,
    CommonModule,
    ModulesRoutingModule,
    ToastrModule,
    BrowserAnimationsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatRadioModule,
    MatCardModule,
    FlexLayoutModule,
    FormsModule,
    ReactiveFormsModule,
    MatToolbarModule,
    MatAutocompleteModule,
    MatIconModule,
    MatButtonModule,
    MatTableModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
    NgxPaginationModule,
    MatTooltipModule,
    MatChipsModule,
    MatBadgeModule,
    MatTooltipModule,
    MatSortModule,
    MatDividerModule,
    MatPaginatorModule,
    // MatPaginator,
    // MatTableDataSource,
    MatExpansionModule,
    MatListModule,
    SharedModulesModule,
    MatSortModule,
    MatTabsModule,
    MatButtonToggleModule,
    MatCarouselModule,
    // SlickCarouselModule,
    MatSlideToggleModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    NgxMatDatetimePickerModule,
    NgxMatTimepickerModule,
    MtxDatetimepickerModule,
    MtxNativeDatetimeModule
    // MatCarouselModule.forRoot(),
  ],
  exports: [MatSlideToggleModule]
})
export class ModulesModule { }












































