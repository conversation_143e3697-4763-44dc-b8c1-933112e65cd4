import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-setup-policies',
  templateUrl: './setup-policies.component.html',
  styleUrls: ['./setup-policies.component.scss']
})
export class SetupPoliciesComponent implements OnInit {

  constructor(public router: Router) { }

  ngOnInit(): void {
  }
  addPolicies(){
    this.router.navigate(['/layout/setuppolicies/listsetuppolicies']);
  }
}
