<div fxFlex fxLayout="column" class="full-height" (window:resize)="onResize($event)">

  <div fxLayout="row" fxLayoutGap="15px" fxLayoutGap.xs="15px" fxLayoutAlign="center center">
    <div fxFlex="4%">
      <img src="\assets\images\view usage report.png">
    </div>
    <div fxFlex="96%" fxLayoutAlign="start center">
      <!-- fxFlex fxLayout="row" fxLayoutGap="15px" fxFlex.xs=61% fxFlex.md=61%-->
      <div>
        <div class="heading">Setup Policies</div>
      </div>
    </div>
  </div>
  <br>
  <div fxFlex fxLayout="row" fxLayoutAlign="center center">
    <button mat-raised-button class="add-btn" (click)="addNewPolicies()">
      ADD NEW POLICIES
    </button>
  </div>

  <div fxLayout="row" fxLayoutAlign="start center">
  </div>
    <div [hidden]="!isLoader" class="tbl-container mat-elevation-z8">
      <mat-table #table class="mat-table mat-cdk-table" [dataSource]="dataSource" matSort
        (matSortChange)="sortData($event)">
        <ng-container matColumnDef="description">
          <mat-header-cell *matHeaderCellDef class="fwb font-16 pr10" mat-sort-header="description">DESCRIPTION
          </mat-header-cell>
          <mat-cell *matCellDef="let element" class="pr10">
            <div fxLayout="row" fxLayoutGap.xs="15px">
              <div fxFlex.xs="45%">
                <span class="mobile-label"> DESCRIPTION:</span>
              </div>
              <div fxFlex.xs="55%" fxLayoutAlign="center center">
                <span class="fwb font-16">
                  {{element.description}}
                </span>
              </div>
            </div>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="accName">
          <mat-header-cell *matHeaderCellDef class="fwb font-16 pr15">ACCOUNT NAME/CARD NO</mat-header-cell>
          <mat-cell *matCellDef="let element" class="pr15">
            <div fxLayout="row" fxLayoutGap.xs="15px">
              <div fxFlex.xs="45%">
                <span class="mobile-label"> ACCOUNT NAME/CARD NO:</span>
              </div>
              <div fxFlex.xs="55%" fxLayout="column" fxLayoutAlign="center start">
                <span class="fwb font-16  word-break">
                  <b>{{element.accName.title}}</b>
                </span>
                <span class="fwb font-16  word-break" *ngIf="element.accName.title === 'CORPORATE'">
                  {{element.accName.name}} - {{element.accName.customerNo}}
                </span>
                <span class="fwb font-16  word-break" *ngIf="element.accName.title !== 'CORPORATE'">
                  {{element.accName.name}}
                </span>
              </div>
            </div>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="timePolicies">
          <mat-header-cell *matHeaderCellDef class="fwb font-16">TIME POLICIES</mat-header-cell>
          <mat-cell *matCellDef="let element">
            <div fxLayout="row" fxLayoutGap.xs="15px">
              <div fxFlex.xs="45%">
                <span class="mobile-label">TIME POLICIES:</span>
              </div>
              <div fxLayout="column" fxFlex.xs="55%" fxLayoutAlign="center start">
                <ng-container *ngFor="let val of element.timePolicies;let k = index">
                  <div class="fwb font-14 word-break pb5">{{k+1}}. {{val}} </div>
                </ng-container>
              </div>
            </div>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="locationPolicies">
          <mat-header-cell *matHeaderCellDef class="fwb font-16">LOCATION POLICIES</mat-header-cell>
          <mat-cell *matCellDef="let element">
            <div fxLayout="row" fxLayoutGap.xs="15px">
              <div fxFlex.xs="45%">
                <span class="mobile-label"> LOCATION POLICIES:</span>
              </div>
              <div fxLayout="column" fxFlex.xs="55%" fxLayoutAlign="center start">
                <ng-container *ngFor="let val of element.locationPolicies;let j = index">
                  <div class="fwb font-14 pb5" fxLayout="column">
                    <div>
                      {{j+1}}. <span class="font-bold">Pickup : </span>{{val.pickupDesc}}
                    </div>
                    <div>
                      <span class="font-bold">Destination : </span>{{val.destDesc}}
                    </div>
                    <div>
                      <span class="font-bold">Distance : </span>{{val.radius}}km
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="amountCap">
          <mat-header-cell *matHeaderCellDef class="fwb font-16 pr15">AMOUNT CAP</mat-header-cell>
          <mat-cell *matCellDef="let element;let i = index" class="pr15">
            <div fxLayout="row" fxLayoutGap.xs="15px" fxLayoutGap="15px">
              <div fxFlex.xs="45%">
                <span class="mobile-label"> AMOUNT CAP:</span>
              </div>
              <div fxFlex.xs="55%" fxLayoutAlign="center center">
                <span class="fwb font-16">
                  {{element.amountCap}}
                </span>
              </div>
            </div>
          </mat-cell>
        </ng-container>
  
        <ng-container matColumnDef="jobType">
          <mat-header-cell *matHeaderCellDef class="fwb font-16">JOB TYPE</mat-header-cell>
          <mat-cell *matCellDef="let element;let i = index">
            <div fxLayout="row" fxLayoutGap.xs="15px">
              <div fxFlex.xs="45%">
                <span class="mobile-label"> JOB TYPE:</span>
              </div>
              <div fxFlex.xs="55%" fxLayoutAlign="center start">
                <span class="fwb font-16 pl15">
                  {{element.jobType.join(", ")}}
                </span>
              </div>
            </div>
          </mat-cell>
        </ng-container>
  
        <ng-container matColumnDef="policySDate">
          <mat-header-cell *matHeaderCellDef class="fwb font-16 pr10" mat-sort-header="policySDate">POLICY START DATE
          </mat-header-cell>
          <mat-cell *matCellDef="let element;let i = index" class="pr10">
            <div fxLayout="row" fxLayoutGap.xs="15px">
              <div fxFlex.xs="45%">
                <span class="mobile-label"> POLICY START DATE:</span>
              </div>
              <div fxFlex.xs="55%" fxLayoutAlign="center start">
                <span class="fwb font-16 pl10">
                  {{element.policySDate}}
                </span>
              </div>
            </div>
          </mat-cell>
        </ng-container>
  
        <ng-container matColumnDef="policyEDate">
          <mat-header-cell *matHeaderCellDef class="fwb font-16 pr10" mat-sort-header="policyEDate">POLICY END DATE
          </mat-header-cell>
          <mat-cell *matCellDef="let element;let i = index" class="pr10">
            <div fxLayout="row" fxLayoutGap.xs="15px">
              <div fxFlex.xs="45%">
                <span class="mobile-label"> POLICY END DATE:</span>
              </div>
              <div fxFlex.xs="55%" fxLayoutAlign="center start">
                <span class="fwb font-16 pl10">
                  {{element.policyEDate}}
                </span>
              </div>
            </div>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef> ACTIONS</mat-header-cell>
          <mat-cell *matCellDef="let element;let i = index" fxLayout="column">
            <div fxLayout="row" fxLayoutGap="0px">
              <div *ngIf="element.isDisabled === false">
                <button mat-icon-button (click)=onEdit(element,i)>
                  <mat-icon class="enable_btn">mode_edit</mat-icon>
                </button>
              </div>
              <div *ngIf="element.isDisabled === true">
                <button mat-icon-button>
                  <mat-icon class="disable_btn">mode_edit</mat-icon>
                </button>
              </div>
  
              <button mat-icon-button (click)=onDelete(element,i) style="cursor: pointer;">
                <mat-icon class="actn_icon">delete_forever</mat-icon>
              </button>
            </div>
          </mat-cell>
        </ng-container>
  
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" colspan="12" class="align">No Data Found</td>
        </tr>
      </mat-table>
  
      <mat-paginator [pageSize]="5" [pageSizeOptions]="[5, 10, 20, 50, 100]" showFirstLastButtons></mat-paginator>
    </div>
</div>