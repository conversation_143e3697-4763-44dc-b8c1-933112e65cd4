@import '@angular/material/theming';
@import '../../../../styles/colors.scss';
@import '../../../../styles/sizing.scss';
@import '../../../../styles/main.scss';
.title{
    font-weight: bold;
    font-size: 20px;
    line-height: 23px;
}
// .mat-icon{
//     color: $cdgc-font-prime;
// }
.tbl-container{
    display: flex;
    flex-direction: column;
    // max-width: 300px;
    max-height: 500px;
    margin-top:10px;
}
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
.hidden{
    visibility: hidden;
    width: 0;
    height: 0;
    
}
.mat-mdc-header-cell{ 
    background-color: #006BA8;
    color:$white;
}
.heading{
    color: $lightGrey !important;
    font-size: 28px !important;
    font-weight:$cdgsz-font-weight-normal !important ;
   margin-bottom: 25px !important;
  }
  .font-bold{
    font-weight:$cdgsz-font-weight-bold !important ;
  }
  .mobile-label{
    display: none;
}
.mat-column-timePolicies{
word-wrap: break-word !important;
// white-space: unset;
flex : 0 0 16% !important;
width : 16% !important
}
.mat-column-locationPolicies{
    word-wrap: break-word !important;
    // white-space: unset;
    flex : 0 0 20% !important;
    width : 20% !important
    }
    .mat-column-amountCap{
        word-wrap: break-word !important;
        white-space: unset;
        flex : 0 0 5% !important;
        width : 5% !important
        }
.word-break{
    word-break: break-word !important;
}
  @media screen and (max-width: 600px) {
    .inp{
        width: 149px !important;
    }
    .mobile-label{
        display: inline-block;
        // width: 100px;
        font-weight: $cdgsz-font-weight-bold;
        // word-wrap: break-word !important;
        word-break: break-all !important
    }
    .mat-mdc-header-row{
        display: none;
    }
    .mat-mdc-row{
        flex-direction: column;
        align-items: start;
        padding: 8px 24px;
        border-radius: 12px !important;
        border: 1px solid $cdgc-border-prime;
        min-height: 28px !important;
        margin-bottom: 15px;
    }
    mat-cell:first-of-type, mat-header-cell:first-of-type, mat-footer-cell:first-of-type {
        padding-left: 0px !important; 
    }
    .mat-column-timePolicies{
        word-wrap: break-word !important;
        // white-space: unset;
        // flex : 0 0 16% !important;
        width : auto !important
        }
    .mat-column-locationPolicies{
            word-wrap: break-word !important;
            // white-space: unset;
            // flex : 0 0 20% !important;
            width : auto !important
    }
    .mat-column-amountCap{
        word-wrap: break-word !important;
        white-space: unset;
        // flex : 0 0 5% !important;
        width : auto !important
        }
}
.disable_btn{
    color: #00000075 !important;
    cursor: pointer !important;
    transition: color 0.5s ease;
}
.enable_btn{
    color: $cdgc-font-prime !important;
    cursor: pointer !important;
    transition: color 0.5s ease;
}
.actn_icon{
    color: $cdgc-font-prime !important;
    cursor: pointer !important;
}
:host ::ng-deep .mat-sort-header-arrow {
    margin: 0 0 0 0 !important;
    // right: 5px !important;
    transform: none !important;
    opacity: 1 !important;
    color: $cdgc-bg-accent !important;
}
[hidden]{
    display: none;
}