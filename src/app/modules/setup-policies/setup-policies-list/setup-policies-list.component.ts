import { Component, HostListener, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NotificationService } from '../../../shared/services/notification.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { CdgSharedService } from '../../../shared/services/cdg-shared.service';
import { CommonModalComponent } from 'src/app/shared/shared-modules/common-modal/common-modal.component';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { Sort } from '@angular/material/sort';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-setup-policies-list',
  templateUrl: './setup-policies-list.component.html',
  styleUrls: ['./setup-policies-list.component.scss'],
  providers: [DatePipe]
})
export class SetupPoliciesListComponent implements OnInit {

  // setUpData: any[] = [];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  // @ViewChild(MatPaginator, { static: false })
  // set paginator(value: MatPaginator) {
  //   this.paginator = value;
  //   this.dataSource.paginator = value;
  // }
  setUpData: any[] = []
  dataSource = new MatTableDataSource<any>(this.setUpData);
  public displayedColumns = ['description', 'accName', 'timePolicies', 'locationPolicies', 'amountCap', 'jobType', 'policySDate', 'policyEDate', 'action'];
  accountnumber: any;
  timePolicy: any[] = [];
  daysList: any[] = [];
  routePolicy: any[] = [];
  jobTypePolicy: any[] = [];
  loadResponse: any;
  currDate: any
  innerWidth: number;
  isMobLayout: boolean;
  disableVal: boolean=true;
  isLoader: boolean=false;
  constructor(private datePipe: DatePipe,public dialog: MatDialog, public cdgService: CdgSharedService, private router: Router, private notifyService: NotificationService, private apiService: ApiServiceService, public localStorage: LocalStorageService) { }

  ngOnInit(): void {
    const date = new Date()
    this.currDate= this.datePipe.transform(date, 'dd-MM-YYYY')
    this.isLoader=false
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = null;
      }
    }
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 600) { // 768px portrait
      this.isMobLayout = true;
    }
    else {
      this.isMobLayout = false;
    }
    this.getPolicies();

  }
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.innerWidth = window.innerWidth;
    console.log(this.innerWidth)
    if (this.innerWidth <= 600) { // 768px portrait
      this.isMobLayout = true;
    }
    else {
      this.isMobLayout = false;
    }
  }
  getPolicies() {
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      "accountNo": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
      "customerNo": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.LOAD_EXISTING_POLICY, obj).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body;
        this.loadResponse = response.body;
        console.log(opt)
        if (response.body.length > 0) {
          this.setUpData = [];
          response.body.forEach((element: any) => {
            // if (element.policyName === "Policy Without Job Type ") {
            this.timePolicy = [];
            this.routePolicy = [];
            this.jobTypePolicy = [];
            if (element.timePolicyList.length > 0) {
              element.timePolicyList.forEach((timeVal: any) => {
                const daysList = Object.keys(timeVal).filter(o => timeVal[o] === true);
                if (daysList.includes("anytime")) {
                  const days = daysList.filter(p => p !== "anytime")
                  this.timePolicy.push("Anytime every " + days.join(', '))
                }
                else {
                  // const days=daysList.filter(p=>p !== "anytime")
                  this.timePolicy.push(timeVal.timeFrom + " - " + timeVal.timeTo + " every " + daysList.join(', '))
                }
              });
            }
            if (element.routePolicyList.length > 0) {
              element.routePolicyList.forEach((routeVal: any) => {
                let obj = {
                  pickupDesc: routeVal.pickupDesc,
                  destDesc: routeVal.destDesc,
                  radius: routeVal.radius
                }
                this.routePolicy.push(obj)
                // this.routePolicy.push(routeVal.pickupDesc + " - " + routeVal.destDesc + " within " + routeVal.radius)
              });
            }
            if (element.jobTypeList.length > 0) {
              element.jobTypeList.forEach((jobTypeVal: any) => {
                this.jobTypePolicy.push(jobTypeVal.jobTypeName)
              });
            }
            let sVal
            if (element.policyStartDate) {
              sVal = element.policyStartDate.split('-')
              let date = this.currDate.split('-')
              if(Number(sVal[2])<Number(date[2])){
                this.disableVal=true
              }
              else if(Number(sVal[2]) === Number(date[2])){
                if(Number(sVal[1])<Number(date[1])){
                  this.disableVal=true
                }
                else if(Number(sVal[1]) === Number(date[1])){
                  if(Number(sVal[1])<Number(date[1])){
                  this.disableVal=true
                }
                else if(Number(sVal[1]) === Number(date[1])){
                  if(Number(sVal[0])<Number(date[0])){
                    this.disableVal=true
                  }
                  else if(Number(sVal[0]) === Number(date[0])){
                    this.disableVal=true
                  }
                  else if(Number(sVal[0]) > Number(date[0])){
                    this.disableVal=false
                  }
                }
                else if(Number(sVal[1]) > Number(date[1])){
                  this.disableVal=false
                }
                }
                else if(Number(sVal[1]) > Number(date[1])){
                  this.disableVal=false
                }
              }
              else if(Number(sVal[2]) > Number(date[2])){
                this.disableVal=false
              }
            }
            else{
              this.disableVal=true
            }
            // let disable: boolean = false;
            // let classVal="disable_btn"

            let accName = {
              title: "",
              name: "",
              accountNo: "",
              customerNo: ""
            }
            if (element.policyBy === 'A') {
              if (element.accountCategory === "CORP") {
                accName = {
                  title: "CORPORATE",
                  name: element.accountName,
                  accountNo: element.accountNo,
                  customerNo: this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number
                }
              }
              else if (element.accountCategory === "DIV") {
                accName = {
                  title: "DIVISION",
                  name: element.accountName,
                  accountNo: element.accountNo,
                  customerNo: this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number
                }
              }
              else if (element.accountCategory === "DEPT") {
                accName = {
                  title: "DEPARTMENT",
                  name: element.accountName,
                  accountNo: element.accountNo,
                  customerNo: this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number
                }
              }
            }
            else {
              accName = {
                title: "CARD NO",
                name: element.cardList[0].cardNo,
                accountNo: element.accountNo,
                customerNo: this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number
              }
            }
            this.setUpData.push({
              description: element.policyName,
              accName: accName,
              timePolicies: this.timePolicy.length > 0 ? this.timePolicy : [],
              locationPolicies: this.routePolicy.length > 0 ? this.routePolicy : [],
              amountCap: element.amountCap,
              jobType: this.jobTypePolicy.length > 0 ? this.jobTypePolicy : [],
              policySDate: element.policyStartDate,
              policyEDate: element.policyEndDate,
              policyId: element.policyId,
              isDisabled:this.disableVal
            })
            // }
          });
          if(response.body.length === this.setUpData.length){
            this.isLoader=true
          this.dataSource = new MatTableDataSource<any>(this.setUpData);
          console.log(this.dataSource, this.setUpData)
          this.dataSource.paginator = this.paginator;
        }
        else{
          this.isLoader=false
        }
      }


      }
    },
      e => {
        this.notifyService.showError(e.error.errorMessage, "Error")
      })
  }
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  addNewPolicies() {
    this.cdgService.isCorpUpdate = false;
    this.router.navigate(['layout/setuppolicies/setuprules']);
    // setuppolicies/setuprules
  }
  onEdit(row: any, i: any) {
    if (!row.isDisabled) {
      this.cdgService.isCorpUpdate = true;
      const data = this.loadResponse.find((o: any) => o.policyId === row.policyId)
      this.cdgService.setupRulesData = data;
      this.router.navigate(['layout/setuppolicies/setuprules'])
    }
  }
  onDelete(row: any, i: any) {
    const dialogConfig = new MatDialogConfig()
    dialogConfig.data = {
      title: "Confirmation",
      msg: "Are you sure you want to End the Policy?",
      btnClose: "Cancel",
      btnConfirm: "Ok",
      func: 'delete'
    }
    const dialogRef = this.dialog.open(CommonModalComponent, dialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        let obj = {
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          "policyId": row.policyId
        }
        this.apiService.put(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.DELETE_SETUP_POLICIES, obj
        ).subscribe((response: any) => {
          if (response.status === 200) {
            let opt = response.body;
            this.getPolicies();
            this.notifyService.showSuccess(opt.successMessage, "Success")
          }
        },
          e => {
            this.notifyService.showError(e.error.errorMessage, "Error")
          });
      }
    });
  }
  sortData(sort: Sort) {
    const data = this.setUpData.slice();
    if (!sort.active || sort.direction === '') {
      this.dataSource.data = data;
      return;
    }

    this.dataSource.data = data.sort((x, y) => {
      const isAsc = sort.direction === 'asc';
      const sXDate = x.policySDate ? x.policySDate.split("-") : x.policySDate;
      const sYDate = y.policySDate ? y.policySDate.split("-") : y.policySDate;
      const eXDate = x.policyEDate ? x.policyEDate.split("-") : x.policyEDate;
      const eYDate = y.policyEDate ? y.policyEDate.split("-") : y.policyEDate
      console.log(eXDate)
      switch (sort.active) {
        case 'description': return this.compareString(x.description, y.description, isAsc);
        case 'policySDate': return sXDate && sYDate ? this.compare(new Date(sXDate[1] + "-" + sXDate[0] + "-" + sXDate[2]), new Date(sYDate[1] + "-" + sYDate[0] + "-" + sYDate[2]), isAsc) : this.compare(new Date(sXDate), new Date(sYDate), isAsc);
        case 'policyEDate': return eXDate && eYDate ? this.compare(new Date(eXDate[1] + "-" + eXDate[0] + "-" + eXDate[2]), new Date(eYDate[1] + "-" + eYDate[0] + "-" + eYDate[2]), isAsc) : eXDate === null && eYDate !== null ? this.compare(new Date(eXDate), new Date(eYDate[1] + "-" + eYDate[0] + "-" + eYDate[2]), isAsc) : eXDate !== null && eYDate === null ? this.compare(new Date(eXDate[1] + "-" + eXDate[0] + "-" + eXDate[2]), new Date(eYDate), isAsc) : eXDate === null && eYDate === null ? this.compare(new Date(eXDate), new Date(eYDate), isAsc) : 0;
        default: return 0;
      }
    });
  }
  compare(a: Date | string | number, b: Date | string | number, isAsc: boolean) {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1)
  }
  compareString(a: string, b: string, isAsc: boolean) {
    return (a.localeCompare(b)) * (isAsc ? 1 : -1)
  }
}
