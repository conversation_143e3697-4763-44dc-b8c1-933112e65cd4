import { Component, OnInit } from '@angular/core';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as _moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core'
import { DatePipe } from '@angular/common';
import { FormBuilder } from '@angular/forms';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { HttpClient } from '@angular/common/http';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { CommonModalComponent } from 'src/app/shared/shared-modules/common-modal/common-modal.component';
import { Router } from '@angular/router';

export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
}


@Component({
  selector: 'app-corp-setup-rules',
  templateUrl: './corp-setup-rules.component.html',
  styleUrls: ['./corp-setup-rules.component.scss'],
  providers: [DatePipe, { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] }, { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]
})

export class CorpSetupRulesComponent implements OnInit {
  productArr: any = [];
  eDate: any;
  vechicle: any = [];
  fare: any = [];
  job: any = [];
  firstdigit: any;
  lastdigit: any = [];
  list: any;
  dataarray: any = [];
  errorsubmitjob: boolean = false;
  errorlocdes: boolean = false;
  errorlocpick: boolean = false;
  errordestination: boolean = false;
  errorpickup: boolean = false;
  errorsubmitvehicle: boolean = false;
  errorsubmitfare: boolean = false;
  errorsubmitlocation: boolean = false;
  errorsubmitamount: boolean = false;
  errorsubmittime: boolean = false;
  // showDateRangeError: boolean = false;
  // displaycard: any;
  setupEditForm: any = {
    description: "",
    locationPolicies: "",
    timePolicies: "",
    amountCap: '',
    accountName: "",
    jobType: ""
  };
  hrList = [
    { viewValue: '00' }, { viewValue: '01' }, { viewValue: '02' },
    { viewValue: '03' }, { viewValue: '04' }, { viewValue: '05' },
    { viewValue: '06' }, { viewValue: '07' }, { viewValue: '08' },
    { viewValue: '09' }, { viewValue: '10' }, { viewValue: '11' },
    { viewValue: '12' }, { viewValue: '13' }, { viewValue: '14' },
    { viewValue: '15' }, { viewValue: '16' }, { viewValue: '17' },
    { viewValue: '18' }, { viewValue: '19' }, { viewValue: '20' }, { viewValue: '21' }, { viewValue: '22' },
    { viewValue: '23' }
  ]
  minList = [
    { viewValue: '00' }, { viewValue: '01' }, { viewValue: '02' }, { viewValue: '03' }, { viewValue: '04' },
    { viewValue: '05' }, { viewValue: '06' }, { viewValue: '07' }, { viewValue: '08' }, { viewValue: '09' },
    { viewValue: '10' }, { viewValue: '11' }, { viewValue: '12' }, { viewValue: '13' }, { viewValue: '14' },
    { viewValue: '15' }, { viewValue: '16' }, { viewValue: '17' }, { viewValue: '18' }, { viewValue: '19' },
    { viewValue: '20' }, { viewValue: '21' }, { viewValue: '22' }, { viewValue: '23' }, { viewValue: '24' },
    { viewValue: '25' }, { viewValue: '26' }, { viewValue: '27' }, { viewValue: '28' }, { viewValue: '29' },
    { viewValue: '30' }, { viewValue: '31' }, { viewValue: '32' }, { viewValue: '33' }, { viewValue: '34' },
    { viewValue: '35' }, { viewValue: '36' }, { viewValue: '37' }, { viewValue: '38' },
    { viewValue: '39' }, { viewValue: '40' }, { viewValue: '41' }, { viewValue: '42' },
    { viewValue: '43' }, { viewValue: '44' }, { viewValue: '45' },
    { viewValue: '46' }, { viewValue: '47' }, { viewValue: '48' }, { viewValue: '49' },
    { viewValue: '50' }, { viewValue: '51' }, { viewValue: '52' }, { viewValue: '53' }, { viewValue: '54' },
    { viewValue: '55' }, { viewValue: '56' }, { viewValue: '57' }, { viewValue: '58' }, { viewValue: '59' }
  ]
  radiusList = [
    { viewValue: 1 }, { viewValue: 2 }, { viewValue: 3 }, { viewValue: 4 },
    { viewValue: 5 }, { viewValue: 6 }, { viewValue: 7 }, { viewValue: 8 }, { viewValue: 9 },
    { viewValue: 10 }, { viewValue: 11 }, { viewValue: 12 }, { viewValue: 13 }, { viewValue: 14 },
    { viewValue: 15 }, { viewValue: 16 }, { viewValue: 17 }, { viewValue: 18 }, { viewValue: 19 },
    { viewValue: 20 }, { viewValue: 21 }, { viewValue: 22 }, { viewValue: 23 }, { viewValue: 24 },
    { viewValue: 25 }, { viewValue: 26 }, { viewValue: 27 }, { viewValue: 28 }, { viewValue: 29 },
    { viewValue: 30 }, { viewValue: 31 }, { viewValue: 32 }, { viewValue: 33 }, { viewValue: 34 },
    { viewValue: 35 }, { viewValue: 36 }, { viewValue: 37 }, { viewValue: 38 },
    { viewValue: 39 }, { viewValue: 40 }, { viewValue: 41 }, { viewValue: 42 },
    { viewValue: 43 }, { viewValue: 44 }, { viewValue: 45 },
    { viewValue: 46 }, { viewValue: 47 }, { viewValue: 48 }, { viewValue: 49 },
    { viewValue: 50 }, { viewValue: 51 }, { viewValue: 52 }, { viewValue: 53 }, { viewValue: 54 },
    { viewValue: 55 }, { viewValue: 56 }, { viewValue: 57 }, { viewValue: 58 }, { viewValue: 59 }
  ]
  data = {
    policyName: "",
    radioValue: "A",
    divsion: "",
    department: "",
    isMainAcc: false,
    producttype: "",
    cardNo: "",
    startDate: new Date,
    endDate: new Date,
    productArray: [
      {
        producttype: "",
        cardNo: "",
        displaycard: [],
        showError: false
      }
    ],
    time: [
      {
        day: [
          { name: 'Mon', checked: false },
          { name: 'Tue', checked: false },
          { name: 'Wed', checked: false },
          { name: 'Thr', checked: false },
          { name: 'Fri', checked: false },
          { name: 'Sat', checked: false },
          { name: 'Sun', checked: false },
          { name: 'PH', checked: false }
        ],
        isAnyTime: false,
        from: {
          hr: '00',
          min: '00'
        },
        to: {
          hr: '23',
          min: '59'
        },
        timeId: null
      },
    ],
    locations: [
      {
        pickupLocation: "",
        destinationLocation: "",
        // mode: [
        //   { name: 'Pickup', checked: false },
        //   { name: 'Destination', checked: false }
        // ],
        radius: 1,
        destDesc: "",
        destLat: null,
        destLong: null,
        pickupDesc: "",
        pickupLat: null,
        pickupLong: null,
        routeId: null
      }
    ],
    amt: "",
    jobType: [
      { name: '', id: '', valid: null, checked: false, updateChecked: false },
      { name: '', id: '', valid: null, checked: false, updateChecked: false },
      { name: '', id: '', valid: null, checked: false, updateChecked: false }
    ],
    vehicleType: [
      { name: '', id: '', valid: null, checked: false, updateChecked: false },
      { name: '', id: '', valid: null, checked: false, updateChecked: false }
    ],
    fareType: [
      { name: '', id: '', valid: null, checked: false, updateChecked: false },
      { name: '', id: '', valid: null, checked: false, updateChecked: false }
    ],
  }
  radioArr = [
    { value: "A", viewValue: "BY ACCOUNT NO." },
    { value: "C", viewValue: "BY CARD NO." }
  ]
  // divitionArr = [
  //   { value: 'cabincrew', viewValue: 'CABIN CREW (1003)' },
  //   { value: 'corporateoffice', viewValue: 'CORPORATE OFFICE (1005)' },
  //   { value: 'csm1', viewValue: 'CSM1 (1004)' }
  // ];
  // departmentArr = [
  //   { value: 'cabincrew', viewValue: 'CABIN CREW (1003)' },
  //   { value: 'corporateoffice', viewValue: 'CORPORATE OFFICE (1005)' },
  //   { value: 'csm1', viewValue: 'CSM1 (1004)' }
  // ];
  divArray: any[] = [];
  deptArray: any[] = [];
  disableDivDept: boolean = false;
  isCorpInvoice: boolean = false;
  isCabinCrew: boolean;
  frstDivSelection: any;
  fetchDept: any[] = [];
  defaultDept: any;
  isShowDepartment: boolean = false;
  checkIsValidPickuplocation: boolean = false;
  destinationInvalidCheck: boolean = false;
  // cardArr = ['601089-6511-1239', '601089-6511-1237', '601089-6511-1235', '601089-6511-1233', '601089-6511-1231', '601089-6511-1230'];
  cardArr: any = [];
  days = [
    { name: 'Mon', checked: false },
    { name: 'Tue', checked: false },
    { name: 'Wed', checked: false },
    { name: 'Thr', checked: false },
    { name: 'Fri', checked: false },
    { name: 'Sat', checked: false },
    { name: 'Sun', checked: false },
    { name: 'PH', checked: false }
  ]
  // isAnyTime: boolean = false;
  accountnumber: any;
  jobtype: any;
  jobcode: any;
  faretype: any;
  farecode: any;
  vechtype: any;
  vechcode: any;
  vechiclelist: any = [];
  joblist: any = [];
  farelist: any = [];
  routerList: any[] = [];
  companyName: any;
  accountSelection: any;
  timeArray: any[] = [];
  prodList: any[] = [];
  showcarderror: boolean = false;
  noPolicy: any;
  updateSelectCard: any[] = [];
  timeUpdateArray: any[] = [];
  jobUpdatelist: any[] = [];
  fareUpdatelist: any[] = [];
  routerUpdateList: any[] = [];
  vechicleUpdatelist: any[] = [];
  updateTimeNo: number = 0;
  updateData: any;
  cardno: any;
  currDate: Date;
  isUpdateDisable: boolean = false;
  multiLocation: any[] = [];
  updateLocationNo: number = 0;
  endDateUpdate: string;
  constructor(public router: Router, public dialog: MatDialog, private http: HttpClient, private datePipe: DatePipe, public localStorage: LocalStorageService, private apiService: ApiServiceService, private fb: FormBuilder, public cdgService: CdgSharedService, private notifyService: NotificationService) { }

  ngOnInit(): void {

    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = null;
      }
    }
    let curr = new Date();
    this.currDate = new Date()
    // this.currDate = new Date(curr.setDate(curr.getDate()+1));
    // console.log(this.currDate)
    // this.data.startDate=this.currDate;
    // this.data.endDate=new Date("null")
    let prodtypeobj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
      "accountNo": this.accountnumber,
      "masterAccountNo": Number(this.accountnumber)
    }

    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_CORPSETUPPRODUCT, prodtypeobj).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body;

        opt.forEach((element: any) => {
          this.productArr.push(element);
        });
      }
    });
    this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.LOAD_JOBTYPE).subscribe((response: any) => {
      if (response.status === 200) {
        this.localStorage.localStorageSet("typesRep", response.body);
        this.data.jobType = [];
        this.data.vehicleType = [];
        this.data.fareType = [];
        this.noPolicy = this.localStorage.localStorageGet("typesRep").numberOfMaximumPolicies
        this.localStorage.localStorageGet("typesRep").jobTypeList.forEach((element: any) => {
          this.data.jobType.push({ 'name': element.jobTypeName, 'id': element.jobTypeCode, 'valid': element.jobTypeId, 'checked': false, updateChecked: false });
        });
        this.localStorage.localStorageGet("typesRep").fareTypeList.forEach((element: any) => {
          this.data.fareType.push({ 'name': element.fareTypeName, 'id': element.fareTypeCode, 'valid': element.fareTypeId, 'checked': false, "updateChecked":false });
        });
        this.localStorage.localStorageGet("typesRep").vehicleTypeList.forEach((element: any) => {
          this.data.vehicleType.push({ 'name': element.vehicleTypeName, 'id': element.vehicleTypeCode, 'valid': element.vehicleTypeId, 'checked': false, "updateChecked":false });
        });
        if (this.cdgService.isCorpUpdate) {
          if (this.cdgService.setupRulesData.jobTypeList.length > 0) {
            this.cdgService.setupRulesData.jobTypeList.forEach((element: any) => {
              this.data.jobType.forEach((job: any) => {
                if (job.name === element.jobTypeName) {
                  job.checked = true;
                  job.updateChecked = true;
                  job.valid = element.jobTypeId;
                }
              });
            });
          }
          if (this.cdgService.setupRulesData.fareTypeList.length > 0) {
            this.cdgService.setupRulesData.fareTypeList.forEach((element: any) => {
              this.data.fareType.forEach((fare: any) => {
                if (fare.name === element.fareTypeName) {
                  fare.checked = true;
                  fare.updateChecked = true;
                  fare.valid = element.fareTypeId;
                }
              });
            });
          }
          if (this.cdgService.setupRulesData.vehicleTypeList.length > 0) {
            this.cdgService.setupRulesData.vehicleTypeList.forEach((element: any) => {
              this.data.vehicleType.forEach((vehicle: any) => {
                if (vehicle.name === element.vehicleTypeName) {
                  vehicle.checked = true;
                  vehicle.updateChecked = true;
                  vehicle.valid = element.vehicleTypeId;
                }
              });
            });
          }
        }
        this.updateData = JSON.stringify(this.data)
      }
    });

    if (this.cdgService.setupRulesData != null) {
      this.setupEditForm.locationPolicies = this.cdgService.setupRulesData.locationPolicies;
      this.setupEditForm.timePolicies = this.cdgService.setupRulesData.timePolicies;
      this.setupEditForm.amountCap = this.cdgService.setupRulesData.amountCap;
      this.setupEditForm.accountName = this.cdgService.setupRulesData.accName;
      this.setupEditForm.jobType = this.cdgService.setupRulesData.jobType;
      this.setupEditForm.description = this.cdgService.setupRulesData.description;
    }
    console.log(this.cdgService.isCorpUpdate, this.cdgService.setupRulesData)
    this.getDivision();
    if (this.cdgService.isCorpUpdate) {
      // this.editValObj=

      this.data.policyName = this.cdgService.setupRulesData.policyName;
      this.data.radioValue = this.cdgService.setupRulesData.policyBy;
      let sVal = this.cdgService.setupRulesData.policyStartDate.split('-')
      this.data.startDate = new Date(sVal[1] + "-" + sVal[0] + "-" + sVal[2]);
      if (this.cdgService.setupRulesData.policyEndDate !== null) {
        let eVal = this.cdgService.setupRulesData.policyEndDate.split('-')

        this.eDate = new Date(eVal[1] + "-" + eVal[0] + "-" + eVal[2]);
      }
      console.log(this.data, sVal)
      if (this.data.startDate <= this.currDate) {
        this.isUpdateDisable = true;
      }
      else {
        this.isUpdateDisable = false;
      }
      if (this.cdgService.setupRulesData.cardList) {
        this.cdgService.setupRulesData.cardList.forEach((x: any) => {
          this.updateSelectCard.push(x.cardNo)
        });
      }

      console.log(this.updateSelectCard)
      if (this.cdgService.setupRulesData.timePolicyList.length > 0) {
        this.data.time = []
        this.updateTimeNo = this.cdgService.setupRulesData.timePolicyList.length - 1;
        console.log(this.updateTimeNo, this.cdgService.setupRulesData.timePolicyList.length)
        this.cdgService.setupRulesData.timePolicyList.forEach((element: any, i: number) => {
          let sTime = element.timeFrom.split(":");
          let eTime = element.timeTo.split(":");
          this.data.time.push({
            day: [
              { name: 'Mon', checked: element.monday },
              { name: 'Tue', checked: element.tuesday },
              { name: 'Wed', checked: element.wednesday },
              { name: 'Thr', checked: element.thursday },
              { name: 'Fri', checked: element.friday },
              { name: 'Sat', checked: element.saturday },
              { name: 'Sun', checked: element.sunday },
              { name: 'PH', checked: element.publicHoliday }
            ],
            isAnyTime: element.anytime,
            from: {
              hr: sTime[0],
              min: sTime[1]
            },
            to: {
              hr: eTime[0],
              min: eTime[1]
            },
            timeId: element.timeId
          })

        });
      }
      if (this.cdgService.setupRulesData.routePolicyList.length > 0) {
        this.data.locations = [];
        this.updateLocationNo = this.cdgService.setupRulesData.routePolicyList.length - 1;
        this.cdgService.setupRulesData.routePolicyList.forEach((element: any) => {
          console.log(this.data.locations)
          let sLoc = (element.pickupDesc) ? element.pickupDesc.split(" ") : "";
          let eLoc = (element.destDesc) ? element.destDesc.split(" ") : "";
          this.data.locations.push({
            pickupLocation: (sLoc.length > 0) ? sLoc[sLoc.length - 1] : "",
            destinationLocation: (eLoc.length > 0) ? eLoc[eLoc.length - 1] : "",
            radius: element.radius,
            destDesc: element.destDesc,
            destLat: element.destLat,
            destLong: element.destLong,
            pickupDesc: element.pickupDesc,
            pickupLat: element.pickupLat,
            pickupLong: element.pickupLong,
            routeId: element.routeId
          });
        });
      }
      this.data.amt = this.cdgService.setupRulesData.amountCap;
      this.endDateUpdate = JSON.stringify(this.eDate);
      this.updateData = JSON.stringify(this.data);
    }
    else {
      this.data = {
        policyName: "",
        radioValue: "A",
        divsion: "",
        department: "",
        isMainAcc: false,
        producttype: "",
        cardNo: "",
        startDate: this.currDate,
        endDate: new Date(""),
        productArray: [
          {
            producttype: "",
            cardNo: "",
            displaycard: [],
            showError: false
          }
        ],
        time: [
          {
            day: [
              { name: 'Mon', checked: false },
              { name: 'Tue', checked: false },
              { name: 'Wed', checked: false },
              { name: 'Thr', checked: false },
              { name: 'Fri', checked: false },
              { name: 'Sat', checked: false },
              { name: 'Sun', checked: false },
              { name: 'PH', checked: false }
            ],
            isAnyTime: false,
            from: {
              hr: '00',
              min: '00'
            },
            to: {
              hr: '23',
              min: '59'
            },
            timeId: null
          },
        ],
        locations: [
          {
            pickupLocation: "",
            destinationLocation: "",
            // mode: [
            //   { name: 'Pickup', checked: false },
            //   { name: 'Destination', checked: false }
            // ],
            radius: 1,
            destDesc: "",
            destLat: null,
            destLong: null,
            pickupDesc: "",
            pickupLat: null,
            pickupLong: null,
            routeId: null
          }
        ],
        amt: "",
        jobType: [
          { name: '', id: '', valid: null, checked: false, updateChecked: false },
          { name: '', id: '', valid: null, checked: false, updateChecked: false },
          { name: '', id: '', valid: null, checked: false, updateChecked: false }
        ],
        vehicleType: [
          { name: '', id: '', valid: null, checked: false, updateChecked: false },
          { name: '', id: '', valid: null, checked: false, updateChecked: false }
        ],
        fareType: [
          { name: '', id: '', valid: null, checked: false, updateChecked: false },
          { name: '', id: '', valid: null, checked: false, updateChecked: false }
        ],
      }
    }
  }
  onSelect(item: any) {
    console.log(item)
    // this.displaycard = [];
    this.showcarderror = false;

    if (item.value) {
      this.data.productArray.forEach((element: any) => {
        if (element.producttype === item.value) {
          element.displaycard = [];
          let cardnumberobj = {

            "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
            "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
            "roleId": this.localStorage.localStorageGet("loginUserDetails").roles.roleId,
            "productType": element.producttype,
            "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.accountnumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
          }
          this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_CARDLIST, cardnumberobj).subscribe((response: any) => {
            if (response.status === 200) {
              let opt = response.body.cardNo;
              if (opt.length > 0) {
                element.showError = false;
                this.list = response.body.cardNo;

                this.firstdigit = String(opt[0]).substr(0, 10);
                element.displaycard.push(this.firstdigit.substr(0, 6));
                element.displaycard.push(this.firstdigit.substr(6, 4));
                // this.data.productArray.forEach((element: any) => {
                //   if (element.producttype === item.value) {
                //     element.displaycard.push(this.firstdigit.substr(0, 6));
                //     element.displaycard.push(this.firstdigit.substr(6, 4));
                //     console.log(element.displaycard)
                //   }
                // });
              }
              else {
                element.showError = true;


              }
            }
            else {
              // this.showcarderror = true;
              element.showError = true;

            }
          });
          // console.log(element.displaycard)
        }
      });


    }
    else {
      //this.isDisableRadioButton = false;
    }
  }
  keyPress(event: any) {
    this.data.productArray.forEach((val: any) => {
      if (!val.showError) {
        this.cardno = val.cardNo;
      }
    });

    this.cardArr = [];
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "accountNo": this.accountnumber,
      "cardNoList": this.list,
      "cardNoLike": this.firstdigit + this.cardno
      //   "accessId" : "<EMAIL>",
      //   "role" :"ROLE_CORPADMIN",
      // "cardNoList": [   
      //       "****************",    
      //       "****************",
      //       "****************",
      //       "****************"
      //   ],
      // "cardNoLike" : "************"

    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.SEARCH_CARDLIST, obj).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body.cardNo;
        this.cardArr = [];
        opt.forEach((element: any) => {
          this.lastdigit.push((String(element).substr(-6)));
          this.cardArr = this.lastdigit;
          // this.cardArr [(String(element).substr(-6))];
          // this.cardArr=[this.lastdigit];

        });
        this.lastdigit = [];
      }
    });

  }

  checkboxChecked(i: number, j: number, val: any) {
    this.data.time.forEach((element, k) => {
      if (i === k) {
        element.day.forEach((value, l) => {
          if (value.name === val.name) {
            value.checked = val.checked
          }
        });
      }
    });
  }
  addTime() {
    if (this.data.time.length < this.noPolicy) {
      this.data.time.push({
        day: [
          { name: 'Mon', checked: false },
          { name: 'Tue', checked: false },
          { name: 'Wed', checked: false },
          { name: 'Thr', checked: false },
          { name: 'Fri', checked: false },
          { name: 'Sat', checked: false },
          { name: 'Sun', checked: false },
          { name: 'PH', checked: false }
        ],
        isAnyTime: false,
        from: {
          hr: '00',
          min: '00'
        },
        to: {
          hr: '23',
          min: '59'
        },
        timeId: null
      })
    }
    else {
      this.notifyService.showInfo("Max Limit reached for Time Policy", 'Warning')
    }
  }
  // onChecked(isAnyTime: boolean) {
  //   if (isAnyTime) {
  //     this.isAnyTime = true;
  //   }
  // }
  addProductType() {
    if (this.data.productArray.length < this.noPolicy) {
      this.data.productArray.push({
        producttype: "",
        cardNo: '',
        displaycard: [],
        showError: false
      })
    }
    else {
      this.notifyService.showInfo("Max Limit reached for Card Entry", 'Warning')
    }
  }
  deleteProduct(index: number) {
    this.data.productArray.splice(index, 1)
  }
  getDivision() {
    this.isCabinCrew = false;
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "masterAccountNo": Number(this.accountnumber)
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_CORPPOLICY_DIV_DEPT, obj).subscribe((response: any) => {
      if (response.status === 200) {

        let data = response.body;
        this.accountSelection = data.corpAccountDto.mainAccount;

        //if Main account checkbox is checked then dept and divison code should be passed as 
        //  "divisionCode": "",
        // "deptCode": "", while submitting

        if (this.accountSelection == true) {
          this.data.isMainAcc = true;
        }
        else {
          this.data.isMainAcc = false;
        }
        if (data.corpAccountDto == null) {
          if (data.divAccountList.length > 1) {
            var obj = {
              "deptList": [],
              "divAccount": {
                "accountCategory": "",
                "accountCode": "",
                "accountName": "ALL",
                "accountNo": '',
                "nameToDisplayOnDropdown": "ALL"
              }
            }
            this.divArray.push(obj)

            data.divAccountList.forEach((element: any) => {
              this.divArray.push(element.divAccount);
            });
            this.frstDivSelection = this.divArray[0];
          }
        }
        if (data.corpAccountDto) {
          if (Object.keys(data.corpAccountDto).length > 0) {
            // let obj = {
            //   accountCategory: data.corpAccountDto.accountCategory,
            //   accountCode: data.corpAccountDto.accountCode,
            //   accountName: data.corpAccountDto.accountName,
            //   accountNo: data.corpAccountDto.accountNo,
            //   nameToDisplayOnDropdown: data.corpAccountDto.nameToDisplayOnDropdown,
            // nameToDisplayOnDropdown: "-CORPORATE INVOICE-"
            // }

            // this.divArray.push(obj);
            this.isCorpInvoice = true;
          }

        }

        if (data.divAccountList) {
          if (data.divAccountList.length > 0) {
            data.divAccountList.forEach((element: any) => {
              this.fetchDept.push(element);
              this.divArray.push(element.divAccount);

              this.frstDivSelection = this.divArray[0];
              // this.divArray.push({
              //   accountCategory: "",
              //   accountCode: "",
              //   accountName: "",
              //   accountNo: "",
              //   nameToDisplayOnDropdown: this.divArray[0]
              // })
              // this.divArray.push(this.divArray[0]);

              // this.divArray.push(this.divArray[0]);

              if (element.deptList.length > 0) {
                this.selectDept(this.divArray[0]);
              }
            });
          }
        }
      }
    });


  }
  selectDept(value: any) {
    this.isShowDepartment = false;
    this.isCorpInvoice = false;
    // this.isCabinCrew = false;
    if (value) {
      if (value.nameToDisplayOnDropdown === "-CORPORATE INVOICE-" || value.nameToDisplayOnDropdown === "") {
        this.isCorpInvoice = true;
      }
      if (value.nameToDisplayOnDropdown !== "ALL" && value.nameToDisplayOnDropdown !== "-CORPORATE INVOICE-" && value.nameToDisplayOnDropdown !== "") {
        const obj = this.fetchDept.find(x => x.divAccount.nameToDisplayOnDropdown === value.nameToDisplayOnDropdown)
        this.deptArray = [];
        if (obj.deptList.length > 0) {
          this.isShowDepartment = true;
          this.isCabinCrew = true;
          this.deptArray.push({
            accountCategory: "",
            accountCode: "",
            accountName: "",
            accountNo: "",
            nameToDisplayOnDropdown: "-DIVISION INVOICE-"
          })
          //if dept selected value is selected as -DIVISION INVOICE- pass deptCode as deptCode:"" during submit
          obj.deptList.forEach((val: any) => {
            this.deptArray.push(val);
          });
          this.defaultDept = this.deptArray[0];
        }
      }
    }
  }
  deleteTime(index: number) {
    this.data.time.splice(index, 1)
  }
  addLocation() {
    if (this.data.locations.length < this.noPolicy) {
      this.data.locations.push({
        pickupLocation: "",
        destinationLocation: "",
        radius: 1,
        destDesc: "",
        destLat: null,
        destLong: null,
        pickupDesc: "",
        pickupLat: null,
        pickupLong: null,
        routeId: null
      });
    }
    else {
      this.notifyService.showInfo("Max Limit reached for Location Policy", 'Warning')
    }
  }
  deleteLocation(index: number) {
    this.data.locations.splice(index, 1)

  }
  cancel() {
    this.cdgService.isCorpUpdate = false;
    this.router.navigate(['/layout/setuppolicies/listsetuppolicies']);
    // this.notifyService.showSuccess('Rules Added Successfully', 'Success')
  }
  geterror() {
    this.errordestination = false;
    this.errorpickup = false;

    this.errorlocdes = false;
    this.errorlocpick = false;
    console.log(this.data)
    if (this.data.locations.length > 1) {
      this.multiLocation = [];
      this.data.locations.forEach((element: any) => {
        let obj = {
          pickupLocation: element.pickupLocation,
          destinationLocation: element.destinationLocation
        }
        this.multiLocation.push(obj)
      });
      console.log(this.multiLocation)
      let loc = this.multiLocation.filter((v: any, i: any, a: any) => a.findIndex((t: any) => (t.pickupLocation === v.pickupLocation && t.destinationLocation === v.destinationLocation)) === i)
      console.log(loc)
      if (loc.length < this.multiLocation.length) {
        this.errorlocpick = true;
      }
      else {
        this.errorlocpick = false;
      }
    }
    this.dataarray = Object.values(this.data);
    // this.dataarray[11].map((element: any) => {
    //   if (this.dataarray[11].length > 1) {

    //     if (this.dataarray[11][0].pickupLocation === this.dataarray[11][1].pickupLocation) {
    //       this.errorlocpick = true;
    //     }
    //     else if (this.dataarray[11][0].pickupLocation === this.dataarray[11][2].pickupLocation) {
    //       this.errorlocpick = true;
    //     }
    //     else {
    //       this.errorlocpick = false;
    //     }

    //     if (this.dataarray[11][0].destinationLocation === this.dataarray[11][1].destinationLocation) {
    //       this.errorlocdes = true;
    //     }
    //     else if (this.dataarray[11][0].destinationLocation === this.dataarray[11][2].destinationLocation) {
    //       this.errorlocdes = true;
    //     }
    //     else {
    //       this.errorlocdes = false;
    //     }
    //   }
    // });
    this.dataarray[11].forEach((element: any) => {
      if (element.pickupLocation !== null) {
        if (element.pickupLocation !== "" && element.destinationLocation === "") {
          this.errordestination = true;
        }

        else {
          this.errordestination = false;
        }
      }
      if (element.destinationLocation !== null) {
        if (element.pickupLocation === "" && element.destinationLocation !== "") {
          this.errorpickup = true;
        }
        else {
          this.errorpickup = false;
        }
      }

    });
    if (this.dataarray[13]) {
      this.errorsubmittime = true;
      this.dataarray[10].forEach((element: any) => {
        element.day.forEach((element: any) => {
          if (element.checked === true) {
            this.errorsubmittime = false;
          }
        });
      });
      this.errorsubmitlocation = true;
      this.dataarray[11].forEach((element: any) => {
        if (element.pickupLocation !== "" || element.destinationLocation !== "") {
          this.errorsubmitlocation = false;
        }

      });

      if (this.dataarray[12] !== "") {
        this.errorsubmitamount = false;
      }
      else {
        this.errorsubmitamount = true;
      }


      if (this.dataarray[13][0].checked == true || this.dataarray[13][1].checked == true || this.dataarray[13][2].checked == true) {

        this.errorsubmitjob = false;
      }
      else {
        this.errorsubmitjob = true;
      }

      if (this.dataarray[14][0].checked == true || this.dataarray[14][1].checked == true) {

        this.errorsubmitvehicle = false;
      }
      else {
        this.errorsubmitvehicle = true;
      }

      if (this.dataarray[15][0].checked == true || this.dataarray[15][1].checked == true) {

        this.errorsubmitfare = false;
      }
      else {
        this.errorsubmitfare = true;
      }
      // this.notifyService.showWarning('Please input any field', 'Warning')
      // return;
    }
  }
  resetVal(val: any) {
    if (val === "A") {
      this.data.productArray = [];
      this.data.productArray.push(
        {
          producttype: "",
          cardNo: "",
          displaycard: [],
          showError: false
        }
      )
      // this.data.cardNo = "";
    }
    else {
      this.frstDivSelection = this.divArray[0]
      this.defaultDept = "";
      this.isShowDepartment = false;

    }
  }
  submit(val: any) {
    this.showcarderror = false;
    let timeArr: any[] = [];
    let locationArr: any[] = [];
    let vehicleArr: any[] = [];
    let jobtypeArr: any[] = [];
    let fareArr: any[] = [];
    if (this.data.locations[0].pickupLocation === null && this.data.locations[0].destinationLocation === null) {
      this.data.locations = [];
    }
    // if(!val){
    // this.notifyService.showWarning('Please fill Mandatory field', 'Warning')
    //   return;
    // }

    if (this.errorsubmitamount === true && this.errorsubmitlocation === true && this.errorsubmitjob === true && this.errorsubmittime === true && this.errorsubmitvehicle === true && this.errorsubmitfare === true) {
      this.notifyService.showWarning('Please fill any one of the field Amount,Job Type,Vechicle Type,Fare Type,Location,Time', 'Warning')
      return;
    }

    if(this.destinationInvalidCheck || this.checkIsValidPickuplocation){
        this.notifyService.showWarning('Please enter a valid pickup or destination location', 'Warning');
        return;
    }

    this.data.time.forEach((val: any) => {
      const data = val.day.filter((o: any) => o.checked === true);
      if (data.length > 0) {
        timeArr.push(true)
      }
    });
    // let loc=[...new Set(this.data.locations.map((o:any)=>{
    //   if(o.pickupLocation && o.destinationLocation){
    //     return o
    //   }}))]
    // let loc=Array.from(new Set(this.data.locations.map((o:any)=> o.pickupLocation)))
    // console.log("helloo",loc)
    this.data.locations.forEach((val: any) => {
      if (val.destinationLocation && val.pickupLocation) {
        locationArr.push(true)
      }
    });
    this.data.vehicleType.forEach((val: any) => {
      if (val.checked) {
        vehicleArr.push(true)
      }
    });
    this.data.jobType.forEach((val: any) => {
      if (val.checked) {
        jobtypeArr.push(true)
      }
    });
    this.data.fareType.forEach((val: any) => {
      if (val.checked) {
        fareArr.push(true)
      }
    });
    // 
    if (val && (this.data.amt !== "" || timeArr.length === this.data.time.length || locationArr.length === this.data.locations.length || vehicleArr.length > 0 || jobtypeArr.length > 0 || fareArr.length > 0)) {

      this.vechiclelist = [];
      // this.vechicle=[];
      this.timeArray = [];
      this.job = [];
      this.joblist = [];
      this.farelist = [];
      // this.fare=[];
      this.routerList = [];
      this.timeArray = [];
      let obj = {};
      this.data.jobType.filter((element: any) => {
        if (element.checked === true) {
          this.job.push(element);
        }
      });
      this.job.forEach((element: any) => {
        this.joblist.push({

          "jobTypeId": element.valid,
          "jobTypeCode": element.id,
          "jobTypeName": element.name
        });
      });
      this.data.fareType.filter((element: any) => {
        if (element.checked === true) {
          this.fare.push(element);
        }

      });
      this.fare.forEach((element: any) => {
        this.farelist.push({
          "fareTypeId": element.valid,
          "fareTypeCode": element.id,
          "fareTypeName": element.name
        });
      });
      this.data.vehicleType.filter((element: any) => {
        if (element.checked === true) {
          this.vechicle.push(element);
        }

      });
      this.data.locations.forEach((element: any) => {
        if (element.pickupLocation !== "" || element.destinationLocation !== "") {
          this.routerList.push(
            {
              radius: element.radius,
              destDesc: element.destDesc,
              destLat: element.destLat,
              destLong: element.destLong,
              pickupDesc: element.pickupDesc,
              pickupLat: element.pickupLat,
              pickupLong: element.pickupLong,
              routeId: element.routeId
            }
          )
        }
      });
      this.vechicle.forEach((element: any) => {
        this.vechiclelist.push({

          "vehicleTypeId": element.valid,
          "vehicleTypeCode": element.id,
          "vehicleTypeName": element.name
        });
      });
      this.data.time.forEach((element: any) => {
        let data: any[] = []
        data = element.day.filter(
          (o: any) => o.checked === true
        );
        if (data.length > 0) {
          let sTimeHr = element.from.hr;
          let sTimeMin = element.from.min
          let eTimeHr = element.to.hr
          let eTimeMin = element.to.min
          this.timeArray.push({
            "anytime": element.isAnyTime,
            "friday": element.day[4].checked,
            "monday": element.day[0].checked,
            "publicHoliday": element.day[7].checked,
            "saturday": element.day[5].checked,
            "sunday": element.day[6].checked,
            "thursday": element.day[3].checked,
            "timeFrom": sTimeHr + ":" + sTimeMin,
            "timeId": element.timeId,
            "timeTo": eTimeHr + ":" + eTimeMin,
            "tuesday": element.day[1].checked,
            "wednesday": element.day[2].checked
          })
        }
      });
      // this.notifyService.showSuccess("yeppee", "success")
      // console.log(obj, "I am readyyy")
      if (this.data.radioValue === "A") {
        obj = {
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "accountNo": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
          "amountCap": this.data.amt,
          "cardList": [],
          "customerNo": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number,
          "deptCode": this.defaultDept ? this.defaultDept.accountCode.toString() : "",
          "divisionCode": this.frstDivSelection?.accountCode ? this.frstDivSelection?.accountCode.toString() : "",
          "errorMessage": "",
          "fareTypeList": this.farelist,
          "jobTypeList": this.joblist,
          "mainAccountSelected": this.data.isMainAcc,
          "policyBy": this.data.radioValue,
          "policyEndDate": this.eDate ? this.datePipe.transform(this.eDate, "dd-MM-YYYY") : null,
          "policyId": null,
          "policyName": this.data.policyName,
          "policyNameBeforeUpdate": "",
          "policyStartDate": this.datePipe.transform(this.data.startDate, "dd-MM-YYYY"),
          "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          "routePolicyList": this.routerList,
          "successMessage": "",
          "timePolicyList": this.timeArray,
          "vehicleTypeList": this.vechiclelist,
          "accountList": null,
          "overWrite": false,
          "accountNoList": [],
        }
      }
      else {
        this.prodList = [];
        this.data.productArray.forEach((val: any) => {
          if (!val.showError) {
            this.prodList.push({
              "cardNo": val.displaycard[0] + val.displaycard[1] + val.cardNo,
              "cardNoFrom": null,
              "cardNoTo": null,
              "productTypeId": val.producttype
            })
          }
        });
        obj = {
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "accountNo": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no,
          "amountCap": this.data.amt,
          "cardList": this.prodList,
          "customerNo": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number,
          "deptCode": null,
          "divisionCode": null,
          "errorMessage": "",
          "fareTypeList": this.farelist,
          "jobTypeList": this.joblist,
          "mainAccountSelected": false,
          "policyBy": this.data.radioValue,
          "policyEndDate": this.eDate ? this.datePipe.transform(this.eDate, "dd-MM-YYYY") : null,
          "policyId": null,
          "policyName": this.data.policyName,
          "policyNameBeforeUpdate": "",
          "policyStartDate": this.datePipe.transform(this.data.startDate, "dd-MM-YYYY"),
          "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          "routePolicyList": this.routerList,
          "successMessage": "",
          "timePolicyList": this.timeArray,
          "vehicleTypeList": this.vechiclelist
        }
      }


      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.SUBMITSETUPRULES, obj).subscribe((response: any) => {
        if (response.status === 200) {
          console.log(response)
          let opt = response.body;
          if (opt.policyBy === 'A') {
            if (opt.existingPolicy) {
              const dialogConfig = new MatDialogConfig()
              dialogConfig.data = {
                title: "Confirmation",
                msg: opt.successMessage,
                btnClose: "No",
                btnConfirm: "Yes",
                func: 'success'
              }
              const dialogRef = this.dialog.open(CommonModalComponent, dialogConfig);
              dialogRef.afterClosed().subscribe(result => {
                // if (result) {
                console.log(result)
                opt['overWrite'] = result ? result : opt['overWrite'];
                let obj = opt;
                this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.SAVE_CORPORATE_POLICY, obj).subscribe((response: any) => {
                  if (response.status === 200) {
                    let opt = response.body;
                    if (opt.successMessage) {
                      this.notifyService.showSuccess(opt.successMessage, "Success")
                      this.router.navigate(['/layout/setuppolicies/listsetuppolicies']);
                    }
                    else {
                      this.notifyService.showError(opt.errorMessage, "Error")
                    }
                  }
                },
                  e => {
                    this.notifyService.showError(e.error.errorMessage, "Error")
                  })
                // }
              });

            } else {
              this.notifyService.showSuccess(opt.successMessage, 'Success')
              this.router.navigate(['/layout/setuppolicies/listsetuppolicies']);
            }
          }
          else if (opt.policyBy === 'C') {
            const dialogConfig = new MatDialogConfig()
            dialogConfig.data = {
              title: "Confirmation",
              msg: opt.successMessage,
              btnClose: "Cancel",
              btnConfirm: "Ok",
              func: 'success'
            }
            const dialogRef = this.dialog.open(CommonModalComponent, dialogConfig);
            dialogRef.afterClosed().subscribe(result => {
              if (result) {
                this.notifyService.showSuccess(opt.successMessage, 'Success');
                this.router.navigate(['/layout/setuppolicies/listsetuppolicies']);
              }
            })
          }
          else {
            this.notifyService.showSuccess(opt.successMessage, 'Success')
            this.router.navigate(['/layout/setuppolicies/listsetuppolicies']);
          }
        }
      }, e => {
        this.notifyService.showError(e.error.errorMessage, "Error")
      }
      );
    }
    else {
      this.notifyService.showWarning('Please fill Mandatory field and select atleast one policy', 'Warning')
      return;
    }
  }
  getLocationDetails(selectedLocation: any, type: string) {
    if(selectedLocation === ""){
      this.checkIsValidPickuplocation = (type === "Pickup") ? false : this.destinationInvalidCheck;
      this.destinationInvalidCheck = (type === "Destination") ? false : this.destinationInvalidCheck;
      return;
    }
    this.http.get("https://www.onemap.gov.sg/api/common/elastic/search?searchVal=" + selectedLocation + "&returnGeom=Y&getAddrDetails=Y&pageNum=1").subscribe((response: any) => {
      this.data.locations.forEach(element => {
        if (element.pickupLocation === selectedLocation) {        
            element.pickupDesc = (response.results.length) ? response.results[0].ADDRESS : "",
            element.pickupLat = (response.results.length) ? response.results[0].LATITUDE : null,
            element.pickupLong = (response.results.length) ? response.results[0].LONGITUDE : null
            this.checkIsValidPickuplocation = (response.results.length == 0) ? true : false; 
        }
        else if (element.destinationLocation === selectedLocation) {  
            element.destDesc = (response.results.length) ? response.results[0].ADDRESS : "",
            element.destLat = (response.results.length) ? response.results[0].LATITUDE : null,
            element.destLong = (response.results.length) ? response.results[0].LONGITUDE : null
            this.destinationInvalidCheck = (response.results.length == 0) ? true : false;
        }
      });
    });
  }
  update(val: any) {
    // this.geterror();
    if (!val) {
      this.notifyService.showWarning('Please fill Mandatory field and select atleast one policy', 'Warning')
      return;
    }
    if(this.destinationInvalidCheck || this.checkIsValidPickuplocation){
      this.notifyService.showWarning('Please enter a valid pickup or destination location', 'Warning');
      return;
    }
    // else if (this.errordestination === true) {
    //   this.notifyService.showWarning('Please Enter Destination Location', 'Warning')
    //   return;
    // }
    // else if (this.errorpickup === true) {
    //   this.notifyService.showWarning('Please Enter Pickup Location', 'Warning')
    //   return;
    // }
    // else if (this.errorlocpick) {
    //   this.notifyService.showWarning('Cannot add same Pickup and Destination Location', 'Warning')
    //   return;
    // }
    else {
      console.log(this.updateData, JSON.stringify(this.data))
      if (this.updateData === JSON.stringify(this.data) && this.endDateUpdate === JSON.stringify(this.eDate)) {
        this.notifyService.showWarning('Please make changes to Update', 'Warning')
        return;
      }
      else {
        this.vechicleUpdatelist = [];
        this.fareUpdatelist = [];
        this.jobUpdatelist = [];
        this.routerUpdateList = [];
        this.timeUpdateArray = [];
        if (this.data.locations[0].pickupLocation === null && this.data.locations[0].destinationLocation === null) {
          this.data.locations = [];
        }
        this.data.time.forEach((element: any) => {
          let data: any[] = []
          data = element.day.filter(
            (o: any) => o.checked === true
          );
          if (data.length > 0) {
            let sTimeHr, sTimeMin, eTimeHr, eTimeMin
            if (element.isAnyTime) {
              sTimeHr = "00";
              sTimeMin = "00"
              eTimeHr = "23"
              eTimeMin = "59"
            }
            else {
              sTimeHr = element.from.hr;
              sTimeMin = element.from.min
              eTimeHr = element.to.hr
              eTimeMin = element.to.min
              // element.to.hr <= 9 ? (element.to.min.toString().includes('0') ? element.to.min : "0" + element.to.min) : element.to.min
            }
            this.timeUpdateArray.push({
              "anytime": element.isAnyTime,
              "friday": element.day[4].checked,
              "monday": element.day[0].checked,
              "publicHoliday": element.day[7].checked,
              "saturday": element.day[5].checked,
              "sunday": element.day[6].checked,
              "thursday": element.day[3].checked,
              "timeFrom": sTimeHr + ":" + sTimeMin,
              "timeId": element.timeId,
              "timeTo": eTimeHr + ":" + eTimeMin,
              "tuesday": element.day[1].checked,
              "wednesday": element.day[2].checked
            })
          }
        });
        this.data.jobType.filter((element: any) => {
          if (element.checked === true) {
            this.job.push(element);
          }
        });
        this.job.forEach((element: any) => {
          this.jobUpdatelist.push({

            "jobTypeId": element.valid,
            "jobTypeCode": element.id,
            "jobTypeName": element.name
          });
        });
        this.data.fareType.filter((element: any) => {
          if (element.checked === true) {
            this.fare.push(element);
          }

        });
        this.fare.forEach((element: any) => {
          this.fareUpdatelist.push({
            "fareTypeId": element.valid,
            "fareTypeCode": element.id,
            "fareTypeName": element.name
          });
        });
        this.data.vehicleType.filter((element: any) => {
          if (element.checked === true) {
            this.vechicle.push(element);
          }

        });
        this.data.locations.forEach((element: any) => {
          if (element.pickupLocation !== "" || element.destinationLocation !== "") {
            this.routerUpdateList.push(
              {
                radius: element.radius,
                destDesc: element.destDesc,
                destLat: element.destLat,
                destLong: element.destLong,
                pickupDesc: element.pickupDesc,
                pickupLat: element.pickupLat,
                pickupLong: element.pickupLong,
                routeId: element.routeId
              }
            )
          }
        });
        this.vechicle.forEach((element: any) => {
          this.vechicleUpdatelist.push({

            "vehicleTypeId": element.valid,
            "vehicleTypeCode": element.id,
            "vehicleTypeName": element.name
          });
        });
        let obj = {
          "policyBy": this.data.radioValue,  //mandatory
          "accessId": this.cdgService.setupRulesData.accessId, //mandatory
          "role": this.cdgService.setupRulesData.role, //mandatory
          "accountNo": this.cdgService.setupRulesData.accountNo,
          "customerNo": this.cdgService.setupRulesData.customerNo,
          "accountName": this.cdgService.setupRulesData.accountName,
          "policyId": this.cdgService.setupRulesData.policyId,   //mandatory
          "policyName": this.data.policyName,
          "cardList": this.cdgService.setupRulesData.cardList ? this.cdgService.setupRulesData.cardList : [],
          "divisionCode": this.cdgService.setupRulesData.divisionCode,
          "deptCode": this.cdgService.setupRulesData.deptCode,
          "amountCap": this.data.amt,
          "policyStartDate": this.datePipe.transform(this.data.startDate, "dd-MM-YYYY"),
          "policyEndDate": this.eDate ? this.datePipe.transform(this.eDate, "dd-MM-YYYY") : null,
          "timePolicyList": this.timeUpdateArray.length > 0 ? this.timeUpdateArray : [],
          "routePolicyList": this.routerUpdateList.length > 0 ? this.routerUpdateList : [],
          "jobTypeList": this.jobUpdatelist.length > 0 ? this.jobUpdatelist : [],
          "fareTypeList": this.fareUpdatelist.length > 0 ? this.fareUpdatelist : [],
          "vehicleTypeList": this.vechicleUpdatelist.length > 0 ? this.vechicleUpdatelist : [],
          "successMessage": this.cdgService.setupRulesData.successMessage,
          "errorMessage": this.cdgService.setupRulesData.errorMessage,
          "accountList": this.cdgService.setupRulesData.accountList,
          "overWrite": this.cdgService.setupRulesData.overWrite,
          "accountNoList": this.cdgService.setupRulesData.accountNoList,
          "existingPolicy": this.cdgService.setupRulesData.existingPolicy,
          "mainAccountSelected": this.cdgService.setupRulesData.mainAccountSelected
        }
        console.log(obj)
        this.apiService.put(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.UPDATE_SETUP_POLICIES, obj).subscribe((response: any) => {
          if (response.status === 200) {
            console.log(response)
            if (response.body.successMessage) {
              this.notifyService.showSuccess(response.body.successMessage, "Success");
              this.router.navigate(['/layout/setuppolicies/listsetuppolicies']);
            }
            else {
              this.notifyService.showError(response.body.errorMessage, "Error")
            }
          }
        }, e => {
          this.notifyService.showError(e.error.errorMessage, "Error")
        }
        );
      }
    }
  }
  // daysDifference() {
  //   if ((this.data.startDate) > (this.data.endDate)) {
  //     this.showDateRangeError = true;
  //   }
  //   else {
  //     this.showDateRangeError = false;
  //   }
  // }
}
