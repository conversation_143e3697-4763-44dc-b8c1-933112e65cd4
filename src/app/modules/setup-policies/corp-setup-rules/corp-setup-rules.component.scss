@import '@angular/material/theming';
@import '../../../../styles/colors.scss';
@import '../../../../styles/sizing.scss';
@import '../../../../styles/main.scss';
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
.astreik{
    color:$red;
}

:host ::ng-deep .main-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
}
/*    Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
 }
 .inp{
    width: 350px !important;
}
.location-inp{
    width: 350px !important;
} 

.sub-header{
    color: $lightGrey !important;
    font-size: 24px;
    font-weight:$cdgsz-font-weight-intermediate ;
}
.submit-btn{
    width:205px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 5px !important;
}
.cancel-btn{
    width:205px;
    background-color:$cdgc-font-accent !important;
    color: $cdgc-bg-hue !important;
    border: 1px solid $cdgc-bg-hue !important;
    border-radius: 5px !important; 
}
.header-div{
    background-color: $cdgc-bg-prime;
    color: $cdgc-font-accent;
    border-radius: 5px;
    padding: 6px 25px;
    font-size: 19px;
    font-weight: $cdgsz-font-weight-bold;
   }
//    :host :ng-deep .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
//     border-color: $lightGrey !important;
// }
    /*    Change_by_navani (mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version.*/
//     :host ::ng-deep .mat-mdc-radio-button.mat-accent.mat-mdc-radio-checked .mat-mdc-radio-inner-circle{
//     background-color: #000000 !important;
//     // border: 1px solid rgba(0, 0, 0, 0.54) !important;

// }
// /*    Change_by_navani (mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version.*/
// :host ::ng-deep .mat-mdc-radio-button.mat-accent.mat-mdc-radio-checked .mat-mdc-radio-outer-circle {
//     border-color:$lightGrey !important;
// }
.date-inp{
  width: 178px;
}
:host input[type=number]::-webkit-outer-spin-button{
    opacity: 1 !important;
}
:host input[type=number]::-webkit-inner-spin-button{
    opacity: 1 !important;
}
.num-inp{
    width: 90px;
}
.add-btn{
    width:140px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 5px !important;
}
.add-icon{
    color: $cdgc-font-accent !important;
    font-size: 24 !important;
}
.heading{
    font-size: 19px;
    font-weight: $cdgsz-font-weight-normal;
}

@media screen and (max-width: 800px) {
    .submit-btn{
        width:158px;
    }
    .cancel-btn{
        width:158px; 
    }
    .inp{
        width: 230px !important;
    }
    .location-inp{
        width: 260px !important;
    } 
}



@media screen and (max-width:700px){
    .inp{
        width: 190px !important;
    }
}

@media screen and (max-width:500px){
    .inp{
        width: 170px !important;
    }
    .date-inp{
        width: 156px !important;
    }
    .submit-btn{
        width:125px !important;
    }
    .cancel-btn{
        width:125px !important; 
    }

    .location-inp{
        width: 210px !important;
    }
}
// .error {
//     font-size: 12px;
// }
.error{
    font-size: $cdgsz-font-size-sm;
    color: $cdgc-font-warn;
}
.pl{
    padding-left: 142px !important;
}
/*    Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-subscript-wrapper {
     padding: 0 0 !important; 
}
.validationfix{
    color: #ff0000;
    font-weight: 600;
    font-size: 75%;
    position: absolute;
    margin-left: -13px;
}