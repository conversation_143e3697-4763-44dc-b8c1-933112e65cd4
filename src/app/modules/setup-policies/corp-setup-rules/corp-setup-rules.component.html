<div fxLayout="column" fxLayoutGap="50px">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="main-icon">list</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">Manage Corporate Rule</span>
        </div>
    </div>
    <form #g="ngForm" name="form">
        <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">

            <div fxFlex fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap.lt-md="30px">
                <div fxFlex fxLayoutAlign="start center">
                    <span class="sub-header">New Rule</span>
                </div>
                <div fxFlex fxLayoutAlign="end center" fxLayout="row" fxLayoutGap="15px">
                    <div>
                        <button mat-raised-button class="cancel-btn" (click)="cancel()">CANCEL
                        </button>
                    </div>
                    <div *ngIf="!cdgService.isCorpUpdate">
                        <button mat-raised-button class="submit-btn" (click)="submit(g.form.valid)">SUBMIT
                        </button>
                    </div>
                    <div *ngIf="cdgService.isCorpUpdate">
                        <button mat-raised-button class="submit-btn" [disabled]="isUpdateDisable"
                            (click)="update(g.form.valid)">UPDATE
                        </button>
                    </div>
                </div>
            </div>

            <div fxFlex class="header-div">
                <span>DETAILS</span>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">POLICY NAME <span class="astreik">*</span>:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%">
                    <mat-form-field appearance="outline" class="inp">
                        <input matInput name="policyName" [(ngModel)]="data.policyName"
                            pattern="\d*[a-zA-Z ][a-zA-Z0-9- ]*$" #policyName="ngModel" [disabled]="isUpdateDisable"
                            required>
                        <!-- onkeypress="return event.charCode >= 65 && event.charCode <=90 || event.charCode >= 95 && event.charCode <=122 || event.charCode === 32"  -->
                        <mat-error *ngIf="g.submitted && policyName.invalid" class="mt20">
                            <div *ngIf="policyName.hasError('required')">
                                Policy Name Is Required
                            </div>
                        </mat-error>
                        <mat-error *ngIf="g.submitted && policyName.invalid" class="mt20">
                            <div *ngIf="policyName.hasError('pattern') ">
                                Policy Name should contain a character
                            </div>
                            <!-- ="((\d+)((\.\d{1,2})?))$ -->
                        </mat-error>
                    </mat-form-field>
                </div>

                <!-- <mat-error *ngIf="isPolicyNoNull" class="pt15">
                    Policy Name Is Required
            </mat-error> -->
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                <mat-radio-group matInput name="radioVal" [(ngModel)]="data.radioValue" fxLayoutGap="30px"
                    (ngModelChange)="resetVal(data.radioValue)" #radioVal="ngModel"
                    [disabled]="this.cdgService.isCorpUpdate">
                    <mat-radio-button *ngFor="let val of radioArr" [value]="val.value" labelPosition="before" color="primary">
                        {{val.viewValue}}
                    </mat-radio-button>
                </mat-radio-group>
            </div>

            <div fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 20px;"
                *ngIf="data.radioValue == 'A' && !this.cdgService.isCorpUpdate && accountSelection">
                <ng-container>
                    <span>ACCOUNT SELECTION</span>
                    <mat-checkbox color="primary" labelPosition="after" [(ngModel)]="data.isMainAcc" name="mainAccount"
                        #mainAccount="ngModel">
                        MAIN ACCOUNT
                    </mat-checkbox>
                </ng-container>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                *ngIf="data.radioValue == 'A' && !this.cdgService.isCorpUpdate">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">DIVISION :</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%">
                    <mat-form-field appearance="outline" class="inp">
                        <!--  <mat-select name="divsion" [(ngModel)]="data.divsion" placeholder="Please Select"
                        #divsion="ngModel" (selectionChange)="selectDept(data.divsion)">

                        <mat-option *ngFor="let acl of divitionArr" [value]="acl.viewValue">
                            {{acl.viewValue}}
                        </mat-option>
                    </mat-select>
                </mat-form-field> -->
                        <!-- <mat-form-field fxFlex.gt-md="35%" fxFlex.lt-md="25%" fxFlex.md="50%" style="width: 100%;"
                appearance='outline'> -->
                        <mat-select [(ngModel)]="frstDivSelection" [disabled]="data.isMainAcc"
                            (selectionChange)="selectDept(frstDivSelection)" name="div" #div="ngModel">
                            <mat-option *ngFor="let div of divArray" [value]="div">
                                {{div.nameToDisplayOnDropdown}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 20px;"
                *ngIf="data.radioValue == 'A' && this.cdgService.isCorpUpdate">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">ACCOUNT CATERORY:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%">
                    {{this.cdgService.setupRulesData.accountCategory}}
                </div>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 20px;"
                *ngIf="data.radioValue == 'A' && this.cdgService.isCorpUpdate">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">ACCOUNT NAME:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%">
                    {{this.cdgService.setupRulesData.accountName}}
                </div>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 20px;"
                *ngIf="data.radioValue == 'A' && this.cdgService.isCorpUpdate">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">ACCOUNT NO.:</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%">
                    {{this.cdgService.setupRulesData.customerNo}}
                </div>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                *ngIf="data.radioValue == 'A' && isShowDepartment && !this.cdgService.isCorpUpdate">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">DEPARTMENT :</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%">
                    <mat-form-field appearance="outline" class="inp">
                        <!-- <mat-select name="department" [(ngModel)]="data.department" placeholder="Please Select"
                        #department="ngModel">

                        <mat-option *ngFor="let acl of departmentArr" [value]="acl.viewValue">
                            {{acl.viewValue}}
                        </mat-option>
                    </mat-select>
                </mat-form-field> -->
                        <!-- <mat-form-field fxFlex.gt-md="35%" fxFlex.lt-md="25%" fxFlex.md="50%" style="width: 100%;"
                appearance='outline'> -->
                        <mat-select [(ngModel)]="defaultDept" [disabled]="data.isMainAcc" name="defaultDep"
                            #defaultDep="ngModel">
                            <mat-option *ngFor="let dept of deptArray" [value]="dept">
                                {{dept?.nameToDisplayOnDropdown}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </div>
            <ng-container *ngFor="let value of data.productArray; let i = index">
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                    *ngIf="data.radioValue == 'C' && !this.cdgService.isCorpUpdate">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">PRODUCT TYPE:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                            <mat-select name="producttype{{i}}" [(ngModel)]="value.producttype"
                                (selectionChange)="onSelect($event)" placeholder="Select Product Type"
                                #producttype="ngModel">
                                <!-- [ngModel]="firstProductSelected" -->
                                <mat-option *ngFor="let acl of productArr" [value]="acl.productId">
                                    {{acl.productName}}
                                    <!-- <mat-select name="cardNo" [(ngModel)]="data.cardNo" placeholder="Please Select" #cardNo="ngModel">
                        <mat-option *ngFor="let acl of cardArr" [value]="acl">
                            {{acl}} -->
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
                    *ngIf="data.radioValue == 'C' && !this.cdgService.isCorpUpdate">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">CARD NO. :</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%" fxLayout="row">
                        <div fxLayout="column" fxLayoutAlign="center end">
                            <span class="pb10"
                                *ngIf="value.displaycard.length > 0">{{value.displaycard.join("-")}}</span>
                            <mat-error *ngIf="value.showError && value.displaycard.length === 0" class="error">
                                No Card Details Found
                            </mat-error>
                        </div>
                        <div fxLayout="row" *ngIf="!value.showError">
                            <mat-form-field appearance="outline" class="inp">
                                <input matInput type="text" name="cardNo{{i}}" #cardNo="ngModel"
                                    onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                    [(ngModel)]="value.cardNo" (ngModelChange)="keyPress($event)"
                                    [matAutocomplete]="auto" />
                                <mat-autocomplete #auto="matAutocomplete">
                                    <mat-option *ngFor="let acl of cardArr" [value]="acl">
                                        {{acl}}
                                    </mat-option>
                                </mat-autocomplete>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 20px;"
                    *ngIf="data.radioValue == 'C' && this.cdgService.isCorpUpdate">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">CARD NO.:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        {{this.updateSelectCard.join(", ")}}
                    </div>
                </div>
                <div fxFlex fxLayoutAlign="end center" *ngIf="i > 0">
                    <button mat-raised-button class="add-btn" (click)="deleteProduct(i)">
                        <mat-icon class="add-icon">delete</mat-icon>DELETE
                    </button>
                </div>
            </ng-container>
            <div fxFlex *ngIf="data.radioValue == 'C' && !this.cdgService.isCorpUpdate">
                <button mat-raised-button class="add-btn" (click)="addProductType()">
                    <mat-icon class="add-icon">add</mat-icon>Add
                </button>
            </div>

            <!-- <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20"
            *ngIf="data.radioValue == 'C'">
            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                <span class="date-title">Card No. :</span>
            </div>
            <div fxFlex="85%" fxFlex.lt-md="65%">
                <mat-form-field appearance="outline" class="inp">
                    <mat-select name="cardNo" [(ngModel)]="data.cardNo" placeholder="Please Select" #cardNo="ngModel">
                        <mat-option *ngFor="let acl of cardArr" [value]="acl">
                            {{acl}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
        </div> -->
            <div fxLayout="row" fxLayout.lt-md="column" fxLayoutGap="80px" fxLayoutGap.lt-md="20px" class="pl20">
                <div fxLayout="row" fxLayoutGap="50px">
                    <span fxFlex.lt-md="35%" class="date-title pt10">START DATE:</span>
                    <mat-form-field appearance="outline" class="date-inp">
                        <input matInput [min]="currDate" [matDatepicker]="picker1" name="startDate"
                            [(ngModel)]="data.startDate" #startDate="ngModel" [disabled]="isUpdateDisable"
                            onkeydown="return false">
                        <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                        <mat-datepicker #picker1></mat-datepicker>
                        <!-- <mat-error *ngIf="g.submitted && startDate.invalid" class="pt15">
                            <div *ngIf="startDate.hasError('required')">
                                Start date Is Required
                            </div>
                        </mat-error> -->
                    </mat-form-field>

                </div>
                <div fxLayout="row" fxLayoutGap="50px">
                    <span fxFlex.lt-md="35%" class="date-title pt10">TO :</span>
                    <mat-form-field appearance="outline" class="date-inp">
                        <input matInput [matDatepicker]="picker2" [min]="this.data.startDate" name="endDate"
                            [(ngModel)]="eDate" #endDate="ngModel" [disabled]="isUpdateDisable">
                        <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                        <mat-datepicker #picker2></mat-datepicker>
                    </mat-form-field>
                    <!-- <mat-form-field appearance="outline" class="date-inp">
                        <input matInput  [matDatepicker]="picker3" name="endDates" #endDates="ngModel" [(ngModel)]="data.endDate" [disabled]="isUpdateDisable">
                        <mat-datepicker-toggle matSuffix [for]="picker3"></mat-datepicker-toggle>
                        <mat-datepicker #picker3></mat-datepicker>
                    </mat-form-field> -->
                    <!-- <mat-form-field appearance="outline" class="date-inp">
                        <input matInput [min]="currDate" [matDatepicker]="picker2" name="endDate" [(ngModel)]="data.endDate"
                              [disabled]="isUpdateDisable">
                        <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                        <mat-datepicker #picker2></mat-datepicker> -->
                    <!-- (dateChange)="daysDifference()" -->
                    <!-- <mat-error *ngIf="g.submitted && endDate.invalid" class="pt15">
                            <div *ngIf="endDate.hasError('required')">
                                End date Is Required
                            </div>
                        </mat-error> -->
                    <!-- </mat-form-field> -->

                </div>

            </div>
            <!-- <div *ngIf="showDateRangeError" class="error pl">
                End Date should be greater than start Date
            </div> -->
            <div fxFlex class="header-div">
                <span>TIME</span>
            </div>
            <ng-container *ngFor="let value of data.time; let i = index">
                <div fxLayout="row" fxFlex class="pl20">
                    <div fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <ng-container *ngFor="let val of value.day;let j=index">
                            <mat-checkbox color="primary" name="day{{i}}{{j}}" [(ngModel)]="val.checked" #day="ngModel"
                                labelPosition="before" value="val.checked" [disabled]="isUpdateDisable">
                                {{val.name}}
                            </mat-checkbox>
                        </ng-container>
                    </div>
                </div>

                <div fxLayoutGap="40px" fxLayoutGap.lt-md="10px" style="padding-left: 20px;">
                    <ng-container>
                        <mat-checkbox color="primary" name="anyTime{{i}}" labelPosition="after"
                            [(ngModel)]="value.isAnyTime" #anyTime="ngModel" [disabled]="isUpdateDisable">
                            ANYTIME
                        </mat-checkbox>
                    </ng-container>
                </div>
                <!-- (change)="onChecked(value.isAnyTime)" -->

                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20" *ngIf="! value.isAnyTime">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">FROM:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%" fxLayoutGap="15px">
                        <mat-form-field appearance="outline" class="num-inp">
                            <!-- <input matInput type="number" min="0" max="23" name="fromHr{{i}}" onkeydown="return false"
                                [(ngModel)]="value.from.hr" autocomplete="off"  #fromHr="ngModel" [disabled]="isUpdateDisable"> -->
                            <mat-select [(ngModel)]="value.from.hr" #fromHr="ngModel" name="fromHr{{i}}"
                                [disabled]="isUpdateDisable">
                                <mat-option *ngFor="let acl of hrList" [value]="acl.viewValue">
                                    {{acl.viewValue}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="num-inp">
                            <mat-select name="fromMin{{i}}" [(ngModel)]="value.from.min" #fromMin="ngModel"
                                [disabled]="isUpdateDisable">
                                <mat-option *ngFor="let acl of minList" [value]="acl.viewValue">
                                    {{acl.viewValue}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20" *ngIf="! value.isAnyTime">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">TO:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%" fxLayoutGap="15px">
                        <mat-form-field appearance="outline" class="num-inp">
                            <mat-select name="toHr{{i}}" [(ngModel)]="value.to.hr" #toHr="ngModel"
                                [disabled]="isUpdateDisable">
                                <mat-option *ngFor="let acl of hrList" [value]="acl.viewValue">
                                    {{acl.viewValue}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="num-inp">
                            <mat-select name="toMin{{i}}" [(ngModel)]="value.to.min" #toMin="ngModel"
                                [disabled]="isUpdateDisable">
                                <mat-option *ngFor="let acl of minList" [value]="acl.viewValue">
                                    {{acl.viewValue}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div fxFlex fxLayoutAlign="end center" *ngIf="i > this.updateTimeNo">
                    <button mat-raised-button class="add-btn" (click)="deleteTime(i)" [disabled]="isUpdateDisable">
                        <mat-icon class="add-icon">delete</mat-icon>DELETE
                    </button>
                </div>
                <!-- <div fxFlex fxLayoutAlign="end center" *ngIf="i > updateTimeNo && this.cdgService.isCorpUpdate">
                    <button mat-raised-button class="add-btn" (click)="deleteTime(i)">
                        <mat-icon class="add-icon">delete</mat-icon>DELETE
                    </button>
                </div> -->
            </ng-container>
            <div fxFlex>
                <button mat-raised-button class="add-btn" (click)="addTime()" [disabled]="isUpdateDisable">
                    <mat-icon class="add-icon">add</mat-icon>Add
                </button>
            </div>
            <div fxFlex class="header-div">
                <span>LOCATION</span>
            </div>
            <div fxFlex>
                <span class="pl20">Minimum radius is 1km</span>
            </div>
            <div fxLayout.gt-md="row" fxLayout.md="row" fxLayout.lt-md="column" fxLayoutGap="40px"
                fxLayoutGap.lt-md="10px" class="pl20">
                <div fxFlex="70%" fxLayout="column" fxLayoutAlign="start start">
                    <ng-container *ngFor="let value of data.locations; let j = index">
                        <div fxLayoutGap="20px" fxLayout="column" class="pb20">
                            <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="40px">
                                <div>
                                    <mat-form-field appearance="outline" class="location-inp">
                                        <input type="text" matInput name="pickupLocation{{j}}" min="0" minlength="6"
                                            maxlength="6" pattern="^[0-9]*$" [(ngModel)]="value.pickupLocation"
                                            #pickupLocation="ngModel" placeholder="Please Enter Pickup Location"
                                            (ngModelChange)="getLocationDetails(value.pickupLocation,'Pickup')"
                                            [disabled]="isUpdateDisable">
                                        <mat-error *ngIf="pickupLocation.invalid" class="mt20">
                                            <div *ngIf="pickupLocation.hasError('pattern') || pickupLocation.hasError('minlength') || pickupLocation.hasError('maxlength') ">
                                                Please Enter Valid Pickup with 6 digits
                                            </div>
                                        </mat-error>

                                        <div *ngIf="checkIsValidPickuplocation && pickupLocation.valid" class="mt20 validationfix">
                                            Please Enter Valid Pickup Location
                                        </div>
                                    </mat-form-field>
                                </div>

                                <div fxLayout="row" fxLayoutAlign="center center">
                                    <mat-form-field appearance="outline" class="num-inp">
                                        <!-- <input matInput type="number" name="radius{{j}}" min="2" max="59" maxlength="2"
                                             autocomplete="off" [(ngModel)]="value.radius" (focusout)="restrictNumbers($event,j)"
                                            #radius="ngModel" [disabled]="isUpdateDisable"> -->
                                            <!-- <input matInput type="number" name="radius{{j}}"> -->
                                            <mat-select name="radius{{j}}" [(ngModel)]="value.radius" #radius="ngModel"
                                            [disabled]="isUpdateDisable">
                                            <mat-option *ngFor="let acl of radiusList" [value]="acl.viewValue">
                                                {{acl.viewValue}}
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                    <span>Km</span>
                                </div>
                            </div>
                            <!-- onkeypress="return event.charCode >= 48 && event.charCode <= 57" -->
                            <!-- <div> -->
                            <!-- <ng-container *ngFor="let val of value.mode;let k=index" >
                            <mat-checkbox color="primary" name="mode{{j}}" [(ngModel)]="val.checked" #mode="ngModel"
                                labelPosition="before" value="val.checked" class="pr20">
                                {{val.name}}
                            </mat-checkbox>
                        </ng-container> -->
                            <!-- </div> -->
                            <div fxLayout="row">
                                <mat-form-field appearance="outline" class="location-inp">
                                    <input type="text" matInput name="destinationLocation{{j}}" min="0" minlength="6"
                                        maxlength="6" pattern="^[0-9]*$" [(ngModel)]="value.destinationLocation"
                                        #destinationLocation="ngModel" placeholder="Please Enter Destination Location"
                                        (ngModelChange)="getLocationDetails(value.destinationLocation,'Destination')"
                                        [disabled]="isUpdateDisable">
                                    <mat-error *ngIf="destinationLocation.invalid" class="mt20">
                                        <div *ngIf="destinationLocation.hasError('pattern') || destinationLocation.hasError('minlength') || destinationLocation.hasError('maxlength')">
                                            Please Enter Valid Destination with 6 digits
                                        </div>
                                    </mat-error>

                                    <div *ngIf="destinationInvalidCheck && destinationLocation.valid" class="mt20 validationfix">
                                        Please Enter Valid Destination Location 
                                    </div>
                                </mat-form-field>
                            </div>
                            <div fxLayoutGap="5px" fxLayoutAlign="end center" *ngIf="j > this.updateLocationNo">
                                <button mat-raised-button class="add-btn" (click)="deleteLocation(j)"
                                    [disabled]="isUpdateDisable">
                                    <mat-icon class="add-icon">delete</mat-icon>DELETE
                                </button>
                            </div>
                        </div>
                    </ng-container>
                </div>
                <div fxFlex="30%">
                    <button mat-raised-button class="add-btn" (click)="addLocation()" [disabled]="isUpdateDisable">
                        <mat-icon class="add-icon">add</mat-icon>Add
                    </button>
                </div>
            </div>
            <!-- </ng-container> -->
            <div fxFlex class="header-div">
                <span>RULES</span>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                    <span class="date-title">AMOUNT CAP ($):</span>
                </div>
                <div fxFlex="85%" fxFlex.lt-md="65%">
                    <mat-form-field appearance="outline" class="inp">
                        <input matInput type="number" min="0" minlength="0" maxlength="7" pattern="\d{0,7}" name="amt"
                            [(ngModel)]="data.amt" #amt="ngModel" autocomplete="off" class="amtcap"
                            [disabled]="isUpdateDisable">
                        <mat-error *ngIf="amt.invalid" class="mt20">
                            <div *ngIf="amt.hasError('pattern') ">
                                Please Enter Valid Amount with 7 digits only
                            </div>
                            <!-- ="((\d+)((\.\d{1,2})?))$ -->
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
            <div fxFlex class="pl20">
                <span class="heading">JOB TYPE:</span>
            </div>
            <!-- <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
            <ng-container *ngFor="let val of data.jobType;let j=index">
                <mat-checkbox color="primary" name="jobType" [(ngModel)]="val.checked" #jobType="ngModel"
                    labelPosition="before" value="val.checked">
                    {{val.name}}
                </mat-checkbox>
            </ng-container>
        </div> -->
            <div fxLayout="column" fxFlex class="pl20">
                <div fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="!cdgService.isCorpUpdate">
                    <ng-container *ngFor="let val of data.jobType;let j=index">
                        <mat-checkbox color="primary" name="jobType{{j}}" [(ngModel)]="val.checked" #jobType="ngModel"
                            labelPosition="before" value="val.checked" [disabled]="isUpdateDisable">
                            {{val.name}}
                        </mat-checkbox>
                    </ng-container>
                </div>
                <div fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="cdgService.isCorpUpdate">
                    <ng-container *ngFor="let val of data.jobType;let j=index">
                        <mat-checkbox color="primary" name="jobType{{j}}" [(ngModel)]="val.checked" #jobType="ngModel"
                            labelPosition="before" value="val.checked"
                            [disabled]="isUpdateDisable || val.updateChecked">
                            {{val.name}}
                        </mat-checkbox>
                    </ng-container>
                </div>
            </div>
            <div fxFlex class="pl20">
                <span class="heading">VEHICLE TYPE:</span>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                <ng-container *ngFor="let val of data.vehicleType;let j=index">
                    <mat-checkbox color="primary" name="vehicleType{{j}}" [(ngModel)]="val.checked"
                        #vehicleType="ngModel" labelPosition="before" value="val.checked" [disabled]="isUpdateDisable || val.updateChecked">
                        {{val.name}}
                    </mat-checkbox>
                </ng-container>
            </div>
            <div fxFlex class="pl20">
                <span class="heading">FARE TYPE:</span>
            </div>
            <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" class="pl20">
                <ng-container *ngFor="let val of data.fareType;let j=index">
                    <mat-checkbox color="primary" name="fareType{{j}}" [(ngModel)]="val.checked" #fareType="ngModel"
                        labelPosition="before" value="val.checked" [disabled]="isUpdateDisable || val.updateChecked">
                        {{val.name}}
                    </mat-checkbox>
                </ng-container>
            </div>

        </div>
    </form>
</div>