<div fxFlex fxLayout="column" class="full-height">
      <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="start start">     
            <div fxFlex="4%">
                <img src="\assets\images\view usage report.png">
              </div>
            <div>
              <div class="heading">Setup Policies</div>
            </div>
        </div> 
        <div fxLayout="row" class="mt50 ml20">
          <p class="sub-title ml5 mr5 mb20"> There are no Assigned Policies click below button to Add new policies</p>
      </div>
        <div fxFlex fxLayout="column" fxLayoutAlign="center center">
            <button mat-raised-button class="add-btn" (click)="addPolicies()">
              ADD POLICIES
            </button>
        </div> 
  </div>
