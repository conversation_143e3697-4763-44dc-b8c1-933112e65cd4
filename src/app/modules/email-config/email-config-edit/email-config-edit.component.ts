import { HttpHeaders } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';

@Component({
  selector: 'app-email-config-edit',
  templateUrl: './email-config-edit.component.html',
  styleUrls: ['./email-config-edit.component.scss']
})
export class EmailConfigEditComponent implements OnInit {

  removable = true;

  
  editEmailForm: FormGroup

  //emailList=['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>' ]

  emailAccTitle: string | undefined;
  emailList: any;
  emailDetails: any;
  emailListNew: string[] ;
  newlyAddedMailList: any[]=[];
  mailListAfterDeleting: any[]=[];
  emailSettingId: number;
  isShowError: boolean = false;
  emailListOriginal: any;
  isShowInfo: boolean = true;
  collection: any;
  emailListVar: any;
  constructor(private cdgService: CdgSharedService, public localStorage: LocalStorageService, private router: Router, private formBuilder: FormBuilder,private apiService: ApiServiceService,private notifyService: NotificationService) { 
    this.editEmailForm =  this.formBuilder.group({
      emailId: ['', [Validators.required,Validators.pattern(/^(\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]{2,4}\s*?,?\s*?)+$/)]]
    });
  }

  ngOnInit(): void {
    this.getEmailDetails();

    this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.RETRIVE_ALL_EMAIL_BY_NAME+ this.localStorage.localStorageGet("emailAccTitle")).subscribe((response : any)=>{
      if (response.status === 200){
         this.collection = response.body;
        //console.log(response,"response")
         this.emailListVar=this.collection.emailSettingList[0];
        //console.log(this.emailList, "vall");
          this.emailListVar.emailList1 = this.emailListVar.emailList1.concat(this.emailListVar.emailList2,this.emailListVar.emailList3,this.emailListVar.emailList4,this.emailListVar.emailList5)          
          //console.log(this.emailList, "vall");
         
          this.emailListVar.emailList1 = this.emailListVar.emailList1.filter(function(el:any){return el != null})  
          
          // Code for removing duplicate entries
        //  this.emailListVar.emailList1 = this.emailListVar.emailList1.filter((v:any,i:any) =>{return  this.emailListVar.emailList1.indexOf(v)===i})
              
          

         this.emailList = this.emailListVar.emailList1;
        // console.log(this.emailList,"newww")
      }
      },
      e=>{
        this.notifyService.showError(e.error.message,'Error');
      });
  }

  getEmailDetails(){
    this.cdgService.emailDetails.subscribe(res => {
      this.emailDetails = res || localStorage.getItem("emailDetails");
      this.emailDetails = localStorage.getItem("emailDetails");
      //this.emailList = JSON.parse(this.emailDetails) && JSON.parse(this.emailDetails).emailList1 || localStorage.getItem("emailList");
      this.emailListOriginal = JSON.parse(this.emailDetails) && JSON.parse(this.emailDetails).emailList1 || localStorage.getItem("emailList");
      this.emailAccTitle = JSON.parse(this.emailDetails) && JSON.parse(this.emailDetails).mailingListName || localStorage.getItem("emailAccTitle");
      this.localStorage.localStorageSet("emailAccTitle",this.emailAccTitle )            
      this.emailSettingId = JSON.parse(this.emailDetails) && JSON.parse(this.emailDetails).emailSettingId || localStorage.getItem("emailSettingId");
    })
  }

  addEmail(email: string) {
    //if(email != "" && this.editEmailForm.valid)
    this.isShowError= true;
    this.isShowInfo=false;
   
    if(email != "" && this.editEmailForm.valid)
    {
    this.emailListNew = email.split(',');
    this.emailListNew.forEach(item => {

      this.emailList.splice(0,0,item);

    });
    // this.emailList.splice(0, 0, this.emailListNew);
    //  console.log(this.emailListNew,"email list");
     this.newlyAddedMailList= this.newlyAddedMailList.concat(this.emailListNew);
    }
    if(this.editEmailForm.valid){
      this.editEmailForm.controls.emailId.reset();
      this.isShowError=false;
    //   setTimeout(()=>this.editEmailForm.controls.emailId.reset()
    // ,3000);
    }
    
    
    
  }

  removeEmail(email: string) {
    this.isShowInfo = false;
    const index = this.emailList.indexOf(email);
    if(this.newlyAddedMailList.includes(email)){
      const indexOfEmail = this.newlyAddedMailList.indexOf(email)
      if (indexOfEmail >= 0) {
        this.newlyAddedMailList.splice(indexOfEmail, 1);
      }
    }

    if (index >= 0) {
      this.emailList.splice(index, 1);
    }
    // console.log(this.emailList,"del emaillist")
  }

  goToEmailConfig() {
    this.router.navigateByUrl('/layout/Emailconfig');
  }

  onSave(){
    // console.log(this.emailSettingId,"iddddddddd")
    // console.log(this.newlyAddedMailList,"newly added mail list");
    // console.log(this.emailList,"del emaillist......ONSAVE")
    this.isShowError= false;
    this.mailListAfterDeleting = this.emailList;
 
    // console.log(this.emailListOriginal,"old")
    // console.log(this.mailListAfterDeleting,"new")
    

    let addObj={
      "emailSettingId" : this.emailSettingId ,
      // "emailList" : this.newlyAddedMailList
      "emailList" : this.mailListAfterDeleting
    }
    // let delObj={
    //   "emailSettingId" : this.emailSettingId ,
    //   "emailList" : this.mailListAfterDeleting
    // }
    //let header = new HttpHeaders().set("Authorization","Basic ************************************")
    if (JSON.stringify(this.emailListOriginal) === JSON.stringify(this.mailListAfterDeleting) && this.isShowInfo){
      this.notifyService.showInfo("No changes made in Mailing List. Please make changes before saving.",'Info')
    }
    else{
      this.apiService.put(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.ADD_TO_MAILINGLIST, addObj).subscribe(
      (response : any)=>{
        if(response.status === 200){
          //console.log(response.body.message);
          if(!this.editEmailForm.controls.emailId.hasError('pattern')){
            this.notifyService.showSuccess(response.body.message,'Success');
            this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.RETRIVE_ALL_EMAIL_BY_NAME+ this.localStorage.localStorageGet("emailAccTitle")).subscribe((response : any)=>{
              if (response.status === 200){
                 this.collection = response.body;
                //console.log(response,"response")
                 this.emailListVar=this.collection.emailSettingList[0];
                //console.log(this.emailList, "vall");
                  this.emailListVar.emailList1 = this.emailListVar.emailList1.concat(this.emailListVar.emailList2,this.emailListVar.emailList3,this.emailListVar.emailList4,this.emailListVar.emailList5)          
                  //console.log(this.emailList, "vall");
                 
                  this.emailListVar.emailList1 = this.emailListVar.emailList1.filter(function(el:any){return el != null})  
                  
                  // Code for removing duplicate entries
                //  this.emailListVar.emailList1 = this.emailListVar.emailList1.filter((v:any,i:any) =>{return  this.emailListVar.emailList1.indexOf(v)===i})       
                 this.emailList = this.emailListVar.emailList1;
                // console.log(this.emailList,"newww")
              }
              },
              // e=>{
              //   this.notifyService.showError(e.error.message,'Error');
              // }
              );

          }
          
        }
      },
      e=>{
        this.notifyService.showError(e.error.message,'Error');
      });
      

  //   this.apiService.put(this.cdgService.localhostUrl + UrlConstants.DELETE_FROM_MAILINGLIST, delObj).subscribe(
  //     (response:any)=>{
  //       if(response.status === 200){
  //       console.log(response);
  //       // this.notifyService.showSuccess(response.body.message,'Success')
  //   }
  // },
  // e=>{
  //   this.notifyService.showError(e.error.message,'Error');
  // });



        }
}
  ngOnDestroy(){
  
  }

}

