@import "./../../../../styles/colors.scss";
@import "./../../../../styles/sizing.scss";

.title {
    font-family: $cdgsz-font-family;
    font-style: normal;
    font-weight: bold;
    font-size: $cdgsz-font-size-lg;
    line-height: 23px;
}

.sub-title {
    font-family: $cdgsz-font-family;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 16px;
}

.align-icon-text {
    display: flex;
    align-items: center;
}

.arrow-icon {
    padding-right: 20px !important;
    color: $cdgc-bg-prime;
    cursor : pointer;
}

.arrow-forward-icon {
    color: $cdgc-bg-prime;
    cursor : pointer;
}
.mat-mdc-chip{
    //width: 181px;
    width: auto !important;
    justify-content: space-between;
    border-radius: 10px !important;
    color:#f7f7f7 !important;
    background-color: #006ba8 !important;
}

.mat-close-icon{
    color:$cdgc-accent !important;
}

.btn{
    border-radius: 4px !important;
    width: 124px !important;
    font-size: 14px !important;
    line-height: 16px !important;
    padding: 10px 0 10px 0 !important;
}

/*Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper{
    padding-bottom: 0 !important;
}

.cell{
    width: 88%;
}

.desktop{
    width: 38%;
}

.error-container {
    margin: 14px 0 14px 0;
}
// .mat-mdc-chip{ //change by navani 
//     background-color: #006ba8 !important;
// }


:host .mat-form-field-appearance-outline .mat-mdc-form-field-flex {
    background-color: #fff !important;
    border: transparent !important;
    border-radius: 10px !important;
    padding-left: 10px !important;
}