<div style=" margin:0 0 0 6%" fxLayout="column" fxLayoutGap="24px" fxLayoutAlign="start stretch">

        <p class="align-icon-text title">
            <mat-icon (click)="goToEmailConfig()" class="arrow-icon">arrow_back</mat-icon>
            {{emailAccTitle}}
        </p>
    
    <p class="sub-title">Edit mailing list to trigger to</p>
<form [formGroup]="editEmailForm">
  
        <!-- <mat-form-field [ngClass.sm]="'cell'" [ngClass.gt-md]="'desktop'" fxFlex="70%" appearance="outline"> -->
            <mat-form-field  fxFlex="70%" appearance="outline">
             <input #input formControlName="emailId"
                 type="email" matInput style="padding-left: 10px;" placeholder="Input Field" required>
            <mat-icon class="arrow-forward-icon" matSuffix (click)="addEmail(input.value)">arrow_forward</mat-icon>
            <mat-error class="error-container" *ngIf="editEmailForm.controls.emailId.hasError('pattern')">
                Please Enter Valid Email
            </mat-error>
             <mat-error class="error-container" *ngIf="editEmailForm.controls.emailId.hasError('required') && isShowError">
                Please Enter Email
            </mat-error> 
        </mat-form-field>
    
    </form>

    <div>
        <mat-chip-grid class="mat-chip-list-stacked">
            <mat-chip  class="mat-chip" *ngFor="let email of emailList" [removable]="removable" (removed)="removeEmail(email)">{{email}}
                <mat-icon class="mat-close-icon" matChipRemove *ngIf="removable">close</mat-icon>
            </mat-chip>
        </mat-chip-grid>
    </div>
    <div>
        <button class="btn" mat-raised-button (click)="onSave()">SAVE</button>
    </div>

</div>