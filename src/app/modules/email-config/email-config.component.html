<!-- <mat-card routerLink='/'>Edit me</mat-card> -->
<div style="height: 100%;" fxLayout="column" fxLayoutAlign="space-between" fxHide.sm fxHide.lt-md>
    <div fxLayout="column" fxLayoutAlign="start">
        <div style=" margin:0 0 0 6%" fxLayout="column" fxLayoutGap="16px" fxLayoutAlign="start stretch" fxHide.sm
            fxHide.lt-md>

            <div>
                <p class="align-icon-text title">
                    <mat-icon class="arrow-icon mat-icon-arrow" (click)="goToDashboard()">arrow_back</mat-icon>
                    Email Configuration
                </p>
            </div>
            <p class="sub-title">Manage and edit where emails will be triggered</p>
            <div fxFlex fxLayout="row" fxLayoutGap="16px">
                <div fxLayout="column" fxLayoutGap="8px" fxLayoutAlign=" center end">
                    <mat-card appearance="outlined" class="customize-mat-card" fxLayout="row" fxLayoutAlign="start center"
                        *ngFor="let item of emailList | paginate: { itemsPerPage: 10, currentPage: p, id: 'desktop' } | slice:0:5; let i = index;">
                        <div fxLayout="column" fxFlex="90%">
                            <div class="content"> {{item.mailingListName}} </div>
                            <div>
                                <p class="tooltip-pointer email-list" matTooltip="{{item.emailList1}}"
                                     [matTooltipPosition]="'below'"
                                    aria-label="Button that displays a tooltip when focused or hovered over">
                                    {{item.emailList1[0]}}, {{item.emailList1[1]}}


                                </p>
                            </div>
                        </div>
                        <div fxFlex="10%">
                            <mat-icon (click)="[goToEmailConfigEdit(), onMatCardClickEvent(item)]" class="mat-icon-arrow">keyboard_arrow_right</mat-icon>
                        </div>
                    </mat-card>
                </div>
                <div fxLayout="column" fxLayoutGap="8px" fxLayoutAlign="center">
                  
                    <mat-card appearance="outlined" class="customize-mat-card" fxLayout="row" fxLayoutAlign="start center"
                        *ngFor="let item of emailList | paginate: { itemsPerPage: 10, currentPage: p, id: 'desktop' } | slice:5:10; let i = index;">
                        <div fxLayout="column" fxFlex="90%">
                            <div class="content"> {{item.mailingListName}} </div>
                            <div>
                                <p class="tooltip-pointer email-list" matTooltip="{{item.emailList1}}"
                                     [matTooltipPosition]="'below'"
                                    aria-label="Button that displays a tooltip when focused or hovered over">
                                    {{item.emailList1}}


                                </p>
                            </div>
                        </div>
                        <div fxFlex="10%">
                            <mat-icon (click)="[goToEmailConfigEdit(), onMatCardClickEvent(item)]" class="mat-icon-arrow">keyboard_arrow_right</mat-icon>
                        </div>
                    </mat-card>

                </div>

            </div>


        </div>   
    </div>
    <div>

        <pagination-controls [directionLinks]="false" class="paginator" (pageChange)="p = $event" id='desktop'>
        </pagination-controls>

    </div>
</div>

    <!-- For mobile -->
    <div style="margin-bottom: 7%" fxHide.gt-md fxHide.md fxLayoutAlign="start stretch" fxLayout="column" fxLayoutGap="24px">
        <div fxLayout="row" fxLayoutGap="46px" fxLayoutAlign="space-between center" class="paginator-cell">

            <p fxFlex="40%" class="align-icon-text-cell title">
                <mat-icon (click)="goToDashboard()" >arrow_back</mat-icon>
                Email Configuration
            </p>

            <pagination-controls [directionLinks]="false" class="paginator" (pageChange)="p = $event " id='cell'>
            </pagination-controls>


        </div>
        <p class="sub-title">Manage and edit where emails will be triggered</p>
        <div fxLayout="column" fxFlex="100%" fxLayoutGap="8px" fxHide.gt-md fxLayoutAlign="center center">
            <mat-card appearance="outlined" fxLayout="row" fxLayoutAlign="space-around center" class="customize-mat-card-cell"
                *ngFor="let item of emailList | paginate: { itemsPerPage: 5, currentPage: p, id: 'cell'} ">
                <div class="content" fxFlex="80%"> {{item.mailingListName}} </div>
                <div fxFlex="10%">
                    <mat-icon (click)="[goToEmailConfigEdit(), onMatCardClickEvent(item)]" class="mat-icon-arrow">keyboard_arrow_right</mat-icon>
                </div>
            </mat-card>
        </div>
    </div>