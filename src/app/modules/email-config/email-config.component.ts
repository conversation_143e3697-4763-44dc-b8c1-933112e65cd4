import { HttpHeaders } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { Router, Routes } from '@angular/router';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';

@Component({
  selector: 'app-email-config',
  templateUrl: './email-config.component.html',
  styleUrls: ['./email-config.component.scss']
})

//const routes: Routes = [];
export class EmailConfigComponent implements OnInit {
  sectionTitle:string ;
  p: number = 1;
    collection: any;
    emailList: any[]=[];
    


  // collection = [
  // {title:"Cabcharge From Email Address",
  //  emailList:[ '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>' ]},
  //  {title:"IBS Users Email Address",
  //  emailList:[ '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>' ]},
  //  {title:"Account Manager Email Address",
  //  emailList:[ '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>' ]},
  //  {title:"Update Profile Notification Email Address",
  //  emailList:[ '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>' ]},
  //  {title:"Report Faulty Card Notification Email Address",
  //  emailList:[ '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>' ]},
  //  {title:"Report Lost Card Notification Email Address",
  //  emailList:[ '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>' ]},
  //  {title:"Enquiry Notification Email Address",
  //  emailList:[ '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>' ]},
  //  {title:"E-invoice Notification Email Address",
  //  emailList:[ '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>' ]},
  //  {title:"Corporate Individual Nomination Notification Email Address",
  //  emailList:[ '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>' ]},
  //  {title:"Department Nomination Notification Email Address",
  //  emailList:[ '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>' ]},
  //  {title:"Supplementary Card Notification Email Address",
  //  emailList:[ '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>' ]},
  //  {title:"Redeem Reward Notification Email Address",
  //  emailList:[ '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>' ]}

  // ];
//   collection =
//   {
//     "emailSettingList": [
//         {
//             "emailSettingId": 1,
//             "emailList1": [
//                 "<EMAIL>",
//                 " <EMAIL>"
//             ],
//             "emailList2": null,
//             "emailList3": null,
//             "emailList4": null,
//             "emailList5": null,
//             "mailingListName": " Report Lost Card Email group"
//         },
//         {
//             "emailSettingId": 2,
//             "emailList1": [
//                 "<EMAIL>",
//                 " <EMAIL>"
//             ],
//             "emailList2": null,
//             "emailList3": null,
//             "emailList4": null,
//             "emailList5": null,
//             "mailingListName": "IBS Users mailing list"
//         },
//         {
//             "emailSettingId": 3,
//             "emailList1": [
//                 "<EMAIL>",
//                 " <EMAIL>",
//                 " <EMAIL>"
//             ],
//             "emailList2": null,
//             "emailList3": null,
//             "emailList4": null,
//             "emailList5": null,
//             "mailingListName": "Update Profile Notification group"
//         },
//         {
//             "emailSettingId": 4,
//             "emailList1": [
//                 "<EMAIL>",
//                 " <EMAIL>"
//             ],
//             "emailList2": null,
//             "emailList3": null,
//             "emailList4": null,
//             "emailList5": null,
//             "mailingListName": "Report faulty Card Email group"
//         },
//         {
//             "emailSettingId": 5,
//             "emailList1": [
//                 "<EMAIL>",
//                 " <EMAIL>"
//             ],
//             "emailList2": null,
//             "emailList3": null,
//             "emailList4": null,
//             "emailList5": null,
//             "mailingListName": "Feedback notification mailing list"
//         },
//         {
//           "emailSettingId": 6,
//           "emailList1": [
//               "<EMAIL>",
//               " <EMAIL>"
//           ],
//           "emailList2": null,
//           "emailList3": null,
//           "emailList4": null,
//           "emailList5": null,
//           "mailingListName": " Report Lost Card Email group"
//       },
//       {
//           "emailSettingId": 7,
//           "emailList1": [
//               "<EMAIL>",
//               " <EMAIL>"
//           ],
//           "emailList2": null,
//           "emailList3": null,
//           "emailList4": null,
//           "emailList5": null,
//           "mailingListName": "IBS Users mailing list"
//       },
//       {
//           "emailSettingId": 8,
//           "emailList1": [
//               "<EMAIL>",
//               " <EMAIL>",
//               " <EMAIL>"
//           ],
//           "emailList2": null,
//           "emailList3": null,
//           "emailList4": null,
//           "emailList5": null,
//           "mailingListName": "Update Profile Notification group"
//       },
//       {
//           "emailSettingId": 9,
//           "emailList1": [
//               "<EMAIL>",
//               " <EMAIL>"
//           ],
//           "emailList2": null,
//           "emailList3": null,
//           "emailList4": null,
//           "emailList5": null,
//           "mailingListName": "Report faulty Card Email group"
//       },
//       {
//           "emailSettingId": 10,
//           "emailList1": [
//               "<EMAIL>",
//               " <EMAIL>"
//           ],
//           "emailList2": null,
//           "emailList3": null,
//           "emailList4": null,
//           "emailList5": null,
//           "mailingListName": "Feedback notification mailing list"
//       }
//     ]
// }
   



  constructor(private router: Router, private cdgService : CdgSharedService,private apiService: ApiServiceService,private notifyService: NotificationService) { }

  ngOnInit(): void {
    this.cdgService.isDataFromAccessId=false;
    //let header = new HttpHeaders().set("Authorization","Basic ************************************")
      this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.RETRIVE_ALL_EMAIL).subscribe((response : any)=>{
      if (response.status === 200){
         this.collection = response.body;
        //console.log(response,"response")
         this.emailList=this.collection.emailSettingList;
        //console.log(this.emailList, "vall");
        this.emailList.forEach((item):any=> {

           item.emailList1 = item.emailList1.concat(item.emailList2,item.emailList3,item.emailList4,item.emailList5)  
          })
          //console.log(this.emailList, "vall");
          this.emailList.forEach((item):any=> {
            item.emailList1 = item.emailList1.filter(function(el:any){return el != null})  
           })

          //  this.emailList.forEach((item):any=> {
          //   item.emailList1 = item.emailList1.filter(function(v:any,i:any){return item.emailList1.indexOf(v)===i})
            
          //  })
          // console.log(this.emailList, "vall");
      }
     
      },
      e=>{
        this.notifyService.showError(e.error.message,'Error');
      });
  }


  goToEmailConfigEdit(){
  this.router.navigateByUrl('/layout/emailEdit');
  }

  onMatCardClickEvent(item:any){
      this.sectionTitle = item.title;
      this.cdgService.emailDetails.next(item);
      localStorage.setItem("emailDetails",JSON.stringify(item));
  }

  goToDashboard(){
    this.router.navigateByUrl('/layout/admindashboard');
  }

}
