@import "./../../../styles/colors.scss";
@import "./../../../styles/sizing.scss";

.paginator {
    text-align: end;
}
::ng-deep ul.ngx-pagination {
    padding: 0 !important;
    width: 100%;
}
::ng-deep .ngx-pagination li {
    margin-right: 8px !important;
    border-radius: 3px !important;
    border: 1px solid $cdgc-bg-prime !important;
   
}

::ng-deep .ngx-pagination a{
    color:  $cdgc-bg-prime !important;
}

::ng-deep .ngx-pagination .current{
    background: $cdgc-bg-prime !important;
}

.content {
    font-weight: $cdgsz-font-weight-bold;
    font-size: $cdgsz-font-size-prime;
    line-height: 19px;
    color: $cdgc-font-prime;
}
.email-list{
    font-weight: $cdgsz-font-weight-normal;
    font-size:14px ;
    line-height: 16px;
    color: $cdgc-font-prime;
    font-family: $cdgsz-font-family;
    word-break: break-all;
}
.align-icon-text {
    display: flex;
    align-items: center;
}

.align-icon-text-cell {
    display: flex;
    align-items: flex-start;
}

.arrow-icon {
    padding-right: 20px !important;
}

.mat-icon-arrow{
    color: $cdgc-font-prime;
    cursor: pointer;
}
.mat-icon{
    color: $cdgc-font-prime;
    cursor: pointer; 
}

.title {
    font-family: $cdgsz-font-family;
    font-style: normal;
    font-weight: bold;
    font-size: $cdgsz-font-size-lg;
    line-height: 23px;
}

.sub-title {
    font-family: $cdgsz-font-family;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 16px;
}

.customize-mat-card-cell {
    height: 80px;
    width: 98%;
    background: $cdgc-bg-mild;
border: 1px solid $cdgc-bg-prime;
box-sizing: border-box;
border-radius: 2px;
}


//need to check
::ng-deep .mat-mdc-tooltip[style*="transform-origin: center top"]{
    
    word-break: break-all !important;
    background-color: $gray;           //#434343
    white-space: normal !important ;
    font-family: $cdgsz-font-family !important;
    font-style: normal !important;
    font-weight: $cdgsz-font-weight-normal !important;
    font-size: 14px !important;
    line-height: 16px !important;
    border-radius: 10px !important;
    overflow:visible !important;
    color:$cdgc-aux !important;
    &:after{
       content: "" ;
       position: absolute ;
       top: -15px ;
       left: 48% ;
       z-index: 999 ;
       width: 0 ;
       bottom: 100%;
       height: 0 ;
       border-bottom: 10px solid $gray;  //#434343
       border-top: 5px solid transparent;
       border-left: 10px solid transparent;
       border-right: 10px solid transparent; 
       
   }

}


::ng-deep .mat-mdc-tooltip[style*="transform-origin: center bottom"]{
    
    word-break: break-all !important;
    white-space: normal !important ;
    background-color: $gray;
    font-family: $cdgsz-font-family !important;
    font-style: normal !important;
    font-weight: $cdgsz-font-weight-normal !important;
    font-size: 14px !important;
    line-height: 16px !important;
    border-radius: 10px !important;
    overflow:visible !important;
    color:$cdgc-aux !important;
    &:after{
       content: "" ;
       position: absolute ;
       bottom: -15px ;
       left: 46% ;
       z-index: 999 ;
       width: 0 ;
       top: 100%;
       height: 0 ;
       transform: rotate(180deg);
       border-bottom: 10px solid $gray;
       border-top: 5px solid transparent;
       border-left: 10px solid transparent;
       border-right: 10px solid transparent; 
       
   }

}


.tooltip-pointer {
    cursor: pointer;
}


:host ::ng-deep .customize-mat-card {
    
        width: 379px;
        height: 120px;
        background: #e9f7ff !important;
        border: 1px solid #006ba8 !important;
        box-sizing: border-box;
        border-radius: 2px;
        padding: 15px;
    
}