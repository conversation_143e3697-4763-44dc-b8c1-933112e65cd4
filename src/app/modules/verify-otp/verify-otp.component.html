<div fxLayout="column" class="container" style="background-color: rgb(199, 209, 209);" (window:resize)="onResize($event)" fxLayoutAlign="center center">
	<div class="login-div" fxLayout="column" fxLayoutGap="20px">
		<div fxLayout="row" fxLayoutGap="14px" fxLayoutAlign="center center" >
            <img src="/assets/images/Cabcharge_white.png"  width="84px" alt="">
            <!-- <hr class="dividerd">
            <p class="heading">Sign In</p> -->
        </div>
		
			<div class="form-div">
				<form [formGroup]="verifyOtpForm" class="form">
                    <div fxFlex>
                        <p class="text-cls">Verify your email address</p>
                    </div>
					<div fxFlex>
                        <mat-label fxFlex class="text-cls font-16"> OTP </mat-label>
                        <mat-form-field appearance="outline" class="inp">
                        <mat-icon matPrefix>fiber_pin</mat-icon>
                            <input matInput placeholder="OTP" required formControlName="otp" style="background-color:white;color:black">
                            <mat-error *ngIf="submitted && verifyOtpForm.controls.otp.hasError('required')">
								OTP Is Required
							</mat-error>
							<mat-error *ngIf="submitted && verifyOtpForm.controls.otp.hasError('minlength')">
								OTP Must Contain 6 Digits
                            </mat-error>
                            <mat-error *ngIf="submitted && verifyOtpForm.controls.otp.hasError('pattern')">
								Please Enter Numbers
							</mat-error>
                        </mat-form-field>
                    </div>
					<div fxFlex>
						<button mat-raised-button class="inp register-btn" (click)="verifyOtp()">Verify</button>
                    </div>
                    <div fxFlex fxLayoutAlign="center" class="text-cls font-12" style="margin: 15px 0 20px 0">
                        <a class="text-cls" href="javascript:void(0)" (click)="resentOTP()"> Resent OTP</a>
                    </div>
				</form>
			</div>
		</div> 
	
	</div>

<!-- </div> -->


