import { Component, OnInit,HostListener } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { NotificationService } from 'src/app/shared/services/notification.service';

@Component({
  selector: 'app-verify-otp',
  templateUrl: './verify-otp.component.html',
  styleUrls: ['./verify-otp.component.scss']
})
export class VerifyOtpComponent implements OnInit {
  public isWebLayout = false;
  public isTabletLayout = false;
  public innerWidth: any;
  submitted = false;
verifyOtpForm:FormGroup;
  constructor(private formBuilder: FormBuilder, private router: Router,private notifyService: NotificationService) { 
    this.verifyOtpForm = this.formBuilder.group({
      otp: ['', [Validators.required, Validators.minLength(6),Validators.pattern("^[0-9]*$")]]
    });
  }

  ngOnInit(): void {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth < 1280) { // 768px portrait
      this.isWebLayout = false;
      this.isTabletLayout = true;
    }
    else {
      this.isWebLayout = true;
      this.isTabletLayout = false;

    }
  }
   get f() {
    return this.verifyOtpForm.controls;
  }
  verifyOtp() {
    this.submitted = true;
      if (this.verifyOtpForm.invalid) {
            return;
        }
      this.router.navigate(['/resetPwd'])
  }
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth < 1280) { // 768px portrait
      this.isWebLayout = false;
      this.isTabletLayout = true;
    }
    else {
      this.isWebLayout = true;
      this.isTabletLayout = false;
    }

  }
  resentOTP() {
        this.submitted = true;
        if (this.verifyOtpForm.invalid) {
            return;
        }
       else{
        this.notifyService.showSuccess("OTP has been sent successfully",'ResentOTP');
       }
    }
    


}
