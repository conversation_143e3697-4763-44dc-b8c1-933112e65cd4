<!--<p>access-control works!</p>-->
<div fxLayout="column" style="height: inherit;" (window:resize)="onResize($event)" class="main-content"
	fxLayoutGap="10px">
	<div fxLayout="row" >
		<div fxFlex fxLayout="row" fxLayoutGap="15px">
			<div class="cursor-pointer">
				<mat-icon (click)="goToPrevPage()">arrow_back</mat-icon>
			</div>
			<div>
				<span class="title">Access Control</span>
			</div>
		</div>
		<div fxFlex fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="end center">
			<div flex-order="1" flex-order-sm="2" fxHide.xs>
				<button mat-raised-button class="btn-custom" (click)="customisation()">CUSTOMIZE</button>
			</div>
			<div flex-order="2" flex-order-sm="1">
				<button mat-raised-button class="btn-apply" (click)="apply()">APPLY</button>
			</div>
		</div>
	</div>
	<!-- <div fxLayout="row" fxHide.gt-xs>
		<div fxFlex fxLayout="row" fxLayoutGap="15px">
			<div class="cursor-pointer">
				<mat-icon (click)="goToPrevPage()">arrow_back</mat-icon>
			</div>
			<div>
				<span class="title">Access Control</span>
			</div>
		</div>
		<div fxFlex fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="end center">
			<div >
				<button mat-raised-button class="btn-apply" (click)="apply()">APPLY</button>
			</div>
		</div>
	</div> -->
	<div>
		<span>Control your organization’s access rights</span>
	</div>
	<div fxLayout="row" fxLayout.xs="column" fxHide.gt-xs fxLayoutGap="10px">
		<div fxFlex fxFlexOrder.gt-xs="1" fxFlexOrder.xs="2">
			<mat-form-field appearance="fill" class="mat">
				<mat-select [(ngModel)]="selectedAcl" name="aclSelect" (ngModelChange)="getDisplayColumn()">
					<mat-option *ngFor="let acl of dataSource" [value]="acl.role.roleName">
						{{acl.role.roleDesc}}
					</mat-option>
				</mat-select>
			</mat-form-field>

		</div>
		<div fxFlex fxLayout="row" fxFlexOrder.gt-xs="2" fxFlexOrder.xs="1" fxLayout.lt-md="column" fxLayoutGap="10px" fxLayoutAlign="end end">
			<div>
				<button mat-raised-button class="btn-custom" (click)="customisation()">CUSTOMIZE</button>
			</div>
		</div>
	</div>
	<div style="overflow-x: auto;">

		<!-- <table mat-table [dataSource]="dataSource" style="width: 100%;">
			<ng-container [matColumnDef]="column" *ngFor="let column of displayedColumns;let i=index">
			  <th mat-header-cell *matHeaderCellDef> {{column}} </th>
			  <td mat-cell *matCellDef="let element"> {{element[column]}} </td>
			</ng-container>
		  
			<tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
			<tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
		  </table> -->
		<div fxLayoutAlign="center center">
			<mat-progress-spinner mode="indeterminate" diameter="55" strokewidth="6" *ngIf="showSpinner">
			</mat-progress-spinner>
		</div>

		<table>
			<tr *ngIf="isMobileLayout" >
				<th scope="col">FEATURE</th>
				<ng-container *ngFor="let column of dataSource;let i=index">
					<ng-container *ngIf="column.role.roleName === this.selectedAcl">
						<th scope="col">{{column.role.roleDesc}}</th>
					</ng-container>
				</ng-container>
			</tr>
			<tr *ngIf="!isMobileLayout" >
				<th scope="col">FEATURE</th>
				<ng-container *ngFor="let column of dataSource;let i=index">
						<th scope="col">{{column.role.roleDesc}}</th>
				</ng-container>
			</tr>
			<ng-container *ngFor="let header of displayRowHeader;let j=index">
				<tr *ngIf="isMobileLayout">
					<td scope="col">{{header}}</td>
					<ng-container *ngFor="let column of dataSource;let l=index">
						<ng-container *ngIf="column.role.roleName === this.selectedAcl">
							<td scope="col">
								<mat-checkbox name="master" color="primary"
									[checked]="getCellDetails(column.role,header).isMenuActive"
									(change)="getCellDetails(column.role,header,$event)"></mat-checkbox>
							</td>
						</ng-container>
					</ng-container>
				</tr>
				<tr *ngIf="!isMobileLayout">
					<td scope="col">{{header}}</td>
					<ng-container *ngFor="let column of dataSource;let l=index">
							<td scope="col">
								<mat-checkbox name="master" color="primary"
									[checked]="getCellDetails(column.role,header).isMenuActive"
									(change)="getCellDetails(column.role,header,$event)"></mat-checkbox>
							</td>
					</ng-container>
				</tr>
			</ng-container>
		</table>
	</div>

</div>

<!-- <app-access-control-customization></app-access-control-customization> -->