import { Component, OnInit, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { Location } from '@angular/common'
import { NotificationService } from 'src/app/shared/services/notification.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
@Component({
  selector: 'app-access-control',
  templateUrl: './access-control.component.html',
  styleUrls: ['./access-control.component.scss']
})
export class AccessControlComponent implements OnInit {
  selectedAcl:any="";
  selectAll: boolean = false;
  showSpinner: boolean = true;
  public aclele = [];
  public nums: any[] = [1, 2, 3, 3]
  aclSelect = [
    { value: 'master', viewValue: 'Master' },
    { value: 'individual', viewValue: 'Corporate Individual' },
    { value: 'admin', viewValue: 'Corporate Admin' },
    { value: 'personal', viewValue: 'Personal' },
    { value: 'subapp', viewValue: 'Subapp' }
  ];
  public AclElementList = [
    {
      feature: "feature1",
      master: true,
      individual: false,
      admin: false,
      personal: true,
      subapp: false,
    },
    {
      feature: "feature2",
      master: true,
      individual: true,
      admin: true,
      personal: true,
      subapp: false,
    },
    {
      feature: "feature3",
      master: true,
      individual: true,
      admin: false,
      personal: true,
      subapp: false,
    },
    {
      feature: "feature4",
      master: true,
      individual: true,
      admin: false,
      personal: true,
      subapp: false,
    }, {
      feature: "feature5",
      master: true,
      individual: true,
      admin: false,
      personal: true,
      subapp: false,
    },
    {
      feature: "feature6",
      master: true,
      individual: true,
      admin: false,
      personal: true,
      subapp: false,
    }
  ]
  public isWebLayout = false;
  public isMobileLayout = false
  public innerWidth: any;
  displayedColumns: string[] = [];
  // dataSource = 'AclElementList';
  public isAllValTureList: any;
  dataSource: any[] = []
  response: any;
  displayRowHeader: any[] = [];
  selectedVal: any;
  constructor(private router: Router, private location: Location, private notifyService: NotificationService, private apiService: ApiServiceService, private cdgService: CdgSharedService) {
  }

  ngOnInit(): void {
    this.cdgService.isDataFromAccessId=false;
    this.retriveAclMatrix();
    this.innerWidth = window.innerWidth;
    if (this.innerWidth < 600) { // 768px portrait
      this.isWebLayout = false;
      this.isMobileLayout = true;
      //   if (this.isAllValTureList.length > 0) {
      //     this.selectAll = false;
      //   }
      //   else {
      //     this.selectAll = true;
      //   }
      // }
    }
    else {
      this.isWebLayout = true;
      this.isMobileLayout = false;
      // this.displayedColumns = ['feature', 'master', 'individual', 'admin', 'personal', 'subapp']
    }
  }
  retriveAclMatrix() {
    this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_ACL_MATRIX).subscribe((response: any) => {
      if (response) {
        this.showSpinner = false;
      }
      if (response.status === 200) {
        let opt = response.body.globalAclList;
        this.displayedColumns.push('Features')
        opt.forEach((val: any, i: number) => {
          this.displayedColumns.push(val.role.roleDesc)
        });
        let data: any[] = []
        opt[0].featureList.forEach((element: any) => {
          data.push(element.mainMenuDetails);
        });
        let mainMenu = data.reduce((a, c) => {
          if (!a.some((item: any) => item.menuId === c.menuId)) {
            a.push(c)
          }
          return a;
        }, []);
        mainMenu.sort((a: any, b: any) => {
          return a.menuDisplayOrder - b.menuDisplayOrder
        })
        mainMenu.forEach((element: any) => {
          const datas = opt[0].featureList.filter((o: any) => o.mainMenuDetails.menuId === element.menuId)
          datas.sort((a: any, b: any) => {
            return a.menuDisplayOrder - b.menuDisplayOrder
          })
          if (datas.length > 0) {
            datas.forEach((subMenu: any) => {
              this.displayRowHeader.push(subMenu.menuName)
            });
          }
        });
        this.dataSource = opt;
        this.selectedAcl=this.dataSource[0].role.roleName;
        this.response = opt
      }
    },
      e => {
        console.log(e)
      })
  }
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth < 600) { // 768px portrait
      this.isWebLayout = false;
      this.isMobileLayout = true;
      // if (this.selectedAcl === 'master') {
      //   this.displayedColumns = ['feature', 'master'];
      //   this.isAllValTureList = this.AclElementList.filter(obj => obj['master'] === false);
      //   if (this.isAllValTureList.length > 0) {
      //     this.selectAll = false;
      //   }
      //   else {
      //     this.selectAll = true;
      //   }
      // }
    }
    else {
      this.isWebLayout = true;
      this.isMobileLayout = false;
      // this.displayedColumns = ['feature', 'master', 'individual', 'admin', 'personal', 'subapp']
    }

  }
  getCellDetails(data: any, header: any, menuActive?: any) {
    let selectedVal: any
    this.dataSource.forEach((element: any) => {
      if (data.roleDesc === element.role.roleDesc) {
        element.featureList.forEach((value: any) => {
          if (value.menuName.includes(header)) {
            if (menuActive) {
              value.isMenuActive = menuActive.checked;
            }
            else {
              selectedVal = value;
            }
          }
        });
      }
    });
    if (selectedVal) {
      return selectedVal
    }
  }
  apply() {
    let obj = {
      "globalAclList": this.dataSource
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.UPDATE_ACL_MATRIX, obj).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body;
        if (opt.message) {
          this.notifyService.showSuccess(opt.message, 'Success');
        }
      }
    },
      e => {
        console.log(e)
      })
  }
  customisation() {
    this.cdgService.rightsId = ""
    this.cdgService.isDataFromAccessId = false;
    this.router.navigateByUrl('/layout/rights');
  }
  getDisplayColumn() {
    console.log(this.selectedAcl,this.dataSource)
    
    // this.getValForSelection('displayColumn');
    // if (this.isAllValTureList.length > 0) {
    //   this.selectAll = false;
    // }
    // else {
    //   this.selectAll = true;
    // }
  }
  toggle() {
    this.AclElementList.forEach((x, i) => {
      this.getValForSelection('toggle', x)
    })
  }
  getValForSelection(funcName: any, x?: any) {
    if (this.selectedAcl === 'individual') {
      if (funcName === 'toggle') {
        x['individual'] = this.selectAll
      }
      else {
        this.displayedColumns = ['feature', 'individual'];
        this.isAllValTureList = this.AclElementList.filter(obj => obj['individual'] === false);
      }
    }
    else if (this.selectedAcl === 'admin') {
      if (funcName === 'toggle') {
        x['admin'] = this.selectAll
      }
      else {
        this.displayedColumns = ['feature', 'admin'];
        this.isAllValTureList = this.AclElementList.filter(obj => obj['admin'] === false);
      }
    }
    else if (this.selectedAcl === 'personal') {
      if (funcName === 'toggle') {
        x['personal'] = this.selectAll
      }
      else {
        this.displayedColumns = ['feature', 'personal'];
        this.isAllValTureList = this.AclElementList.filter(obj => obj['personal'] === false);
      }
    }
    else if (this.selectedAcl === 'subapp') {
      if (funcName === 'toggle') {
        x['subapp'] = this.selectAll
      }
      else {
        this.displayedColumns = ['feature', 'subapp'];
        this.isAllValTureList = this.AclElementList.filter(obj => obj['subapp'] === false);
      }
    }
    else {
      if (funcName === 'toggle') {
        x['master'] = this.selectAll
      }
      else {
        this.displayedColumns = ['feature', 'master'];
        this.isAllValTureList = this.AclElementList.filter(obj => obj['master'] === false);
      }
    }
  }
  goToPrevPage() {
    // this.location.back();
    this.router.navigateByUrl('/layout/admindashboard');
  }
}
