@import '@angular/material/theming';
@import './../../../styles/colors.scss';
@import './../../../styles/sizing.scss';
@import './../../../styles/main.scss';
.title{
    font-weight: bold;
    font-size: 20px;
    line-height: 23px;
}
.mat-icon{
    color: $cdgc-font-prime;
}
.btn-custom{
    border: 1px solid #006BA8 !important;
    background-color: white !important;
    border-radius: 4px !important;
    color:#006BA8 !important;
    font-weight: 500 !important;
    font-size: 14px !important;
}
.btn-apply{
    // border: 1px solid #006BA8;
    background-color: #006BA8;
    border-radius: 4px !important;
    color:white !important;
    font-weight: 500 !important;
    font-size: 14px !important; 
}
table{
    width: 100%;
    // border: ;
    border-radius: 4px;
    background: white;
    border-collapse: collapse;
}
th{
    background-color: #006BA8 !important;
    color: white !important;    
    font-size: 14px;
    padding: 15px 10px;
}
th:nth-child(1){
    border-top-left-radius: 10px;
}
th:last-child{
    border-top-right-radius: 10px;
}
td{
    font-size: 14px !important;
    padding: 5px 10px !important;
    text-align: center !important;
}
tr{
    border-radius: 12px;
    color: #4d4d4d !important;
    border: 1px solid #F6F6F6;
    box-sizing: border-box;  
}
tr:nth-child(even){
    background: #CBECFF;
    // border: 1px solid #F6F6F6;
    // box-sizing: border-box;    
}
        
tr:nth-child(odd){
    background: #E9F7FF;
    // border: 1px solid #F6F6F6;
    // box-sizing: border-box;
}
.mat-mdc-cell{
    font-weight: normal;
font-size: 14px;
line-height: 16px;
}

@media screen and (max-width: 600px) {
    .main{
        padding-left: 30px;
        padding-right: 30px;
        padding-top: 25px;
    }
}

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .mat-mdc-form-field-text-underline {
    position: absolute;
     width: 0% !important;
    /* pointer-events: none; */
    /* transform: scale3d(1, 1.0001, 1); */
}

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .mat-mdc-form-field-fill .mat-mdc-form-field-flex {
    border-radius: 30px !important;
    padding: .75em .75em 0 .75em;
}

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .mat-mdc-form-field-fill .mat-mdc-form-field-text-infix {
    /* padding: 0.25em 0 0.75em 0; */
    bottom: 16px;
    padding-bottom: 0px;
}

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .mat-mdc-form-field-fill .mat-mdc-form-field-flex {
    background-color: #006BA8 !important;
    color: white !important;
}

/* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
:host::ng-deep.mat-mdc-select-arrow {
    margin-top: 13px !important;
    color: white !important; 
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
:host::ng-deep.mat-mdc-select-value {
    color: white !important; 
}