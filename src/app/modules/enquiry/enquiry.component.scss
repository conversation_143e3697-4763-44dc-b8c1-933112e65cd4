@import '@angular/material/theming';
@import './../../../styles/colors.scss';
@import './../../../styles/sizing.scss';
@import './../../../styles/main.scss';

.inp50{
    width: 80% !important
}
 input::-webkit-inner-spin-button,
 input::-webkit-outer-spin-button{
     -webkit-appearance: none;
     margin: 0;
 }
 .error-container{
    color: #ff0000;
    font-weight: 600;
    font-size: 13px;
 }
 .fw{
    font-weight: $cdgsz-font-weight-bold;
 }
 .reg-container{
    position: relative;
  }
 .bg{
    background-image: url(/assets/images/DTK_3657.jpg) !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    // position: fixed;
    min-width: 100% !important;
  }
  .btn{
    background-color: $cdgc-bg-hue !important;
  }
  .enquiry-form{
    // width:800px; 
    // background-color: $cdgc-bg-prime;
    background-color: #f2f2f2 !important;
     border-radius: 20px;
     align-items: center;
     padding-left:15px;
     padding-right:15px;
    //  margin-left:85px;
    //  margin-right:85px;
    //  margin:0px 85px 0px 85px;
}
// .header-clr{
//     color:$white;
// }
 @media screen and (max-width: 600) {
    .contctwidth{
        width:50% !important;
}
.inp80{
    width: 50% !important;
}
.ml25{
    margin-left: 25px;
}
 }
// @media screen and (max-width: 1279px) {
//     .contactmt15{
//         margin-top: 15px;
//     }
// }
@media screen and (min-width: 768px) and (max-width: 1240px) {
        .contactmt15{
        margin-top: 15px;
    }
    .inp80{
        width: 100% !important 
    }
 }
 @media screen and (min-width: 1030px) and (max-width: 1366px) {
    .contactmt15{
        margin-top: 15px;
    }
    .inp80{
        width: 100% !important 
    }
 }

 ::ng-deep .mat-mdc-text-field-wrapper {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  
  ::ng-deep  .mat-mdc-select-arrow-wrapper {
    transform: translateY(-25%);
    display: table-cell;
    vertical-align: middle;
  }
  
 ::ng-deep .inp50 .mat-mdc-select-arrow {
    height: 1px !important;
    width: 1px !important;
    margin-top: 10px !important;
    // margin-right: 10px !important;
  }
  
  ::ng-deep .mat-mdc-select-arrow svg {
    display: none !important;
  }
