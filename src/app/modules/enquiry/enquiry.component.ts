import { Component, OnInit } from '@angular/core';
import { FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms';
import { NotificationService } from './../../shared/services/notification.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { CdgSharedService } from './../../shared/services/cdg-shared.service'
import { Router } from '@angular/router';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
// import { invalid } from '@angular/compiler/src/render3/view/util';

@Component({
  selector: 'app-enquiry',
  templateUrl: './enquiry.component.html',
  styleUrls: ['./enquiry.component.scss']
})
export class EnquiryComponent implements OnInit {
  submitted: boolean = false;
  showerror: boolean = false;
  showcontacterror: boolean = false;
  enquiryForm:FormGroup
  enquiryList = [
    { viewValue: 'CORPORATE CABCHARGE CARD' },
    { viewValue: 'PERSONAL CABCHARGE CARD' },
    { viewValue: 'CORPORATE CABCHARGE E-VOUCHER' },
    { viewValue: 'PREMIER SERVICE' },
    { viewValue: 'TAXI VOUCHERS' }
  ]
  industryList = [
    { viewValue: 'ADVERTISING' },
    { viewValue: 'AEROSPACE' },
    { viewValue: 'AUTOMOBILE' },
    { viewValue: 'BANKING' },
    { viewValue: 'BIOTECH' },
    { viewValue: 'CONSTRUCTION' },
    { viewValue: 'CONSULTING' },
    { viewValue: 'EDUCATION' },
    { viewValue: 'EMBASSY' },
    { viewValue: 'ENTERTAINMENT' },
    { viewValue: 'EVENTS & EXHIBITION' },
    { viewValue: 'EXECUTIVE SEARCH' },
    { viewValue: 'F & B' },
    { viewValue: 'GOVT AGENCIES' },
    { viewValue: 'HOSPITALITY' },
    { viewValue: 'IT' },
    { viewValue: 'LEGAL' },
    { viewValue: 'LOGISTICS' },
    { viewValue: 'MANUFACTURING' },
    { viewValue: 'MEDIA' },
    { viewValue: 'NON-PROFIT' },
    { viewValue: 'OIL & GAS' },
    { viewValue: 'PHARMACEUTICAL' },
    { viewValue: 'PRINTING' },
    { viewValue: 'PROPERTY' },
    { viewValue: 'RETAIL' },
    { viewValue: 'SECURITY' },
    { viewValue: 'SERVICE' },
    { viewValue: 'SHIPPING' },
    { viewValue: 'TELECOMMUNICATION' },
    { viewValue: 'TRADING' },
    { viewValue: 'TRAINING & CONSULTANCY' },
    { viewValue: 'TRAVEL' }
  ]
  salutation = [
    { viewValue: 'MR' },
    { viewValue: 'MS' },
    { viewValue: 'MRS' },
    { viewValue: 'MDM' },
    { viewValue: 'DR' }
  ]
  constructor(private apiService: ApiServiceService,public localStorage: LocalStorageService, private fb: FormBuilder, private notifyService: NotificationService, private cdgService: CdgSharedService, private router: Router) { 
    this.enquiryForm= this.fb.group({
      enquiryOn: ['', [Validators.required]],
      salutation: ['', [Validators.required]],
      name: ['', [Validators.required]],
      designation: [''],
      // contact:['',Validators.required],
      email: ['', [Validators.required]],
      companyName: ['', [Validators.required]],
      industry: ['', [Validators.required]],
      contactHp: [''],
      // , [Validators.required]
      contactFax: [''],
      contactOff: [''],
      block: ['', [Validators.required]],
      unitNo: [''],
      unitNo1:[''],
      roadName: ['', [Validators.required]],
      buildingName: [''],
      postalCode: ['', [Validators.required]],
      transportAmt: ['', [Validators.required]]
    })
  }

  ngOnInit(): void {
    this.localStorage.localStorageSet("isLoginSuccessfull",false);
  }
  onSubmit() {
    this.submitted = true;
    this.showerror = false;
   
    // const invalid=[];
    // const controls = this.enquiryForm.controls;
    // for(const name in controls){
    //   if(controls[name].invalid){
    //     invalid.push(name);
    //   }
    // }
    
    if (this.enquiryForm.controls.contactHp.value === '' && 
     this.enquiryForm.controls.contactOff.value === '' && this.enquiryForm.controls.contactFax.value === '') {
      // this.enquiryForm.controls.contactFax.value === '' ||
      this.showerror = true;
      // this.notifyService.showWarning('Please fill in atleast one contact number', 'Enquiry');
      return;
    }
    if (this.enquiryForm.controls.contactHp.value === null && 
      this.enquiryForm.controls.contactOff.value === null && this.enquiryForm.controls.contactFax.value === null) {
        // this.enquiryForm.controls.contactFax.value === null || 
      this.showerror = true;
      // this.notifyService.showWarning('Please fill in atleast one contact number', 'Enquiry');
      return;
    }

    if (this.enquiryForm.invalid) {
      this.notifyService.showWarning('Please fill in Mandatory fields', 'Enquiry');
      return;

    }
    else {
      let obj = {
        "enquiryOn": this.enquiryForm.controls.enquiryOn.value,
        "salutation": this.enquiryForm.controls.salutation.value,
        "name": this.enquiryForm.controls.name.value,
        "designation": this.enquiryForm.controls.designation.value,
        "officeHp": this.enquiryForm.controls.contactHp.value,
        "officeTelephone": this.enquiryForm.controls.contactOff.value,
        "officeFax": this.enquiryForm.controls.contactFax.value,
        "emailId": this.enquiryForm.controls.email.value,
        "companyName": this.enquiryForm.controls.companyName.value,
        "industryName": this.enquiryForm.controls.industry.value,
        "addressBlock": this.enquiryForm.controls.block.value,
        "addressUnit": '#' +this.enquiryForm.controls.unitNo.value +'-' + this.enquiryForm.controls.unitNo1.value,
        "addressRoad": this.enquiryForm.controls.roadName.value,
        "addressBuilding": this.enquiryForm.controls.buildingName.value,
        "addressPostal": this.enquiryForm.controls.postalCode.value,
        "monthlyTransportAmount": this.enquiryForm.controls.transportAmt.value
      }
      console.log(obj)
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.ENQUIRYFORM, obj).subscribe((response: any) => {
        if (response.status === 200) {
          if (response.body.successMessage !== null) {
            this.notifyService.showSuccess(response.body.successMessage, 'Enquiry');
          }
          else {
            this.notifyService.showError(response.body.errorMessage, 'Enquiry');
          }
          this.submitted = false;
          this.enquiryForm.reset();
        }
      });

    }
  }
  reset() {
    this.submitted = false;
    this.showerror = false;
    this.enquiryForm.reset();
  }
  // validationFun(event:any) {
   
  //    if(this.enquiryForm.controls.contactFax.value !== '' && 
  //    this.enquiryForm.controls.contactFax.value !== null){

  //       if(this.enquiryForm.controls.contactFax.value.toString().length < 7){
  //         this.showcontacterror=true;
  //       }
  //       else{
  //         this.showcontacterror=false;
  //       }
  //     }
  //     else{
  //       this.showcontacterror=false
  //     }
      
  // }
}





