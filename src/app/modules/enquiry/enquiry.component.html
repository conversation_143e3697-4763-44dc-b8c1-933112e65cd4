<div style="min-height: 100vh;" class="reg-container bg">


<div   fxLayout="column" fxLayoutAlign="center center" fxLayoutAlign.xs="start start" >
    <!-- style="margin-left:25px" -->
    <div class="form-div enquiry-form">
        <!-- class="" -->
        <form [formGroup]="enquiryForm" class="form">
            <div fxFlex fxLayout="row" class="mb10 fw">
                <span class="header-clr">Enquiry Form</span>
            </div>
            <div fxFlex fxLayout="row" class="mb10">
                <span>All fields with <span style="color: red;">* </span>must be filled.</span>
            </div>
            <div fxFlex fxLayout="row" fxLayout.xs= "column">
                <div fxFlex="35%"class="mt15">
                    ENQUIRY ON <span style="color: red;">* </span>
                </div>
                <div fxFlex="65%">
                    <mat-form-field appearance="outline" class="inp50">
                        <mat-select placeholder="PLEASE SELECT ONE" formControlName="enquiryOn">
                          <mat-option *ngFor = "let type of enquiryList;" [value]="type.viewValue" >{{type.viewValue}}</mat-option>
                        </mat-select>
                        <mat-error *ngIf="submitted && enquiryForm.controls.enquiryOn.hasError('required')">
                            Please Select One
                        </mat-error>
                      </mat-form-field>
                  
                </div>
            </div>
            <div fxFlex fxLayout="row" class="mb10">
                <p><u>ABOUT CONTACT PERSON</u></p>
            </div>
            <div fxFlex fxLayout="row" fxLayout.xs= "column">
                <div fxFlex="35%" fxFlex.xs="30%" class="mt15">
                    SALUTATION <span style="color: red;">* </span>
                </div>
                <div fxFlex="65%">
                    <mat-form-field appearance="outline" class="inp50">
                        <mat-select placeholder="PLEASE SELECT ONE" formControlName="salutation">
                          <mat-option *ngFor = "let item of salutation;" [value]="item.viewValue" >{{item.viewValue}}</mat-option>
                        </mat-select>
                        <mat-error *ngIf="submitted && enquiryForm.controls.salutation.hasError('required')">
                            Please Select A Salutation
                        </mat-error>
                      </mat-form-field>
                     
                </div>
            </div>
            <div fxFlex fxLayout="row" fxLayout.xs= "column">
                <div fxFlex="35%" fxFlex.xs="30%" class="mt15">
                    NAME <span style="color: red;">* </span>
                </div>
                <div fxFlex="65%">
                    <mat-form-field appearance="outline" class="inp50">
                        <input formControlName="name"  autocomplete="off" matInput required>
                        <mat-error *ngIf="submitted && enquiryForm.controls.name.hasError('required')">
                            Please Fill In Name
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
            <div fxFlex fxLayout="row" fxLayout.xs= "column">
                <div fxFlex="35%" fxFlex.xs="30%" class="mt15">
                   <span> DESIGNATION</span>
                </div>
                <div fxFlex="65%">
                    <mat-form-field appearance="outline" class="inp50">
                        <input formControlName="designation" autocomplete="off" matInput>
                    </mat-form-field>
                </div>
            </div>
            <div fxFlex fxLayout="row" fxLayout.xs="column">
                <div fxFlex="35%" fxFlex.xs="30%" class="mt15">
                   <span> CONTACT<span style="color: red;">*</span></span>
                </div>
                <div fxFlex="20%" fxFlex.xs="33.33%">
                    <mat-form-field appearance="outline" style="width:80%" >
                        <!-- class="inp80" -->
                        <input type="number" formControlName="contactHp" onkeypress="if(this.value.length>19 || event.charCode===69 || event.charCode===101)return false;" autocomplete="off" matInput > 
                        <!-- required   -->
                       
                        
                    </mat-form-field>
                    <div *ngIf="submitted && this.showerror" class="error-container">
                        Please fill in atleast one contact number
                    </div>
                    
                </div>
               

                <div class="contactmt15" fxFlex="6.6%">Hp</div>
                <div fxFlex="15%" fxFlex.xs="33.33%">
                    <mat-form-field appearance="outline" style="width:80%">
                        <!-- class="inp80" -->
                        <input type="number"  formControlName="contactOff" onkeypress="if(this.value.length>49 || event.charCode===69 || event.charCode===101)return false;"  autocomplete="off" matInput>
                        <!-- <mat-error   *ngIf="submitted && this.showerror">
                            Please fill in atleast one contact number
                        </mat-error> -->
                        <!-- required -->
                        
                    </mat-form-field>
                </div>
                <div class="contactmt15" fxFlex="6.6%">Off</div>
                <div fxFlex="15%">
                    <mat-form-field appearance="outline" style="width:80%">
                        <!-- class="inp80" -->
                        <input formControlName="contactFax" type="number" 
                        autocomplete="off" matInput >
                        <!-- (input)="validationFun($event)" -->
                       <!-- onkeypress="if(this.value.length>19 || event.charCode===69 || event.charCode===101)return false;" -->
                        
                        <!-- <mat-error   *ngIf="submitted && this.showerror">
                            Please Fill Fax
                        </mat-error> -->
                         </mat-form-field>
                </div>
              
                <div class="contactmt15">Fax</div>
               
             
            </div>
            <!-- <div  style="text-align: right;color: red;font-size: 13px;" *ngIf="this.showcontacterror" colspan="17">
                
                Fax no cannot be less than 8 digits
            </div>  -->
            <div fxFlex fxLayout="row" fxLayout.xs="column">
                <div fxFlex="35%" fxFlex.xs="30%" class="mt15">
                    <span> EMAIL<span style="color: red;">*</span></span>
                </div>
                <div fxFlex="65%">
                    <mat-form-field appearance="outline" class="inp50">
                        <input id="email"  autocomplete="off" pattern="^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$"  formControlName="email" type="email" matInput required>
                        <mat-error *ngIf="submitted && enquiryForm.controls.email.hasError('required')">
                            Please Fill In Email Address
                        </mat-error>
                        <mat-error *ngIf="submitted && enquiryForm.controls.email.hasError('pattern')">
                            Please Enter Valid Email
                        </mat-error>
                        
                    </mat-form-field>
                </div>
            </div>
            <div fxFlex fxLayout="row" class="mb10">
                <p><u>ABOUT YOUR COMPANY</u></p>
            </div>
            <div fxFlex fxLayout="row" fxLayout.xs="column">
                <div fxFlex="35%" fxFlex.xs="30%" class="mt15">
                    NAME OF COMPANY <span style="color: red;">* </span>
                </div>
                <div fxFlex="65%">
                    <mat-form-field appearance="outline" class="inp50">
                        <input formControlName="companyName"  autocomplete="off" matInput required>
                        <mat-error *ngIf="submitted && enquiryForm.controls.companyName.hasError('required')">
                            Please Fill In Company Name 
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
            <div fxFlex fxLayout="row" fxLayout.xs="column">
                <div fxFlex="35%" fxFlex.xs="30%" class="mt15">
                    INDUSTRY <span style="color: red;">* </span>
                </div>
                <div fxFlex="65%">
                    <mat-form-field appearance="outline" class="inp50">
                        <mat-select placeholder="PLEASE SELECT ONE" formControlName="industry">
                          <mat-option *ngFor = "let industry of industryList;" [value]="industry.viewValue" >
                              {{industry.viewValue}}
                         </mat-option>
                        </mat-select>
                        <mat-error *ngIf="submitted && enquiryForm.controls.industry.hasError('required')">
                            Please Select Industry
                        </mat-error>
                      </mat-form-field>
                    
                </div>
            </div>
            <div fxFlex fxLayout="row" fxLayout.xs="column">
                <div fxFlex="35%" fxFlex.xs="30%" class="mt15">
                    MAILING ADDRESS <span style="color: red;">* </span>
                </div>
                <div fxFlex="65%" fxLayout="column">
                    <div fxFlex fxLayout="row" fxLayout.xs="column">
                        <div fxFlex="30%" class="mt15">BLOCK/STREET NO.<span style="color: red;">* </span></div>
                        <div fxFlex="70%">
                            <mat-form-field appearance="outline" class="inp80">
                                <input formControlName="block" matInput autocomplete="off" required>
                                <mat-error *ngIf="submitted && enquiryForm.controls.block.hasError('required')">
                                    Please Fill In Block/Street Number
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div fxFlex fxLayout="row" fxLayout.xs="column">
                        <div fxFlex="30%" class="mt15"> <span>UNIT NO </span></div>
                        <div fxFlex="5%" class="mt15"> <span># </span></div>
                        <div fxFlex="20%">
                            <mat-form-field appearance="outline" style="width:80%">
                                <!-- class="inp80"  -->
                                <input formControlName="unitNo"  autocomplete="off" matInput>
                            </mat-form-field>
                        </div>
                        <div fxFlex="5%" class="mt15"> <span fxLayoutAlign="center center">- </span></div>
                        <div fxFlex="40%">
                            <mat-form-field appearance="outline" class="inp80">
                                <input formControlName="unitNo1"  autocomplete="off" matInput>
                            </mat-form-field>
                        </div>
                    </div>
                    <div fxFlex fxLayout="row" fxLayout.xs="column">
                        <div fxFlex="30%" class="mt15">ROAD NAME <span style="color: red;">* </span></div>
                        <div fxFlex="70%">
                            <mat-form-field appearance="outline" class="inp80">
                                <input formControlName="roadName"  autocomplete="off" matInput required>
                                <mat-error *ngIf="submitted && enquiryForm.controls.roadName.hasError('required')">
                                    Please Fill In Road Name
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div fxFlex fxLayout="row" fxLayout.xs="column">
                        <div fxFlex="30%" class="mt15"><span>BUILDING NAME</span></div>
                        <div fxFlex="70%">
                            <mat-form-field appearance="outline" class="inp80">
                                <input formControlName="buildingName"  autocomplete="off" matInput >
                            </mat-form-field>
                        </div>
                    </div>
                    <div fxFlex fxLayout="row" fxLayout.xs="column">
                        <div fxFlex="30%" class="mt15">POSTAL CODE<span style="color: red;">* </span></div>
                        <div fxFlex="70%">
                            <mat-form-field appearance="outline" class="inp80">
                                <input formControlName="postalCode" type="number" onkeypress="if(event.charCode===69 || event.charCode===101)return false;" autocomplete="off" matInput required>
                                <mat-error *ngIf="submitted && enquiryForm.controls.postalCode.hasError('required')">
                                    Please Fill In Postal Code
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>
            <div fxFlex fxLayout="row" fxLayout.xs="column">
                <div fxFlex="35%" fxFlex.xs="30%" class="mt15">
                    MONTHLY TRANSPORT AMOUNT ($) <span style="color: red;">* </span>
                </div>
                <div fxFlex="65%">
                    <mat-form-field appearance="outline" class="inp50">
                        <input formControlName="transportAmt" type="number" onkeypress="if(event.charCode===69 || event.charCode===101)return false;" autocomplete="off" matInput required>
                        <mat-error *ngIf="submitted && enquiryForm.controls.transportAmt.hasError('required')">
                            Please Fill In Transport Amount
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
           
            
            <div fxFlex class="mb10">
                <p class="cursor-pointer">
                   Note: Once you click submit, please wait for the acknowledgement message to ensure we
                     receive your submission. If you do not see <br>the acknowledgement message, please submit again
                </p>
            </div>
            <br>
            <div fxFlex fxLayout="row" fxLayoutGap="2%" fxLayoutAlign="center center" class="mb20">
                <div fxFlex="20%" fxFlex.xs="42%">
                    <button mat-raised-button class="inp signin-btn btn" (click)="onSubmit()">SUBMIT</button>
                </div>
                <div fxFlex fxFlex="20%" fxFlex.xs="42%">
                    <button mat-raised-button class="inp register-btn btn" (click)="reset()">RESET</button>
                </div>
            </div>
     
        </form>
    </div>
</div>

</div>