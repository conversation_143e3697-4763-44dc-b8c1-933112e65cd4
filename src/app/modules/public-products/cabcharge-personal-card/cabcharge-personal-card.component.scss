@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';

.img{
    height: 132px ;
    width:228px;
    background-color: gray;
}

.mat-mdc-raised-button {
    border-radius: 35px !important;
    width: 228px !important;
    margin: 24px 0 24px 0 !important;
    height: 40px !important;
    background-color: $cdgc-hue !important ;
    font-size: 14px !important;
    line-height: 16px !important;
    
}

ul{
    padding-left: 0.9375em !important;
}

ul li{
    padding: 0 0 16px 5px !important;
    font-size: 10px !important;
    line-height: 16px !important;
     font-family: $cdgsz-font-family !important;
     font-weight: $cdgsz-font-weight-normal !important;
}

.heading{
    font-size: 24px !important;
}

.mat-icon{
    color: $cdgc-bg-prime !important;
    padding-right: 12px !important;
}

.mat-mdc-outlined-button{
    color: $cdgc-font-prime !important;
    border: 1px solid $cdgc-border-prime !important;
    width: 162px !important;
    height: 40px !important;
    border-radius: 5px !important;
    font-size: $cdgsz-font-size-xxs !important;
}

.title{
    font-size: $cdgsz-font-size-xxs;
    font-family:$cdgsz-font-family ;
    font-weight: $cdgsz-font-weight-bold;
    line-height: 16px;
    margin: 0;
}

.content{
    font-size: $cdgsz-font-size-xs;
    font-family:$cdgsz-font-family ;
    line-height: 23px;
    font-weight: $cdgsz-font-weight-normal ;
    padding: 16px 0 8px 0;
}