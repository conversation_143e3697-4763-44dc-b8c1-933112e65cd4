import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { DatePipe } from '@angular/common';
import { Moment } from 'moment';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { CdgSharedService } from './../../shared/services/cdg-shared.service';
import { Pipe, PipeTransform } from '@angular/core';
// import { MatTableDataSource } from '@angular/material/table';
// import { MatPaginator } from '@angular/material/paginator';
import { NotificationService } from './../../shared/services/notification.service';
import { HttpClient } from '@angular/common/http';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { CommonModalComponent } from 'src/app/shared/shared-modules/common-modal/common-modal.component';

export const MY_FORMATS = {
  parse: {
    dateInput: 'DD MMM YYYY'
  },
  display: {
    dateInput: 'DD MMM YYYY',
    monthYearLabel: 'DD MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'DD MMM YYYY'
  }
}
export interface Element {
  holidayDesc: string;
  holidayDate: Date;
}
@Component({
  selector: 'app-ph-config',
  templateUrl: './ph-config.component.html',
  styleUrls: ['./ph-config.component.scss'],
  providers: [DatePipe, { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] }, { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]
})
export class PhConfigComponent implements OnInit {
  selectedValue: any;
  holidayDesc: any;
  isLoading: boolean = true;
  selectedDate: any
  list: any[] = []
  //  dataSource!: MatTableDataSource<any>;
  // dataSource = this.holidayList.publicHolidayList;
  // private paginator! : MatPaginator;
  // @ViewChild(MatPaginator) set matPaginator(mp: MatPaginator){
  //   this.paginator = mp;
  //   this.setDataSourceAttributes();
  // }
  phConfigData: any[] = [];
  Paginator: any;
  @ViewChild(MatPaginator, { static: false })
  set paginator(value: MatPaginator) {
    this.Paginator = value;
    this.dataSource.paginator = value;
  }
  dataSource = new MatTableDataSource<any>(this.phConfigData);
  public displayedColumns = ['event', 'action'];
  lastSevenYears: number[] = [];
  constructor(public dialog: MatDialog, private router: Router, public cdgService: CdgSharedService, public datePipe: DatePipe, private notifyService: NotificationService, private http: HttpClient, private apiService: ApiServiceService) {

  }

  ngOnInit(): void {
    this.cdgService.isDataFromAccessId = false;
    let max = new Date().getFullYear();
    let min = max - 3;
    max = max + 1;
    for (var i = min; i <= max; i++) {
      this.lastSevenYears.push(i);
    }
    if (this.cdgService.newPhAddedYr) {
      this.selectedDate = this.cdgService.newPhAddedYr;
    } else {
      this.selectedDate = this.lastSevenYears[this.lastSevenYears.length - 2]
    }
    this.retriveByYear();
  }
  retriveByYear() {
    let holidayArr: any[] = []
    this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2  + UrlConstants.GET_HOLIDAY_BY_YR + this.selectedDate).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body;
        console.log(opt.publicHolidayList)
        opt.publicHolidayList.forEach((data: any) => {
          const splitData=data.holidayDate.split("-")
          let obj = {
            "holidayDesc": data.holidayDesc,
            "holidayDate": splitData[2]+"-"+splitData[1]+"-"+splitData[0]
          }
          // this.cdgService.dateConversion(data.holidayDate)
          holidayArr.push(obj)
        });
        console.log(holidayArr)
        this.list = holidayArr
        this.dataSource.data = holidayArr;
        // setTimeout(()=>this.dataSource.paginator = this.paginator,1500);
      }
    },
      e => {
        console.log(e)
      });
  }
  onEdit(row: any, i: any) {
    this.cdgService.dataRow = row;
    //this.dataSource.paginator = this.paginator;
    this.router.navigate(['layout/ph/edit'])
  }
  Search() {
    let holidayArr: any[] = []
    this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_HOLIDAY_BY_DESC + this.holidayDesc).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body;
        console.log(response)
        opt.publicHolidayList.forEach((data: any) => {
          let obj = {
            "holidayDesc": data.holidayDesc,
            "holidayDate": this.cdgService.dateConversion(data.holidayDate)
          }
          holidayArr.push(obj)
        });
        console.log(holidayArr)
        this.list = holidayArr
        this.dataSource.data = holidayArr;
        //  setTimeout(()=>this.dataSource.paginator = this.paginator,3000);
        //  this.isLoading = false;
      }
    },
      e => {
        console.log(e);
        // this.isLoading = false;
      });
  }


  onBackButtonClick() {
    this.router.navigate(['layout/admindashboard']);
  }
  goToAdd() {
    // this.cdgService.addHolidayListDetails();
    this.router.navigate(['layout/ph/add']);
  }
  onDelete(row: any, i: any) {
    const dialogConfig = new MatDialogConfig()
    dialogConfig.data = {
      title: "Confirmation",
      msg: "Are you sure you want to delete ?",
      btnClose: "Cancel",
      btnConfirm: "Ok",
      func: 'delete'
    }
    const dialogRef = this.dialog.open(CommonModalComponent, dialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.apiService.delete(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.DELETE_PUBLIC_HOLIDAY + this.datePipe.transform(row.holidayDate, "dd-MM-yyyy")).subscribe((response: any) => {
          if (response.status === 200) {
            let opt = response.body;
            this.retriveByYear();
            this.notifyService.showSuccess(opt.message, "Success")
          }
        },
          e => {
            console.log(e)
          });
      }
    });
  }

  // Public holiday saved.----for add
  //Public Holiday 2021-02-27 updated successfully------edit
}




