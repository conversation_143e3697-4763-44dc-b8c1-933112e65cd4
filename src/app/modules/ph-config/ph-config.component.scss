@import '@angular/material/theming';
@import './../../../styles/colors.scss';
@import './../../../styles/sizing.scss';
@import './../../../styles/main.scss';
// .overallBody{
//     margin-top:50px;
//     margin-left:300px;
// }
.title {
    font-family: $cdgsz-font-family;
    font-style: normal;
    font-weight: bold;
    font-size: $cdgsz-font-size-lg;
    line-height: 23px;
}
.mat-icon{
    color: $cdgc-font-prime;
}

.sub-title {
    font-family: $cdgsz-font-family;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 16px;
}
.mr140{
    margin-right:140px;
}
.font-12{ 
    font-size:  $cdgsz-font-size-xs;
}
.font-14{
   font-size: $cdgsz-font-size-sm; 
}
.full-height{
    height:100vh
}
.mb20{
    margin-bottom: 20px;
}
.add-btn{
    background: $prime !important;
    color:$white !important;
    border-radius: 4px;
    height: 36px;
    width: 124px;
    box-shadow: 0px 0px 2px rgba(0,0,0,0.12), 0px 2px 2px;
    font-family: Roboto;
    font-style: normal;
    font-weight: 500;
    font-size: 14px !important; 
    line-height: 16px;
}
.inp-w80{
    width:80% !important;
}
.actn_icon{
    font-size: 20px;
    width:20px;
    height:20px;
    color: $prime !important;
    cursor:pointer;
}
table {
  width: 100%;
}
.mat-mdc-header-cell{ 
    background-color: #006BA8 !important;
    color:$white;
}

// ::ng-deep .th.mat-header-cell:last-of-type, td.mat-cell:last-of-type, td.mat-footer-cell:last-of-type {
//     padding-left: 50px !important;
//     padding-right: 0px !important;
// }
.fwb{
    font-weight:$cdgsz-font-weight-bold;
}
.mat-mdc-row:nth-child(even){
    background-color: #E9F7FF !important;
}
.mat-mdc-row:nth-child(odd){ 
    background-color: #CBECFF !important;
}

@media screen and (min-width: 768px) and (max-width: 1240px) {
    .ml50{
        margin-left:50px;
    }
    .mr50{
        margin-right:50px; 
    }
    .mat-column-action{
        width:10%
    }
 }
 @media screen and (min-width: 1030px) and (max-width: 1366px) {
    .ml50{
        margin-left:50px;
    }
    .mr50{
        margin-right:50px; 
    }
    .mat-column-action{
        width:10%
    }
 
 }
 @media screen and (max-width: 960px) {
    // .mat-column-action{
    //     width:30%
    // }
    // ::ng-deep  .mat-select{
    //     width:50% !important;
    //   }
    //   ::ng-deep .mat-select-arrow-wrapper{
    //     padding-top: 5px !important;
    //   }
      .mat-column-action{
        width:30%
    }
    //   ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
    //     border: 2px solid $cdgc-border-aux !important;
    //     background: $white !important;
    //     width: 100% !important;
    //     }
    //    .ml-btn{
    //        margin-left:50px;
    //    } 
    //    .mr-btn{
    //     margin-right:50px;
    // } 
 
 }
 .align{
    text-align: center !important;
 }
 .loader{
    display:flex;
    justify-content: center;
    align-items: center;
    background: $white;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep.mat-form-field-appearance-outline .mat-mdc-form-field-text-infix {
    padding: 0 0 1em 0 !important; 
}
.word-break{
    word-break: break-all !important;
}
table{
    border-spacing: unset !important;
    // border-top-left-radius: 10px !important;
}
.header{
    background-color: $cdgc-bg-prime !important;
    color: $cdgc-bg-accent !important;
}
// ::ng-deep th{
//     padding: 12px !important;
//     text-align: inherit !important;
//     padding-left: 24px !important;
// }
// ::ng-deep tr:nth-child(even){
//     background-color: #E9F7FF !important;
   
// }
// ::ng-deep tr:nth-child(odd){ 
//     background-color: #CBECFF !important;
//     padding-left: 24px !important;
//     padding-right: 24px !important;
// }
// ::ng-deep td{ 
//     padding-left: 24px !important;
//     padding-right: 24px !important;
// }

:host ::ng-deep mat-mdc-select-value {
    color: black!important;
}

  

    