<div  fxLayout="column" class="full-height">

  <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="center center">

    <div fxFlex fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="start center" fxFlex.xs=61% fxFlex.md=61%>
      <div class="cursor-pointer">
        <mat-icon (click)="onBackButtonClick()">arrow_back</mat-icon>
      </div>
      <div>
        <div class="title">PH Configuration</div>
      </div>
    </div>
    <div  fxLayout="row" fxLayout.lt-md="row" fxLayoutAlign="end center" class="cursor-pointer">
      <button mat-raised-button class="add-btn" (click)="goToAdd()">+ ADD</button>
    </div>
  </div><br>
  <!-- <div fxLayout="row" fxFlex=5% fxFlex.sm=10% fxFlex.md=10% class="ml5 mr5"> -->
  <p class="sub-title ml5 mr5 mb20">Manage and edit Public Holiday configuration</p>
  <!-- </div> -->

  <div fxLayout="row" fxLayoutAlign="start center" fxLayoutAlign.sm="start start" fxLayoutGap="5px"
    class="ml-btn mr-btn">
    <!-- <div fxFlex=25% fxFlex.xs="50%" fxFlex.sm="50%">
                <mat-form-field appearance="outline" class="inp">
                  <input matInput  type="text" name = "holidayDesc" [(ngModel)]="holidayDesc" 
                   placeholder="Search">
                  <mat-icon matSuffix class="cursor-pointer" (click)="Search()">search</mat-icon>
              </mat-form-field>
               
            </div> -->
    <div fxFlex=25% fxFlex.xs="50%" fxFlex.sm="50%">
      <mat-form-field appearance="outline" class="inp-w80">
        <mat-select [(ngModel)]="selectedDate" name="year" [ngModelOptions]="{standalone:true}"
          (ngModelChange)="retriveByYear()">
          <mat-option *ngFor="let yr of lastSevenYears" [value]="yr">
            {{yr}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>
  <!-- <div fxLayout="row" fxLayoutAlign="start center"> -->
    <!-- <div class="mat-elevation-z8 mb15">
      <table mat-table [dataSource]="dataSource" >
        <ng-container matColumnDef="event">
          <th mat-header-cell *matHeaderCellDef class="fwb font-16">Public Holiday List </th>
          <td mat-cell *matCellDef="let element;let i = index">
            <div class="fwb font-14 word-break"> {{element.holidayDesc}} </div>
            <div class="font-12" *ngIf="element.holidayDate"> {{element.holidayDate | date:'yyyy-MM-dd'}} </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef> </th>
          <td mat-cell *matCellDef="let element;let j = index">
            <div >
              <button mat-icon-button style="cursor: pointer;" (click)=onEdit(element,j)>  <mat-icon class="actn_icon" >mode_edit</mat-icon></button>
              <button mat-icon-button style="cursor: pointer;" (click)=onDelete(element,j)>  <mat-icon class="actn_icon" >delete_forever</mat-icon></button>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" colspan="12" class="align">No Data Found</td>
        </tr>
      </table>
    </div> -->
      <!-- <table>
        <tr class="header">
          <th>Public Holiday List</th>
          <th></th>
        </tr>
        <tr *ngFor="let val of list;let j = index">
          <td>
            <div class="fwb font-14 word-break"> {{val.holidayDesc}} </div>
            <div class="font-12" *ngIf="val.holidayDate"> {{val.holidayDate }} </div>
          </td>
          <td>
            <div fxLayout="row" fxLayoutAlign="end center" >
              <button mat-icon-button style="cursor: pointer;" (click)=onEdit(val,j)>  <mat-icon class="actn_icon" >mode_edit</mat-icon></button>
              <button mat-icon-button style="cursor: pointer;" (click)=onDelete(val,j)>  <mat-icon class="actn_icon" >delete_forever</mat-icon></button>
             
            </div>
          </td>
        </tr>
      </table> -->
      <div class="tbl-container">
      <mat-table #table [dataSource]="dataSource">
        <ng-container matColumnDef="event">
          <mat-header-cell *matHeaderCellDef class="fwb font-16">Public Holiday List</mat-header-cell>
          <mat-cell *matCellDef="let element;let i = index">
            <!-- <span class="mobile-label"> Public Holiday List</span> -->
            <div fxLayout="column">
            <div class="fwb font-14 word-break"> {{element.holidayDesc}} </div>
            <div class="font-12" *ngIf="element.holidayDate"> {{element.holidayDate}} </div>
          </div>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef class="fwb font-16 pr15"></mat-header-cell>
          <mat-cell *matCellDef="let element;let i = index" class="pr15" fxLayoutAlign="end center">
            <!-- <span class="mobile-label"> ACCOUNT NAME/CARD NO</span> -->
            <div >
              <button mat-icon-button style="cursor: pointer;" (click)=onEdit(element,i)>  <mat-icon class="actn_icon" >mode_edit</mat-icon></button>
              <button mat-icon-button style="cursor: pointer;" (click)=onDelete(element,i)>  <mat-icon class="actn_icon" >delete_forever</mat-icon></button>
            </div>
          </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" colspan="12" class="align">No Data Found</td>
        </tr>
      </mat-table>

      <!-- <div *ngIf="isLoading" class="loader">
              <mat-progress-spinner color="primary" mode="indeterminate" diameter="55" strokeWidth="5">
              </mat-progress-spinner>
            </div> -->
      <mat-paginator *ngIf="dataSource.data.length > 5" [pageSizeOptions]="[5, 10, 20, 50, 100]" showFirstLastButtons>
      </mat-paginator>
    </div>
  </div>

<!-- </div> -->