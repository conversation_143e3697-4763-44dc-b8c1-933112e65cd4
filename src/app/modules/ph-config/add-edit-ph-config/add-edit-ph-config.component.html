<div fxLayout="column" style="height: inherit;" class="ml20 mt20">
	<div fxLayout="row">
		<div fxFlex fxLayout="row" fxLayoutGap="15px">
			<div class="cursor-pointer">
				<mat-icon (click)="onBackButtonClick()">arrow_back</mat-icon>
			</div>
			<div>
                <span class="title" *ngIf="isAdd">Add New PH</span>
                <span class="title" *ngIf="isEdit">Edit PH</span>
			</div>
		</div>
    </div>

    <div fxFlex  fxLayoutAlign="start start" *ngIf="isAdd">
        <div class="form-div">
            <p class="font-12 fw-n">Enter the details of new public holiday</p>
            <form class="form" [formGroup]="addNewPhForm" (ngSubmit)="onAdd()">
                <div fxFlex fxLayout="column" class="form-inner-div">
                  
                    <div fxFlex>
                        <mat-form-field appearance="outline" class="inp">
                            <input matInput placeholder="Name" formControlName="holidayDesc" >
                            <mat-error *ngIf="submitted && addNewPhForm.hasError('required')">
                                Please enter Name
                            </mat-error>
                        </mat-form-field>
                    </div>

                    <div fxFlex>
                        <mat-form-field style="width:65%;" appearance="outline">
                            <mat-icon matSuffix (click)="picker11.open()">date_range</mat-icon>
                            <input matInput placeholder="Date" formControlName="holidayDate"  [matDatepicker]="picker11" >
                        <mat-datepicker #picker11></mat-datepicker>
                        </mat-form-field>
                    </div>

                    <!-- <div fxFlex>
                        <mat-form-field style="width:60%" appearance="outline"   >
                            <mat-select formControlName="year" placeholder="year" (change)="choosedYear($event)">
                                <mat-option *ngFor = "let year of years" [value]="year">
                                {{year.year}}
                                </mat-option>
                               </mat-select>
                        </mat-form-field>
                    </div> -->
                    <div fxFlex>
                        <button mat-raised-button class="sub-btn" >ADD</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div fxFlex  fxLayoutAlign="start start" *ngIf="!isAdd">
        <div class="form-div">
            <p class="font-12 fw-b">Edit the details of a public holiday</p>
            <form class="form" [formGroup]="editForm">
                <div fxFlex fxLayout="column" class="form-inner-div">
                  
                    <div fxFlex>
                        <mat-form-field appearance="outline" class="inp">
                            <input type="text" matInput formControlName="holidayDesc"
                             [(ngModel)]="formDetails.holidayDesc" 
                            required>
                            <mat-error *ngIf="submitted && addNewPhForm.hasError('required')">
                                Please enter Name
                            </mat-error>
                        </mat-form-field>
                    </div>

                    <div fxFlex>
                        <mat-form-field style="width:60%;" appearance="outline">
                            <mat-icon matSuffix (click)="picker12.open()">date_range</mat-icon>
                            <input matInput placeholder="Date" formControlName="holidayDate"  [(ngModel)]="formDetails.holidayDate" [disabled]="isEdit" [matDatepicker]="picker12">
                        <mat-datepicker #picker12></mat-datepicker>
                        </mat-form-field>
                    </div>

                    <div fxFlex>
                        <button mat-raised-button class="sub-btn" (click)="onSubmit()">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>












<!-- <div *ngIf="!isAdd">
    <form [formGroup]="editForm">
        <div fxLayout="column" class="form-inner-div">
            <div fxFlex="20%"><span class="label">Name</span></div>
            <div fxFlex="80%">
                <mat-form-field appearance="outline" class="inp">
                    <input type="text" matInput formControlName="holidayDesc"
                        [(ngModel)]="formDetails.holidayDesc" 
                        required>
                </mat-form-field>
            </div>
        </div>
        <div fxLayout="column" class="form-inner-div">
            <div fxFlex="20%"><span class="label">Date</span></div>
            <div fxFlex="80%">
                <mat-form-field style="width:60%;" appearance="outline">
                    <mat-icon matSuffix (click)="picker12.open()">date_range</mat-icon>
                    <input matInput placeholder="Date" formControlName="holidayDate"  [(ngModel)]="formDetails.holidayDate"  [matDatepicker]="picker12">
                <mat-datepicker #picker12></mat-datepicker>
                </mat-form-field>
            </div>

        </div>
       
        <div fxLayout="row" fxLayoutGap="15px">
            <div >
                <button mat-raised-button class="sub-btn" (click)="onSubmit()">Save</button>
            </div>
        </div>
    </form>
</div> -->

  




    
   