@import '@angular/material/theming';
@import './../../../.././styles/colors.scss';
@import './../../../.././styles/sizing.scss';
@import './../../../.././styles/main.scss';
.font-12{ 
    font-size:  $cdgsz-font-size-xs;
}
.title {
    font-family: $cdgsz-font-family;
    font-style: normal;
    font-weight: bold;
    font-size: $cdgsz-font-size-lg;
    line-height: 23px;
}

.sub-btn{
        background: $prime;
        color:$white;
        border-radius: 4px;
        height: 36px;
        width: 124px;
        font-family: Roboto;
        font-weight: 500;
        font-size: 14px !important; 
}
.mat-icon{
    color: $cdgc-font-prime;
}
.full-height {
    height: 100vh !important;
  }
  .mt150{
      margin-top:150px;
  }
  .fw-n{
      font-weight:normal;
  }
   .invalid-entry
  {
      color:$red;
  }
  @media screen and (max-width: 960px) {
    .fogot-pwd-img-container{
       display:none;
   }
   .mt150{
       margin-top:65px;
   }

}