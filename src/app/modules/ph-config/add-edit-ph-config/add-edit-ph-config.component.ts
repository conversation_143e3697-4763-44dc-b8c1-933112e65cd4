import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { CdgSharedService } from './../../../shared/services/cdg-shared.service';
import { NotificationService } from '../../../shared/services/notification.service';
import { FormControl } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { DatePipe } from '@angular/common';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';

export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
}

import { Moment } from 'moment';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
@Component({
  selector: 'app-add-edit-ph-config',
  templateUrl: './add-edit-ph-config.component.html',
  styleUrls: ['./add-edit-ph-config.component.scss'],
  providers: [DatePipe, { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] }, { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]
})
export class AddEditPhConfigComponent implements OnInit {
  //  addNewPhForm: FormGroup;
  submitted = false;
  formDetails: any = {
    holidayDesc: "",
    holidayDate: ""
  };
  // addMode: any;
  editMode: any;
  holidaydate: any;
  isEdit: boolean = false;
  isAdd: boolean = false;

  //  public years = [
  //   {year: 2021},
  //   {year: 2022},
  //   {year: 2023},
  //   {year: 2024}
  // ];
  addNewPhForm:FormGroup;
  editForm: FormGroup
  constructor(private fb: FormBuilder, private router: Router, private route: ActivatedRoute,
    private cdgService: CdgSharedService, private http: HttpClient, private datePipe: DatePipe, private apiService: ApiServiceService, private notifyService: NotificationService) { 
      this.addNewPhForm= this.fb.group({
        holidayDesc: ['', Validators.required],
        holidayDate: ['', Validators.required],
        // year: [2021, Validators.required]
      });
      this.editForm = this.fb.group({
        holidayDesc: ['', Validators.required],
        holidayDate: ['', Validators.required]
      })
    }

  ngOnInit(): void {
    this.cdgService.addMode = this.route.snapshot.paramMap.get('add');
    console.log(this.cdgService.addMode)
    if (this.cdgService.addMode === 'add') {
      console.log('add form');
      this.isAdd = true;
      this.isEdit = false
    }
    else {
      this.isEdit = true;
      this.isAdd = false;
      this.formDetails.holidayDesc = this.cdgService.dataRow.holidayDesc;
      this.formDetails.holidayDate = new Date(this.cdgService.dataRow.holidayDate);
      console.log(this.formDetails)

    }
    // this.editMode = this.route.snapshot.paramMap.get('edit');
    // console.log(this.editMode)
    // if(this.editMode != null || this.editForm != undefined){
    //   console.log('edit form');
    // this.isEdit = true;
    // this.isAdd = false;
    // this.formDetails = this.cdgService.dataRow;

    // }
  }
  get f() {
    return this.addNewPhForm.controls;
  }
  // choosedYear(e:any){
  // console.log(e.target.value);
  // }
  onAdd() {
    this.submitted = true;
    if (this.addNewPhForm.invalid) {
      return;
    }
    else {

      console.log(this.addNewPhForm.value)
      this.cdgService.newPhAddedYr = moment(this.addNewPhForm.value.holidayDate, "DD/MM/YYYY").year()
      console.log(this.cdgService.newPhAddedYr)
      // this.addNewPhForm.controls.holidayDate= new FormControl(moment());
      this.cdgService.addHolidayListDetails(this.addNewPhForm.value)
      //localhost:9051/api/module/addpublicholiday
      let objdate = {
        "holidaydesc": this.addNewPhForm.value.holidayDesc,
        "holidaydate": this.datePipe.transform(this.addNewPhForm.value.holidayDate, "dd-MM-yyyy")


      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 +'/api/module/addpublicholiday', objdate).subscribe((response: any) => {
        console.log('api value =>', response);
        this.notifyService.showSuccess(response.message, "Success");
      });
    }
  }
  onSubmit() {
    this.cdgService.getUpdatedPhConfig(this.formDetails);
    // localhost:9051/api/module/updatepublicholiday
    let objupdate = {
      "holidaydesc": this.formDetails.holidayDesc,
      "holidaydate": this.datePipe.transform(this.formDetails.holidayDate, "dd-MM-yyyy")
    }
    this.cdgService.newPhAddedYr = new Date(this.formDetails.holidayDate).getFullYear()
    this.apiService.put(this.cdgService.localhostUrlNew + UrlConstants.WSO2 +'/api/module/updatepublicholiday', objupdate).subscribe((response: any) => {
      console.log('api value =>', response);
      this.notifyService.showSuccess(response.message, "Success");

    });
  }
  onBackButtonClick() {
    this.router.navigate(['layout/ph']);
  }
}







