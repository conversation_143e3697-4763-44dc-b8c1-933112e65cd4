<!-- 

<div fxFlex fxLayout="row" class="full-height" fxLayoutAlign="center center">
    <div fxLayout="column" fxLayoutAlign="center center">    
        <div fxLayout="row" fxFlex=20%>
            <div fxFlex fxLayout="column">          
               <p class="font-12">Your Password has been Changed</p>
               <mat-icon>done<mat-icon>
                  <div fxFlex fxLayoutAlign="center center">
                    <button
                    style="width: 100%"
                    mat-raised-button
                    class="sub-btn"
                    (click)="onClick()">
                    Sign In
                  </button>
                  </div>
            </div>
        </div>
    </div>
</div> -->


 

<div  fxFlex fxLayout="row" class="full-height" fxLayoutAlign="center center">
<div fxLayout="column" fxLayoutAlign="center center" >
 <mat-icon>done</mat-icon>
  <p class="font-12">Your Password has been Changed</p>
 <div fxFlex fxLayoutAlign="center center"  style="width:100%">
 <button mat-raised-button class="sub-btn" (click)="onClick()">  Sign In
 </button>
 </div>
</div>
</div>

