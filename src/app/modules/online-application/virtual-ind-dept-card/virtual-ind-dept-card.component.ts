import { Component, OnInit } from '@angular/core';
import { NgForm, FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms'
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';
import { element } from 'protractor';
import { CommonModalComponent } from 'src/app/shared/shared-modules/common-modal/common-modal.component';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';

@Component({
  selector: 'app-virtual-ind-dept-card',
  templateUrl: './virtual-ind-dept-card.component.html',
  styleUrls: ['./virtual-ind-dept-card.component.scss']
})
export class VirtualIndDeptCardComponent implements OnInit {

  deparmentCard: any[] = [];
  virtualCard: any[] = [];
  individualCard: any[] = [];
  productValue: string = "";
  isIndCard: boolean = false;
  isVirtCard: boolean = false;
  isDeptCard: boolean = true;

  divArray: any[] = [];
  deptArray: any[] = [];
  isShowDepartment: boolean = false;
  frstDivSelection: any;
  fetchDept: any[] = [];
  deptValue: any;
  defaultDept: any;
  companyName: any;


  departmentData = {
    costCenter: "",
    deptName: "",
    nameOnCard: "",
    numberOfCard: "",
    remarks: "",
    dept: {},
    contactPerson: "",
    contactTel: "",
    constactMob: "",
    addressBlock: "",
    addressUnit: "",
    addressStreet: "",
    addressBuilding: "",
    addressArea: "",
    countryCode: "",
    city: "",
    state: "",
    postal: "",
    deptArray: [],
    isShowDept: false,
    isShowShippingDetails: false
  }
  virtualData = {
    costCenter: "",
    salutation: "",
    name: "",
    nameOnCardVir: "",
    jobTitle: "",
    telephone: "",
    mobileNo: "",
    email: "",
    remarks: "",
    dept: {},
    contactPerson: "",
    contactTel: "",
    constactMob: "",
    addressBlock: "",
    addressUnit: "",
    addressStreet: "",
    addressBuilding: "",
    addressArea: "",
    countryCode: "",
    city: "",
    state: "",
    postal: "",
    deptArray: [],
    isShowDept: false,
    isShowShippingDetails: false
  }

  individualData = {
    costCenter: "",
    salutation: "",
    name: "",
    nameOnCardInd: "",
    jobTitle: "",
    telephone: "",
    mobileNo: "",
    email: "",
    remarks: "",
    dept: {},
    contactPerson: "",
    contactTel: "",
    constactMob: "",
    addressBlock: "",
    addressUnit: "",
    addressStreet: "",
    addressBuilding: "",
    addressArea: "",
    countryCode: "",
    city: "",
    state: "",
    postal: "",
    deptArray: [],
    isShowDept: false,
    isShowShippingDetails: false
  }
  nameTitle = [
    { value: 'dr', viewValue: 'DR' },
    { value: 'mdm', viewValue: 'MDM' },
    { value: 'mr', viewValue: 'MR' },
    { value: 'mrs', viewValue: 'MRS' },
    { value: 'ms', viewValue: 'MS' },
    { value: 'pro', viewValue: 'PRO (PROFESSOR)' },
  ]
  productType = [
    { value: 'dc', viewValue: 'Departmental Card' },
    { value: 'ic', viewValue: 'Individual Contactless Corporate card' },
    { value: 'vc', viewValue: 'Virtual Card' },
  ]
  isShowDept: boolean = false
  isShowShippingDetails: boolean = false
  accountnumber: any;
  isCorpInvoice: boolean;
  isCabinCrew: boolean;
  hideError: boolean = true;
  hideIndError: boolean = true;
  hideVirError: boolean = true;
  isCardTypeDisable: boolean = false;
  constructor(private router: Router, public dialog: MatDialog, private fb: FormBuilder, public localStorage: LocalStorageService, private apiService: ApiServiceService, public cdgService: CdgSharedService, private notifyService: NotificationService) { }

  ngOnInit(): void {
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }
    }

    this.isCardTypeDisable = false;
    this.getDivision();
    if (this.cdgService.cardTypeSelected.isComingFromPreview) {
      if (this.cdgService.cardTypeSelected.function === "cancel") {
        this.isCardTypeDisable = false;
        this.onProductSelection(this.cdgService.cardTypeSelected.cardType)
        this.productValue = this.cdgService.cardTypeSelected.cardType;
        if (this.cdgService.cardTypeSelected.cardType === "dc") {
          this.localStorage.localStorageSet("deparmentCard", []);
        }
        else if (this.cdgService.cardTypeSelected.cardType === "ic") {
          this.localStorage.localStorageSet("individualCard", []);
        }
        else if (this.cdgService.cardTypeSelected.cardType === "vc") {
          this.localStorage.localStorageSet("virtualCard", []);
        }
        this.reset();
      }
      else {
        this.isCardTypeDisable = true;
        this.productValue = this.cdgService.cardTypeSelected.cardType;
        this.onProductSelection(this.cdgService.cardTypeSelected.cardType)
        if (this.cdgService.cardTypeSelected.cardType === "dc") {
          this.deparmentCard = this.localStorage.localStorageGet("deparmentCard");
          console.log(this.deparmentCard)
        }
        else if (this.cdgService.cardTypeSelected.cardType === "ic") {
          this.individualCard = this.localStorage.localStorageGet("individualCard");
        }
        else if (this.cdgService.cardTypeSelected.cardType === "vc") {
          this.virtualCard = this.localStorage.localStorageGet("virtualCard");
        }
      }

    }
    else {

      this.deparmentCard.push(this.departmentData)
      this.virtualCard.push(this.virtualData)
      this.individualCard.push(this.individualData)
      this.isDeptCard = true;
      this.isCardTypeDisable = false;
      this.productValue = "dc";



    }

  }

  getDivision() {
    let obj = {
      "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
      "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
      "masterAccountNo": Number(this.accountnumber)
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2 + UrlConstants.GET_DIV_DEPTLIST, obj).subscribe((response: any) => {
      this.divArray = [];
      if (response.status === 200) {
        let data = response.body;
        if (data.divAccountList) {
          if (data.divAccountList.length > 0) {
            data.divAccountList.forEach((element: any) => {
              this.fetchDept.push(element);
              this.divArray.push(element.divAccount);
              if (element.deptList.length > 0) {
                this.departmentData.isShowDept = true;
              }
              else {
                this.departmentData.isShowDept = false;
              }
            });
          }
        }
      }
    });
  }
  selectDept(value: any, index: any) {
    if (this.isDeptCard) {
      this.deparmentCard[index].isShowDept = false;
      this.deparmentCard[index].isShowShippingDetails = false;
      this.isCorpInvoice = false;
      this.deparmentCard[index].dept = "";
      if (value === "-CORPORATE INVOICE-") {
        this.isCorpInvoice = true;
      }
      if (value !== "ALL" && value !== "-CORPORATE INVOICE-") {
        const obj = this.fetchDept.find(x => x.divAccount.nameToDisplayOnDropdown === value)
        console.log(obj, this.divArray)
        this.deparmentCard[index].deptArray = [];
        if (obj.deptList.length > 0) {
          this.deparmentCard[index].isShowDept = true;
          obj.deptList.forEach((val: any) => {
            this.deparmentCard[index].deptArray.push(val);
          });
        }
        else {
          this.deparmentCard[index].isShowDept = false;
          this.fetchShippingAdress(index, value);
        }
      }
    }
    if (this.isIndCard) {
      this.individualCard[index].isShowDept = false;
      this.individualCard[index].isShowShippingDetails = false;
      this.isCorpInvoice = false;
      this.individualCard[index].dept = "";
      if (value === "-CORPORATE INVOICE-") {
        this.isCorpInvoice = true;
      }
      if (value !== "ALL" && value !== "-CORPORATE INVOICE-") {
        const obj = this.fetchDept.find(x => x.divAccount.nameToDisplayOnDropdown === value)
        this.individualCard[index].deptArray = [];
        console.log(obj)
        if (obj.deptList.length > 0) {
          this.individualCard[index].isShowDept = true;
          obj.deptList.forEach((val: any) => {
            this.individualCard[index].deptArray.push(val);
          });
        }
        else {
          this.individualCard[index].isShowDept = false;
          this.fetchShippingAdress(index, value);
        }
      }
    }
    if (this.isVirtCard) {
      this.virtualCard[index].isShowDept = false;
      this.virtualCard[index].isShowShippingDetails = false;
      this.isCorpInvoice = false;
      this.virtualCard[index].dept = "";
      if (value === "-CORPORATE INVOICE-") {
        this.isCorpInvoice = true;
      }
      if (value !== "ALL" && value !== "-CORPORATE INVOICE-") {
        const obj = this.fetchDept.find(x => x.divAccount.nameToDisplayOnDropdown === value)
        this.virtualCard[index].deptArray = [];
        if (obj.deptList.length > 0) {
          this.virtualCard[index].isShowDept = true;
          obj.deptList.forEach((val: any) => {
            this.virtualCard[index].deptArray.push(val);
          });
        }
        else {
          this.virtualCard[index].isShowDept = false;
          this.fetchShippingAdress(index, value);
        }
      }
    }
  }
  addNominee() {
    if (this.isDeptCard && !this.isVirtCard && !this.isIndCard) {
      if (this.deparmentCard.length < 20) {
        this.hideError = false;
        this.deparmentCard.push({
          costCenter: "",
          deptName: "",
          nameOnCard: "",
          numberOfCard: "",
          remarks: "",
          dept: {},
          contactPerson: "",
          contactTel: "",
          constactMob: "",
          addressBlock: "",
          addressUnit: "",
          addressStreet: "",
          addressBuilding: "",
          addressArea: "",
          countryCode: "",
          city: "",
          state: "",
          postal: "",
          deptArray: [],
          isShowDept: false,
          isShowShippingDetails: false,
        });
      }
      else {
        this.notifyService.showInfo("Max Limit reached for adding Nominee", 'Warning')
      }
    }
  }
  fetchShippingAdress(index: any, costCenter: any, dept?: any, deptList?: any) {
    let obj = {}
    if (costCenter && dept) {
      const data = this.divArray.find((o: any) => o.nameToDisplayOnDropdown === costCenter)
      console.log(data)
      obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "shippingDetailsDtoList": [{
          "divCode": this.divArray.find((o: any) => o.nameToDisplayOnDropdown === costCenter).accountCode.toString(),
          "deptCode": deptList.find((o: any) => o.nameToDisplayOnDropdown === dept).accountCode.toString()
        }],
        "accountNo": this.accountnumber,

      }
    } else {
      obj = {
        "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
        "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
        "shippingDetailsDtoList": [{
          "divCode": this.divArray.find((o: any) => o.nameToDisplayOnDropdown === costCenter).accountCode.toString()
        }],
        "accountNo": this.accountnumber,
      }
    }
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.GET_ONLINE_APP_CARDVIEW, obj).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body;
        if (this.isDeptCard) {
          this.deparmentCard.forEach((element: any, i: number) => {
            if (i === index) {
              element.isShowShippingDetails = true;
              element.contactPerson = opt.contactPersonName,
                element.contactTel = opt.contactPersonTel,
                element.constactMob = opt.contactPersonMobile,
                element.addressBlock = opt.addressBlock,
                element.addressUnit = opt.addressUnit,
                element.addressStreet = opt.addressStreet,
                element.addressBuilding = opt.addressBuilding,
                element.addressArea = opt.addressArea,
                element.countryCode = opt.countryCode,
                element.city = opt.city,
                element.state = opt.state,
                element.postal = opt.postal
            }
          });
        }
        else if (this.isIndCard) {
          this.individualCard.forEach((element: any, i: number) => {
            if (i === index) {
              element.isShowShippingDetails = true;
              element.contactPerson = opt.contactPersonName,
                element.contactTel = opt.contactPersonTel,
                element.constactMob = opt.contactPersonMobile,
                element.addressBlock = opt.addressBlock,
                element.addressUnit = opt.addressUnit,
                element.addressStreet = opt.addressStreet,
                element.addressBuilding = opt.addressBuilding,
                element.addressArea = opt.addressArea,
                element.countryCode = opt.countryCode,
                element.city = opt.city,
                element.state = opt.state,
                element.postal = opt.postal
            }
          });
        }
        else if (this.isVirtCard) {
          this.virtualCard.forEach((element: any, i: number) => {
            if (i === index) {
              element.isShowShippingDetails = true;
              element.contactPerson = opt.contactPersonName,
                element.contactTel = opt.contactPersonTel,
                element.constactMob = opt.contactPersonMobile,
                element.addressBlock = opt.addressBlock,
                element.addressUnit = opt.addressUnit,
                element.addressStreet = opt.addressStreet,
                element.addressBuilding = opt.addressBuilding,
                element.addressArea = opt.addressArea,
                element.countryCode = opt.countryCode,
                element.city = opt.city,
                element.state = opt.state,
                element.postal = opt.postal
            }
          });
        }
      }
    },
      (e: any) => {
        this.notifyService.showError(e.error.errorMessage, "Error")
      })
    // }

  }
  addVirtualNominee() {
    if (this.isVirtCard && !this.isDeptCard && !this.isIndCard) {
      if (this.virtualCard.length < 20) {
        this.hideVirError = false;
        this.virtualCard.push({
          costCenter: "",
          salutation: "",
          name: "",
          nameOnCardVir: "",
          jobTitle: "",
          telephone: "",
          mobileNo: "",
          email: "",
          remarks: "",
          dept: {},
          contactPerson: "",
          contactTel: "",
          constactMob: "",
          addressBlock: "",
          addressUnit: "",
          addressStreet: "",
          addressBuilding: "",
          addressArea: "",
          countryCode: "",
          city: "",
          state: "",
          postal: "",
          deptArray: [],
          isShowDept: false,
          isShowShippingDetails: false
        })
      }
      else {
        this.notifyService.showInfo("Max Limit reached for adding Nominee", 'Warning')
      }
    }
  }
  addIndNominee() {
    if (this.isIndCard && !this.isDeptCard && !this.isVirtCard) {
      if (this.individualCard.length < 20) {
        this.hideIndError = false;
        this.individualCard.push({
          costCenter: "",
          salutation: "",
          name: "",
          nameOnCardInd: "",
          jobTitle: "",
          telephone: "",
          mobileNo: "",
          email: "",
          remarks: "",
          dept: {},
          contactPerson: "",
          contactTel: "",
          constactMob: "",
          addressBlock: "",
          addressUnit: "",
          addressStreet: "",
          addressBuilding: "",
          addressArea: "",
          countryCode: "",
          city: "",
          state: "",
          postal: "",
          deptArray: [],
          isShowDept: false,
          isShowShippingDetails: false
        })
      }
      else {
        this.notifyService.showInfo("Max Limit reached for adding Nominee", 'Warning')
      }
    }
  }
  onProductSelection(value: any) {

    if (value === "ic") {
      this.isIndCard = true;
      this.isVirtCard = false;
      this.isDeptCard = false;
    }

    if (value === "vc") {
      this.isVirtCard = true;
      this.isIndCard = false;
      this.isDeptCard = false;
    }
    if (value === "dc") {
      this.isDeptCard = true;
      this.isIndCard = false;
      this.isVirtCard = false;
    }
    this.reset()
  }
  deleteNominee(index: number) {
    if (this.isDeptCard) {
      this.deparmentCard.splice(index, 1)
    }
    if (this.isVirtCard) {
      this.virtualCard.splice(index, 1)
    }
    if (this.isIndCard) {
      this.individualCard.splice(index, 1)
    }
  }

  submit(valid: any) {
    this.localStorage.localStorageSet("costCenterArray", this.divArray);
    if (this.isDeptCard) {
      if (valid) {
        this.localStorage.localStorageSet("deparmentCard", this.deparmentCard)
        this.localStorage.localStorageSet("individualCard", null)
        this.localStorage.localStorageSet("virtualCard", null)
        this.localStorage.localStorageSet("isApplicationReloaded", false);
        this.router.navigate(['layout/onlineApplication/preview']);
      }
      else {
        this.hideError = true;
        this.notifyService.showWarning('Please check if Mandatory fields are blank or some field value entered is Invalid', 'Warning')

      }

    }
    else if (this.isIndCard) {
      if (valid) {
        console.log(this.individualCard)
        this.localStorage.localStorageSet("deparmentCard", null)
        this.localStorage.localStorageSet("individualCard", this.individualCard)
        this.localStorage.localStorageSet("virtualCard", null)
        this.localStorage.localStorageSet("isApplicationReloaded", false);
        this.router.navigate(['layout/onlineApplication/preview']);
      }
      else {
        this.hideIndError = true;
        this.notifyService.showWarning('Please check if Mandatory fields are blank or some field value entered is Invalid', 'Warning')
      }

    }
    else if (this.isVirtCard) {
      if (valid) {
        this.localStorage.localStorageSet("deparmentCard", null)
        this.localStorage.localStorageSet("individualCard", null)
        this.localStorage.localStorageSet("virtualCard", this.virtualCard)
        this.localStorage.localStorageSet("isApplicationReloaded", false);
        this.router.navigate(['layout/onlineApplication/preview']);
      }
      else {
        this.hideVirError = true;
        this.notifyService.showWarning('Please check if Mandatory fields are blank or some field value entered is Invalid', 'Warning')
      }

    }
  }

  reset() {
    if (this.isDeptCard) {
      this.hideError = false;
      this.deparmentCard = [{
        costCenter: "",
        deptName: "",
        nameOnCard: "",
        numberOfCard: "",
        remarks: "",
        dept: {},
        contactPerson: "",
        contactTel: "",
        constactMob: "",
        addressBlock: "",
        addressUnit: "",
        addressStreet: "",
        addressBuilding: "",
        addressArea: "",
        countryCode: "",
        city: "",
        state: "",
        postal: "",
        deptArray: [],
        isShowDept: false,
        isShowShippingDetails: false
      }];
    }
    else if (this.isIndCard) {
      this.hideIndError = false;
      this.individualCard = [{
        costCenter: "",
        salutation: "",
        name: "",
        nameOnCardInd: "",
        jobTitle: "",
        telephone: "",
        mobileNo: "",
        email: "",
        remarks: "",
        dept: {},
        contactPerson: "",
        contactTel: "",
        constactMob: "",
        addressBlock: "",
        addressUnit: "",
        addressStreet: "",
        addressBuilding: "",
        addressArea: "",
        countryCode: "",
        city: "",
        state: "",
        postal: "",
        deptArray: [],
        isShowDept: false,
        isShowShippingDetails: false
      }];
    }
    else if (this.isVirtCard) {
      this.hideVirError = false;
      this.virtualCard = [{
        costCenter: "",
        salutation: "",
        name: "",
        nameOnCardVir: "",
        jobTitle: "",
        telephone: "",
        mobileNo: "",
        email: "",
        remarks: "",
        dept: {},
        contactPerson: "",
        contactTel: "",
        constactMob: "",
        addressBlock: "",
        addressUnit: "",
        addressStreet: "",
        addressBuilding: "",
        addressArea: "",
        countryCode: "",
        city: "",
        state: "",
        postal: "",
        deptArray: [],
        isShowDept: false,
        isShowShippingDetails: false
      }];
    }
  }
}