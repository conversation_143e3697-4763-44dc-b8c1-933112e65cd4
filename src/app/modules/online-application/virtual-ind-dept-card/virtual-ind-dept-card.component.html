<!-- <div fxLayout="column" class="bckgrnd padding" fxLayoutAlign="start start">
    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
        <div fxFlex="30%" fxFlex.lt-md="30%" fxLayoutAlign="start center">
            <span class="date-title">Product type:</span>
        </div>
        <div fxFlex="70%" fxFlex.lt-md="70%">
            <mat-form-field appearance="outline" class="inp">
                <mat-select name="productValue" [(ngModel)]="productValue" placeholder="Departmental Card"   
                (selectionChange)="onProductSelection(productValue)"> 
                    <mat-option *ngFor="let product of productType" [value]="product.value">
                        {{product.viewValue}}
                    </mat-option>
                </mat-select>
        </mat-form-field>
        </div>
    </div>
</div> -->


<div fxLayout="column" class="pb20">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">card_membership</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center" *ngIf="isDeptCard">
            <span class="header pb5">Departmental Card Nomination Form</span>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center" *ngIf="isIndCard">
            <span class="header pb5">Individual Card Nomination Form</span>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center" *ngIf="isVirtCard">
            <span class="header pb5">Virtual Card Nomination Form</span>
        </div>
    </div>
</div>


<div fxLayout="column"  class="bckgrnd margin-left">
    <div fxLayoutAlign="start center" class="margin">
     <span class="date-title">PRODUCT TYPE: <span class="asterisk"><sup>*</sup></span>:</span>
    </div>
     <div  fxLayout="row" fxLayoutAlign="center center" fxLayoutAlign.sm = "start center" class="margin">
         <div fxFlex > 
            <mat-form-field appearance="outline" class="product-inp">
                <mat-select name="productValue" [(ngModel)]="productValue"   
                (selectionChange)="onProductSelection(productValue)" [disabled]="isCardTypeDisable"> 
                    <mat-option *ngFor="let product of productType" [value]="product.value">
                        {{product.viewValue}}
                    </mat-option>
                </mat-select>
        </mat-form-field>
         </div>
     </div>
   </div>
<br>
<!-- DEPARTMENTAL CARD STARTS -->
<!-- fxLayoutGap="50px" -->
<div fxLayout="column"  *ngIf="isDeptCard && !isIndCard && !isVirtCard">
    <!-- <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">card_membership</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">Departmental Card Nomination Form</span>
        </div>
    </div> -->
    <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
       
        <form #d="ngForm" name="form">
            <ng-container *ngFor="let val of deparmentCard; let i = index" >
                <mat-card appearance="outlined" fxLayout="column" fxLayoutGap="20px" style="padding: 15px;border-radius: 10px;">
                <div fxFlex class="header-div">
                    <span>NOMINEES DETAILS</span>
                   </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">COST CENTER/ DIVISION:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                                <mat-select name="costCenter{{i}}" [(ngModel)]="deparmentCard[i].costCenter" 
                                placeholder="Please Select"  #costCenter="ngModel"  
                                (selectionChange)="selectDept(deparmentCard[i].costCenter,i)">
                                    <mat-option *ngFor="let division of divArray" [value]="division.nameToDisplayOnDropdown">
                                        {{division.nameToDisplayOnDropdown}}
                                    </mat-option>
                                </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="val.isShowDept">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">DEPARTMENT:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                                <mat-select name="dept{{i}}" [(ngModel)]="deparmentCard[i].dept" (selectionChange)="fetchShippingAdress(i,deparmentCard[i].costCenter,deparmentCard[i].dept,deparmentCard[i].deptArray)" placeholder="Please Select"  #dept="ngModel">
                                    
                                    <mat-option *ngFor="let dept of deparmentCard[i].deptArray" [value]="dept.nameToDisplayOnDropdown">
                                        {{dept.nameToDisplayOnDropdown}}
                                    </mat-option>
                                </mat-select>
                        </mat-form-field>
                    </div>
                    
                </div>
                <div fxLayout="column" fxLayoutGap="20px" *ngIf="val.isShowShippingDetails">
                    <div fxFlex fxLayoutAlign="start center">
                        <span class="subHeading">SHIPPING CONTACT DETAILS:</span>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                            <span class="date-title">CONTACT PERSON:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.contactPerson}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                            <span class="date-title">CONTACT TELEPHONE:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.contactTel}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                            <span class="date-title">CONTACT MOBILE:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.constactMob}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end start">
                            <span class="date-title">CONTACT ADDRESS:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                           <div><span *ngIf="val.addressBlock">{{val.addressBlock}}<br/></span>
                            <span *ngIf="val.addressUnit">{{val.addressUnit}},</span> <span *ngIf="val.addressStreet">{{val.addressStreet}}<br/></span>
                            <span *ngIf="val.addressBuilding"> {{val.addressBuilding}},</span><span *ngIf="val.addressArea">{{val.addressArea}}<br/></span>
                            <span *ngIf="val.city">{{val.city}},</span><span *ngIf="val.state">{{val.state}}<br/></span>
                            <span *ngIf="val.countryCode">{{val.countryCode}},</span><span *ngIf="val.postal">{{val.postal}}</span>
                           </div>
                        </div>
                    </div>
                </div>
                <div fxFlex fxLayoutAlign="start center">
                    <span class="subHeading">DEPARTMENTAL CARD DETAILS:</span>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">DIV/DEPT FULL NAME:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                        <input matInput name="deptName{{i}}" [(ngModel)]="deparmentCard[i].deptName" autocomplete="off" #deptName="ngModel">

                              
                            <!-- <mat-error *ngIf="d.submitted && deptName.invalid && deparmentCard[i].deptName !== null && hideError">
                                <div *ngIf="deptName.hasError('required')">
                                    Div/Dept Full Name Is Required
                                </div>
                            </mat-error> -->
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">NAME ON CARD<span class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                        <input matInput name="nameOnCard{{i}}" [(ngModel)]="deparmentCard[i].nameOnCard" autocomplete="off" #nameOnCard="ngModel" required>
                            <mat-error *ngIf="d.submitted && nameOnCard.invalid && deparmentCard[i].nameOnCard !== null && hideError">
                                <div *ngIf="nameOnCard.hasError('required')">
                                   Name Is Required
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">NUMBER OF CARDS:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                        <input matInput type="number" name="numberOfCard{{i}}" min="0" [(ngModel)]="deparmentCard[i].numberOfCard" autocomplete="off" onkeypress="return event.charCode >= 48 && event.charCode <= 57" #numberOfCard="ngModel">
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">REMARKS:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                    <textarea matInput placeholder="Message" rows="6" name="remarks{{i}}" [(ngModel)]="deparmentCard[i].remarks" autocomplete="off"  #remarks="ngModel" ></textarea>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayoutGap="5px" fxLayoutAlign="end center" *ngIf="deparmentCard.length > 1">
                    <button mat-raised-button class="delete-btn" (click)="deleteNominee(i)">Delete Nominee<mat-icon>delete_sweep
                        </mat-icon></button>
                </div>
        </mat-card>
            </ng-container>
            <div fxFlex class="mt20" fxLayoutGap="20px" fxLayoutAlign="start start">
                <button mat-raised-button class="reset-btn"
                    (click)="reset()">RESET</button>
                <button mat-raised-button class="update-btn"
                    (click)="submit(d.form.valid)">SUBMIT</button>
            </div>
            <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="end center">
                <div fxLayoutGap="5px">
                    <button mat-raised-button class="add-btn" (click)="addNominee()">Add Nominees<mat-icon>group_add</mat-icon>
                        </button>
                </div>
            </div>
        </form>
    </div>
</div>
<!-- DEPARTMENTAL CARD ENDS -->

<!-- VIRTUAL CARD STARTS -->
<!-- fxLayoutGap="10px" -->
<div fxLayout="column" *ngIf="!isIndCard && isVirtCard && !isDeptCard"> 
    <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
       
        <form #v="ngForm" name="form">
            <ng-container *ngFor="let val of virtualCard; let i = index" >
                <mat-card appearance="outlined" fxLayout="column" fxLayoutGap="20px" style="padding: 15px;border-radius: 10px;">
                <div fxFlex class="header-div">
                    <span>NOMINEES DETAILS</span>
                   </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">COST CENTER/ DIVISION:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                                <mat-select name="costCenter{{i}}" [(ngModel)]="virtualCard[i].costCenter" placeholder="Please Select"  #costCenter="ngModel"  (selectionChange)="selectDept(virtualCard[i].costCenter,i)">
                                    <mat-option *ngFor="let acl of divArray" [value]="acl.nameToDisplayOnDropdown">
                                        {{acl.nameToDisplayOnDropdown}}
                                    </mat-option>
                                </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="val.isShowDept">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">DEPARTMENT:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                                <mat-select name="dept{{i}}" [(ngModel)]="virtualCard[i].dept" (selectionChange)="fetchShippingAdress(i,virtualCard[i].costCenter,virtualCard[i].dept,virtualCard[i].deptArray)" placeholder="Please Select"  #dept="ngModel">
                                    <mat-option *ngFor="let acl of virtualCard[i].deptArray" [value]="acl.nameToDisplayOnDropdown">
                                        {{acl.nameToDisplayOnDropdown}}
                                    </mat-option>
                                </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="column" fxLayoutGap="20px" *ngIf="val.isShowShippingDetails">
                    <div fxFlex fxLayoutAlign="start center">
                        <span class="subHeading">SHIPPING CONTACT DETAILS:</span>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                            <span class="date-title">CONTACT PERSON:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.contactPerson}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                            <span class="date-title">CONTACT TELEPHONE:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.contactTel}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                            <span class="date-title">CONTACT MOBILE:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.constactMob}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end start">
                            <span class="date-title">CONTACT ADDRESS:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                           <div><span *ngIf="val.addressBlock">{{val.addressBlock}}<br/></span>
                            <span *ngIf="val.addressUnit">{{val.addressUnit}},</span> <span *ngIf="val.addressStreet">{{val.addressStreet}}<br/></span>
                            <span *ngIf="val.addressBuilding"> {{val.addressBuilding}},</span><span *ngIf="val.addressArea">{{val.addressArea}}<br/></span>
                            <span *ngIf="val.city">{{val.city}},</span><span *ngIf="val.state">{{val.state}}<br/></span>
                            <span *ngIf="val.countryCode">{{val.countryCode}},</span><span *ngIf="val.postal">{{val.postal}}</span>
                           </div>
                        </div>
                    </div>
                </div>
                <div fxFlex fxLayoutAlign="start center">
                    <span class="subHeading">VIRTUAL CARD HOLDER DETAILS:</span>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" >
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">SALUTATION:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                                <mat-select name="salutation{{i}}" [(ngModel)]="virtualCard[i].salutation" placeholder="Please Select"  #salutation="ngModel">
                                    <mat-option *ngFor="let acl of nameTitle" [value]="acl.viewValue">
                                        {{acl.viewValue}}
                                    </mat-option>
                                </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">CARDHOLDER NAME<span class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                        <input matInput name="name{{i}}" [(ngModel)]="virtualCard[i].name"  #name="ngModel" autocomplete="off" required> 
                            <mat-error *ngIf="v.submitted && name.invalid && virtualCard[i].name !== null && hideVirError">
                                <div *ngIf="name.hasError('required')">
                                    Cardholder Name Is Required
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">EMPLOYEE ID:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                        <input matInput type="text" name="nameOnCardVir{{i}}" [(ngModel)]="virtualCard[i].nameOnCardVir" autocomplete="off" #nameOnCardVir="ngModel">
                        <!-- onkeypress="return event.charCode >= 48 && event.charCode <= 57" -->
                            <!-- <mat-error *ngIf="v.submitted && nameOnCardVir.invalid && virtualCard[i].nameOnCardVir !== null && hideVirError" >
                                <div *ngIf="nameOnCardVir.hasError('required')">
                                    Employee ID Is Required
                                </div>
                            </mat-error> -->
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">MOBILE NO<span class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <!-- <mat-form-field [ngClass.lt-md]="{'mt20' : mobileNo.invalid} " appearance="outline" class="inp"> -->
                            <mat-form-field  appearance="outline" class="inp">

                        <input matInput   min="0" minlength="8" maxlength="8" pattern="\d{8,8}" name="mobileNo{{i}}" autocomplete="off" [(ngModel)]="virtualCard[i].mobileNo"  #mobileNo="ngModel" required>
                        <mat-error *ngIf="v.submitted && mobileNo.invalid && virtualCard[i].mobileNo !== null && hideVirError">
                            <div *ngIf="mobileNo.hasError('pattern') ">
                                Please Enter Valid Mobile Number Containing 8 Digits Only
                            </div>
                            <div *ngIf="mobileNo.hasError('required')">
                                Mobile Number Is Required
                            </div>
                        </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">EMAIL ADDRESS:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                        <input type="email" pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-.]+$" matInput name="email{{i}}"  [(ngModel)]="virtualCard[i].email" autocomplete="off" #email="ngModel">
                        <!-- pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$" -->
                        <mat-error *ngIf="v.submitted && email.invalid" >
                            <div *ngIf="email.hasError('pattern')">
                              Please Enter Valid Email
                            </div>
                        </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">REMARKS:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                    <textarea matInput placeholder="Message" rows="6" name="remarks{{i}}" [(ngModel)]="virtualCard[i].remarks"  #remarks="ngModel" ></textarea>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayoutGap="5px" fxLayoutAlign="end center" *ngIf="virtualCard.length > 1">
                    <button mat-raised-button class="delete-btn" (click)="deleteNominee(i)">Delete Nominee<mat-icon>delete_sweep
                        </mat-icon></button>
                </div>
        </mat-card>
            </ng-container>
            <div fxFlex class="mt20" fxLayoutGap="20px" fxLayoutAlign="start start">
                <button mat-raised-button class="reset-btn"
                    (click)="reset()">RESET</button>
                <button mat-raised-button class="update-btn"
                    (click)="submit(v.form.valid)">SUBMIT</button>
            </div>
            <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="end center">
                <div fxLayoutGap="5px">
                    <button mat-raised-button class="add-btn" (click)="addVirtualNominee()">Add Nominees<mat-icon>group_add</mat-icon>
                        </button>
                </div>
            </div>
        </form>
    </div>
</div>
<!-- VIRTUAL CARD ENDS -->


<!-- INDIVIDUAL CARD STARTS -->
<!-- fxLayoutGap="50px"  -->
<div fxLayout="column" *ngIf="isIndCard && !isVirtCard && !isDeptCard"> 
    <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
       
        <form #g="ngForm" name="form">
            <ng-container *ngFor="let val of individualCard; let i = index" >
                <mat-card appearance="outlined" fxLayout="column" fxLayoutGap="20px" style="padding: 15px;border-radius: 10px;">
                <div fxFlex class="header-div">
                    <span>NOMINESS DETAILS</span>
                   </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">COST CENTER/ DIVISION:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                                <mat-select name="costCenter{{i}}" [(ngModel)]="individualCard[i].costCenter" placeholder="Please Select"  #costCenter="ngModel"  (selectionChange)="selectDept(individualCard[i].costCenter,i)">
                                    <mat-option *ngFor="let acl of divArray" [value]="acl.nameToDisplayOnDropdown">
                                        {{acl.nameToDisplayOnDropdown}}
                                    </mat-option>
                                </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="val.isShowDept">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">DEPARTMENT:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                                <mat-select name="dept{{i}}" [(ngModel)]="individualCard[i].dept" (selectionChange)="fetchShippingAdress(i,individualCard[i].costCenter,individualCard[i].dept,individualCard[i].deptArray)" placeholder="Please Select"  #dept="ngModel">
                                    <mat-option *ngFor="let acl of individualCard[i].deptArray" [value]="acl.nameToDisplayOnDropdown">
                                        {{acl.nameToDisplayOnDropdown}}
                                    </mat-option>
                                </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="column" fxLayoutGap="20px" *ngIf="val.isShowShippingDetails">
                    <div fxFlex fxLayoutAlign="start center">
                        <span class="subHeading">SHIPPING CONTACT DETAILS:</span>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                            <span class="date-title">CONTACT PERSON:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.contactPerson}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                            <span class="date-title">CONTACT TELEPHONE:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.contactTel}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                            <span class="date-title">CONTACT MOBILE:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.constactMob}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end start">
                            <span class="date-title">CONTACT ADDRESS:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                           <div><span *ngIf="val.addressBlock">{{val.addressBlock}}<br/></span>
                            <span *ngIf="val.addressUnit">{{val.addressUnit}},</span> <span *ngIf="val.addressStreet">{{val.addressStreet}}<br/></span>
                            <span *ngIf="val.addressBuilding"> {{val.addressBuilding}},</span><span *ngIf="val.addressArea">{{val.addressArea}}<br/></span>
                            <span *ngIf="val.city">{{val.city}},</span><span *ngIf="val.state">{{val.state}}<br/></span>
                            <span *ngIf="val.countryCode">{{val.countryCode}},</span><span *ngIf="val.postal">{{val.postal}}</span>
                           </div>
                        </div>
                    </div>
                </div>
                <div fxFlex fxLayoutAlign="start center">
                    <span class="subHeading">CONTACTLESS CORPORATE CARD HOLDER DETAILS:</span>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" >
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">SALUTATION:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                                <mat-select name="salutation{{i}}" [(ngModel)]="individualCard[i].salutation" placeholder="Please Select"  #salutation="ngModel">
                                    <mat-option *ngFor="let acl of nameTitle" [value]="acl.viewValue">
                                        {{acl.viewValue}}
                                    </mat-option>
                                </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">CARDHOLDER NAME<span class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                        <input matInput name="name{{i}}" [(ngModel)]="individualCard[i].name" autocomplete="off" #name="ngModel" required> 
                            <mat-error *ngIf="g.submitted && name.invalid && individualCard[i].name!== null && hideIndError">
                                <div *ngIf="name.hasError('required')">
                                    Cardholder Name Is Required
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">EMPLOYEE ID:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                        <input matInput type="text" name="nameOnCardInd{{i}}" [(ngModel)]="individualCard[i].nameOnCardInd" autocomplete="off"  #nameOnCardInd="ngModel">
                        <!-- onkeypress="return event.charCode >= 48 && event.charCode <= 57" -->
                            <!-- <mat-error *ngIf="g.submitted && nameOnCardInd.invalid && individualCard[i].nameOnCardInd !== null && hideIndError">
                                <div *ngIf="nameOnCardInd.hasError('required')">
                                    Employee ID Is Required
                                </div>
                            </mat-error> -->
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">MOBILE NO:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                        <input matInput type="text"  min="0" minlength="8" maxlength="8" pattern="\d{8,8}" name="mobileNo{{i}}" [(ngModel)]="individualCard[i].mobileNo" autocomplete="off"  #mobileNo="ngModel" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                        <mat-error *ngIf="g.submitted && mobileNo.invalid && individualCard[i].mobileNo !== null && hideVirError">
                            <div *ngIf="mobileNo.hasError('pattern') ">
                                Please Enter Valid Mobile Number Containing 8 Digits Only
                            </div>
                            <!-- <div *ngIf="mobileNo.hasError('required')">
                                Mobile Number Is Required
                            </div> -->
                        </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">EMAIL ADDRESS:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field  appearance="outline" class="inp">
                        <input type="email"  pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-.]+$" matInput name="email{{i}}" [(ngModel)]="individualCard[i].email"  autocomplete="off" #email="ngModel">
                        <!-- pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$" -->
                        <mat-error *ngIf="g.submitted && email.invalid">
                            <div *ngIf="email.hasError('pattern')">
                              Please Enter Valid Email
                            </div>
                        </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">REMARKS:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                    <textarea matInput placeholder="Message" rows="6" name="remarks{{i}}" [(ngModel)]="individualCard[i].remarks"  #remarks="ngModel" ></textarea>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayoutGap="5px" fxLayoutAlign="end center" *ngIf="individualCard.length > 1">
                    <button mat-raised-button class="delete-btn" (click)="deleteNominee(i)">Delete Nominee<mat-icon>delete_sweep
                        </mat-icon></button>
                </div>
        </mat-card>
            </ng-container>
            <div fxFlex class="mt20" fxLayoutGap="20px" fxLayoutAlign="start start">
                <button mat-raised-button class="reset-btn"
                    (click)="reset()">RESET</button>
                <button mat-raised-button class="update-btn"
                    (click)="submit(g.form.valid)">SUBMIT</button>
            </div>
            <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="end center">
                <div fxLayoutGap="5px">
                    <button mat-raised-button class="add-btn" (click)="addIndNominee()">Add Nominees<mat-icon>group_add</mat-icon>
                        </button>
                </div>
            </div>
        </form>
    </div>
</div>
<!-- INDIVIDUAL CARD ENDS -->




