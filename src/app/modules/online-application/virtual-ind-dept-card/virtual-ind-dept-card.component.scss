@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
.bckgrnd{
    background-color: #E7E7E7;
}
.padding{
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 15px;
    // padding-left: 50px;
    padding-right: 15px;
}
.inp{
    width: 310px !important;
}
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
.main-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
    height: 40px !important;
    width: 40px !important;
}
.margin-left{
    margin-left: 65px !important;
}
.update-btn{
    width:205px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
}
.reset-btn{
    width:205px;
    border-radius: 10px !important;  
}
.header-div{
    background-color: $cdgc-bg-prime !important;
    color: $cdgc-font-accent !important;
    border-radius: 5px;
    padding: 6px 25px;
    font-size: 19px;
    font-weight: $cdgsz-font-weight-bold !important;
   }
   .date-title{
    // font-size: 19px;
    font-weight: $cdgsz-font-weight-intermediate;
   }
   .subHeading{
    font-weight: $cdgsz-font-weight-bold;

   }
@media screen and (max-width: 500px) {
    .inp{
        width: 170px !important;
    }
    .margin-left{
        margin-left: 0px !important;
    }
 }
 @media screen and (max-width: 330px) {
    .inp{
        width: 156px !important;
    }
    .margin-left{
        margin-left: 0px !important;
    }
}
@media screen and (min-width: 601px){
    .product-inp{
        width: 500px;
    }
    .margin-left{
        margin-left: 0px !important;
    }
    .margin{
        margin-left: 50px;
    }
}