<div fxLayout="column" fxLayoutGap="50px">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">card_membership</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">Virtual Card Nomination Form</span>
        </div>
    </div>
    <div fxLayout="column" class="custom-padding-left" fxLayoutGap="20px">
        <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="end center">
            <div fxLayoutGap="5px">
                <button mat-raised-button class="add-btn" (click)="addNominee()">Add Nominees<mat-icon>group_add</mat-icon>
                    </button>
            </div>
        </div>
        <form #f="ngForm" name="form">
            <ng-container *ngFor="let val of deparmentCard; let i = index" >
                <mat-card appearance="outlined" fxLayout="column" fxLayoutGap="20px" style="padding: 15px;border-radius: 10px;">
                <div fxFlex class="header-div">
                    <span>Nominees Details</span>
                   </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">Cost Center/ Division:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                                <mat-select name="costCenter{{i}}" [(ngModel)]="deparmentCard[i].costCenter" placeholder="Please Select"  #costCenter="ngModel"  (selectionChange)="onSelection(deparmentCard[i].costCenter,i)">
                                    
                                    <mat-option *ngFor="let acl of costcenter" [value]="acl.viewValue">
                                        {{acl.viewValue}}
                                    </mat-option>
                                </mat-select>
                            <!-- <mat-error *ngIf="f.submitted && costCenter.invalid" class="pt15">
                                <div *ngIf="costCenter.hasError('required')">
                                    Required
                                </div>
                            </mat-error> -->
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="val.isShowDept">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">Department:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                                <mat-select name="dept{{i}}" [(ngModel)]="deparmentCard[i].dept" placeholder="Please Select"  #dept="ngModel">
                                    
                                    <mat-option *ngFor="let acl of departmentList" [value]="acl.viewValue">
                                        {{acl.viewValue}}
                                    </mat-option>
                                </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="column" fxLayoutGap="20px" *ngIf="val.isShowShippingDetails">
                    <div fxFlex fxLayoutAlign="start center">
                        <span class="bold-header">Shipping Contact Details:</span>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                            <span class="date-header">Contact Person:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{this.cdgService.loggedInUserDetails.userName}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                            <span class="date-title">Contact Telephone:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{this.cdgService.loggedInUserDetails.telephone}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                            <span class="date-title">Contact Mobile:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{this.cdgService.loggedInUserDetails.mobNo}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                            <span class="date-title">Contact Address:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                           <span> {{this.cdgService.loggedInUserDetails.blockNo}}<br/>
                            {{this.cdgService.loggedInUserDetails.unitNo}},{{this.cdgService.loggedInUserDetails.streetName}}<br/>
                            {{this.cdgService.loggedInUserDetails.buildingName}},{{this.cdgService.loggedInUserDetails.area}}<br/>
                            {{this.cdgService.loggedInUserDetails.country}},{{this.cdgService.loggedInUserDetails.postalCode}}
                        </span>
                        </div>
                    </div>
                </div>
                <div fxFlex fxLayoutAlign="start center">
                    <span class="bold-header">Virtual Card Holder Details:</span>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" >
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">Salutation:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                                <mat-select name="salutation{{i}}" [(ngModel)]="deparmentCard[i].salutation" placeholder="Please Select"  #salutation="ngModel">
                                    
                                    <mat-option *ngFor="let acl of nameTitle" [value]="acl.viewValue">
                                        {{acl.viewValue}}
                                    </mat-option>
                                </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">Full Name<span class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                        <input matInput name="name{{i}}" [(ngModel)]="deparmentCard[i].name"  #name="ngModel" required>

                              
                            <mat-error *ngIf="f.submitted && name.invalid" class="pt15">
                                <div *ngIf="name.hasError('required')">
                                    Full Name Is Required
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">Name On Card<span class="asterisk"><sup>*</sup></span>:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                        <input matInput name="nameOnCard{{i}}" [(ngModel)]="deparmentCard[i].nameOnCard"  #nameOnCard="ngModel" required>
                            <mat-error *ngIf="f.submitted && nameOnCard.invalid" class="pt15">
                                <div *ngIf="nameOnCard.hasError('required')">
                                   Name On Card Is Required
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">Job Title:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                        <input matInput name="jobTitle{{i}}" [(ngModel)]="deparmentCard[i].jobTitle"  #jobTitle="ngModel">
                            <!-- <mat-error *ngIf="f.submitted && nameOnCard.invalid" class="pt15">
                                <div *ngIf="nameOnCard.hasError('required')">
                                   Name On Card is required
                                </div>
                            </mat-error> -->
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">Telephone No:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                        <input matInput name="telephone{{i}}"  pattern="[0-9]{8}" [(ngModel)]="deparmentCard[i].telephone"  #telephone="ngModel">
                        <mat-error *ngIf="f.submitted && telephone.invalid"  class="pt15">
                            <div *ngIf="telephone.hasError('pattern')" >
                                Please Enter Valid Telephone Number Containing 8 Digits Only
                            </div>
                        </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">Mobile No:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <!-- <mat-form-field [ngClass.lt-md]="{'mt20' : telephone.invalid}" appearance="outline" class="inp"> -->
                            <mat-form-field appearance="outline" class="inp">

                        <input matInput  pattern="[0-9]{8}" name="mobileNo{{i}}" [(ngModel)]="deparmentCard[i].mobileNo"  #mobileNo="ngModel">
                            <mat-error *ngIf="f.submitted && mobileNo.invalid"  class="pt15">
                                <div *ngIf="mobileNo.hasError('pattern') ">
                                    Please Enter Valid Mobile Number Containing 8 Digits Only
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">Email Address:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <!-- <mat-form-field [ngClass.lt-md]="{'mt20' : mobileNo.invalid}" appearance="outline" class="inp"> -->
                            <mat-form-field appearance="outline" class="inp">

                        <input type="email" pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$" matInput name="email{{i}}" [(ngModel)]="deparmentCard[i].email"  #email="ngModel">

                            <mat-error *ngIf="f.submitted && email.invalid" class="pt15">
                                <div *ngIf="email.hasError('pattern')">
                                  Please Enter Valid Email
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                    <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                        <span class="date-title">Remarks:</span>
                    </div>
                    <div fxFlex="85%" fxFlex.lt-md="65%">
                        <mat-form-field appearance="outline" class="inp">
                    <textarea matInput placeholder="Message" rows="6" name="remarks{{i}}" [(ngModel)]="deparmentCard[i].remarks"  #remarks="ngModel" ></textarea>
                        </mat-form-field>
                    </div>
                </div>
                <div fxLayoutGap="5px" fxLayoutAlign="end center" *ngIf="i > 0">
                    <button mat-raised-button class="delete-btn" (click)="deleteNominee(i)">Delele Nominee<mat-icon>delete_sweep
                        </mat-icon></button>
                </div>
        </mat-card>
            </ng-container>
            <div fxFlex class="mt20">
                <button mat-raised-button class="update-btn"
                    (click)="submit(f.form.valid)">SUBMIT</button>
            </div>
        </form>
    </div>
</div>