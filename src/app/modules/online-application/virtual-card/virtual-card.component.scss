@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
.header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
.main-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
    height: 40px !important;
    width: 40px !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0px !important;
 }
 .inp{
    width: 350px !important;
}
.update-btn{
    width:205px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
}
.header-div{
    background-color: $cdgc-bg-prime !important;
    color: $cdgc-font-accent !important;
    border-radius: 5px;
    padding: 6px 25px;
    font-size: 19px;
    font-weight: $cdgsz-font-weight-bold !important;
   }
   .bold-header{
    font-weight: $cdgsz-font-weight-bold !important;
   }
@media screen and (max-width: 500px) {
    .inp{
        width: 180px !important;
    }
 }
 @media screen and (min-width: 500px) and (max-width: 800px) {
    .inp{
        width: 250px !important;
    }
 }
 @media screen and (max-width: 500px) {
    .inp{
        width: 170px !important;
    }
 }
 @media screen and (max-width: 330px) {
    .inp{
        width: 156px !important;
    }
 }