<!-- Departmental Card Preview -->
<div fxLayout="column" fxLayoutGap="50px" *ngIf="this.deparmentCard.length > 0 && !isShowErrorPage">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">preview</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">{{header}}</span>
        </div>
    </div>
    <div fxLayout="column" fxLayoutGap="10px" class="custom-padding-left">
        <div fxLayout="row" fxLayoutGap="5px">
            <span class="subHeading">Account Name (Account No) :</span>
            <span class="subHeadingLite">{{accountName}} ({{accNo}})</span>
        </div>

        <ng-container *ngFor="let val of deparmentCard; let i = index">
            <div fxLayout="column" fxLayoutGap="20px">
                <mat-card appearance="outlined" fxLayout="column" fxLayoutGap="20px" style="padding: 15px;border-radius: 10px;">
                    <div fxFlex class="header-div">
                        <span>Nominees Details</span>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Cost Center/ Division:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.costCenter}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="val.isShowDept">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Department:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.dept}}
                        </div>

                    </div>
                    <div fxLayout="column" fxLayoutGap="20px" *ngIf="val.isShowShippingDetails">
                        <div fxFlex fxLayoutAlign="start center">
                            <span class="subHeading">Shipping Contact Details:</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                                <span class="date-title">Contact Person:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                {{val.contactPerson}}
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                                <span class="date-title">Contact Telephone:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                {{val.contactTel}}
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                                <span class="date-title">Contact Mobile:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                {{val.constactMob}}
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end start">
                                <span class="date-title">Contact Address:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                <div><span *ngIf="val.addressBlock">{{val.addressBlock}}<br /></span>
                                    <span *ngIf="val.addressUnit">{{val.addressUnit}},</span> <span
                                        *ngIf="val.addressStreet">{{val.addressStreet}}<br /></span>
                                    <span *ngIf="val.addressBuilding"> {{val.addressBuilding}},</span><span
                                        *ngIf="val.addressArea">{{val.addressArea}}<br /></span>
                                    <span *ngIf="val.city">{{val.city}},</span><span
                                        *ngIf="val.state">{{val.state}}<br /></span>
                                    <span *ngIf="val.countryCode">{{val.countryCode}},</span><span
                                        *ngIf="val.postal">{{val.postal}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div fxFlex fxLayoutAlign="start center">
                        <span class="subHeading">Departmental Card Details:</span>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Div/Dept Full Name:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.deptName}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Name On Card<span class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.nameOnCard}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Number Of Cards:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.numberOfCard}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Remarks:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.remarks}}
                        </div>
                    </div>
                </mat-card>
            </div>
        </ng-container>
    </div>
    <div fxFlex class="mt20" fxLayoutGap="20px" fxLayoutAlign="end center">
        <button mat-raised-button class="reset-btn" (click)="cancel()">CANCEL</button>
        <button mat-raised-button class="edit-btn" (click)="edit()">EDIT</button>
        <button mat-raised-button class="update-btn" (click)="submit()">CONFIRM</button>
    </div>
</div>

<!-- Individual Card Preview -->
<div fxLayout="column" fxLayoutGap="50px" *ngIf="this.individualCard.length > 0 && !isShowErrorPage">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">preview</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">{{header}}</span>
        </div>
    </div>
    <div fxLayout="column" fxLayoutGap="10px" class="custom-padding-left">
        <div fxLayout="row" fxLayoutGap="5px">
            <span class="subHeading">Account Name (Account No) :</span>
            <span class="subHeadingLite">{{accountName}} ({{accNo}})</span>
        </div>

        <ng-container *ngFor="let val of individualCard; let j = index">
            <div fxLayout="column" fxLayoutGap="20px">
                <mat-card appearance="outlined" fxLayout="column" fxLayoutGap="20px" style="padding: 15px;border-radius: 10px;">
                    <div fxFlex class="header-div">
                        <span>Nominees Details</span>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Cost Center/ Division:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.costCenter}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="val.isShowDept">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Department:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.dept}}
                        </div>
                    </div>
                    <div fxLayout="column" fxLayoutGap="20px" *ngIf="val.isShowShippingDetails">
                        <div fxFlex fxLayoutAlign="start center">
                            <span class="subHeading">Shipping Contact Details:</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                                <span class="date-title">Contact Person:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                {{val.contactPerson}}
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                                <span class="date-title">Contact Telephone:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                {{val.contactTel}}
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                                <span class="date-title">Contact Mobile:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                {{val.constactMob}}
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end start">
                                <span class="date-title">Contact Address:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                <div><span *ngIf="val.addressBlock">{{val.addressBlock}}<br /></span>
                                    <span *ngIf="val.addressUnit">{{val.addressUnit}},</span> <span
                                        *ngIf="val.addressStreet">{{val.addressStreet}}<br /></span>
                                    <span *ngIf="val.addressBuilding"> {{val.addressBuilding}},</span><span
                                        *ngIf="val.addressArea">{{val.addressArea}}<br /></span>
                                    <span *ngIf="val.city">{{val.city}},</span><span
                                        *ngIf="val.state">{{val.state}}<br /></span>
                                    <span *ngIf="val.countryCode">{{val.countryCode}},</span><span
                                        *ngIf="val.postal">{{val.postal}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div fxFlex fxLayoutAlign="start center">
                        <span class="subHeading">Contactless Corporate Card Holder Details:</span>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Salutation:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.salutation}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Cardholder Name<span class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.name}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Employee Id:</span>
                            <!-- <span class="asterisk"><sup>*</sup></span> -->
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.nameOnCardInd}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Mobile No:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.mobileNo}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Email Address:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.email}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Remarks:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.remarks}}
                        </div>
                    </div>
                </mat-card>
            </div>
        </ng-container>
    </div>
    <div fxFlex class="mt20" fxLayoutGap="20px" fxLayoutAlign="end center">
        <button mat-raised-button class="reset-btn" (click)="cancel()">CANCEL</button>
        <button mat-raised-button class="edit-btn" (click)="edit()">EDIT</button>
        <button mat-raised-button class="update-btn" (click)="submit()">CONFIRM</button>
    </div>
</div>

<!-- Virtual Card Preview -->
<div fxLayout="column" fxLayoutGap="50px" *ngIf="this.virtualCard.length > 0 && !isShowErrorPage">
    <div fxFlex fxLayout="row" fxLayoutGap.lt-md="30px">
        <div fxFlex="5%">
            <mat-icon class="material-icons-outlined main-icon">preview</mat-icon>
        </div>
        <div fxFlex="95%" fxLayoutAlign="start center">
            <span class="header pb5">{{header}}</span>
        </div>
    </div>
    <div fxLayout="column" fxLayoutGap="10px" class="custom-padding-left">
        <div fxLayout="row" fxLayoutGap="5px">
            <span class="subHeading">Account Name (Account No) :</span>
            <span class="subHeadingLite">{{accountName}} ({{accNo}})</span>
        </div>

        <ng-container *ngFor="let val of virtualCard; let j = index">
            <div fxLayout="column" fxLayoutGap="20px">
                <mat-card appearance="outlined" fxLayout="column" fxLayoutGap="20px" style="padding: 15px;border-radius: 10px;">
                    <div fxFlex class="header-div">
                        <span>Nominees Details</span>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Cost Center/ Division:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.costCenter}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px" *ngIf="val.isShowDept">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Department:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.dept}}
                        </div>
                    </div>
                    <div fxLayout="column" fxLayoutGap="20px" *ngIf="val.isShowShippingDetails">
                        <div fxFlex fxLayoutAlign="start center">
                            <span class="subHeading">Shipping Contact Details:</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                                <span class="date-title">Contact Person:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                {{val.contactPerson}}
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                                <span class="date-title">Contact Telephone:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                {{val.contactTel}}
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end center">
                                <span class="date-title">Contact Mobile:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                {{val.constactMob}}
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                            <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="end start">
                                <span class="date-title">Contact Address:</span>
                            </div>
                            <div fxFlex="85%" fxFlex.lt-md="65%">
                                <div><span *ngIf="val.addressBlock">{{val.addressBlock}}<br /></span>
                                    <span *ngIf="val.addressUnit">{{val.addressUnit}},</span> <span
                                        *ngIf="val.addressStreet">{{val.addressStreet}}<br /></span>
                                    <span *ngIf="val.addressBuilding"> {{val.addressBuilding}},</span><span
                                        *ngIf="val.addressArea">{{val.addressArea}}<br /></span>
                                    <span *ngIf="val.city">{{val.city}},</span><span
                                        *ngIf="val.state">{{val.state}}<br /></span>
                                    <span *ngIf="val.countryCode">{{val.countryCode}},</span><span
                                        *ngIf="val.postal">{{val.postal}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div fxFlex fxLayoutAlign="start center">
                        <span class="subHeading">Virtual Card Holder Details:</span>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Salutation:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.salutation}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Cardholder Name<span class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.name}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Employee Id:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.nameOnCardVir}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Mobile No<span class="asterisk"><sup>*</sup></span>:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.mobileNo}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Email Address:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.email}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="40px" fxLayoutGap.lt-md="10px">
                        <div fxFlex="15%" fxFlex.lt-md="35%" fxLayoutAlign="start center">
                            <span class="date-title">Remarks:</span>
                        </div>
                        <div fxFlex="85%" fxFlex.lt-md="65%">
                            {{val.remarks}}
                        </div>
                    </div>
                </mat-card>
            </div>
        </ng-container>
    </div>
    <div fxFlex class="mt20" fxLayoutGap="20px" fxLayoutAlign="end center">
        <button mat-raised-button class="reset-btn" (click)="cancel()">CANCEL</button>
        <button mat-raised-button class="edit-btn" (click)="edit()">EDIT</button>
        <button mat-raised-button class="update-btn" (click)="submit()">CONFIRM</button>
    </div>
</div>

<!-- No Preview Available -->
<div fxLayout="column" fxLayoutGap="10px" *ngIf="isShowErrorPage" fxLayoutAlign="center center"
    style="height: 100% !important;">
    <div class="noPreviewMsg">Sorry No Preview is</div>
    <div fxLayout="row" fxLayoutAlign="center center" class="noPreviewMsg"><span>Available </span> <span>
            <mat-icon class="material-icons-outlined main-preview-icon">sentiment_dissatisfied</mat-icon>
        </span></div>

</div>