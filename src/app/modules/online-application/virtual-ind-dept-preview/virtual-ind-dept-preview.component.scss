@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';

.header-div{
    background-color: $cdgc-bg-prime;
    color: $cdgc-font-accent;
    border-radius: 5px;
    padding: 6px 25px;
    font-size: 19px;
    font-weight: $cdgsz-font-weight-bold;
   }
   .main-icon{
    color: $lightGrey !important;
    font-size: $cdgsz-font-size-xxl !important;
   }
   .header{
    color: $lightGrey !important;
    font-size: 28px;
    font-weight:$cdgsz-font-weight-normal ;
}
.subHeading{
    font-weight:$cdgsz-font-weight-bold ;
    font-size: $cdgsz-font-size-md;
}
.subHeadingLite{
    font-weight:$cdgsz-font-weight-intermediate ;
    font-size: $cdgsz-font-size-md;
}
.noPreviewMsg{
    font-weight:$cdgsz-font-weight-bold ;
    font-size: $cdgsz-font-size-xxl; 
    color: $lightGrey !important;
}
.main-preview-icon{
    font-size: 3rem !important;
    color: $lightGrey !important;
}
.date-title{
    // font-size: 19px;
    font-weight: $cdgsz-font-weight-bold;
   }
   .edit-btn{
    background-color: #519585 !important;
    width: 170px !important;
    border-radius: 10px !important;
    // font-size: 12px !important;
}
.update-btn{
    width:170px;
    background-color:$cdgc-bg-hue !important;
    color: $cdgc-font-accent !important;
    border-radius: 10px !important;
}
.reset-btn{
    width:170px;
    border-radius: 10px !important;  
}