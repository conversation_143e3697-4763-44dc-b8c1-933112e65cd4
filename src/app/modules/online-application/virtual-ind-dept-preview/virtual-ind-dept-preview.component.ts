import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';

@Component({
  selector: 'app-virtual-ind-dept-preview',
  templateUrl: './virtual-ind-dept-preview.component.html',
  styleUrls: ['./virtual-ind-dept-preview.component.scss']
})
export class VirtualIndDeptPreviewComponent implements OnInit {
  header: any;
  accountnumber: any;
  accNo: any;
  accountName: any;
  deparmentCard: any[] = [];
  virtualCard: any[] = [];
  individualCard: any[] = [];
  isShowErrorPage: boolean = false;
  customernumber: any;
  customerName: any;
  constructor(private router: Router, public dialog: MatDialog, private fb: FormBuilder, public localStorage: LocalStorageService, private apiService: ApiServiceService, public cdgService: CdgSharedService, private notifyService: NotificationService) { }

  ngOnInit(): void {
    if (this.localStorage.localStorageGet("customerNoSelected") != null) {
      this.accountnumber = this.localStorage.localStorageGet("customerNoSelected");
      if (this.localStorage.localStorageGet("isApplicationReloaded")) {
        this.cdgService.cardTypeSelected.isComingFromPreview = false;
        this.router.navigate(['layout/onlineApplication']);
      }
    }
    else {
      if (this.accountnumber = this.localStorage.localStorageGet("custNo") != null) {
        this.accountnumber = this.localStorage.localStorageGet("custNo").view;
      }
      else {
        this.accountnumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].account_no;
      }
    }
    if (this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails) {
      this.customernumber = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number;
      this.customerName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name

    }
    else {
      console.log(this.localStorage.localStorageGet('customerviewvalue'), this.localStorage.localStorageGet('viewvalueonload'));

      if (this.localStorage.localStorageGet('customerviewvalue') === null) {
        this.customernumber = this.localStorage.localStorageGet('viewvalueonload').substr(0, 6);
        this.customerName = this.localStorage.localStorageGet('viewvalueonload').substr(9)
      }
      else {
        this.customernumber = this.localStorage.localStorageGet('customerviewvalue').substr(0, 6);
        this.customerName = this.localStorage.localStorageGet('customerviewvalue').substr(9)
      }
    }
    this.accountName = this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails ? this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name : this.customerName,
      this.accNo = this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_MASTERUSER ? this.customernumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number;
    console.log(this.localStorage.localStorageGet("deparmentCard"))
    this.isShowErrorPage = false;
    if (this.localStorage.localStorageGet("deparmentCard")) {
      if (this.localStorage.localStorageGet("deparmentCard").length > 0) {
        this.header = "Departmental Card Nomination Form Preview"
        this.deparmentCard = this.localStorage.localStorageGet("deparmentCard")

      } else {
        this.isShowErrorPage = true;
      }
    }
    else if (this.localStorage.localStorageGet("individualCard")) {
      if (this.localStorage.localStorageGet("individualCard").length > 0) {
        this.header = "Individual Card Nomination Form Preview"
        this.individualCard = this.localStorage.localStorageGet("individualCard")
      }
      else {
        this.isShowErrorPage = true;
      }
    }
    else if (this.localStorage.localStorageGet("virtualCard")) {
      if (this.localStorage.localStorageGet("virtualCard").length > 0) {
        this.header = "Virtual Card Nomination Form Preview"
        this.virtualCard = this.localStorage.localStorageGet("virtualCard")
      }
      else {
        this.isShowErrorPage = true;
      }
    }
    else {
      this.isShowErrorPage = true;
    }

  }
  cancel() {
    this.cdgService.cardTypeSelected.isComingFromPreview = true;
    this.cdgService.cardTypeSelected.function = "cancel"
    if (this.localStorage.localStorageGet("deparmentCard")) {
      if (this.localStorage.localStorageGet("deparmentCard").length > 0) {
        this.cdgService.cardTypeSelected.cardType = "dc";
      }
    }
    else if (this.localStorage.localStorageGet("individualCard")) {
      if (this.localStorage.localStorageGet("individualCard").length > 0) {
        this.cdgService.cardTypeSelected.cardType = "ic"
      }
    }
    else if (this.localStorage.localStorageGet("virtualCard")) {
      if (this.localStorage.localStorageGet("virtualCard").length > 0) {
        this.cdgService.cardTypeSelected.cardType = "vc"
      }
    }
    this.router.navigate(['layout/onlineApplication']);
  }
  edit() {
    this.cdgService.cardTypeSelected.isComingFromPreview = true;
    this.cdgService.cardTypeSelected.function = "edit"
    if (this.localStorage.localStorageGet("deparmentCard")) {
      if (this.localStorage.localStorageGet("deparmentCard").length > 0) {
        this.cdgService.cardTypeSelected.cardType = "dc";
      }
    }
    else if (this.localStorage.localStorageGet("individualCard")) {
      if (this.localStorage.localStorageGet("individualCard").length > 0) {
        this.cdgService.cardTypeSelected.cardType = "ic"
      }
    }
    else if (this.localStorage.localStorageGet("virtualCard")) {
      if (this.localStorage.localStorageGet("virtualCard").length > 0) {
        this.cdgService.cardTypeSelected.cardType = "vc"
      }
    }
    this.router.navigate(['layout/onlineApplication']);
  }
  submit() {
    if (this.localStorage.localStorageGet("deparmentCard")) {
      if (this.localStorage.localStorageGet("deparmentCard").length > 0) {
        let reqData: any[] = [];
        this.deparmentCard.forEach((element: any) => {
          console.log(element.dept)
          let obj = {
            "divCode": element.costCenter ? this.localStorage.localStorageGet("costCenterArray").find((o: any) => o.nameToDisplayOnDropdown === element.costCenter).accountCode : "",
            "deptCode": Object.keys(element.dept).length > 0 ? element.deptArray.find((o: any) => o.nameToDisplayOnDropdown === element.dept).accountCode : "",
            "divAccount": element.costCenter ? this.localStorage.localStorageGet("costCenterArray").find((o: any) => o.nameToDisplayOnDropdown === element.costCenter).accountName : "",
            "deptAccount": Object.keys(element.dept).length > 0 ? element.deptArray.find((o: any) => o.nameToDisplayOnDropdown === element.dept).accountName : "",
            "contactPerson": element.contactPerson,
            "contactTel": element.contactTel,
            "contactMobile": element.constactMob,
            "addressBlock": element.addressBlock,
            "addressUnit": element.addressUnit,
            "addressStreet": element.addressStreet,
            "addressBuilding": element.addressBuilding,
            "addressArea": element.addressArea,
            "countryCode": element.countryCode,
            "city": element.city,
            "state": element.state,
            "postal": element.postal,
            "numberofCards": element.numberOfCard ? element.numberOfCard.toString() : "",
            "newDivOrDeptName": element.deptName,
            "nameOnCard": element.nameOnCard,
            "remarks": element.remarks
          }
          reqData.push(obj)
        })
        let reqObj = {
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          "productType": "DEPARTMENT_CARD",
          "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.customernumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number,
          "accountName": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails ? this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name : this.customerName,
          "shippingDetailsDtoList": reqData
        }
        this.getSubmitResponse(reqObj)
      } else {
        this.isShowErrorPage = true;
      }
    }
    else if (this.localStorage.localStorageGet("individualCard")) {
      if (this.localStorage.localStorageGet("individualCard").length > 0) {
        let reqData: any[] = [];
        this.individualCard.forEach((element: any) => {
          let obj = {
            "divCode": element.costCenter ? this.localStorage.localStorageGet("costCenterArray").find((o: any) => o.nameToDisplayOnDropdown === element.costCenter).accountCode : "",
            "deptCode": Object.keys(element.dept).length > 0 ? element.deptArray.find((o: any) => o.nameToDisplayOnDropdown === element.dept).accountCode : "",
            "divAccount": element.costCenter ? this.localStorage.localStorageGet("costCenterArray").find((o: any) => o.nameToDisplayOnDropdown === element.costCenter).accountName : "",
            "deptAccount": Object.keys(element.dept).length > 0 ? element.deptArray.find((o: any) => o.nameToDisplayOnDropdown === element.dept).accountName : "",
            "contactPerson": element.contactPerson,
            "contactTel": element.contactTel,
            "contactMobile": element.constactMob,
            "addressBlock": element.addressBlock,
            "addressUnit": element.addressUnit,
            "addressStreet": element.addressStreet,
            "addressBuilding": element.addressBuilding,
            "addressArea": element.addressArea,
            "countryCode": element.countryCode,
            "city": element.city,
            "state": element.state,
            "postal": element.postal,
            "remarks": element.remarks,
            "salutation": element.salutation,
            "cardHolderName": element.name,
            "employeeId": element.nameOnCardInd,
            "mobileNo": element.mobileNo,
            "emailAddress": element.email
          }
          reqData.push(obj)
        })
        let reqObj = {
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          "productType": "INDIVIDUAL_CARD",
          "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.customernumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number,
          "accountName": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails ? this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name : this.customerName,
          "shippingDetailsDtoList": reqData
        }
        this.getSubmitResponse(reqObj)
      }
      else {
        this.isShowErrorPage = true;
      }
    }
    else if (this.localStorage.localStorageGet("virtualCard")) {
      if (this.localStorage.localStorageGet("virtualCard").length > 0) {
        let reqData: any[] = [];
        this.virtualCard.forEach((element: any) => {
          let obj = {
            "divCode": element.costCenter ? this.localStorage.localStorageGet("costCenterArray").find((o: any) => o.nameToDisplayOnDropdown === element.costCenter).accountCode : "",
            "deptCode": Object.keys(element.dept).length > 0 ? element.deptArray.find((o: any) => o.nameToDisplayOnDropdown === element.dept).accountCode : "",
            "divAccount": element.costCenter ? this.localStorage.localStorageGet("costCenterArray").find((o: any) => o.nameToDisplayOnDropdown === element.costCenter).accountName : "",
            "deptAccount": Object.keys(element.dept).length > 0 ? element.deptArray.find((o: any) => o.nameToDisplayOnDropdown === element.dept).accountName : "",
            "contactPerson": element.contactPerson,
            "contactTel": element.contactTel,
            "contactMobile": element.constactMob,
            "addressBlock": element.addressBlock,
            "addressUnit": element.addressUnit,
            "addressStreet": element.addressStreet,
            "addressBuilding": element.addressBuilding,
            "addressArea": element.addressArea,
            "countryCode": element.countryCode,
            "city": element.city,
            "state": element.state,
            "postal": element.postal,
            "remarks": element.remarks,
            "salutation": element.salutation,
            "cardHolderName": element.name,
            "employeeId": element.nameOnCardVir,
            "mobileNo": element.mobileNo,
            "emailAddress": element.email
          }
          reqData.push(obj)
        })
        let reqObj = {
          "accessId": this.localStorage.localStorageGet("loginUserDetails").accessId,
          "role": this.localStorage.localStorageGet("loginUserDetails").roles.roleName,
          "productType": "VIRTUAL_CARD",
          "accountNo": this.localStorage.localStorageGet("loginUserDetails").roles.roleName === RoleConstants.ROLE_MASTERUSER ? this.customernumber : this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].customer_number,
          "accountName": this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails ? this.localStorage.localStorageGet("landingScreenNamesDto").accountDetails[0].parent_account_name : this.customerName,
          "shippingDetailsDtoList": reqData
        }
        this.getSubmitResponse(reqObj)
      }
      else {
        this.isShowErrorPage = true;
      }
    }
    else {
      this.isShowErrorPage = true;
    }
  }

  getSubmitResponse(reqObj: any) {
    this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2OS + UrlConstants.GET_ONLINE_APP_CARD_SUBMIT, reqObj).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body;
        this.notifyService.showSuccess(opt.successMessage, "Success")
        this.cdgService.cardTypeSelected.isComingFromPreview = true;
        this.cdgService.cardTypeSelected.function = "cancel"
        if (this.localStorage.localStorageGet("deparmentCard")) {
          this.cdgService.cardTypeSelected.cardType = "dc";
        }
        else if (this.localStorage.localStorageGet("individualCard")) {
          this.cdgService.cardTypeSelected.cardType = "ic"
        }
        else if (this.localStorage.localStorageGet("virtualCard")) {
          this.cdgService.cardTypeSelected.cardType = "vc"
        }
        else {
          this.isShowErrorPage = true;
        }
        this.router.navigate(['layout/onlineApplication']);
      }
    },
      (e: any) => {
        this.notifyService.showError(e.error.errorMessage, "Error")
      })


  }
}
