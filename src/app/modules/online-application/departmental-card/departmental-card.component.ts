import { Component, OnInit } from '@angular/core';
import { NgForm, FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms'
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';

// export const Data{
//   costCenter:string="12";
// }
@Component({
  selector: 'app-departmental-card',
  templateUrl: './departmental-card.component.html',
  styleUrls: ['./departmental-card.component.scss']
})
export class DepartmentalCardComponent implements OnInit {
  deparmentCard: any[] = [];
  data = {
    costCenter: "",
    deptName: "",
    nameOnCard: "",
    remarks: "",
    dept: "",
    isShowDept: false,
    isShowShippingDetails: false
  }
  costcenter = [
    { value: 'cabincrew', viewValue: 'Cabin Crew (1003)' },
    { value: 'corporateoffice', viewValue: 'Corporate Office (1003)' },
    { value: 'csm1', viewValue: 'CSM1 (1004)' },
  ]
  departmentList = [
    { value: 'a', viewValue: 'Dept A' },
    { value: 'b', viewValue: 'Dept B' },
    { value: 'c', viewValue: 'Dept C' },
  ]
  isShowDept: boolean = false
  isShowShippingDetails: boolean = false

  constructor(private fb: FormBuilder, public cdgService: CdgSharedService, private notifyService: NotificationService) { }

  ngOnInit(): void {
    this.deparmentCard.push(this.data)
  }
  addNominee() {
    this.deparmentCard.push({
      costCenter: "",
      deptName: "",
      nameOnCard: "",
      remarks: "",
      dept: "",
      isShowDept: false,
      isShowShippingDetails: false
    })

  }
  deleteNominee(index: number) {
    this.deparmentCard.splice(index, 1)

  }
  onSelection(val: any, index: number) {
    this.deparmentCard.forEach((element, i) => {
      if (i === index) {
        if (val === 'Cabin Crew (1003)') {
          element.isShowDept = true;
          element.isShowShippingDetails = true;
        }
        else {
          element.isShowDept = false;
          element.isShowShippingDetails = true;
        }
      }
    });

  }
  submit(valid: any) {
    if (valid) {
      this.notifyService.showSuccess('Nominees Added Successfully', 'Success')
    }
    else {
      this.notifyService.showError('Mandatory Fields cannot be blank', 'Error')

    }
  }
}
