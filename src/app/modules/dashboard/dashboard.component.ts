import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CdgSharedService } from './../../shared/services/cdg-shared.service'

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {

  constructor(private router: Router,public cdgService: CdgSharedService) { }

  ngOnInit(): void {
    this.cdgService.isDataFromAccessId=false;
  }

  AccessControl(){
    this.router.navigateByUrl('/layout/accesscontrol');
  }
    AccessId(){
      this.cdgService.showMatrix=false;
    this.router.navigateByUrl('/layout/Accessid');
  }
    EmailConfig(){
    this.router.navigateByUrl('/layout/Emailconfig');
  }
    PhConfig(){
    this.router.navigateByUrl('/layout/ph');
  }

  RetrieveOtp(){
    this.cdgService.showMatrix=false;
    this.router.navigateByUrl('/layout/retrieveOtp');
  }
  
  //   PublicPages(){
  //   this.router.navigateByUrl('/layout/publicpages');
  // }
}
