<div fxLayout="column" fxLayoutAlign="center center" fxLayoutGap="15px">
  <div>
    <p class="title">Welcome, Super Admin</p>
  </div>
  <div>
    <mat-card appearance="outlined" class="customize-mat-card" fxLayout="row">
      <div fxFlex="80%" fxLayoutAlign="start center">
        <span class="content">Access Control</span>
      </div>
      <div fxFlex="20%" fxLayoutAlign="center center">
        <button mat-icon-button (click)="AccessControl()">
          <mat-icon class="mat-icon">keyboard_arrow_right</mat-icon>
        </button>
      </div>
    </mat-card>
  </div>
  <div>
    <mat-card appearance="outlined" class="customize-mat-card" fxLayout="row">
      <div fxFlex="80%" fxLayoutAlign="start center">
        <span class="content">Access ID</span>
      </div>
      <div fxFlex="20%" fxLayoutAlign="center center">
        <button mat-icon-button (click)="AccessId()">
          <mat-icon class="mat-icon">keyboard_arrow_right</mat-icon>
        </button>
      </div>
    </mat-card>
  </div>
  <div>
    <mat-card appearance="outlined" class="customize-mat-card" fxLayout="row">
      <div fxFlex="80%" fxLayoutAlign="start center">
        <span class="content">Email Configuration</span>
      </div>
      <div fxFlex="20%" fxLayoutAlign="center center">
        <button mat-icon-button (click)="EmailConfig()">
          <mat-icon class="mat-icon">keyboard_arrow_right</mat-icon>
        </button>
      </div>
    </mat-card>
  </div>
  <div>
    <mat-card appearance="outlined" class="customize-mat-card" fxLayout="row">
      <div fxFlex="80%" fxLayoutAlign="start center">
        <span class="content">PH Configuration</span>
      </div>
      <div fxFlex="20%" fxLayoutAlign="center center">
        <button mat-icon-button (click)="PhConfig()">
          <mat-icon class="mat-icon">keyboard_arrow_right</mat-icon>
        </button>
      </div>
    </mat-card>
  </div>
  <div>
    <mat-card appearance="outlined" class="customize-mat-card" fxLayout="row">
      <div fxFlex="80%" fxLayoutAlign="start center">
        <span class="content">Retrieve OTP</span>
      </div>
      <div fxFlex="20%" fxLayoutAlign="center center">
        <button mat-icon-button (click)="RetrieveOtp()">
          <mat-icon class="mat-icon">keyboard_arrow_right</mat-icon>
        </button>
      </div>
    </mat-card>
  </div>
  <div>
    <!-- <mat-card class="customize-mat-card" fxLayout="row">
                         <div fxFlex="80%" fxLayoutAlign="start center">
                       <span class="content">Public Pages</span>
                    </div>
                     <div fxFlex="20%" fxLayoutAlign="center center">
                         <button mat-icon-button (click)="PublicPages()">
                        <mat-icon class="mat-icon">keyboard_arrow_right</mat-icon>
                         </button>
                    </div>
		</mat-card> -->
  </div>
</div>