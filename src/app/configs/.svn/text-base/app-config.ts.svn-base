export class AppConfig {

    static readonly DATE_FMT = 'dd/MMM/yyyy';
    static readonly DATE_SUFFIX_FMT = 'DD MMM YYYY';
    // static readonly DATE_TIME_FMT = `${Constants.DATE_FMT} hh:mm:ss`;
    static readonly DATE_TIME_FMT = `${AppConfig.DATE_FMT}` + ' hh:mm:ss';
    static readonly DATE_TIME_SUFFIX_FMT = `${AppConfig.DATE_SUFFIX_FMT}` + ' hh:mm a';
    static readonly DATE_TIME_FMT_NEW = `${AppConfig.DATE_SUFFIX_FMT}` + ' hh:mm ';
        
    static readonly spinner = {
        options: {
            lines: 13, // The number of lines to draw
            length: 11, // The length of each line
            width: 4, // The line thickness
            radius: 11, // The radius of the inner circle
            scale: 0.55, // Scales overall size of the spinner
            corners: 0.9, // Corner roundness (0..1)
            speed: 1.2, // Rounds per second
            rotate: 0, // The rotation offset
            animation: 'spinner-line-fade-quick', // The CSS animation name for the lines
            direction: 1, // 1: clockwise, -1: counterclockwise
            className: 'spinner', // The CSS class to assign to the spinner
        },
        overlay: true
    }

    // Routing menu mapping
    // Icon type 1 - Mat icon, 2 - Custom icons
    // Icon if type 1 mat icon name, else icon path
    static readonly featureRoutes = {
        opsUser: {
            "DASH-BRD": { routerLink: "dashboard", iconType: "1", icon: "dashboard" },
            "MY-RSTR": { routerLink: "my-roster", iconType: "1", icon: "event_note" },
            "MY-LVE": { routerLink: "my-leave", iconType: "1", icon: "timer_off" },
            "MY-INCTVE-ALWNCE": { routerLink: "my-incentive", iconType: "1", icon: "attach_money" },
            "MY-PRFL": { routerLink: "my-profile", iconType: "1", icon: "account_circle" },
            "FSH-MRNE": { routerLink: "fish", iconType: "1", icon: "account_circle" }
        },
        admin: {
            "MY-RSTR": { routerLink: "roster-mgmnt", iconType: "1", icon: "event_note" },
            "DASH-BRD": { routerLink: "dashboard", iconType: "1", icon: "dashboard" },
            "LVE-MGMT": { routerLink: "leave-mgmnt", iconType: "1", icon: "event_note" }
        }
    }

    static readonly subfeatureRoutes = {
        opsUser: {
            "APLY-LVE": { routerLink: "apply" },
            "LVE-DIRY": { routerLink: "diary" },
            "PLND-LVE": { routerLink: "plan" },
            "EPH": { routerLink: "eph" }
        }
    }

    // Roster instances config - Dropdown menu
    static readonly rosterInstances = {
        // "pilot": { label: "PILOT", routerLink: "pilot" },
        // "tug": { label: "TUG", routerLink: "tug" },
        "pilot": { label: "PILOT", routerKey: "pilot", key: "pilot" },
        "tug": { label: "TUG", routerKey: "tug", key: "tug" },
        "mcc": { label: "MCC", routerKey: "mcc", key: "mcc" },
        "driver": { label: "DRIVER", routerKey: "driver", key: "driver" },
        "launch": { label: "LAUNCH", routerKey: "launch", key: "launch" },
        "waterboat": { label: "WATERBOAT", routerKey: "waterboat", key: "waterboat" },
        "technician": { label: "TECHNICIAN", routerKey: "technician", key: "technician" },
        "basesupervisor": { label: "BASE SUPERVISOR", routerKey: "basesupervisor", key: "basesupervisor" },
        "officestaff": { label: "OFFICE STAFF (JO)", routerKey: "officestaff", key: "officestaff" },
    }

    // Instance roles config - Dropdown menu
    static readonly instanceRoles = {
        // "admin": { label: "ADMIN", routerLink: "admin" },
        // "opsUser": { label: "OP-STAFF", routerLink: "staff" },
        "admin": { label: "ADMIN", routerKey: "admin", key: "admin" },
        "opsUser": { label: "OP-STAFF", routerKey: "staff", key: "opsUser" },
    }

    static readonly rolesConfig = {
        admin: { routerPath: 'admin', roleCode: 'admin', roleLabel: 'ADMIN' },
        opsUser: { routerPath: 'staff', roleCode: 'opsUser', roleLabel: 'OPS USER' },
        dso: { routerPath: 'dso', roleCode: 'dso', roleLabel: 'DSO' }
    }

    static readonly rosterInstancesConfig = {
        pilot: { rosterCode: "pilot", rosterLabel: "PILOT", routerPath: "pilot" },
        tug: { rosterCode: "tug", rosterLabel: "TUG", routerPath: "tug" },
        mcc: { rosterCode: "mcc", rosterLabel: "MCC", routerPath: "mcc" },
        officestaff: { rosterCode: "officestaff", rosterLabel: "OFFICE STAFF", routerPath: "officestaff" },
        waterboat: { rosterCode: "waterboat", rosterLabel: "WATERBOAT", routerPath: "waterboat" },
        technician: { rosterCode: "technician", rosterLabel: "FLEET ENGINEERING", routerPath: "technician" },
        basesupervisor: { rosterCode: "basesupervisor", rosterLabel: "BASE LEADER", routerPath: "basesupervisor" },
        driver: { rosterCode: "driver", rosterLabel: "DRIVER", routerPath: "driver" },
        launch: { rosterCode: "launch", rosterLabel: "LAUNCH", routerPath: "launch" },
        mas: { rosterCode: "mas", rosterLabel: "MAS", routerPath: "mas" }
    }

}
