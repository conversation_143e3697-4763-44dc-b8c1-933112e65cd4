import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatInputModule } from '@angular/material/input';
import { LandingRoutingModule } from './landing-routing.module';
import { LandingComponent } from './landing.component';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule} from '@angular/material/menu';
import {MatSelectModule} from '@angular/material/select';
// import { MatCarouselModule} from  '@ngbmodule/material-carousel';
// import { SlickCarouselModule } from 'ngx-slick-carousel';


@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    LandingRoutingModule,
    MatInputModule,
    MatCheckboxModule,
    MatButtonModule,
    FlexLayoutModule.withConfig({ addFlexToParent: false }),
    MatCardModule,
    MatIconModule,
    MatMenuModule,
    MatSelectModule,
    // MatCarouselModule,
    // SlickCarouselModule
  ]
})
export class LandingModule { }
