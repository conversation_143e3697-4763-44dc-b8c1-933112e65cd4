import { Component, OnInit, HostListener } from '@angular/core';
import { Router } from '@angular/router';
// import { Mat<PERSON>arousel,MatCarouselComponent} from '@ngbmodule/material-carousel'
interface Food {
  value: string;
  viewValue: string;
}
@Component({
  selector: 'app-landing',
  templateUrl: './landing.component.html',
  styleUrls: ['./landing.component.scss']
})
export class LandingComponent implements OnInit {
  public isWebLayout = false;
  public isTabletLayout = false;
  public innerWidth: any;
  public btnName:string='VIEW ALL';
  public isShowProduct:boolean=false;
  public selectedValue: string='';
  selectedMenu:string='HOME';
  topMenuList=['HOME','ABOUT US','PRODUCT','USEFUL LINKS','CONTACT US']
  slide=[
    {
      page:1,
      img:'/assets/images/Cabcharge_white.png'
    },
    {
      
      page:2,
      img:'/assets/images/Cabcharge_white.png'
    }
  ]
  products=[
    {
      page:1,
      productList:[
        {
          title:'Cabcharge Personal',
          description:' eu ut sapien. Consecturer augue pretium, ullamcorper dui mollis tincidunt. In viverra amet',
          img:'/assets/images/cabcharge_logo.png'
        },
        {
          title:'Cabcharge Corporate',
          description:' eu ut sapien. Consecturer augue pretium, ullamcorper dui mollis tincidunt. In viverra amet',
          img:'/assets/images/cabcharge_logo.png'
        },
        {
          title:'Cabcharge e-Voucher',
          description:' eu ut sapien. Consecturer augue pretium, ullamcorper dui mollis tincidunt. In viverra amet',
          img:'/assets/images/cabcharge_logo.png'
        },
        {
          title:'Cabcharge Priority',
          description:' eu ut sapien. Consecturer augue pretium, ullamcorper dui mollis tincidunt. In viverra amet',
          img:'/assets/images/cabcharge_logo.png'
        }
      ]
    },
    {
      page:2,
      productList:[
        {
          title:'Cabcharge Individual',
          description:' eu ut sapien. Consecturer augue pretium, ullamcorper dui mollis tincidunt. In viverra amet',
          img:'/assets/images/cabcharge_logo.png'
        }
      ]
    }
  ]
  public foods: Food[] = [
    {value: 'steak-0', viewValue: 'Steak'},
    {value: 'pizza-1', viewValue: 'Pizza'},
    {value: 'tacos-2', viewValue: 'Tacos'}
  ];
  constructor(private router: Router) { }

  ngOnInit() {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth < 1280) { // 768px portrait
      this.isWebLayout = false;
      this.isTabletLayout = true;
    }
    else {
      this.isWebLayout = true;
      this.isTabletLayout = false;

    }
  }
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.innerWidth = window.innerWidth;
    console.log(this.innerWidth)
    if (this.innerWidth < 1280) { // 768px portrait
      this.isWebLayout = false;
      this.isTabletLayout = true;
    }
    else {
      this.isWebLayout = true;
      this.isTabletLayout = false;
    }

  }
  contatUs(){
    this.router.navigateByUrl('/contact-us');
  }
  loginNow() {
    this.router.navigateByUrl('/login');
  }

toggle(){
 this.isShowProduct = !this.isShowProduct;
 if(this.isShowProduct){
   this.btnName= 'VIEW LESS'
 }
 else{
   this.btnName= 'VIEW ALL'
 }
}
}