<div fxLayout="column" style="height: inherit;" (window:resize)="onResize($event)" class="content">
	<div fxFlex fxLayout="row">
		<mat-toolbar class="tool-bar-cls p2">
			<div fxFlex fxLayout="column">
				<div fxLayout="row">
					<div fxFlex=72%  fxLayout="row">
						<div fxLayoutAlign="center center" fxHide.gt-md>
							<button mat-icon-button class="mb10" [matMenuTriggerFor]="menu">
								<mat-icon class="ic-view-hedline">view_headline</mat-icon>
							</button>
							<mat-menu #menu="matMenu">
								<ng-container *ngFor="let menu of topMenuList; let i = index">
								<button mat-menu-item>
									<span>{{menu}}</span>
								</button>
							</ng-container>
							</mat-menu>
						</div><img src="/assets/images/Cabcharge_white.png" width="84px" alt=""></div>
					<div fxFlex=28% fxLayout="row" class="top-nav-div" fxLayoutGap="20px" fxHide.lt-md fxHide.md fxLayoutAlign="end center">
						<div fxLayout="row">
							<mat-icon class="top-nav-icon material-icons-outlined pt5">call</mat-icon><span>6550
								8746</span>
						</div>
						<div fxLayout="row">
							<mat-icon class="top-nav-icon material-icons-outlined pt5">email</mat-icon>
							<span>sales&#64;cdgtaxi.com.sg</span>
						</div>
						<div fxLayout="row">
							<mat-icon class="top-nav-icon material-icons-outlined pt5">account_circle</mat-icon><span>My
								Account</span>
						</div>


					</div>

				</div>
				<div fxLayout="row" fxHide.lt-md fxHide.md>
					<div fxFlex=75% fxLayout="row" fxLayoutGap="20px">
						<ng-container *ngFor="let menu of topMenuList; let i = index">
							<div class="top-nav-subdiv cursor-pointer">{{menu}}</div>
						</ng-container>
					</div>
					<div fxFlex=25%></div>
				</div>
			</div>
		</mat-toolbar>
	</div>
<div fxFlex>
	<mat-carousel timings="250ms ease-in" [autoplay]="false" color="primary" maxWidth="auto" [loop]="true"
	[hideArrows]="false" [hideIndicators]="false" [useKeyboard]="true" [useMouseWheel]="false"
	orientation="ltr">
	<mat-carousel-slide #matCarouselSlide  [hideOverlay]="false"
		*ngFor="let slide of slide; let i = index" [image]="slide.img">
		<div fxLayout="column" fxLayoutGap="30px" fxLayoutAlign="center center">
		<div fxLayout="column" fxLayoutGap="15px" fxLayoutAlign="center center" class="landing-content">
			<div class="landing-title" fxLayout="column" fxLayoutAlign="center center">
			<span>ComfortDelgro Cab<i class="charge">Charge</i>,</span>
			</div>
			<div class="landing-sub" fxLayout="column" fxLayoutAlign="center center">
				<span>Your Preferred Card In Singapore</span>
				</div>
		</div>
		<div fxFlex fxLayout="column" fxLayoutAlign="center center">
			<span class="sub-title">A unique charge account system for the</span>
			<span class="sub-title"> payment of taxi fares</span>
		</div>
		<div fxFlex fxLayout="row" fxLayout.lt-md="column" fxLayoutGap="10px" fxLayoutAlign="center start" class="btn">
			<div>
				<button mat-raised-button class="bookcabbtn pt15 pb15">Check your bill online</button>
			</div>
			<div>
				<button mat-raised-button class="checkbillbtn pt15 pb15">CabCharge Products</button>
			</div>
		</div>
	</div>
	</mat-carousel-slide>
</mat-carousel>
</div>
<div fxFlex class="login-div" >
<div fxLayout="column" class="mt55 pl35 mb55" fxLayoutGap="30px">
	<span>Easy Access</span>
	<div fxFlex fxLayout="column" class="quickview" fxLayoutGap="10px">
		<span>Quick View</span>
		<span>Of Transacrions!</span>
	</div>
	<div>
		<button mat-raised-button class="login-btn pt15 pb15" (click)="loginNow()">Login to your Account</button>
	</div>
</div>
</div>
<div fxLayout="column" class="product-Content mt50" fxLayoutGap="10px" style="margin-bottom: 50px;">
	<div fxFlex class="landing-title pl20 mt20">
		<span>Products</span>
	</div>
	<div fxFlex>
		<mat-carousel timings="250ms ease-in" [autoplay]="false" color="primary" maxWidth="auto" [loop]="true"
			[hideArrows]="false" [hideIndicators]="false" [useKeyboard]="true" [useMouseWheel]="false"
			orientation="ltr">
			<mat-carousel-slide #matCarouselSlide overlayColor="#fff" [hideOverlay]="false"
				*ngFor="let slide of products; let i = index">
				<div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="30px" fxLayoutAlign="center center" class="mt35">
					<ng-container *ngFor="let prod of slide.productList; let i = index">
						<mat-card appearance="outlined" class="card">
							<img mat-card-image src={{prod.img}} alt="product">
							<mat-card-title class="prodTitle">{{prod.title}}</mat-card-title>
							<mat-card-content class="prodCont">
								<p>{{prod.description}}</p>
							</mat-card-content>
							<mat-card-actions fxLayoutAlign="center center">
								<button mat-raised-button>APPLY</button>
							</mat-card-actions>
						</mat-card>
					</ng-container>
				</div>
			</mat-carousel-slide>
		</mat-carousel>

	</div>
</div>
<div fxFlex fxLayout="row" fxLayout.lt-md="column" class="happyClients" fxLayoutGap.lt-sm="40px">
<div fxFlex="35%" fxLayout="column" fxLayoutGap="20px" fxLayoutAlign="center center">
<div fxLayout="column" fxLayout.lt-sm="row" class="happyClient-header" fxLayoutGap="15px">
	<span>Happy</span>
	<span>Clients</span>
</div>
<div class="testimonal" fxHide.lt-sm>
	<span>Testimonals</span>
</div>
</div>
<div fxFlex="65%" fxLayoutAlign="center center" fxLayout="column">
	<div fxFlex fxLayout="column" fxLayoutAlign="center center">
		<span><mat-icon style="transform: rotate(180deg);" class="start-quote">format_quote</mat-icon> Surprising always cheaper than Grab or GOJEK.Fast</span>
	<span>response time and always many taxis available around</span>
	<span>you, even in industrial areas.</span>
	</div>
	<div fxFlex><mat-icon class="end-quote">format_quote</mat-icon></div>
<div fxLayoutAlign="center center" fxLayout="column">
	<span>Ms Serene Chiu</span>
	<span>Compliment via Google Play Store</span>

</div>
</div>
</div>
<div fxFlex fxLayout="column" class="contact-details" fxLayoutGap="30px">
<div fxFlex fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="20px">
	<div fxFlex fxLayout="column" fxLayoutGap="7px">
		<span class="about">About</span>
		<span class="cdg">CabCharge</span>
		<span class="pt20">Cabcharge is a unique charge<br>account systen for the payment of taxi<br>fares.With an itemized monthly<br>statement and report collating all taxy<br>trips made,it eliminates the need to <br>process petty cash claims and <br>reimbursements.</span>
	</div>
	<div fxFlex fxLayout="column" fxLayoutGap="7px">
		<span class="about">Presense</span>
		<span class="cdg">Social Market</span>
	</div>
	<div fxFlex fxLayout="column" fxLayoutGap="7px">
		<span class="about">Question?</span>
		<span class="cdg">Contact Us</span>
		<span class="pt20">Cabcharge Account &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; +65-6550 8746 <br>(Corporate/Personal)</span>
		<span class="pt20">Taxi Vouchers &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; +65-6550 8608</span>
	</div>

</div>
<div fxLayoutAlign="center center">
	<span style="text-align: center;">Terms and Condition | Privacy Policy <br> &#64;Cabcharge Asia Pte Ltd <br>Company Reg No: 198102368C</span>
</div>
</div>
</div>
