
@import '@angular/material/theming';
@import './../../styles/colors.scss';
@import './../../styles/sizing.scss';

.tool-bar-cls{
    width: 100%;
    display: flex;
    padding-top:50px !important;
    padding-bottom: 42px !important;
    background-color: $cdgc-bg-prime !important;
    font-style: $cdgsz-font-weight-normal;
    font-weight: $cdgsz-font-weight-normal;
    
}
.top-nav-icon{
    color: $cdgc-bg-accent !important;
    font-size: 20px !important;

}
.top-nav-div{
    color: $cdgc-bg-accent !important;
    font-size: 12px !important;
}
.top-nav-subdiv{
    color: $cdgc-bg-accent !important;
    font-size: 16px !important; 
    font-style: $cdgsz-font-weight-normal;
    font-weight: $cdgsz-font-weight-intermediate;
}

     .mb10{
     margin-bottom: 10px !important;
     }
.landing-content{
    margin-top: 90px !important;
    color: $cdgc-font-accent !important;
}
.charge{
    font-weight: $cdgsz-font-weight-normal;
    font-style: normal;
}
.btn{
    margin-top: 40px !important;
}
    .bookcabbtn{
        background-color: $cdgc-hue !important;
            border-radius: 7px !important;
            font-size: 18px !important;
            font-weight: $cdgsz-font-weight-normal;
            font-style: normal;
            width: 250px;
            padding: 7px;
    }
    .checkbillbtn{
        border:1px solid $cdgc-accent !important;
        border-radius: 7px !important;
        font-size: 18px !important;
        font-weight: $cdgsz-font-weight-normal;
        font-style: normal;
        width: 250px;
        padding: 7px;
    }
    .landing-title{
    font-weight: $cdgsz-font-weight-bold;
    font-style: normal;
    font-size: $cdgsz-font-size-xxl;

    }
    .landing-sub{
    font-weight: $cdgsz-font-weight-normal;
    font-style: normal;
    font-size: 42px;
    }
    .sub-title{
    font-weight: $cdgsz-font-weight-normal;
    font-style: normal;
    font-size: 28px;
    color: $cdgc-font-accent !important;
    }
    .mt55{
     margin-top: 55px;
     }
     .mb55{
        margin-bottom: 55px;
        }
     .pl35{
         padding-left: 35px;
     }
   .card {
  max-width: 190px;
  border: 1px solid $cdgc-bg-prime;
}
.content{
    background-color: $cdgc-bg-accent !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
::ng-deep .mat-form-field-appearance-outline .mat-form-field-prefix, .mat-form-field-appearance-outline .mat-form-field-suffix {
    top: unset !important;
    bottom: 63px !important;
    margin-right: 10px;
}
.ic-view-hedline{
    color: $cdgc-bg-accent !important;
}
::ng-deep .carousel{
    height:450px !important;
    min-height:450px !important;
}
::ng-deep.carousel-slide{
    height:450px !important;
    min-height:450px !important;
}
::ng-deep .carousel-inner{
    height:450px !important;
    min-height:450px !important;
}
::ng-deep .carousel-inner>.item{
    height:450px !important;
    min-height:450px !important;
}
::ng-deep .carousel-inner>.item>a>img, .carousel-inner>.item>img{
    line-height: 1;
}
::ng-deep .carousel-slide-overlay{
    height:450px !important;
}
::ng-deep .mat-icon{
    color:  $cdgc-bg-prime !important;
}
::ng-deep .mat-mdc-mini-fab.mat-accent{
  background-color: $cdgc-bg-prime !important;
  border: 1px solid $cdgc-bg-accent;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version. */
::ng-deep .mat-mdc-mini-fab.mat-mdc-button-disabled{
    background-color: $cdgc-bg-accent !important;
    border: 1px solid $cdgc-bg-prime;
  }
  .prodTitle{
    font-size: $cdgsz-font-size-prime;
    font-weight: $cdgsz-font-weight-bold;
  }
  .prodCont{
    font-size: $cdgsz-font-size-prime;
    font-weight: $cdgsz-font-weight-normal;
  }

  .login-div{
      background-color: $cdgc-bg-prime;
      color: $cdgc-font-accent !important;
  }
  .login-btn {
      background-color: $cdgc-bg-accent !important;
      color: $cdgc-font-dark !important;
      border-radius: 4px !important;
  }
  .quickview{
    font-size: $cdgsz-font-size-xxl;
    font-weight: $cdgsz-font-weight-intermediate;
  }
  .happyClients{
      background-color: #808080;
      padding:95px 30px 85px 30px;
      color: $cdgc-font-accent !important;

  }
  .happyClient-header{
    font-style: normal;
    font-size: $cdgsz-font-size-xxl;
    font-weight: $cdgsz-font-weight-bold;
}
.testimonal{
    font-size: 22px;
}
.align-center{
    text-align: center;
}
.start-quote{
    height: 55px;
    font-size: 52px;
    color:  $cdgc-bg-accent !important;

}
.end-quote{
    height: 55px;
    font-size: 52px;
    margin-left: 350px;
    color:  $cdgc-bg-accent !important;

}
.contact-details{
    background-color: #202020;
    color: $cdgc-font-accent !important;
    font-size: 12px !important;
    padding: 30px;
}
.about{
    font-size: 20px !important;
}
.cdg{
    font-size: 24px !important;
}
@media screen and (max-width: 1279px) {
    .example-card {
  max-width: 350px;
}
}
@media screen and (max-width: 600px) {
.landing-sub{
    font-size: 27px;
    }
    .landing-title{
        font-weight: $cdgsz-font-weight-intermediate;
        font-style: normal;
        font-size: 35px;
    
        }
        .sub-title{
            font-size: 21px;
        }
        .start-quote{
            height: 24px;
            font-size: 24px;
        }
        .end-quote{
            height: 24px;
            font-size: 24px;
            margin-left: 350px;
        }
        ::ng-deep .carousel{
            height:1650px !important;
            min-height:1650px !important;
        }
        ::ng-deep.carousel-slide{
            height:1650px !important;
            min-height:1650px !important;
        }
        ::ng-deep .carousel-inner{
            height:1650px !important;
            min-height:1650px !important;
        }
        ::ng-deep .carousel-inner>.item{
            height:1650px !important;
            min-height:1650px !important;
        }
        ::ng-deep .carousel-inner>.item>a>img, .carousel-inner>.item>img{
            line-height: 1;
        }
        ::ng-deep .carousel-slide-overlay{
            height:1650px !important;
        }
}
@media screen and (max-width: 400px) {
.landing-sub{
    font-weight: $cdgsz-font-weight-intermediate;
    font-size: 18px;
    }
    .landing-title{
        font-weight: $cdgsz-font-weight-bold;
        font-style: normal;
        font-size: 24px;
    
        }
        .sub-title{
            font-size: 17px;
        }
        ::ng-deep .carousel> button:first-of-type {
    left: 0px !important;
}
::ng-deep .carousel> button:last-of-type {
    right: 0px !important;
}

}