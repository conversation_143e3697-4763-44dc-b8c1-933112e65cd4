<form style="margin-top: 4%;" [formGroup]="registrationForm">
    <div fxFlex fxLayout="column"  fxLayoutAlign="center">
        <div fxFlex fxLayout="row" fxLayoutGap="8px" fxLayoutAlign="center center"style="margin-bottom: 0px ;">
            <mat-form-field class="align-icon-text username" style="width:100%;" fxFlex="100%" appearance="outline">
                <mat-icon matPrefix >account_circle</mat-icon>
                <input (keyup)="resetVerify()" #username formControlName="username" type="email" matInput
                placeholder="Email" (input)="toLowerCaseConverstion()" required>
                    <!-- (<EMAIL>) -->

                <mat-error class="error-container" *ngIf="registrationForm.controls.username.hasError('required')">
                    Please Enter Email
                </mat-error>
                <mat-error class="error-container" *ngIf="registrationForm.controls.username.hasError('email')">
                    Please Enter Valid Email Address
                </mat-error>
                <button type="button" matSuffix mat-button  fxFlex="30%" *ngIf="!verifyBtn" (click)="verify(username.value,cardNumber.value)" class="verify">
                    Verify
                </button>
                <button type="button" matSuffix mat-button fxFlex="30%" *ngIf="verifyBtn" (click)="verify(username.value,cardNumber.value)" class="done">
                    <mat-icon class="mat-icon-done">done</mat-icon>
                </button>
               
            </mat-form-field>
            
            <!-- <button *ngIf="!enableRegister" (click)="verify(username.value)" class="verify" style="width:100%;"
                fxFlex="30%">
                Verify
            </button>
            <button mat-button *ngIf="enableRegister" (click)="verify(username.value)" class="verify" style="width:100%;"
                fxFlex="30%">
                <mat-icon>done</mat-icon>
            </button> -->
        </div>

        <mat-form-field style="width:100% " appearance="outline">
            <mat-icon matPrefix >credit_card</mat-icon> <input #cardNumber type="number" (keyup)="resetVerify()" formControlName="cardNumber"
                matInput placeholder="Card No." required minlength="16" maxlength="16">
            <mat-error class="error-container" *ngIf="registrationForm.controls.cardNumber.hasError('required')">
                Please Enter Card Number
            </mat-error>
            <mat-error class="error-container" *ngIf="registrationForm.controls.cardNumber.hasError('pattern')">
                Please Enter a Valid Card Number
            </mat-error>
            <!-- <mat-error class="error-container" *ngIf="registrationForm.controls.cardNumber.hasError('minLength')">
                Please Enter Valid Integer Card Number
            </mat-error>
            <mat-error class="error-container" *ngIf="registrationForm.controls.cardNumber.hasError('maxLength')">
                Please Enter Valid Integer Card Number
            </mat-error> -->
        </mat-form-field>


        <!-- <mat-form-field style="width:100%;" appearance="outline">
            <mat-icon class="pointer" (click)="picker.open()">date_range</mat-icon>
            <input matInput type="text" placeholder="Expiry Date (mm/yyyy)" formControlName="expiryDate" [matDatepicker]="picker" onkeydown="return false">
            <mat-datepicker #picker></mat-datepicker>
        </mat-form-field> -->
        <mat-form-field style="width:100%;" appearance="outline">
            <!-- <mat-label>Month and Year</mat-label> -->
            <input matInput [matDatepicker]="dp" placeholder="Expiry Date (mm/yyyy)" [formControl]="date">
            <mat-datepicker-toggle matPrefix [for]="dp"></mat-datepicker-toggle>
            <mat-datepicker #dp
                            startView="multi-year"
                            (yearSelected)="chosenYearHandler($event)"
                            (monthSelected)="chosenMonthHandler($event, dp)"
                            panelClass="example-month-picker">
            </mat-datepicker>
          </mat-form-field>


        <mat-form-field style="width:100%;" appearance="outline">
            <button mat-icon-button matPrefix matTooltip="{{getPasswordInfo()}}"><mat-icon class="mb16">https</mat-icon></button>
            <input #input autocomplete="new-password" [type]="hide ? 'password' : 'text'" formControlName="password"
                type="password" matInput placeholder="Password" (focusout)="passwordValidation()" required>
                <!-- showError &&  -->
            <mat-error class="error-container" *ngIf="showError && registrationForm.controls.password.hasError('required')">
                Please Enter Password
            </mat-error>
            <mat-error class="error-container" *ngIf="showError && registrationForm.controls.password.hasError('pattern')">
                Password Does Not Meet The Criteria
            </mat-error>
            <!-- showError &&  -->
            <button mat-icon-button matSuffix (click)="hide = !hide" [attr.aria-label]="'Hide password'"
                [attr.aria-pressed]="hide">
                <mat-icon class="password-icon">{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
            </button>
        </mat-form-field>
 

        <mat-form-field style="width:100%;" class="marginBottom0" appearance="outline">
            <mat-icon matPrefix >check_circle</mat-icon> 
            <input #confirmPassword [type]="hideConfirm ? 'password' : 'text'" (input)="confirmPasswordValidation()"
                formControlName="confirmPassword" matInput placeholder="Confirm Password"
                required>
            <button mat-icon-button matSuffix (click)="hideConfirm = !hideConfirm" [attr.aria-label]="'Hide password'"
                [attr.aria-pressed]="hideConfirm">
                <mat-icon class="password-icon">{{hideConfirm ? 'visibility_off' : 'visibility'}}</mat-icon>
            </button>
        </mat-form-field>
        <div>
            <mat-error style="margin-top: 0px; margin-bottom: 0px;font-size: 75%;" class="error-container" *ngIf="showMatchError">
                Password & Confirm Password Does Not Match
            </mat-error> 
        </div>


        <div fxFlex>
            <div fxFlexFill fxLayoutAlign="center center">
                <button type="button" mat-raised-button (click)="goToVerifyOTP()" [disabled]="!enableRegister">
                    Register
                </button>
            </div>
        </div>

        <div class="footer-div">
            <p class="footer">By signing up, I agree to Cabcharge’s <a href="">Terms of Use </a> and <a href="">Privacy
                    Policy</a></p>
            <p class="footer">Already have an account? <a href="">Sign In instead</a></p>
        </div>


    </div>
</form>