import { Component, OnInit } from '@angular/core';
import { Validators, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import { DatePipe } from '@angular/common';
import * as moment from 'moment';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MatD<PERSON>pic<PERSON> } from '@angular/material/datepicker';
import * as _moment from 'moment';
// tslint:disable-next-line:no-duplicate-imports
import { Moment } from 'moment';
// const moment = _rollupMoment || _moment;

// export const MY_FORMATS = {
//   parse: {
//     dateInput: 'LL'
//   },
//   display: {
//     dateInput: 'MM/YYYY',
//     monthYearLabel: 'YYYY',
//     dateA11yLabel: 'LL',
//     monthYearA11yLabel: 'YYYY'
//   }
// }
export const MY_FORMATS = {
  parse: {
    dateInput: 'MM/YYYY',
  },
  display: {
    dateInput: 'MM/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};
@Component({
  selector: 'app-register-personal',
  templateUrl: './register-personal.component.html',
  styleUrls: ['./register-personal.component.scss'],
  providers: [DatePipe, {
    provide: DateAdapter,
    useClass: MomentDateAdapter,
    deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
  },

    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]

})
export class RegisterPersonalComponent implements OnInit {

  hide = true;
  hideConfirm = true;
  submitted = false;
  showMatchError = false;

  registrationForm: FormGroup;

  enableRegister: boolean = false;
  verifyBtn: boolean = false;
  showError: boolean;
  date = new FormControl();
  constructor(private datePipe: DatePipe, private fb: FormBuilder, private router: Router, private apiService: ApiServiceService, private notifyService: NotificationService, private cdgService: CdgSharedService, public localStorage: LocalStorageService) {
    this.registrationForm = this.fb.group({
      username: ['', [Validators.required, Validators.email]],
      cardNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{16,16}$/)]],
      // , Validators.min(16), Validators.max(16),
      expiryDate: [''],
      password: ['', [Validators.required, Validators.minLength(8), Validators.maxLength(20), Validators.pattern("(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[$@$!%*?&#^]).{8,20}")]],
      confirmPassword: ['', Validators.required]
    },
  
    );
   }

  ngOnInit(): void {
    this.localStorage.localStorageSet("isLoginSuccessfull", false);
    // this.date.setValue("")
    // this.registrationForm.value.expiryDate=moment()
  }
  chosenMonthHandler(normalizedMonth: Moment, datepicker: MatDatepicker<Moment>) {
    const ctrlValue = this.date.value;
    ctrlValue.month(normalizedMonth.month());
    this.date.setValue(ctrlValue);
    console.log(this.date.value)
    datepicker.close();
  }
  chosenYearHandler(normalizedYear: Moment) {
    this.date = new FormControl(moment());
    const ctrlValue = this.date.value;
    ctrlValue.year(normalizedYear.year());
    this.date.setValue(ctrlValue);
  }
  // onSubmit() {
  //   // Change_by_navani : Use EventEmitter with form value
  //   // console.warn(this.registrationForm.value);
  // }


  // verify(val:string){
  //   this.enableRegister=false;
  //    if(val=="<EMAIL>"){
  //      this.enableRegister=true;
  //    }
  // }
  getPasswordInfo() {
    let val = '';
    val = 'Password should contain at least one capital letter and one small letter. \n Password should contain at least one number. \n Password should contain at least one special character like !,@,#,$,%,^,&,* \n Password length should contain a min of 8 and a maximum of 20 characters';
    return val;
  }
  verify(userName: string, cardNumber: string) {
    console.log(this.datePipe.transform(this.date.value, "MMDDYYYY"))
    this.enableRegister = false;
    this.verifyBtn = false;
    this.showError = false;
    if (userName && cardNumber) {
      let obj = {
        accessId: userName,
        cardNumber: cardNumber
      }
      // this.enableRegister = true;
      // this.verifyBtn = true;
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2Apikey + UrlConstants.VERIFY_PERSONAL_CARDHOLDER, obj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body;
          this.enableRegister = true;
          this.verifyBtn = true;
          this.showError = false;
        }
      },
        e => {
          // console.log(e)
          this.notifyService.showError(e.error.errorMessage, 'Error')
        })
    }
    else {
      this.notifyService.showWarning('Email and Card Number is required for Verification', 'Waring')
    }
  }

  resetVerify() {
    this.enableRegister = false;
    this.verifyBtn = false;
  }

  goToVerifyOTP() {

    if (this.registrationForm.invalid) {
      console.log(this.registrationForm.value)
      this.showError = true;
      return;
    }
    else {
      let obj = {
        accountType: "CORPORATE/PERSONAL CARDHOLDER",
        accessId: this.registrationForm.value.username,
        cardNumber: this.registrationForm.value.cardNumber,
        cardExpiryDate: this.datePipe.transform(this.date.value, "MMYYYY"),
        password: this.registrationForm.value.password ? this.registrationForm.value.password.trim() : '',
        confirmPassword: this.registrationForm.value.confirmPassword ? this.registrationForm.value.confirmPassword.trim() : ''
      }
      // this.cdgService.popupObj.title = "Success";
      // this.cdgService.popupObj.msg = "email sent";
      // this.cdgService.showRegMsgDiv = true;
      // this.cdgService.registrationOptObj = obj;
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2Apikey + UrlConstants.REGISTER_PERSONAL_CARDHOLDER, obj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body;
          this.cdgService.popupObj.title = "Success";
          this.cdgService.popupObj.msg = opt.statusMessage;
          this.cdgService.popupObj.component = "registerPersonal"
          this.cdgService.showRegMsgDiv = true;
          this.cdgService.registrationOptObj = obj;
          this.cdgService.registerUserLogin.accessId = this.registrationForm.value.username
          this.cdgService.registerUserLogin.password = this.registrationForm.value.password
          this.localStorage.localStorageSet("registrationOptObj", this.cdgService.registrationOptObj)
        }
      },
        e => {
          console.log(e)
          this.notifyService.showError(e.error.errorMessage, 'Error')
        })
    }
  }
  passwordValidation() {
    if (this.registrationForm.value.password !== "") {
      this.cdgService.popupObj.function = "";
      this.cdgService.showRegMsgDiv = false;
      this.cdgService.passwordValidation = [
        {
          msg: "Password should contain at least one capital letter and one small letter",
          icon: "done"
        },
        {
          msg: "Password should contain at least one number",
          icon: "done"
        },
        {
          msg: "Password should contain at least one special character like !,@,#,$,%,^,&,*",
          icon: "done"
        },
        {
          msg: "Password length should contain a min of 8 and a maximum of 20 characters",
          icon: "done"
        }
      ]
      this.confirmPasswordValidation(); // check confirm password as same.
      const matches = this.registrationForm.value.password.match(/\d+/g);
      const format = /[`!@#$%^&*\\|,.<>\/?]+/
      const capsFormat = /[A-Z]/
      const smallLetterFormat = /[a-z]/
      console.log(this.registrationForm.value.password.length, format.test(this.registrationForm.value.password))
      if (!capsFormat.test(this.registrationForm.value.password) || !smallLetterFormat.test(this.registrationForm.value.password) || matches === null || format.test(this.registrationForm.value.password) === false || this.registrationForm.value.password.length <= 7 || this.registrationForm.value.password.length > 20) {
        if (!capsFormat.test(this.registrationForm.value.password) || !smallLetterFormat.test(this.registrationForm.value.password)) {
          this.cdgService.passwordValidation[0].icon = "close"
        }
        if (matches === null) {
          this.cdgService.passwordValidation[1].icon = "close"
        }
        if (!format.test(this.registrationForm.value.password)) {
          this.cdgService.passwordValidation[2].icon = "close"
        }
        if (this.registrationForm.value.password.length <= 7 || this.registrationForm.value.password.length > 20) {
          this.cdgService.passwordValidation[3].icon = "close"
        }
        this.cdgService.popupObj.function = "passwordValidation";
        this.cdgService.showRegMsgDiv = true;
        console.log(this.cdgService.passwordValidation)
      }

    }
  }

  toLowerCaseConverstion() {
    if (this.registrationForm.controls.username.value)
      this.registrationForm.get('username')?.setValue(this.registrationForm.controls.username.value.toLowerCase())
  }

  confirmPasswordValidation() {
    if (this.registrationForm.value.password !== "" && this.registrationForm.value.confirmPassword !== "") {

      if (this.registrationForm.value.password !== this.registrationForm.value.confirmPassword) {
        this.showMatchError = true;
      } else {
        this.showMatchError = false;
      }
    }
  }

}
