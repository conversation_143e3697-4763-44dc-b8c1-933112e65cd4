<!-- <div fxFlex fxLayout="row" fxLayout.lt-md="column">
    <mat-toolbar class="tool-bar-cls p2">
        <div fxFlex.gt-md=70%>
            <img src="/assets/images/cab-charge-label.png" alt="">
        </div>
        <div fxFlex.gt-md=30% fxFlexfxLayout="row" class="top-menu-main-div">
            <div fxFlex>
                <button class="hidden-sm" mat-icon-button>
                    <span class="top-menu">About Us</span>
                </button>
            </div>
            <div fxFlex>
                <button class="hidden-sm" mat-icon-button>
                    <span class="top-menu">Products</span>
                </button>
            </div>
            <div fxFlex>
                <button class="hidden-sm" mat-icon-button>
                    <span class="top-menu">Contact Us</span>
                </button>
            </div>

        </div>
    </mat-toolbar>
</div> -->

<!-- Main Code  -->

<div style="min-height: 100vh;" class="reg-container bg"> 
    <div *ngIf="cdgService.showRegMsgDiv && cdgService.popupObj.function !== 'passwordValidation'" class="box" [ngStyle]="{'background-color': cdgService.popupObj.title == 'Success' ? ' #28a745' :  '#ff0000'}">
		<div  fxLayout="column" class="p5">
			<p class="head">{{cdgService.popupObj.title}}</p>
			<p class="msg">{{cdgService.popupObj.msg}}</p>
			<div fxFlex fxLayoutAlign="end center" >
				<button mat-raised-button class="ok-btn" [ngStyle]="{'background-color': cdgService.popupObj.title == 'Success' ? '#DA6C2A' :  ' #808080'}" (click)="success()">Ok</button>
			</div>
		</div>
    </div>
    <div *ngIf="cdgService.showRegMsgDiv && cdgService.popupObj.function === 'passwordValidation'" class="validationBox" [ngStyle]="{'background-color': '#b9b9bb'}">
		<div  fxLayout="column" class="p5">
            <p class="head">Password Must Include</p>
            <ng-container *ngFor="let val of cdgService.passwordValidation; let i = index">
                <p class="msg" fxLayoutAlign="start start"> <mat-icon class="material-icons-outlined main-preview-icon" [ngStyle]="{'color': val.icon == 'done' ? ' #28a745' :  '#ff0000'}">{{val.icon}}</mat-icon>{{val.msg}}</p>
            </ng-container>
            <!-- 'Password should contain at least one capital letter. \n Password should contain at least one number. \n Password should contain at least one special character like @,!,%,&,* \n Password length should contain a min of 8 and a maximum of 20 characters' -->
			<div fxFlex fxLayoutAlign="end center" >
				<button mat-raised-button class="ok-btn" [ngStyle]="{'background-color': '#b9b9bb'}" (click)="passValidation()">Ok</button>
			</div>
		</div>
	</div>
<div fxLayout=column fxLayoutAlign="center center">
    <div class="registration-form" fxLayout="column" fxLayoutGap="4%" fxLayoutAlign="start center">
        <!-- <div>
            <p class="heading">Register your Cabcharge account</p>
        </div> -->
        <div style="width:100%" fxLayout="row"  fxLayoutAlign="start center" >
            <img class="img" src="/assets/images/Cabcharge_white.png" width="84px" alt="">
            <hr class="vl">
            <p class="heading">Register</p>
        </div>
        <div fxLayout="row">
            <mat-button-toggle-group #toggleGroup="matButtonToggleGroup" [(ngModel)]="selectedVal" class="toggleGroup" (ngModelChange)="getVal()" >
                <mat-button-toggle value="corporateAdmin" class="button-1">
                    <mat-icon class="icon">domain</mat-icon>
                    <p class="register-type">Corporate Admin</p>
                </mat-button-toggle>
                <mat-button-toggle value="corporatePersonal">
                    <mat-icon class="icon">person</mat-icon>
                    <p class="register-type">Corporate/Personal</p>
                </mat-button-toggle>
            </mat-button-toggle-group>
        </div>
        <!-- <div  [ngClass.gt-md]="'desktop'" [ngClass.sm]="'cell'">
            <app-register-personal *ngIf="toggleGroup.value == 'corporatePersonal'"></app-register-personal>
        </div>
        <div [ngClass.gt-md]="'desktop'" [ngClass.sm]="'cell'">
            <app-register-admin *ngIf="toggleGroup.value == 'corporateAdmin'"></app-register-admin>
        </div> -->
        <div  style="width: 93%;">
            <app-register-personal *ngIf="toggleGroup.value == 'corporatePersonal'"></app-register-personal>
        </div>
        <div style="width: 93%;">
            <app-register-admin *ngIf="toggleGroup.value == 'corporateAdmin'"></app-register-admin>
        </div>
    </div>
</div>  
</div>    
    <!-- <div fxHide.lt-md fxFlex="50%" style="background-color: #0077c0;">
    </div> -->
<!-- </div> -->