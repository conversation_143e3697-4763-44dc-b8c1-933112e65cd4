@import "@angular/material/theming";
@import "./../../styles/colors.scss";
@import "./../../styles/sizing.scss";

.tool-bar-cls {
    width: 100%;
    display: flex;
    padding-top: 60px !important;
    padding-bottom: 30px !important;
}

.top-menu-main-div {
    display: flex;
}

.register-type {
    font-size: $cdgsz-font-size-prime !important;
    font-weight: $cdgsz-font-weight-bold !important;
    color: #f2f2f2;
}

.icon {
    font-size: 75px !important;
    height: 95px !important;
    width: 80px !important;
    color: #f2f2f2 !important;
}

.heading {
    font-family: Roboto;
    font-style: normal;
    font-weight: bold !important;
    font-size: $cdgsz-font-size-xl !important; //22px
    line-height: 25px;
    color: #f2f2f2;
    // text-align: center;
    // padding: 34px 0 16px 0;
}

.full-height {
    min-height: 100vh !important;
}

// .desktop {
//     width: 90%;
// }

// .cell {
//     width: 100%;
// }

.img{
    padding:5% 0 2% 5%;
}
.vl{
    border: 1px solid #f2f2f2;
    height: 27px;
    margin: 0 14px 0 8px !important;
}
.registration-form{
    width:400px; 
    background-color: $cdgc-bg-prime;
     border-radius: 10px;
     margin:44px 0 44px 0;
}

// .register{
//     width:100% !important;
//     height: 100%;
//     border-radius: 10px;
//     background-color: $cdgc-bg-prime !important;
// }

 /*Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
 .mat-button-toggle.mat-button-toggle-appearance-standard{
    background-color: $cdgc-bg-prime !important;
    border: 0 !important;
    border-color: red ;
}

/*Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
 .mat-button-toggle.mat-button-toggle-checked.mat-button-toggle-appearance-standard{
    background-color: $cdgc-hue !important;
}

// ::ng-deep #username.mat-form-field-appearance-outline .mat-form-field-infix{
//     padding : 0 !important;
//     border-top: 0 !important;
// }
// ::ng-deep #username.mat-form-field-appearance-outline .mat-form-field-flex {
//     padding-right: 0px !important;
// }


.reg-container{
    position: relative;
  }
  .box{
    width: 375px !important;
    height: 152px;
    position: absolute;
    top: 9%;
    left: 50%;
    margin: 0;
    transform: translate(-50%,-50%);
    -ms-transform:translate(-50%,-50%);
    opacity: 1 !important;
    z-index: 9;
    border-radius: 10px;
    // background-color: $cdgc-bg-prime;
    box-shadow: 2px 2px 4px 2px rgba(243, 6, 6, 0.14);
    // box-shadow: 0 2px 4px 0 rgba(0,0,0,0.14);
    border: 4px double $cdgc-accent;
  
  }
  .validationBox{
    width: 375px !important;
    height: 211px !important;
    position: absolute;
    top: 12.1%;
    left: 50%;
    margin: 0;
    transform: translate(-50%,-50%);
    -ms-transform:translate(-50%,-50%);
    opacity: 1 !important;
    z-index: 9;
    border-radius: 10px;
    // background-color: $cdgc-bg-prime;
    box-shadow: 2px 2px 4px 2px rgba(243, 6, 6, 0.14);
    // box-shadow: 0 2px 4px 0 rgba(0,0,0,0.14);
    border: 4px double $cdgc-accent;
  
  }
  .ok-btn{
    width: 60px;
    height: 34px;
    border:1px solid $cdgc-accent !important;
    // background-color:$hue !important;
    color: $cdgc-accent;
    border-radius: 4px !important;
    font-size: $cdgsz-font-size-xs !important;
  }
  .head{
    margin: 0px !important;
    color: $cdgc-accent;
    font-weight: $cdgsz-font-weight-bold;
  }
  .msg{
    margin: 0px !important;
    color: $cdgc-accent;
  
  }
  .bg{
    background-image: url(/assets/images/DTK_3657.jpg) !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    // position: fixed;
    min-width: 100% !important;
  }

 @media screen and (max-width: 570px){
    .registration-form{
        width:92% !important; 
        background-color: $cdgc-bg-prime;
         border-radius: 10px;
         margin:44px 0 44px 0;
    }
 }

 @media screen and (max-width: 320px){
    .registration-form{
        width:98% !important; 
        background-color: $cdgc-bg-prime;
         border-radius: 10px;
         margin:44px 0 44px 0;
    }
    
 }


//change by navani 
// :host ::ng-deep .mat-button-toggle-appearance-standard {
//   border-radius: 5px!important;
//   border: transparent !important;
//   background-color: transparent !important;
// }

// ::ng-deep .mat-button-toggle-appearance-standard.mat-button-toggle-checked {
//   /* color: #000000de; */
//   background-color: #DA6C2A !important;
//   border: transparent !important;
//   border-radius: 5px!important;
// }
