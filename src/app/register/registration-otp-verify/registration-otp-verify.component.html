<div class="bg" fxLayoutAlign="center center">
    <div fxLayout=column fxLayoutAlign="center center">
        <div fxLayout="column"  class = "otp-form" fxLayoutGap="4%" fxLayoutAlign="start center" style="padding: 20px;">
            <!-- [ngClass.gt-md]="'otp-desktop'" [ngClass.md]="'otp-desktop'" [ngClass.sm]="'otp-cell'" -->
            <!-- [ngClass.lt-sm]="'otp-cell'" -->
            <div style="width:100%" fxLayout="row" fxLayoutGap="1px" fxLayoutAlign="start center">
                <img class="img" src="/assets/images/Cabcharge_white.png" width="84px" alt="">
                <hr class="vl">
                <p class="heading">OTP Verification</p>
            </div>
            <!-- <div class="otp-image" src="/assets/images/cab-charge-otp-logo.png" width="20px" height="50px"></div> -->
            <!-- <div>

            </div> -->
            <div style="width:90%" fxLayout="column" fxLayoutAlign="start">
                <p class="sub-heading">Verify your email address</p>
                <P class="content">An OTP is sent to your registered email address. Please enter the OTP below. Valid
                    for 10 minutes.
                </P>
            </div>
            <div *ngIf="isShowMsg" class="msg" fxLayoutAlign="center center"
                [ngStyle]="{'color': msgObj.title == 'Success' ? ' #ffffff' :  '#ff0000'}">
                <!-- 28a745 -->
                {{this.msgObj.msg}}
            </div>
            <form [formGroup]="verifyOtpForm" style="width:90%">
                <div fxLayout="column" fxLayoutAlign="center center ">
                    <mat-form-field style="width:100% " appearance="outline">
                        <!-- <mat-label>OTP</mat-label> -->
                        <mat-icon matPrefix class="mat-icon">fiber_pin</mat-icon> <input matInput formControlName="otp"
                            placeholder="OTP" required>
                        <mat-error class="error-container" *ngIf="verifyOtpForm.controls.otp.hasError('required')">
                            Please Enter OTP
                        </mat-error>
                        <!-- <mat-error class="error-container" *ngIf="verifyOtpForm.controls.otp.hasError('minLength')">
                                OTP must contain 6 digits
                            </mat-error> -->
                        <mat-error class="error-container" *ngIf="verifyOtpForm.controls.otp.hasError('pattern')">
                            Please Enter Number
                        </mat-error>

                    </mat-form-field>

                    <div style="width: 100%;">
                        <button mat-raised-button (click)="submit()">
                            Submit
                        </button>
                    </div>
                </div>
                <div fxLayout="column" fxLayoutAlign="center center" (click)="resendOtp()" class="pb10"><u
                        class="cursor-pointer res">Resend OTP</u></div>

            </form>

        </div>

    </div>
</div>