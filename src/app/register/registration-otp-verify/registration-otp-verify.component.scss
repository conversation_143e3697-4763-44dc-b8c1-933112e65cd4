@import '@angular/material/theming';
@import './../../../styles/colors.scss';
@import './../../../styles/sizing.scss';
@import './../../../styles/main';

.otp-desktop{
    width:400px; 
    background-color: $cdgc-bg-prime;
     border-radius: 10px;
     margin:44px 0 44px 0;
}

.otp-form{
    width:400px; 
    background-color: $cdgc-bg-prime;
    border-radius: 10px;
    margin:44px 0 44px 0; 
}


.otp-cell{
    // width:100%; 
    width:87%; 
    background-color: $cdgc-bg-prime;
     border-radius: 10px;
     margin:44px 0 44px 0;
}



.img{
    padding:5% 0 2% 5%;
}
.vl{
    border: 1px solid #f2f2f2;
    height: 27px;
    margin: 0 14px 0  !important;
}

.heading {
    font-family: Roboto;
    font-style: normal;
    font-weight: bold !important;
    font-size: $cdgsz-font-size-xl !important; //22px
    line-height: 25px;
    color: #f2f2f2;
    // text-align: center;
    // padding: 34px 0 16px 0;
}

.mat-mdc-raised-button {
    border-radius: 35px !important;
    width: 100% !important;
    margin: 24px 0 24px 0 !important;
    height: 40px !important;
    background-color: $cdgc-hue !important ;
    
}

.mat-icon{
    color: $cdgc-bg-prime !important;
    //padding-top: 5px !important;
}
.icon {
    font-size: 75px !important;
    height: 95px !important;
    width: 80px !important;
    color: #f2f2f2 !important;
}
.register-type {
    font-size: $cdgsz-font-size-prime;
    font-weight: $cdgsz-font-weight-bold;
    color: #f2f2f2;
}
.sub-heading{
    font-size: 14px;
    font-family: $cdgsz-font-family;
    font-weight: $cdgsz-font-weight-bold ;
    line-height:16px;
    font-style: normal;
    color: $cdgc-aux;
}

.content{
    font-size: 12px;
    font-family: $cdgsz-font-family;
    font-weight: $cdgsz-font-weight-normal ;
    line-height:14px;
    font-style: normal;
    color: $cdgc-aux;
}

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0 !important;
}

::ng-deep a {
    color:$cdgc-aux !important;
    font-size: 12px !important;
    font-family: $cdgsz-font-family !important;
    font-weight: $cdgsz-font-weight-normal !important;
    line-height:14px !important;
    font-style: normal !important;
    margin-bottom: 6% !important;
}

.otp-desktop-bg{
    background-color: aliceblue;
    min-height: 100vh !important;
}

// .otp-cell-bg{
//     background-color: $cdgc-bg-prime;
//     min-height: 100vh !important;
// }

.otp-image{
    // width: 224px;
    // height: 146px;
    width: 143px;
    height: 143px;
    background-size: 143px;
    // background-color: seashell;
    background-image: url(/assets/images/Cabcharge_white.png) !important;
}

.error-container {
    margin: 14px 0 14px 0;
}

/* Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
::ng-deep .mat-form-field-appearance-outline .mdc-floating-label {
//   text-align: center !important;
  top: -28px !important;
  
//   display: flex !important;
//   align-items: center !important;
  
  
}
// ::ng-deep .mat-form-field-appearance-outline .mat-form-field-empty .mat-form-field-label{
//     color: white !important;
// }

// ::ng-deep .mat-form-field-should-float{
//     text-align: center !important;

// }

// ::ng-deep .mat-form-field-label{
//     color: white !important;
// }
.res{
    color: $cdgc-font-accent;
    font-size: 14px;
}
.msg{
    font-weight:$cdgsz-font-weight-bold;
}
.bg{
    background-image: url(/assets/images/DTK_3657.jpg) !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    // position: fixed;
    min-width: 100% !important;
    min-height: 100vh !important;
  }
//   @media screen and (max-width: 559px) {
//     .inp-width{
//         width: 250px !important;
//     }
//   }
//   @media screen and (min-width: 500px) and (max-width: 800px) {
//     .inp-width{
//         width: 250px !important;
//     }
//   }
/* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version. */
:host ::ng-deep .mat-mdc-button-toggle.mat-button-toggle-appearance-standard{
    background-color: $cdgc-bg-prime !important;
    border: 0 !important;
}

/* Change_by_navani (mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version. */
:host ::ng-deep .mat-mdc-button-toggle.mat-mdc-button-toggle-checked.mat-button-toggle-appearance-standard{
    background-color: $cdgc-hue !important;
}
:host input[type=number]::-webkit-outer-spin-button{
    opacity: 0;
}
:host input[type=number]::-webkit-inner-spin-button{
    opacity: 0;
}
@media screen and (max-width: 570px){
    .otp-form{
        width:92% !important; 
        background-color: $cdgc-bg-prime;
         border-radius: 10px;
         margin:44px 0 44px 0;
    }
 }

 @media screen and (max-width: 320px){
    .otp-form{
        width:98% !important; 
        background-color: $cdgc-bg-prime;
         border-radius: 10px;
         margin:44px 0 44px 0;
    }
 }