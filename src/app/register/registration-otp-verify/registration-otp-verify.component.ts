import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';

@Component({
  selector: 'app-registration-otp-verify',
  templateUrl: './registration-otp-verify.component.html',
  styleUrls: ['./registration-otp-verify.component.scss']
})
export class RegistrationOtpVerifyComponent implements OnInit {

  verifyOtpForm:FormGroup;
  // otpCorpAdminForm = this.formBuilder.group({
  //   accessId:['',[Validators.required,Validators.email]],
  //   contactPersonId:['',[Validators.required,Validators.pattern(/^-?(0|[1-9]\d*)?$/)]],
  //   otp: ['', [Validators.required]]
  // });
  // otpPersonalForm = this.formBuilder.group({
  //   accessId:['',[Validators.required,Validators.email]],
  //   cardNo:['',[Validators.required,Validators.pattern(/^-?(0|[1-9]\d*)?$/)]],
  //   otp: ['', [Validators.required]]
  // });
  // , Validators.minLength(6), Validators.pattern("^[0-9]*$")
  accessId: any;
  contactPersonId: any;
  accountType: any;
  otpVal: any;
  cardNo: any;
  isShowMsg: boolean = false;
  msgObj = {
    title: "",
    msg: ""
  }
  selectedRole: any = "CORPORATE ADMIN"
  registerParam: string | null;
  registrationOptObj: any;
  id: any;
  corpParam: any;
  personalParam: any;
  data2: any;

  constructor(private formBuilder: FormBuilder, private route: ActivatedRoute, private router: Router, private notifyService: NotificationService, public cdgService: CdgSharedService, private apiService: ApiServiceService, public localStorage: LocalStorageService) { 
    this.verifyOtpForm= this.formBuilder.group({
      otp: ['', [Validators.required]]
    });
  }

  ngOnInit(): void {
   
    this.localStorage.localStorageSet("isLoginSuccessfull",false);
    console.log(this.router.url, this.route.url)
    this.registerParam = this.route.snapshot.paramMap.get('register');
    if (this.registerParam === "register") {
      this.cdgService.isVerifyOtpRoutePath = "register";
    } else {
      this.localStorage.localStorageSet("isTokenApiCalled",false);
      this.cdgService.isVerifyOtpRoutePath = "email";
    }
    console.log("reee", this.cdgService.isVerifyOtpRoutePath)
    console.log(this.registerParam, "registerParam")
    if (this.cdgService.isVerifyOtpRoutePath === 'register') {
      // if (this.cdgService.registrationOptObj) {
      console.log(this.cdgService.isVerifyOtpRoutePath, "routeeee")
      this.cdgService.registrationOptObj = this.localStorage.localStorageGet("registrationOptObj");
      if (this.cdgService.registrationOptObj) {
        this.accountType = this.cdgService.registrationOptObj.accountType;
      }
      if (this.accountType === "CORPORATE ADMIN") {
        this.accessId = this.cdgService.registrationOptObj.accessId;
        this.contactPersonId = this.cdgService.registrationOptObj.contactPersonId;
      }
      else if (this.accountType === "CORPORATE/PERSONAL CARDHOLDER") {
        this.accessId = this.cdgService.registrationOptObj.accessId;
        this.cardNo = this.cdgService.registrationOptObj.cardNumber;
      }
    }
    else {
      let data: any = this.router.url.toString().split('id=')
      let data1: any = data[1]?.split("&")
      // this.accountType=this.selectedRole;
      // console.log('hell0',this.route.snapshot.params.id,this.route.snapshot.params.param1,this.route.snapshot.params.param2)
      //  console.log(this.route.snapshot.paramMap.get('param1'))
      this.id = data1 ? data1[0] : ""
      this.data2 = data1 ? data1[1]?.split("=") : "";

      if (this.data2[0] === 'param1') {
        this.corpParam = this.data2 ? this.data2[1] : "";
      }
      else if (this.data2[0] === 'param2') {
        this.personalParam = this.data2 ? this.data2[1] : "";
      }
    }
    // }
  }
  resendOtp() {
    //   this.accountType = this.selectedRole;
    // }
    console.log(this.accountType)
    this.isShowMsg = false;
    // this.accountType=this.selectedRole;
    // if((this.otpCorpAdminForm.controls.accessId.valid && this.otpCorpAdminForm.controls.contactPersonId.valid) ||
    // (this.otpPersonalForm.controls.accessId.valid && this.otpPersonalForm.controls.cardNo.valid )){
    // this.notifyService.showSuccess("Otp Sent Successfully", 'Success')
    // }else if (this.verifyOtpForm.controls.otp.value === null){
    //    console.log(this.verifyOtpForm,"verify otp form")
    // }else if ((!this.otpCorpAdminForm.controls.accessId.valid && !this.otpCorpAdminForm.controls.contactPersonId.valid) ||
    // (!this.otpPersonalForm.controls.accessId.valid && !this.otpPersonalForm.controls.cardNo.valid )){
    //   this.notifyService.showError("Please fill the required fields",'Error');
    // }
    if (this.cdgService.isVerifyOtpRoutePath !== 'email') {

    if (this.accountType === "CORPORATE ADMIN") {
      let obj
      obj = {
        "accessId": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.id : this.cdgService.registrationOptObj.accessId,
        "contactPersonId": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.corpParam : this.cdgService.registrationOptObj.contactPersonId,
        "accountType": "CORPORATE ADMIN"
      }
    
      // this.msgObj.title="Success";
      // this.msgObj.msg="New otp with email sent successfully";
      // this.isShowMsg=true;
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2Apikey + UrlConstants.RESEND_EMAIL_WITH_OTP_CORP_ADMIN, obj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body
          this.msgObj.title = "Success";
          this.msgObj.msg = opt.statusMessage;
          this.isShowMsg = true;
        }

      },
        e => {
          this.msgObj.title = "Error";
          this.msgObj.msg = e.error.errorMessage;
          this.isShowMsg = true;
        });
    }
    else if (this.accountType === "CORPORATE/PERSONAL CARDHOLDER") {
      let obj
      obj = {
        "accessId": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.id : this.cdgService.registrationOptObj.accessId,
        "cardNumber": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.personalParam : this.cdgService.registrationOptObj.cardNumber,
        "accountType": "CORPORATE/PERSONAL CARDHOLDER"
      }
    
      // this.msgObj.title="Success";
      // this.msgObj.msg="New otp with email sent successfully";
      // this.isShowMsg=true;
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2Apikey + UrlConstants.RESEND_EMAIL_WITH_OTP_PERSONAL_CARDHOLDER, obj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body
          this.msgObj.title = "Success";
          this.msgObj.msg = opt.statusMessage;
          this.isShowMsg = true;
        }

      },
        e => {
          this.msgObj.title = "Error";
          this.msgObj.msg = e.error.errorMessage;
          this.isShowMsg = true;
        });
    }
  }
  else{
    if(this.data2[0] === 'param1'){
      let obj = {
        "id": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.id : this.cdgService.registrationOptObj.accessId,
        "param1": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.corpParam : this.cdgService.registrationOptObj.contactPersonId,
        "accountType": "CORPORATE ADMIN"
      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2Apikey + UrlConstants.RESEND_EMAIL_WITH_OTP_CORP_ADMIN_LINK, obj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body
          this.msgObj.title = "Success";
          this.msgObj.msg = opt.statusMessage;
          this.isShowMsg = true;
        }

      },
        e => {
          this.msgObj.title = "Error";
          this.msgObj.msg = e.error.errorMessage;
          this.isShowMsg = true;
        }); 
    }
    else{
     let obj = {
        "id": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.id : this.cdgService.registrationOptObj.accessId,
        "param2": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.personalParam : this.cdgService.registrationOptObj.cardNumber,
        "accountType": "CORPORATE/PERSONAL CARDHOLDER"
      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2Apikey + UrlConstants.RESEND_EMAIL_WITH_OTP_PERSONAL_CARDHOLDER_LINK, obj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body
          this.msgObj.title = "Success";
          this.msgObj.msg = opt.statusMessage;
          this.isShowMsg = true;
        }

      },
        e => {
          this.msgObj.title = "Error";
          this.msgObj.msg = e.error.errorMessage;
          this.isShowMsg = true;
        });
    }
  }
  }
  submit() {
    this.isShowMsg = false;
    // if (this.cdgService.isVerifyOtpRoutePath === 'email') {
    //   this.accountType = this.selectedRole;
    // }
    this.otpVal = this.verifyOtpForm.controls.otp.value;
    if (this.cdgService.isVerifyOtpRoutePath !== 'email') {

    if (this.accountType === "CORPORATE ADMIN") {
      let corpAdminObj
        corpAdminObj = {
          "loginAccessId": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.id : this.accessId,
          "contactPersonId": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.corpParam : this.contactPersonId,
          "otpVal": this.otpVal
        }
      // this.cdgService.localhostUrl 
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2Apikey + UrlConstants.VALIDATE_CORP_ADMIN_OTP, corpAdminObj).subscribe((response: any) => {
        if (response.status === 200) {
          //console.log(response);
          let opt = response.body;
          if (opt.statusMessage) {
            this.notifyService.showSuccess(opt.statusMessage, 'Success')
            this.cdgService.isRegistered = true;
            this.router.navigateByUrl('/login');
            this.localStorage.localStorageClear();
          }
          // else{
          //   this.notifyService.showWarning(opt.errorMessage,'Warning')
          // }
        }
      },
        e => {
          //this.notifyService.showError(e.error.message,'Error');
          //this.notifyService.showError("Incorrect Otp.",'Error');
          // console.log(e);
          this.notifyService.showError(e.error.errorMessage, 'Error')

        });
    }
    else if (this.accountType === "CORPORATE/PERSONAL CARDHOLDER") {
      let personalCardHolderObj
        personalCardHolderObj = {
          "loginAccessId": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.id : this.accessId,
          "cardNumber": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.personalParam : this.cardNo,
          "otpVal": this.otpVal
        }
      
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2Apikey + UrlConstants.VALIDATE_PERSONAL_CARDHOLDER_OTP, personalCardHolderObj).subscribe((response: any) => {
        if (response.status === 200) {
          //console.log(response);
          let opt = response.body;
          if (opt.statusMessage) {
            this.notifyService.showSuccess(opt.statusMessage, 'Success')
            this.cdgService.isRegistered = true;
            this.router.navigateByUrl('/login');
            this.localStorage.localStorageClear();
          }
          // else{
          //   this.notifyService.showWarning(opt.errorMessage,'Warning')
          // }
        }

      },
        e => {
          //  // this.notifyService.showError("Incorrect Otp.", 'Error');
          //   console.log(e);
          this.notifyService.showError(e.error.errorMessage, 'Error')
        });
    }
  }
  else{
    if(this.data2[0] === 'param1'){
      let corpAdminObj = {
        "id": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.id : this.accessId,
        "param1": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.corpParam : this.contactPersonId,
        "otpVal": this.otpVal
      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2Apikey + UrlConstants.VALIDATE_CORP_ADMIN_OTP, corpAdminObj).subscribe((response: any) => {
        if (response.status === 200) {
          //console.log(response);
          let opt = response.body;
          if (opt.statusMessage) {
            this.notifyService.showSuccess(opt.statusMessage, 'Success')
            this.cdgService.isRegistered = true;
            this.router.navigateByUrl('/login');
            this.localStorage.localStorageClear();
          }
          // else{
          //   this.notifyService.showWarning(opt.errorMessage,'Warning')
          // }
        }
      },
        e => {
          //this.notifyService.showError(e.error.message,'Error');
          //this.notifyService.showError("Incorrect Otp.",'Error');
          // console.log(e);
          this.notifyService.showError(e.error.errorMessage, 'Error')

        });
    }
    else{
      let personalCardHolderObj = {
        "id": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.id : this.accessId,
        "param2": this.cdgService.isVerifyOtpRoutePath === 'email' ? this.personalParam : this.cardNo,
        "otpVal": this.otpVal
      }
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2Apikey + UrlConstants.VALIDATE_PERSONAL_CARDHOLDER_OTP, personalCardHolderObj).subscribe((response: any) => {
        if (response.status === 200) {
          //console.log(response);
          let opt = response.body;
          if (opt.statusMessage) {
            this.notifyService.showSuccess(opt.statusMessage, 'Success')
            this.cdgService.isRegistered = true;
            this.router.navigateByUrl('/login');
            this.localStorage.localStorageClear();
          }
          // else{
          //   this.notifyService.showWarning(opt.errorMessage,'Warning')
          // }
        }

      },
        e => {
          //  // this.notifyService.showError("Incorrect Otp.", 'Error');
          //   console.log(e);
          this.notifyService.showError(e.error.errorMessage, 'Error')
        });
    }
  }


  }
}