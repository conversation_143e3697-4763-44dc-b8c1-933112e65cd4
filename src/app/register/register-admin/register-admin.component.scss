@import '@angular/material/theming';
@import './../../../styles/colors.scss';
@import './../../../styles/sizing.scss';



.tool-bar-cls {
    width: 100%;
    display: flex;
    padding-top: 60px !important;
    padding-bottom: 30px !important;
}

.top-menu-main-div {
    display: flex;
}


/*Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
:host ::ng-deep .mat-mdc-text-field-wrapper {
    padding-bottom: 0 !important;
}

// .mat-icon { // Change_by_navani
//     padding: 0px 16px 0 0;
// }

/*Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .mat-mdc-form-field-text-infix {
    display: flex !important;
    align-items: center !important;
}

.mat-mdc-icon-button{
    height: 24px;
}


.mat-mdc-raised-button {
    border-radius: 35px !important;
    width: 100% !important;
    margin: 24px 0 24px 0 !important;
    height: 40px !important;
    background-color: $cdgc-hue !important ;
    
}

.footer {
    text-align: center;
    font-family: Roboto;
    font-style: normal;
    font-weight: normal;
    font-size: 10px;
    line-height: 12px;
    color:#f2f2f2;
}

.verify {
    text-align: center !important;
    width: 90px;
    height:53.5px;
    font-family: Roboto !important;
    font-style: normal !important;
    font-weight: normal !important;
    font-size: $cdgsz-font-size-prime !important; //16px
    line-height: 19px;
    border-radius: 5px !important;
    cursor: pointer;
    background-color: $cdgc-hue;
    color: #f2f2f2 !important;
    outline: none;
    border:1px solid #f2f2f2 !important;
    max-width: 98% !important;
    
}
::ng-deep .done {
    text-align: center !important;
    width: 45%;
    height:53.5px;
    font-family: Roboto;
    font-style: normal;
    font-weight: normal;
    font-size: $cdgsz-font-size-prime !important; //16px
    line-height: 19px;
    border-radius: 5px !important;
    cursor: pointer;
    background-color: $green;
    color: #f2f2f2;
    outline: none;
    border:1px solid #f2f2f2 !important;
    
}
::ng-deep a {
    color: #f2f2f2 !important;
}
.footer-div {
    padding-bottom: 25px;
}

.error-container {
    margin: 0px !important;
}

/*Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .username.mat-form-field-appearance-outline .mat-mdc-form-field-text-infix{
    padding : 0 !important;
    border-top: 0 !important;
}
/*Change_by_navani (mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .username.mat-form-field-appearance-outline .mat-mdc-form-field-text-infix {
    padding-right: 0px !important;
}

::ng-deep .username.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix {
     padding-left: 0px!important; 
     padding-top: 12px !important;
     padding-bottom: 12px !important;
}

.mat-icon{
    color: $cdgc-bg-prime !important;
}


.username{
    justify-content: space-between !important;
}

.mat-icon-done{
    color: #f2f2f2 !important;
}

// .mat-error{
//     color:#f2f2f2 !important;
// }

.password-icon{
    padding-bottom: 12px !important;
}
 .mat-label-pos{
    padding: 0px 0 30px 38px !important;
   // margin-bottom: 100px !important;
}
.mb16{
    margin-bottom: 16px;
    }
    ::ng-deep .mat-mdc-tooltip{
        white-space: pre-line !important;
    }
    :host input[type=number]::-webkit-outer-spin-button{
        opacity: 0;
    }
    :host input[type=number]::-webkit-inner-spin-button{
        opacity: 0;
    }

    .marginBottom0{
        margin-bottom: 0px !important;
    }
