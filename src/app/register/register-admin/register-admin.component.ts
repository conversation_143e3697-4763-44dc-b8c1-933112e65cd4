import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>, FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';

@Component({
  selector: 'app-register-admin',
  templateUrl: './register-admin.component.html',
  styleUrls: ['./register-admin.component.scss']
})
export class RegisterAdminComponent implements OnInit {

  hide = true;
  hideConfirm = true;
  showMatchError = false;
  submitted = false;

  registrationForm:FormGroup
  enableRegister: boolean = false;
  verifyBtn: boolean = false;
  showError: boolean;

  constructor(private fb: FormBuilder, private router: Router, private apiService: ApiServiceService, private notifyService: NotificationService, private cdgService: CdgSharedService, public localStorage: LocalStorageService) { 
    this.registrationForm = this.fb.group({
      username: ['', [Validators.required, Validators.email]],
      contactPersonID: ['', [Validators.required, Validators.pattern(/^-?(0|[1-9]\d*)?$/)]],
      RCBNumber: [''],
      password: ['', [Validators.required, Validators.minLength(8), Validators.maxLength(20), Validators.pattern("(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[$@$!%*?&#^]).{8,20}") ]],
      confirmPassword: ['', Validators.required]
    },
  
    );
  }

  ngOnInit(): void {
    this.localStorage.localStorageSet("isLoginSuccessfull",false);
  }

  getPasswordInfo() {
    let val = '';
    val = 'Password should contain at least one capital letter and one small letter. \n Password should contain at least one number. \n Password should contain at least one special character like !,@,#,$,%,^,&,* \n Password length should contain a min of 8 and a maximum of 20 characters';
    return val;
  }

  verify(userName: string, contactPersonID: string) {
    this.enableRegister = false;
    this.verifyBtn = false;
    this.showError = false;
    if (userName && contactPersonID) {
      let obj = {
        "accessId": userName,
        "contactPersonId": contactPersonID
      }
      // this.enableRegister = true;
      // this.verifyBtn = true;
      // 9005
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2Apikey + UrlConstants.VERIFY_CORP_ADMIN, obj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body;
          this.enableRegister = true;
          this.verifyBtn = true;
          this.showError = false;
        }
      },
        e => {
          this.notifyService.showError(e.error.errorMessage, 'Error')
          // console.log(e)
        })
    }
    else {
      this.notifyService.showWarning('Email and Contact Person ID is required for Verification', 'Warning')
    }
  }

  resetVerify() {
    this.enableRegister = false;
    this.verifyBtn = false;
  }
  goToVerifyOTP() {
    if (this.registrationForm.invalid) {
      this.showError = true;
      return;
    }
    else {
      let obj = {
        "accountType": "CORPORATE ADMIN",
        "accessId": this.registrationForm.value.username,
        "rcbNo": this.registrationForm.value.RCBNumber,
        "contactPersonId": this.registrationForm.value.contactPersonID,
        "password": this.registrationForm.value.password ? this.registrationForm.value.password.trim() : '',
        "confirmPassword":this.registrationForm.value.confirmPassword ? this.registrationForm.value.confirmPassword.trim() : ''
      }
      // this.cdgService.popupObj.title = "Success";
      // this.cdgService.popupObj.msg = "email sent";
      // this.cdgService.popupObj.component="registerAdmin"
      // this.cdgService.showRegMsgDiv = true;
      // this.cdgService.registrationOptObj = obj;
      this.apiService.post(this.cdgService.localhostUrlNew + UrlConstants.WSO2Apikey + UrlConstants.REGISTER_CORP_ADMIN, obj).subscribe((response: any) => {
        if (response.status === 200) {
          let opt = response.body;
          this.cdgService.popupObj.title = "Success";
          this.cdgService.popupObj.msg = opt.statusMessage;
          this.cdgService.popupObj.component = "registerAdmin"
          this.cdgService.showRegMsgDiv = true;
          this.cdgService.registerUserLogin.accessId = this.registrationForm.value.username;
          this.cdgService.registerUserLogin.password = this.registrationForm.value.password
          this.cdgService.registrationOptObj = obj;
          this.localStorage.localStorageSet("registrationOptObj", this.cdgService.registrationOptObj);
        }
      },
        e => {
          console.log(e)
          this.notifyService.showError(e.error.message, 'Error');
        })
    }
  }
  passwordValidation() {
    if (this.registrationForm.value.password !== "") {
      this.cdgService.popupObj.function = "";
      this.cdgService.showRegMsgDiv = false;
      this.cdgService.passwordValidation = [
        {
          msg: "Password should contain at least one capital letter and one small letter",
          icon: "done"
        },
        {
          msg: "Password should contain at least one number",
          icon: "done"
        },
        {
          msg: "Password should contain at least one special character like !,@,#,$,%,^,&,*",
          icon: "done"
        },
        {
          msg: "Password length should contain a min of 8 and a maximum of 20 characters",
          icon: "done"
        }
      ]
      this.confirmPasswordValidation(); // check confirm password as same.
      const matches = this.registrationForm.value.password.match(/\d+/g);
      const format = /[`!@#$%^&*\\|,.<>\/?]+/
      const capsFormat=/[A-Z]/
      const smallLetterFormat=/[a-z]/
      console.log(capsFormat.test(this.registrationForm.value.password))
      if (!capsFormat.test(this.registrationForm.value.password) || !smallLetterFormat.test(this.registrationForm.value.password)  || matches === null || format.test(this.registrationForm.value.password) === false || this.registrationForm.value.password.length <= 7 || this.registrationForm.value.password.length > 20) {
        if (!capsFormat.test(this.registrationForm.value.password) || !smallLetterFormat.test(this.registrationForm.value.password)) {
          this.cdgService.passwordValidation[0].icon = "close"
        }
        if (matches === null) {
          this.cdgService.passwordValidation[1].icon = "close"
        }
        if (!format.test(this.registrationForm.value.password)) {
          this.cdgService.passwordValidation[2].icon = "close"
        }
        if (this.registrationForm.value.password.length <= 7 || this.registrationForm.value.password.length > 20) {
          this.cdgService.passwordValidation[3].icon = "close"
        }
        this.cdgService.popupObj.function = "passwordValidation";
        this.cdgService.showRegMsgDiv = true;
        console.log(this.cdgService.passwordValidation)
      }

    }
  }
  toLowerCaseConverstion(){
    this.registrationForm.get('username')?.setValue(this.registrationForm.controls.username.value? this.registrationForm.controls.username.value.toLowerCase() : '')
  }

  confirmPasswordValidation(){
    if (this.registrationForm.value.password !== "" && this.registrationForm.value.confirmPassword !== "") {

      if(this.registrationForm.value.password !== this.registrationForm.value.confirmPassword){
        this.showMatchError = true;
      }else{
        this.showMatchError = false;
      }
    }
  }

}
