<form style="width:100%" [formGroup]="registrationForm">
    <div fxFlex fxLayout="column"  fxLayoutAlign="center">
     <div fxFlex fxLayout="row"  fxLayoutGap="8px" fxLayoutAlign="center center" style="margin-bottom: 0px;">
            <mat-form-field class="username" style="width:100%;" fxFlex="100%" appearance="outline">
                <mat-icon matPrefix >account_circle</mat-icon> 
                <input (keyup)="resetVerify()" #username id="username"
                formControlName="username" type="email" matInput placeholder="Email" (input)="toLowerCaseConverstion()" required>
                   
                <mat-error class="error-container" *ngIf="registrationForm.controls.username.hasError('required')">
                    Please Enter Email
                </mat-error>
                <mat-error class="error-container" *ngIf="registrationForm.controls.username.hasError('email')">
                    Please Enter Valid Email Address
                </mat-error>
                <button type="button" mat-button matSuffix *ngIf="!verifyBtn" (click)="verify(username.value,contactPersonID.value)" class="verify"
                 >
                Verify
            </button>
            <button type="button" mat-button  *ngIf="verifyBtn" (click)="verify(username.value,contactPersonID.value)" class="done"
                >
                <mat-icon class="mat-icon-done">done</mat-icon>
            </button>
            </mat-form-field>
            <!-- <button *ngIf="!verifyBtn" (click)="verify(username.value,contactPersonID.value)" class="verify"
                style="width:100%;" fxFlex="30%">
                Verify
            </button>
            <button mat-button *ngIf="verifyBtn" (click)="verify(username.value,contactPersonID.value)" class="verify"
                style="width:100%;" fxFlex="30%">
                <mat-icon>done</mat-icon>
            </button> -->
        </div>

        <mat-form-field style="width:100% " appearance="outline">
            <mat-icon matPrefix class="mat-icon">contacts</mat-icon> <input #contactPersonID type="number" (keyup)="resetVerify()"
                formControlName="contactPersonID" matInput placeholder="Contact Person ID" required>
            <mat-error class="error-container" *ngIf="registrationForm.controls.contactPersonID.hasError('required')">
                Please Enter Contact Person Id
            </mat-error>
            <mat-error class="error-container" *ngIf="registrationForm.controls.contactPersonID.hasError('pattern')">
                Please Enter Valid Integer
            </mat-error>
        </mat-form-field>


        <mat-form-field style="width:100%;" appearance="outline" >
            <mat-icon matPrefix class="mat-icon">work</mat-icon>
            <!-- <mat-label placeholder="abc" class="mat-label" >RCB Number</mat-label> -->
            <input matInput placeholder="RCB Number" formControlName="RCBNumber">
            <!-- <mat-error class="error-container" *ngIf="showError && registrationForm.controls.RCBNumber.hasError('required')">
                Please Enter RCB Number
            </mat-error> -->
        </mat-form-field>


        <mat-form-field style="width:100%;" appearance="outline">
            <button mat-icon-button matPrefix matTooltip="{{getPasswordInfo()}}"><mat-icon  class="mb16 mat-icon">https</mat-icon></button>
            <input #input autocomplete="new-password" [type]="hide ? 'password' : 'text'" formControlName="password"
                type="password" matInput placeholder="Password" (focusout)="passwordValidation()" required>
            <mat-error class="error-container" *ngIf="showError && registrationForm.controls.password.hasError('required')">
                Please Enter Password
            </mat-error>
          
            <mat-error class="error-container" *ngIf="showError && registrationForm.controls.password.hasError('pattern')">
                Password Does Not Meet The Criteria
            </mat-error>
            <button mat-icon-button matSuffix (click)="hide = !hide" [attr.aria-label]="'Hide password'"
                [attr.aria-pressed]="hide">
                <mat-icon class="password-icon mat-icon">{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
            </button>
        </mat-form-field>

        <mat-form-field style="width:100%;" class="marginBottom0" appearance="outline">
            <mat-icon matPrefix class="mat-icon">check_circle</mat-icon> 
            <input [type]="hideConfirm ? 'password' : 'text'" #confirmPassword (input)="confirmPasswordValidation()"
            formControlName="confirmPassword" matInput placeholder="Confirm Password"
             required />
            <button mat-icon-button matSuffix (click)="hideConfirm = !hideConfirm" [attr.aria-label]="'Hide password'"
                [attr.aria-pressed]="hideConfirm">
                <mat-icon  class="password-icon mat-icon">{{hideConfirm ? 'visibility_off' : 'visibility'}}</mat-icon>
            </button>
        </mat-form-field>
        <div>
            <mat-error style="margin-top: 0px; margin-bottom: 0px; font-size: 75%;" class="error-container" *ngIf="showMatchError">
                Password & Confirm Password Does Not Match
            </mat-error> 
        </div>


        <div fxFlex>
            <div fxFlexFill fxLayoutAlign="center center">
                <button type="button" mat-raised-button (click)="goToVerifyOTP()" [disabled]="!enableRegister" >
                    Register  
                </button>
            </div>
        </div>

        <div class="footer-div">
            <p class="footer">By signing up, I agree to Cabcharge’s <a href="">Terms of Use </a> and <a href="">Privacy
                    Policy</a></p>
            <p class="footer">Already have an account? <a href="">Sign In instead</a></p>
        </div>


    </div>
</form>