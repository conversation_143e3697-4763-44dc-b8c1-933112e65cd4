import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { Router } from '@angular/router';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';

@Component({
  selector: 'app-register-confirm',
  templateUrl: './register-confirm.component.html',
  styleUrls: ['./register-confirm.component.scss']
})
export class RegisterConfirmComponent implements OnInit {
  id: any;
  data2: any;
  param: any;
  msgObj = {
    title: "",
    msg: ""
  }
  isShowMsg: boolean = false;
  constructor(private fb: FormBuilder, private router: Router, private apiService: ApiServiceService, private notifyService: NotificationService, public cdgService: CdgSharedService, public localStorage: LocalStorageService) { }

  ngOnInit(): void {
    this.localStorage.localStorageSet("isLoginSuccessfull",false);
    let data: any = this.router.url.toString().split('id=')
    let data1: any = data[1]?.split("&")
    // this.accountType=this.selectedRole;
    // console.log('hell0',this.route.snapshot.params.id,this.route.snapshot.params.param1,this.route.snapshot.params.param2)
    //  console.log(this.route.snapshot.paramMap.get('param1'))
    this.id = data1 ? data1[0] : ""
    this.data2 = data1 ? data1[1]?.split("=") : "";

    if (this.data2[0] === 'param1') {
      this.param = this.data2 ? this.data2[1] : "";
    }
  }
  confirm() {
    console.log('registration confirm button has clicked before the API call hit');
    console.log(this.cdgService.showRegMsgDivConfirm);
    // this.cdgService.localhostUrl
    this.apiService.get(this.cdgService.localhostUrlNew + UrlConstants.WSO2Apikey + UrlConstants.REGISTER_CONFIRM + '?id=' + this.id + '&param1=' + this.param).subscribe((response: any) => {
      if (response.status === 200) {
        let opt = response.body
        // this.msgObj.title = "Success";
        // this.msgObj.msg = opt.statusMessage;
        // this.isShowMsg = true;        
        this.cdgService.popupObjCon.title = "Success";
        this.cdgService.popupObjCon.msg = opt.statusMessage;
        this.cdgService.popupObjCon.component = "registerConfirm"
        this.cdgService.popupObjCon.function="errorValidation"
        this.cdgService.showRegMsgDivConfirm = true;
        console.log('registration confirm button api call got a response successfully');
        console.log(this.cdgService.showRegMsgDivConfirm);
      }

    },
      e => {
        // this.msgObj.title = "Error";
        // this.msgObj.msg = e.error.errorMessage;
        // this.isShowMsg = true;        
        this.cdgService.popupObjCon.title = "Error";
        this.cdgService.popupObjCon.msg = e.error.message;
        this.cdgService.popupObjCon.component = "registerConfirm"
        this.cdgService.popupObjCon.function="errorValidation"
        this.cdgService.showRegMsgDivConfirm = true;
        console.log('registration confirm button api call received a error');
        console.log(this.cdgService.showRegMsgDivConfirm);
      });
  }
  success(){
    this.router.navigateByUrl('/login');
  }
  goToLogin() {
    this.router.navigate(['/login'])
  }
}
