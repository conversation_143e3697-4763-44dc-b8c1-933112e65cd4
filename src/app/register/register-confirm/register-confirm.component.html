<div fxLayout=column  fxLayoutAlign="center center" style="min-height: 100vh;" class="reg-container bg">
    <div *ngIf="cdgService.showRegMsgDivConfirm" class="box" [ngStyle]="{'background-color': cdgService.popupObjCon.title == 'Success' ? ' #28a745' :  '#ff0000'}">
		<div  fxLayout="column" class="p5">
			<p class="head">{{cdgService.popupObjCon.title}}</p>
			<p class="msg">{{cdgService.popupObjCon.msg}}</p>
			<div fxFlex fxLayoutAlign="end center" >
				<button mat-raised-button class="ok-btn" [ngStyle]="{'background-color': cdgService.popupObjCon.title == 'Success' ? '#DA6C2A' :  ' #808080'}" (click)="success()">Ok</button>
			</div>
		</div>
    </div>
    <div fxLayout=column fxLayoutAlign="center center">
        <div fxLayout="column"  class = "otp-form" fxLayoutAlign="start center" style="padding: 20px;">
            <!-- [ngClass.gt-md]="'otp-desktop'" [ngClass.md]="'otp-desktop'" [ngClass.sm]="'otp-cell'" -->
            <!-- [ngClass.lt-sm]="'otp-cell'" -->
            <div style="width:100%" fxLayout="row" fxLayoutGap="1px" fxLayoutAlign="start center">
                <img class="img" src="/assets/images/Cabcharge_white.png" width="84px" alt="">
                <hr class="vl">
                <p class="heading">Registration Confirmation</p>
            </div>
            <!-- <div class="otp-image" src="/assets/images/cab-charge-otp-logo.png" width="20px" height="50px"></div> -->
            <!-- <div>

            </div> -->
            <div style="width:90%" fxLayout="column" fxLayoutAlign="center start">
                <p class="sub-heading">Are you sure you want to Confirm ?</p>
                <!-- <P class="content">An OTP is sent to your registered email address. Please enter the OTP below. Valid
                    for 10 minutes.
                </P> -->
            </div>
            <!-- <div *ngIf="isShowMsg" class="msg" fxLayoutAlign="center center"
                [ngStyle]="{'color': msgObj.title == 'Success' ? ' #ffffff' :  '#ff0000'}">
                {{this.msgObj.msg}}
            </div> -->
            <!-- <form [formGroup]="verifyOtpForm" style="width:90%">
                <div fxLayout="column" fxLayoutAlign="center center ">
                    <mat-form-field style="width:100% " appearance="outline">
                        <mat-icon matPrefix class="mat-icon">fiber_pin</mat-icon> <input matInput formControlName="otp"
                            placeholder="OTP" required>
                        <mat-error class="error-container" *ngIf="verifyOtpForm.controls.otp.hasError('required')">
                            Please Enter OTP
                        </mat-error>
                        <mat-error class="error-container" *ngIf="verifyOtpForm.controls.otp.hasError('pattern')">
                            Please Enter Number
                        </mat-error>

                    </mat-form-field>

                    <div style="width: 100%;">
                        <button mat-raised-button (click)="submit()">
                            Submit
                        </button>
                    </div>
                </div>
                <div fxLayout="column" fxLayoutAlign="center center" (click)="resendOtp()" class="pb10"><u
                        class="cursor-pointer res">Resend OTP</u></div>

            </form> -->
            <div style="width: 80%;" fxLayoutAlign="start center">
                <button mat-raised-button [disabled]="this.cdgService.showRegMsgDivConfirm" (click)="confirm()">
                    Confirm
                </button>
            </div>
            <!-- (click)="confirm()" -->
            <div fxLayout="column" fxLayoutAlign="center center" (click)="goToLogin()" class="pb10"><u
                class="cursor-pointer res">Go Back To Login?</u></div>
        </div>

    </div>
</div>