import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CdgSharedService } from '../shared/services/cdg-shared.service';


@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss']
})
export class RegisterComponent implements OnInit {
  public selectedVal: string = 'corporateAdmin';


  constructor(public cdgService: CdgSharedService, private router: Router, private route: ActivatedRoute) { }

  ngOnInit(): void {
  }
  getVal(){
    this.cdgService.popupObj={
      title: "",
      msg: "",
      component: "",
      function:""
    }
    this.cdgService.showRegMsgDiv = false;
  }
  success() {
    if (this.cdgService.popupObj.component === 'registerAdmin' || this.cdgService.popupObj.component === 'registerPersonal') {
      this.cdgService.isVerifyOtpRoutePath = "register"
      this.router.navigateByUrl('register/registerVerifyOTP/register');
    }
    this.cdgService.showRegMsgDiv = false;
  }
  passValidation() {
    this.cdgService.showRegMsgDiv = false;
    this.cdgService.passwordValidation = [
      {
        msg: "Password should contain at least one capital letter",
        icon: "done"
      },
      {
        msg: "Password should contain at least one number",
        icon: "done"
      },
      {
        msg: "Password should contain at least one special character like @,!,%,&,*",
        icon: "done"
      },
      {
        msg: "Password length should contain a min of 8 and a maximum of 20 characters",
        icon: "done"
      }
    ]
  }




}
