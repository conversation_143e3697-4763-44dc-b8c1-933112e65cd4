import { NgModule, APP_INITIALIZER } from '@angular/core';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { LandingComponent } from './landing/landing.component';
import { LoginComponent } from './login/login.component';
import { LoginModule } from './login/login.module';
import { RegisterComponent } from './register/register.component';

import { LayoutModule } from '@angular/cdk/layout';
import { OverlayModule } from '@angular/cdk/overlay';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { environment } from '../environments/environment';
import { AsyncPipe, DecimalPipe } from '@angular/common';
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { FlexLayoutModule } from '@angular/flex-layout';
import {LayoutComponent} from './layout/layout.component';
import { SharedModulesModule } from './shared/shared-modules/shared-modules.module';
import { ModulesModule } from '././modules/modules.module';
import { MatMenuModule} from '@angular/material/menu';
import {MatFormFieldModule} from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {MatInputModule} from '@angular/material/input';
import {MatCardModule} from '@angular/material/card';
import {MatSelectModule} from '@angular/material/select';
import {MatButtonToggleModule} from '@angular/material/button-toggle';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MatCarouselModule } from '@magloft/material-carousel'
import {MatTableModule} from '@angular/material/table';
import {MatCheckboxModule} from '@angular/material/checkbox';
import { RegisterModule } from './register/register.module';
// import { MatCarouselModule} from  '@ngbmodule/material-carousel'
// import { SlickCarouselModule } from 'ngx-slick-carousel';
import { MatAutocompleteModule} from '@angular/material/autocomplete'
import { MatBadgeModule} from '@angular/material/badge'
import { MatTooltipModule} from '@angular/material/tooltip'
import { MatSortModule } from '@angular/material/sort';
// import {MomentDateAdapter} from '@angular/material-moment-adapter';
import {MatDatepickerModule} from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MAT_DATE_LOCALE} from '@angular/material/core'
import {MatDividerModule} from '@angular/material/divider';
import { MatRadioModule } from '@angular/material/radio';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatPaginatorModule } from '@angular/material/paginator';
import {MatTabsModule} from '@angular/material/tabs';
import { ServiceWorkerModule } from '@angular/service-worker';
import { PromptComponent } from './shared/shared-modules/prompt/prompt.component';
import { PwaService } from './shared/services/pwa.service';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import { AuthGuard } from './shared/guard';
import { MatDialogModule } from '@angular/material/dialog';
import { SessionTimeoutService } from './shared/services/session-timeout.service'
import { NgxMatDatetimePickerModule, NgxMatNativeDateModule, NgxMatTimepickerModule } from '@angular-material-components/datetime-picker';
import { MtxDatetimepickerModule } from '@ng-matero/extensions/datetimepicker';
import { MatChipsModule } from '@angular/material/chips';
import { ToastrModule } from 'ngx-toastr';
const initializer = (pwaService: PwaService) => () => pwaService.initPwaPrompt();

@NgModule({ declarations: [
        AppComponent,
        LandingComponent,
        LoginComponent,
        LayoutComponent,
        RegisterComponent,
        PromptComponent
    ],
    // {provide:MAT_DATE_LOCALE,useValue:'en-GB'}
    bootstrap: [AppComponent], schemas: [
        CUSTOM_ELEMENTS_SCHEMA
    ], imports: [BrowserModule,
        AppRoutingModule,
        LoginModule,
        BrowserAnimationsModule,
        FlexLayoutModule,
        LayoutModule,
        OverlayModule,
        MatNativeDateModule,
        MatDatepickerModule,
        // HttpClient,
        MatButtonModule,
        MatIconModule,
        MatListModule,
        MatSidenavModule,
        MatToolbarModule,
        MatBottomSheetModule,
        SharedModulesModule,
        ModulesModule,
        MatMenuModule,
        MatFormFieldModule,
        FormsModule,
        ReactiveFormsModule,
        MatInputModule,
        MatCardModule,
        MatSelectModule,
        MatButtonToggleModule,
        MatTableModule,
        MatCheckboxModule,
        RegisterModule,
        MatAutocompleteModule,
        MatBadgeModule,
        MatTooltipModule,
        MatSortModule,
        MatDividerModule,
        MatRadioModule,
        MatSlideToggleModule,
        MatPaginatorModule,
        MatTabsModule,
        MatProgressSpinnerModule,
        MatDialogModule,
        NgxMatDatetimePickerModule,
        NgxMatTimepickerModule,
        NgxMatNativeDateModule,
        MatChipsModule,
        MtxDatetimepickerModule,
        MatCarouselModule.forRoot(),
        // MomentDateAdapter,
        ToastrModule.forRoot({ preventDuplicates: true }),
        // MatCarouselModule.forRoot(),
        ServiceWorkerModule.register('ngsw-worker.js', {
            enabled: environment.production,
            // Register the ServiceWorker as soon as the app is stable
            // or after 30 seconds (whichever comes first).
            registrationStrategy: 'registerWhenStable:30000'
        })], providers: [AsyncPipe, { provide: AuthGuard, useClass: AuthGuard }, [DecimalPipe],
        { provide: APP_INITIALIZER, useFactory: initializer, deps: [PwaService], multi: true }, { provide: MAT_DATE_LOCALE, useValue: 'en-GB' }, provideHttpClient(withInterceptorsFromDi())] })
export class AppModule { }































