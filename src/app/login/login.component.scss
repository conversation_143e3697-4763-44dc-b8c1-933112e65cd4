@import '@angular/material/theming';
@import './../../styles/colors.scss';
@import './../../styles/sizing.scss';
@import './../../styles/main.scss';


.tool-bar-cls{
    width: 100%;
    display: flex;
    padding-top:60px !important;
    padding-bottom: 30px !important;
    
}
.top-menu-main-div{
    display: flex;
}
.login-title{
    font-size: $cdgsz-font-size-xl;
    font-weight: $cdgsz-font-weight-bold;
}
.top-menu{
    font-family: Roboto;
    font-style: $cdgsz-font-weight-normal;
    font-weight: $cdgsz-font-weight-normal;
    font-size: $cdgsz-font-size-lg;
    line-height: 23px;
    text-align: center;
}
.full-height {
  height: calc(100vh - 90px) !important;
//   background: $cdgc-bg-prime;
  flex: 1 1 auto !important;
}
.enquiry{
  color: $cdgc-font-accent;
  color: $cdgc-font-accent;
  font-size: 16px;
  font-weight: 400;
}
.form-div{
  
    width: 375px !important;
    
}
@media screen and (max-width: 960px) {
    // 1919
.full-height {
  height: auto !important;
  padding-top: 20px !important;
//   background: $cdgc-bg-prime;
  flex: 1 1 auto !important;
}
}
.login-form-input-field{
  width:210px;
}
.icon{
  font-size: 75px !important;
  height: 95px !important;
  width:80px !important;
}
.login-type{
    font-size: $cdgsz-font-size-prime;
    font-weight: $cdgsz-font-weight-bold;
}
.btn{
    width: 375px !important;
}
.register-btn{
        border:1px solid $cdgc-accent !important;
        margin-bottom: 10px !important;
        margin-top: 10px !important;
        background-color:$cdgc-prime !important;
        color: $cdgc-accent;
}
.signin-btn{
  // border:1px solid $cdgc-accent !important;
  margin-bottom: 10px !important;
  margin-top: 10px !important;
  background-color:$cdgc-hue !important;
  color: $cdgc-accent;
}
@media screen and (max-width: 650px) {
    .form {
    padding-left: 20px !important;
    padding-right: 20px !important;
}
.form-div{
  // border-radius: 15px;
  // margin-left: 115px;
  // background: #ccebff;
  // margin-bottom:10%;
  // width:300px;
  // padding:20px;
  // background-color: $cdgc-bg-prime;
  width: 275px !important;
  
}
}
.login-div{
  border-radius: 15px;
  // margin-left: 115px;
  // background: #ccebff;
  // margin-bottom:10%;
  // width:300px;
  padding:20px;
  background-color: $cdgc-bg-prime;
}
// :host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-prefix, .mat-form-field-appearance-outline .mat-form-field-suffix {
//   top: unset !important;
//   bottom: 63px !important;
//   margin-right: 10px;
// }


.dividerd{
  border: 1px solid #f2f2f2;
  height: 27px;
  margin: 0 14px 0 0 !important;
}
.heading {
  font-family: Roboto;
  font-style: normal;
  font-weight: bold !important;
  font-size: $cdgsz-font-size-xl !important; //22px
  line-height: 25px;
  color: #f2f2f2;
}
.display-height{
  min-height: 100vh;
}
.bg{
  background-image: url(/assets/images/DTK_3657.jpg) !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  background-size: cover !important;
  // position: fixed;
  min-width: 100% !important;
}
:host .mat-icon{
  color: $cdgc-bg-prime !important;
}

//v15_updated
//text_input 
::ng-deep .mat-mdc-form-field {
  border: transparent !important;
  border-radius: 10px !important;
  top: 0px !important;
}

::ng-deep .mdc-notched-outline__leading, ::ng-deep .mdc-notched-outline__notch, ::ng-deep .mdc-notched-outline__trailing {
  border-radius: 0px !important;
  border-color: $cdgc-border-aux !important;
  border-style: solid;
}

::ng-deep mdc-button__label{
  color: white;
  letter-spacing: normal;
}

::ng-deep .inp.mat-form-field-appearance-outline .mat-mdc-form-field-text-infix {
  padding: 0 0 1em!important;
}

:host ::ng-deep .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix{
  padding-left: 0px !important;
  padding-top: 12px !important; //text-box padding
  padding-bottom: 12px !important;
}






