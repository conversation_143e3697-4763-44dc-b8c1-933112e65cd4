<div class="display-height pt5 pb5 bg"
	(window:resize)="onResize($event)">
	<div fxLayout="column" fxLayoutAlign="center center" class="display-height" *ngIf="isShowRoleDiv">
	<div class="login-div" fxLayout="column" fxLayoutGap="20px">
		<div fxLayout="row" fxLayoutGap="14px" fxLayoutAlign="center center">
			<img src="/assets/images/Cabcharge_white.png" width="84px" alt="">
		</div>
		<div class="form-div">
			<form [formGroup]="roleForm">
				<div fxFlex>
					<p class="text-cls">Select the role with which you want to proceed</p>
				</div>
				<div fxFlex>
					<!-- <mat-label fxFlex class="text-cls font-16"> Roles </mat-label> -->
					<mat-form-field appearance="outline" class="inp">
						<mat-select formControlName="roleName" name="roleName" placeholder="Please Select Role"
							 (selectionChange)="getSelectedRole()" required>
							<mat-option *ngFor="let acl of roleList" [value]="acl.roleName">
								{{acl.roleDesc}}
							</mat-option>
						</mat-select>
						<mat-error *ngIf="roleForm.controls.roleName.hasError('required')">
							Please Enter Role
						</mat-error>
					</mat-form-field>
				</div>
				<!-- <div fxFlex *ngIf="isShowAccountList"> -->
					<!-- <mat-label fxFlex class="text-cls font-16"> Roles </mat-label> -->
					<!-- <mat-form-field appearance="outline" class="inp">
						<mat-select formControlName="accNo" name="accNo" placeholder="Please Select Customer No">
							<mat-option *ngFor="let acl of accList" [value]="acl">
								{{acl}}
							</mat-option>
						</mat-select>
					</mat-form-field>
				</div>  -->
				<div fxFlex>
					<button mat-raised-button class="inp register-btn" (click)="onSubmit()">Submit</button>
				</div>
			</form>
		</div>
	</div>
</div>

<!-- Commented the intermediate screen part for master role (single login) -->
<!-- <div fxLayout="column" fxLayoutAlign="center center" class="display-height" *ngIf="!isShowRoleDiv">
	<div class="login-div" fxLayout="column" fxLayoutGap="20px">
		<div fxLayout="row" fxLayoutGap="14px" fxLayoutAlign="center center">
			<img src="/assets/images/Cabcharge_logo_new.png" width="150px" alt="">
		</div>
		<div class="form-div">
			<form [formGroup]="accountNoForm">
				<div fxFlex>
					<p class="text-cls">Select the Customer No with which you want to proceed</p>
				</div>
				<div fxFlex *ngIf="isShowAccountList">
					<mat-form-field appearance="outline" class="inp">
						<mat-select formControlName="accNo" name="accNo" placeholder="Please Select Account No" required>
							<mat-option *ngFor="let acl of accList" [value]="acl">
								{{acl}}
							</mat-option>
						</mat-select>
						<mat-error *ngIf="accountNoForm.controls.accNo.hasError('required')">
							Please Enter Customer No
						</mat-error>
					</mat-form-field>
				</div>
				<div fxFlex>
					<button mat-raised-button class="inp register-btn" (click)="onSubmitAccountNo()">Submit</button>
				</div>
			</form>
		</div>
	</div>
</div> -->
</div>