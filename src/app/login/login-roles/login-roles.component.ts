import { Component, OnInit, HostListener } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { throwError } from 'rxjs';
import { ApiServiceService } from 'src/app/shared/services/api-service.service';
import { CdgSharedService } from 'src/app/shared/services/cdg-shared.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';

@Component({
  selector: 'app-login-roles',
  templateUrl: './login-roles.component.html',
  styleUrls: ['./login-roles.component.scss']
})
export class LoginRolesComponent implements OnInit {
  public isWebLayout = false;
  public isTabletLayout = false;
  public innerWidth: any;
  // @HostListener('window:beforeunload', ['$event'])
  // unloadNotification($event: any) {
  //   sessionStorage.setItem("roles", JSON.stringify(this.cdgService.userRoles))
  // }
  submitted = false;

  roleForm:FormGroup
  accountNoForm: FormGroup
  roleList: any[] = [];
  accNoList :any;
  isShowAccountList: boolean = false;
  accList: any;
  isShowRoleDiv:boolean=false;
  username:any;
  constructor(public localStorage: LocalStorageService, private formBuilder: FormBuilder, private apiService: ApiServiceService, private http: HttpClient, public cdgService: CdgSharedService, private router: Router, private notifyService: NotificationService) { 
    this.accountNoForm = this.formBuilder.group({
      accNo: ['',[Validators.required]]
    });

    this.roleForm= this.formBuilder.group({
      roleName: ['', [Validators.required]],
      accNo: ['']
    });

  }

  ngOnInit(): void {
    let data = this.localStorage.localStorageGet("roles")
    this.username = this.localStorage.localStorageGet("accessId");
    this.roleList = data;
    if(this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_MASTERUSER){
      this.isShowRoleDiv=false;
      let accList=this.cdgService.customernumber;
      this.localStorage.localStorageSet("accList",accList);
      this.accList=this.localStorage.localStorageGet("accList")
      this.isShowAccountList = true;
    }
    else{
      this.isShowRoleDiv=true
      this.isShowAccountList = false;
    }
    this.innerWidth = window.innerWidth;
    if (this.innerWidth < 1280) { // 768px portrait
      this.isWebLayout = false;
      this.isTabletLayout = true;
    }
    else {
      this.isWebLayout = true;
      this.isTabletLayout = false;

    }
  }
  // get f() {
  //   return this.roleForm.controls;
  // }
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth < 1280) { // 768px portrait
      this.isWebLayout = false;
      this.isTabletLayout = true;
    }
    else {
      this.isWebLayout = true;
      this.isTabletLayout = false;
    }

  }
  getSelectedRole() {
    // if (this.roleForm.value.roleName === RoleConstants.ROLE_MASTERUSER) {
    //   let accList=this.cdgService.customernumber;
    //   this.localStorage.localStorageSet("accList",accList);
    //   this.accList=this.localStorage.localStorageGet("accList")
    //   this.isShowAccountList = true;
    // }
    // else {
    //   this.isShowAccountList = false;
    // }
  }
  onSubmit() {
    this.submitted = true;
    if (this.roleForm.invalid) {
      return;
    }
    else {
     
      this.apiService.get(this.cdgService.localhostUrlNew  + UrlConstants.WSO2 + UrlConstants.USER_ROLE + this.roleForm.value.roleName + '&accessid='+ this.username).subscribe((response: any) => {
        if (response.status === 200) {
          if(response.body.cardValidationErrorMessage !== null){
            let value = response.body.cardValidationErrorMessage;
            this.notifyService.showError(value,'Login');
            this.router.navigateByUrl('/login');
          }
          else{
            let opt = response.body;
            this.cdgService.loginUserDetails.accessId = opt.accessId;
            this.cdgService.loginUserDetails.roles = opt.roles[0];
            this.cdgService.loggedInRole = opt.roles[0].roleName;
            this.cdgService.routingMenuList = opt.menus;
            let role = this.cdgService.loggedInRole;
            if(role==="ROLE_CORPADMIN" || role==="ROLE_CORPCARDHOLDER"){
              if(opt.landingScreenNamesDto.accountDetails){
                opt.landingScreenNamesDto.accountDetails.forEach((element:any )=> {
                  this.cdgService.customernumber =element.customer_number;
                });
                }
             
            }
            this.cdgService.landingScreenNamesDto = opt.landingScreenNamesDto;
            this.localStorage.localStorageSet("loginUserDetails", this.cdgService.loginUserDetails)
            this.localStorage.localStorageSet("loggedInRole", opt.roles[0].roleName)
            this.localStorage.localStorageSet("routingMenuList", opt.menus)
            this.localStorage.localStorageSet("landingScreenNamesDto", opt.landingScreenNamesDto)
            this.cdgService.getLandingPage()
          }
          
          // this.router.navigateByUrl('/layout/dashboard');
        }
      },
        e => {
          console.log(e)
        })
    }

  }
  onSubmitAccountNo(){
    if(this.accountNoForm.invalid){
      return;
    }
    else{
    this.cdgService.getLandingPage();
    }
  }
}
