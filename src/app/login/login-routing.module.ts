import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoginComponent } from './login.component';
import {LayoutComponent} from './../layout/layout.component';

const routes: Routes = [                        
        {
            path:'',
            component: LoginComponent  // <-- Child component 
        }
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LoginRoutingModule { }
