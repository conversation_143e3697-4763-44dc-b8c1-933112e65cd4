import { Component, OnInit, HostListener, ViewEncapsulation, ViewChild } from '@angular/core';
import { FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms';
import { NotificationService } from './../shared/services/notification.service'
import { CdgSharedService } from './../shared/services/cdg-shared.service'
import { ActivatedRoute, Router } from '@angular/router';
import { ApiServiceService } from '../shared/services/api-service.service';
import { throwError } from 'rxjs';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';
import { HttpClient, HttpResponse, HttpHeaders } from '@angular/common/http';
import { LocalStorageService } from '../shared/services/local-storage.service';
import { SessionTimeoutService } from '../shared/services/session-timeout.service';
import * as CryptoJS from 'crypto-js'
import { CookieService } from 'ngx-cookie-service';


@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  public isWebLayout = false;
  public isTabletLayout = false;
  public innerWidth: any;
  public isRegisterNowButtonClicked: boolean = false;
  public selectedVal: string = 'corporateAdmin';
  public hide = true;
  public count: number = 0;
  public userlogin: any = {
    "accessid": "",
    "password": "",
    "userName": "",
    "cardNo": null,
    "company": "",
    "accountNo": null,
    "telephone": null,
    "faxNo": null,
    "officeNo": null,
    "mobNo": null,
    "email": "",
    "alterNname": "",
    "alterTelephone": "",
    "alterFaxNo": "",
    "alterMobNo": "",
    "alteremail": "",
    "blockNo": "",
    "unitNo": "",
    "streetName": "",
    "buildingName": "",
    "area": "",
    "country": "",
    "city": "",
    "state": "",
    "postalCode": ""
  }
  public isShowErrorMsg: boolean = false;
  public isShowLockMsg: boolean = false;
  public arr = [1, 3, 'Apple', 'Orange', 'Banana', true, false];
  // public password:any;
  // loginform: FormGroup
  corporateAdminForm: FormGroup;
  corporatePersonalForm: FormGroup;
  loggingError: any;
  registerParam: any;
  refreshTokenTimeout: any;
  pswrd: any;
  pwds: any;
  constructor(public sessionService: SessionTimeoutService, public localStorage: LocalStorageService, private route: ActivatedRoute, private fb: FormBuilder, private http: HttpClient, private notifyService: NotificationService, private cdgService: CdgSharedService, private router: Router, private apiService: ApiServiceService, private cookieService: CookieService) {
    this.corporateAdminForm = this.fb.group({
      username: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(8), Validators.pattern("^(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$")]]
    });
    this.corporatePersonalForm = this.fb.group({
      username: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]],
    });
  }

  ngOnInit() {
    // this.cdgService.isTokenApiCalled=false;
    // 'eyJ4NXQiOiJOVGRtWmpNNFpEazNOalkwWXpjNU1tWm1PRGd3TVRFM01XWXdOREU1TVdSbFpEZzROemM0WkE9PSIsImtpZCI6ImdhdGV3YXlfY2VydGlmaWNhdGVfYWxpYXMiLCJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.s8J4Wd4o1FvTYVXVMCB5MepKTakKVHopfJrZ3rZ6BxPkZILQrOtEnhNZ1Lsz7wifqZNtVTGNGzJB0FBT9tTLyxMzZdQ8QDr-E5ybXIveEnmwYPdPdDL-h4aCM79Qt82Gy2-xOXahrBL0Hfe1qcSZLWF1X2Onpp2MCGBZ8xWa7Jke5e6Vic_uc6QbYgZp7Wg20iKsVt0sfV6__-1lct27sYydP0ZY3Wnj5jamSuuPVvG6Q1tAkTaGf_UrL7ambMi7-zxYfc1CCr8ieo_z1QO0jQZkctgFd7r2bQEZ2tfHkClb4GI8ypYt7rTUR1TK9gwPXB2VcXMidzyIbFuDkHwa3Q=='
    this.localStorage.localStorageSet("isTokenApiCalled", false);
    this.localStorage.localStorageSet("apikey",
      //new_apiKey
      ''
    );
    this.localStorage.localStorageSet("isLoginSuccessfull", false)
    if (this.cdgService.isRegistered) {
      this.corporateAdminForm.get('username')?.setValue(this.cdgService.registerUserLogin.accessId)
      this.corporateAdminForm.get('password')?.setValue(this.cdgService.registerUserLogin.password)
      this.corporatePersonalForm.get('username')?.setValue(this.cdgService.registerUserLogin.accessId)
      this.corporatePersonalForm.get('password')?.setValue(this.cdgService.registerUserLogin.password)
    }

    this.innerWidth = window.innerWidth;
    if (this.innerWidth < 1280) { // 768px portrait
      this.isWebLayout = false;
      this.isTabletLayout = true;
    }
    else {
      this.isWebLayout = true;
      this.isTabletLayout = false;

    } 
    
    this.localStorage.localStorageSet("loginUserDetails", null);
    this.localStorage.localStorageSet("loggedInRole", null);
    this.localStorage.localStorageSet("routingMenuList", null);
    this.localStorage.localStorageSet("landingScreenNamesDto", null); 

  }
  @HostListener('window:resize', ['$event'])
  @HostListener('paste', ['$event']) blockPaste(e: KeyboardEvent) {
    e.preventDefault();
  }
  @HostListener('copy', ['$event']) blockCopy(e: KeyboardEvent) {
    e.preventDefault();
  }
  @HostListener('cut', ['$event']) blockCut(e: KeyboardEvent) {
    e.preventDefault();
  }
  onResize(event: any) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth < 1280) { // 768px portrait
      this.isWebLayout = false;
      this.isTabletLayout = true;
    }
    else {
      this.isWebLayout = true;
      this.isTabletLayout = false;
    }

  }
  toggle() {
    this.isShowLockMsg = false;
    this.isShowErrorMsg = false;
  }
  onSubmit() {
    this.hide = true;
    this.loggingError = "";
    this.isShowLockMsg = false;
    
    this.localStorage.localStorageSet('isTokenApiCalled', true);
    console.log(this.localStorage.localStorageGet('isTokenApiCalled'))

    this.cdgService.isLoginSuccessfull = false;
    if (this.corporatePersonalForm.valid) {
      this.isShowLockMsg = false
 
      let accessId = this.corporatePersonalForm.value.username;
      let pwd = this.corporatePersonalForm.value.password.trim();
      
      this.pwds = encodeURIComponent(pwd);
      // this.localStorage.localStorageSet('strngAccId', strngAccId);
      const encryptData = CryptoJS.AES.encrypt(JSON.stringify(this.pwds),"cabCharge_8221").toString();
      this.cookieService.set('encodedPswd',encryptData);
      // this.localStorage.localStorageSet('encodedPswd', this.pwds);
      // this.localStorage.localStorageSet("authorizationToken",'Basic'+" "+'a0U0Nl9ubFFsTlZyUmhDaXNmU19RUUZxZGYwYTp1WUhhcGR6eFhieFRta2x5ZEdkWlp5SDNFbG9h')
      console.log(this.localStorage.localStorageGet("authorizationToken"))
      console.log(this.localStorage.localStorageGet('isTokenApiCalled'));
      this.localStorage.localStorageSet('isTokenApiCalled', true);

      const body = {
        grant_type: "password",
        username: accessId,
        password: this.pwds
      }
       
      
      this.apiService.post(this.cdgService.webServerUrl + UrlConstants.GET_AUTH, body)
        .subscribe((response: any) => {
          console.log(response)
          if (response) {
            this.startRefreshTokenTimer();
          }
          if (response.status === 200) {
            let opt = response.body;
            
            
            this.localStorage.localStorageSet("token", opt.token_type + " " + opt.access_token);
            this.localStorage.localStorageSet("auth", opt.token_type + " " + opt.access_token);
            this.localStorage.localStorageSet("expiresIn", opt.expires_in * 1000);

            // Initializes a seession expirty timer based on the duration stored in local storage
            this.sessionService.startTimer(this.localStorage.localStorageGet("expiresIn"));
            this.login();
            
            this.cdgService.isLoginSuccessfull = true;
            this.localStorage.localStorageSet("accessId", accessId);
            this.localStorage.localStorageSet("isLoginSuccessful", this.cdgService.isLoginSuccessfull);
          }
        },
        e => {
          console.log(e)
          this.localStorage.localStorageSet('isTokenApiCalled', false);
          //this.login();
          if(e.error && e.error.error_description){
            this.loggingError = e.error.error_description;
          }else if (e.error && e.error.message){
            this.loggingError = e.error.message;
          }else{
            this.loggingError = "Credentials are incorrect";
          }
          
          this.isShowLockMsg = true
        })
    }
  }

  startRefreshTokenTimer() {
    const expires = new Date(2 * 1000);
    const timeout = expires.getTime() - Date.now() - (60 * 1000);
    this.refreshTokenTimeout = setTimeout(() => this.refreshToken(), timeout)
  }

  refreshToken() {
    this.startRefreshTokenTimer();
  }

login() {
    let obj = {
      "accessid": this.corporatePersonalForm.value.username,
      "password": this.corporatePersonalForm.value.password.trim()
    }
 
    
    // const options = {
    //   headers: new HttpHeaders
    //     (
    //       {
    //         'Content-Type': 'application/json',
    //         'apikey': this.localStorage.localStorageGet("apikey")
    //       })
    // }
    
    //  options

    this.cdgService.isLoginSuccessfull = false;
    // this.cdgService.isTokenApiCalled=false;
    this.localStorage.localStorageSet('isTokenApiCalled',false);
    // + UrlConstants.WSO2Apikey
    this.apiService.post(this.cdgService.localhostUrlNew  + UrlConstants.WSO2Apikey + UrlConstants.LOGIN, obj).subscribe((response: any) => {
      // console.log(response.body);
      console.log(response);
      if (response.status === 200) {
      // let opt = response.body;
      let opt = response.body;
      // this.cdgService.auth = response.headers.get('authorization')
      this.cdgService.isLoginSuccessfull = true;
      this.localStorage.localStorageSet("accessId", opt.accessId);

      // this.localStorage.localStorageSet("auth","Bearer" + " " +'**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************')
      // this.localStorage.localStorageSet("auth", response.headers.get('authorization'))
      this.localStorage.localStorageSet("isLoginSuccessfull", this.cdgService.isLoginSuccessfull)
      if (opt.cardValidationErrorMessage) {
        this.loggingError = opt.cardValidationErrorMessage;
        this.isShowLockMsg = true
      }
      else {

        if (opt.roles.length === 1) {

          this.cdgService.loginUserDetails.accessId = opt.accessId;
          this.cdgService.loginUserDetails.roles = opt.roles[0];
          this.cdgService.loggedInRole = opt.roles[0].roleName;
          this.cdgService.routingMenuList = opt.menus;
          if (opt.landingScreenNamesDto !== null) {
            if (opt.landingScreenNamesDto.accountDetails) {
              opt.landingScreenNamesDto.accountDetails.forEach((element: any) => {
                this.cdgService.customernumber = element.customer_number;
                console.log('otp', this.cdgService.customernumber);
              });
            }
          }
          this.cdgService.landingScreenNamesDto = opt.landingScreenNamesDto;

          this.localStorage.localStorageSet("loginUserDetails", this.cdgService.loginUserDetails)
          this.localStorage.localStorageSet("loggedInRole", opt.roles[0].roleName)
          this.localStorage.localStorageSet("routingMenuList", opt.menus)
          this.localStorage.localStorageSet("landingScreenNamesDto", opt.landingScreenNamesDto)
          // if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_MASTERUSER) {
          //   this.router.navigate(['/login/login-roles']);
          // }
          // else {
          this.cdgService.getLandingPage()
          // }
        }
        else {
          this.cdgService.userRoles = opt.roles
          this.localStorage.localStorageSet("roles", opt.roles)
          this.router.navigate(['/login/login-roles']);
        }
      }
      this.cdgService.userLoginDetails = opt.accessId
      }
    },
      e => {
        console.log(e)
        if(e.error && e.error.error_description){
          this.loggingError = e.error.error_description;
        }else if (e.error && e.error.message){
          this.loggingError = e.error.message;
        }else{
          this.loggingError = "Credentials are incorrect";
        }
        
        this.isShowLockMsg = true
      })

  }
  goToRegister() {
    this.router.navigate(['/register']);
  }

  gotToForgotPwd() {
    this.router.navigate(['/forgotPwd']);
  }

  gotEnquiryForm() {
    this.router.navigate(['/enquiry']);
  }

  redirectToEnquiryForm() {
    window.open('https://zig.sg/4hefoEN', '_blank');
  }

  showToasterSuccess() {
    this.notifyService.showSuccess("Data shown successfully !!", "ItSolutionStuff.com")
  }
}


