<div class="display-height pt5 pb5 bg"  (window:resize)="onResize($event)">  
	<div fxLayout="column" fxLayoutAlign="center center" class="display-height">
		<div class="login-div" fxLayout="column" fxLayoutGap="20px">
			<div fxLayout="row" fxLayoutGap="14px" fxLayoutAlign="start center" >
				<img src="/assets/images/Cabcharge_white.png"  width="84px" alt="">
				<hr class="dividerd">
				<p class="heading">Sign In</p>
			</div>
			<div  *ngIf="isShowLockMsg" class="mat-error" fxLayoutAlign="center center" style="word-wrap: break-word;">
				{{this.loggingError}}
			</div>
			<div  fxLayout="row" fxLayoutAlign="center center">
				<div class="form-div">
					<form [formGroup]="corporatePersonalForm" class="form">
						<div fxLayout="column" fxLayoutGap="3px">
						<div fxFlex>
							<mat-form-field appearance="outline" class="inp">
								<mat-icon matPrefix>account_circle</mat-icon>
								<input id="username" style="background-color:white;color:black" formControlName="username" type="email" matInput placeholder="Email" required>
								<mat-error *ngIf="corporatePersonalForm.controls.username.hasError('required')">
									Please Enter Email
								</mat-error>
								<mat-error *ngIf="corporatePersonalForm.controls.username.hasError('email')">
									Please Enter Valid Email
								</mat-error>
							</mat-form-field>
						</div>
						<div fxFlex>
							<mat-form-field appearance="outline" class="inp">
								<mat-icon matPrefix>https</mat-icon>
								<input appBlockCopyPaste matInput #input [type]="hide ? 'password' : 'text'" type="password" formControlName="password" placeholder="Password"
								(keyup.enter)="onSubmit()" required>
								
								<mat-error *ngIf="corporatePersonalForm.controls.password.hasError('required')">
									Please Enter Password
								</mat-error>
								<mat-error *ngIf="corporatePersonalForm.controls.password.hasError('pattern')">
									Password Should Meet All The Criteria Mentioned Above
								</mat-error>
								<button mat-icon-button matSuffix  (click)="hide = !hide" [attr.aria-label]="'Hide password'" [attr.aria-pressed]="hide">
						<mat-icon >{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
					</button>
							</mat-form-field>
						</div>
						<div fxFlex>
							<p (click)="gotToForgotPwd()">
								<u class="cursor-pointer enquiry">Forgot password?</u>
							</p>
						</div>
					<div fxFlex>
							<button type="button" mat-raised-button class="inp signin-btn" (click)="onSubmit()">Sign In</button>
						</div>
						<div fxFlex>
							<button type="button" mat-raised-button class="inp register-btn" (click)="goToRegister()">Register</button>
						</div>
					</div>
					<div fxFlex fxLayoutAlign="start center">
							<u class="cursor-pointer enquiry" (click)="redirectToEnquiryForm()">Enquiry Form</u>
					</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
