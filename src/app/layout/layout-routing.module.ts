import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from './layout.component';
import { DashboardComponent } from './../modules/dashboard/dashboard.component';
import { AccessControlComponent } from './../modules/access-control/access-control.component';
import { AccessIdComponent } from './../modules/access-id/access-id.component';
import { EmailConfigComponent } from './../modules/email-config/email-config.component';
import { EmailConfigEditComponent } from '../modules/email-config/email-config-edit/email-config-edit.component';
import { PhConfigComponent } from './../modules/ph-config/ph-config.component';
import { ModulesComponent } from './../modules/modules.component';
import { ModulesModule } from './../modules/modules.module';
import { PublicPagesComponent } from './../modules/public-pages/public-pages.component';
import { AccessRightsComponent } from './../modules/access-rights/access-rights.component';
import { RetrieveOtpComponent } from '../modules/retrieve-otp/retrieve-otp.component';

const routes: Routes = [
  {
    path: '',
    component: LayoutComponent,
  // },
    // children: [
    //   {
    //             path: 'contactus',
    //             component: 'ContactusComponent'
    //         },
    // ]
  //   children: [
  //     {
  //         path: 'dashboard',
  //         component: DashboardComponent
  //     },
  //     {
  //         path: 'accesscontrol',
  //         component: AccessControlComponent,
  //     },
  //     {
  //         path: 'Accessid',
  //         component: AccessIdComponent
  //     },
  //     {
  //         path: 'Emailconfig',
  //         component: EmailConfigComponent
  //     },
  //     {
  //          path: 'emailEdit' ,
  //          component: EmailConfigEditComponent
  //     },
  //     {
  //         path: 'ph',
  //         component: PhConfigComponent
  //     },
  //     {
  //         path: 'addPh',
  //         component: AddPhConfigComponent
  //     },
  //     {
  //         path: 'publicpages',
  //         component: PublicPagesComponent
  //     },
  //     {
  //         path: 'rights',
  //         component: AccessRightsComponent
  //     }
  // ]

  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LayoutRoutingModule { }
