import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { timer } from 'rxjs';
import {LocalStorageService} from '../services/local-storage.service'
import {CdgSharedService} from '../services/cdg-shared.service';
import { ApiServiceService } from '../../shared/services/api-service.service';
import { UrlConstants } from 'src/assets/url-constants/url-constants';
import * as CryptoJS from 'crypto-js';
import { CookieService } from 'ngx-cookie-service';



@Injectable({
  providedIn: 'root'
})
export class SessionTimeoutService {
  internal: any;
  idleTimeoutId: any;
  // timeLeft: number = 10
  constructor(
    public router: Router, 
    private cdgService: CdgSharedService,
    public localStorage: LocalStorageService, 
    private apiService: ApiServiceService,
    private cookieService: CookieService
    ) { }
    
  startTimer(timeLeft: number) {
    this.internal = setInterval((o: any) => {
      if (timeLeft > 0) {
        this.localStorage.localStorageSet('remainingSessionTime',timeLeft);
        timeLeft--;
      }
      else {
        this.startUserIdleTimer();
        this.stopTimer();                     
        }
    }, 1000)
  }
  stopTimer(){
    clearInterval(this.internal)
   }
   startUserIdleTimer(){
     this.idleTimeoutId = setTimeout(() => {
       if(this.cdgService.isUserIdle){
         this.clearUserIdleTimer();       
       }else{
         this.cdgService.isLoginSuccessfull = false;
         this.cdgService.isRegistered = false;
         this.stopTimer()
         this.localStorage.localStorageClear();
         this.cdgService.eVoucherDetails.mode="";
         this.cdgService.mainList = [];
         this.cdgService.routerList = [];
         clearTimeout(this.idleTimeoutId);
         this.router.navigate(['/login']);
       }        
     }, 120000)
   }
   clearUserIdleTimer(){
       clearTimeout(this.idleTimeoutId);
       this.refreshToken();  
   }
   refreshToken(){
    const decrypt = CryptoJS.AES.decrypt(this.cookieService.get('encodedPswd'),'cabCharge_8221');
    const decryptData = JSON.parse(decrypt.toString(CryptoJS.enc.Utf8));
     this.localStorage.localStorageSet('isRefreshTokenApiCalled',true)
     const body = {
     }
     this.apiService.post(this.cdgService.localhostUrlNew +       
       UrlConstants.GET_AUTH +'refresh_token&username=' + this.localStorage.localStorageGet("accessId") + decryptData + '&refresh_token=' + this.localStorage.localStorageGet("refreshToken"), body)
         .subscribe
         ((response: any) => {              
           if (response) {
             let opt = response.body;
             this.localStorage.localStorageSet("sessionExpiryToken",opt.expires_in);
             this.stopTimer()
             this.startTimer(opt.expires_in)
             this.localStorage.localStorageSet('refreshToken',opt.refresh_token);
             this.localStorage.localStorageSet("auth", opt.token_type + " " + opt.access_token);
             this.localStorage.localStorageSet('isRefreshTokenApiCalled',false)
           }
         },
         e => {
           console.log('error occured in refresh token',e);
         }) 
   }
}
