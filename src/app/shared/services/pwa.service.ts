import { Injectable } from '@angular/core';
import { timer } from 'rxjs';
import { take } from 'rxjs/operators';
import { PromptComponent } from '../shared-modules/prompt/prompt.component';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { Platform } from '@angular/cdk/platform';

@Injectable({
  providedIn: 'root'
})
export class PwaService {
  private promptEvent: any;

  constructor(
    private bottomSheet: MatBottomSheet,
    private platform: Platform
  ) { }

  initPwaPrompt() {
    if (this.platform.ANDROID) {
      window.addEventListener('beforeinstallprompt', (event: any) => {
        event.preventDefault();
        this.promptEvent = event;
        this.openPromptComponent('android',this.promptEvent);
      });
    }
    if (this.platform.IOS) {
      const isInStandaloneMode = ('standalone' in window.navigator) 
      // && (window.navigator['standalone']); //Update change
      if (!isInStandaloneMode) {
        this.openPromptComponent('ios',this.promptEvent);
      }
    }
  }

  openPromptComponent(mobileType: 'ios' | 'android', eventPrompt:any) {
    if (this.platform.ANDROID) {
      mobileType='android';
    }
    if (this.platform.IOS) {
      mobileType='ios';
    }
    // if (localStorage.getItem('isLoggedin')) {
      timer(3000)
    .pipe(take(1))
    .subscribe(() => {
      console.log("mobileType: "+mobileType);
      this.bottomSheet.open(PromptComponent, { data: { mobileType, promptEvent: this.promptEvent } });
    });
  // }
  // else
  // {
  //   setTimeout(()=>{
  //     this.openPromptComponent(mobileType,eventPrompt);
  //   }, 10000);
  // }
   
  }
}
