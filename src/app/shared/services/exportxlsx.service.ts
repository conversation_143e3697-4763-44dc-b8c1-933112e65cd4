import { Injectable } from '@angular/core';
import * as FileSaver from 'file-saver';
import * as XLSX from 'xlsx'
const EXCEL_TYPE='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION='.xlsx'
@Injectable({
  providedIn: 'root'
})
export class ExportxlsxService {

  constructor() { }
  public exportAsExcel(json:any[],fileName:string):void{
    const ws : XLSX.WorkSheet=XLSX.utils.json_to_sheet(json);
    const wb:XLSX.WorkBook={Sheets:{'data':ws},SheetNames:['data']};
    const excelBuffer:any=XLSX.write(wb,{bookType:'xlsx',type:'array'});
    this.saveAsExcel(excelBuffer,fileName);
  }
  private saveAsExcel(buffer : any , fileName:string):void{
    const content:Blob = new Blob([buffer],{type:EXCEL_TYPE});
    FileSaver.saveAs(content,fileName+EXCEL_EXTENSION);
  }
}
