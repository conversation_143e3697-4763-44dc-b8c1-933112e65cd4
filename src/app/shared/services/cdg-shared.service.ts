import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { Router } from '@angular/router';
import { RoleConstants } from 'src/assets/url-constants/url-constants';
import { LocalStorageService } from './local-storage.service';
// import { environment } from 'src/environments/environment.prod';    // for production build
import { environment } from 'src/environments/environment';      // for development and local run

@Injectable({
  providedIn: 'root'
})
export class CdgSharedService {
  
  localhostUrl = environment.baseUrl; 
  localhostUrlms = environment.baseUrl;
  localhostMicroServiceUrl = environment.baseUrl;
  localhostUrlNew = environment.baseUrl; 
  webServerUrl = environment.baseUrl;

  loginType = "";
  showMenu: boolean;
  accessid: any;
  wasInside: boolean = false;
  isCorpUpdate: boolean = false;
  showRegMsgDivConfirm: boolean = false
  deparmentCard: any[] = [];
  individualCard: any[] = []
  virtualCard: any[] = []
  costCenterArr: any[] = []
  // isComingFromPreview:boolean=false;
  cardTypeSelected = {
    isComingFromPreview: false,
    cardType: "",
    function: ""
  }
  supplementaryPreview = {
    supplementarypreview: false,
    cardType: "",
    function: ""
  }
  upload = {
    isfromback: false,
    function: ""
  }
  supplementarypreview: boolean = false;
  public menuIconName = 'view_headline';
  emailDetails = new BehaviorSubject<object>({});
  showMatrix: boolean = false;
  id: any;
  userDetails: any[] = [];
  customerNoSelected: any;
  status: string = ""
  showRightsMatrix: boolean = false;
  monthlySummaryObj: any;
  monthlySummaryDivObj: any;
  rightsId: any;
  rightsDataSource: any;
  holidayDesc: any;
  holidayDate: any;
  dataRow: any;
  setupRulesData: any;
  customernumber: any;
  evoucherAdd: boolean = false;
  auth: any = ''
  userRoles: any[] = []
  routingMenuList: any[] = []
  userLoginDetails: any;
  isLoginSuccessfull: boolean = false;
  addMode: any;
  newPhAddedYr: number;
  isVerifyOtpRoutePath: string = "email";
  isUserIdle: boolean;
  landingScreenNamesDto = {
    account_no: "",
    main_account_name: "",
    sub_account_name: "",
    parent_account_name: "",
    salesperson_name: "",
    customer_number: "",
    masterDropdownDto: {}

  }
  loginUserDetails = {
    accessId: "",
    roles: {
      accountcategory: "",
      roleDesc: "",
      roleId: 0,
      roleName: ""
    }
  }
  popupObj = {
    title: "",
    msg: "",
    component: "",
    function: ""
  }
  popupObjCon = {
    title: "",
    msg: "",
    component: "",
    function: ""
  }
  passwordValidation = [
    {
      msg: "Password should contain at least one capital letter",
      icon: "done"
    },
    {
      msg: "Password should contain at least one number",
      icon: "done"
    },
    {
      msg: "Password should contain at least one special character like @,!,%,&,*",
      icon: "done"
    },
    {
      msg: "Password length should contain a min of 8 and a maximum of 20 characters",
      icon: "done"
    }
  ]
  showRegMsgDiv: boolean = false;
  registrationOptObj: any;
  isDataFromAccessId: boolean = false;
  // loggedInRole:string="Personal Card"
  // loggedInRole:string="Personal Card Sub"
  //loggedInRole:string="Individual Card"
  //loggedInRole:string="Subapplicant"
  loggedInRole: string = "ROLE_CORPADMIN"
  // 
  eVoucherDetails = {
    "mode": "",
    "productType": "",
    "quantityReq": '',
    "division": "",
    "prodArr": ['kkk'],
    "divArr": ['lll']
  }
  expSidebar: boolean = false;
  showAccessIdDetails: boolean = false;
  loggedInUserDetails = {
    "accessid": "",
    "password": "",
    "userName": "",
    "cardNo": "",
    "company": "",
    "accountNo": "",
    "telephone": "",
    "faxNo": "",
    "officeNo": "",
    "mobNo": "",
    "email": "",
    "alterNname": "",
    "alterTelephone": "",
    "alterFaxNo": "",
    "alterMobNo": "",
    "alteremail": "",
    "blockNo": "",
    "unitNo": "",
    "streetName": "",
    "buildingName": "",
    "area": "",
    "country": "",
    "city": "",
    "state": "",
    "postalCode": "",
    "position": ""
  };
  selectedCard =
    {
      index: 0,
      accessId: "",
      contactperosonid: "",
      cardsno: "",
      role: [''],
      isDeativate: false
    }
  public holidayList = {
    publicHolidayList: [{
      "holidayDesc": "HOLIDAY 1",
      "holidayDate": "05-03-2021"
    },
    {
      "holidayDesc": "HOLIDAY 2",
      "holidayDate": "04-03-2021"
    },
    {
      "holidayDesc": "HOLIDAY 3",
      "holidayDate": "03-10-2021"
    },
    {
      "holidayDesc": "HOLIDAY 4",
      "holidayDate": "12-31-2021"
    },
    {
      "holidayDesc": "HOLIDAY 5",
      "holidayDate": "01-03-2021"
    }]
  }
  mainMenuList: any[] = [];
  mainList: any[] = [];
  routerList: any[] = [];
  registerUserLogin = {
    "accessId": "",
    "password": ""
  }
  isRegistered: boolean = false;
  constructor(private router: Router, public localStorage: LocalStorageService) {
    console.log(router.url)
    // let storedEmailDetails = localStorage.getItem('storedEmailDetails');
    // if(storedEmailDetails){
    //   this.setEmailDetails(JSON.parse(storedEmailDetails), false);
    // }
  }

  //  setEmailDetails(emailDetails:object, storeEmailDetails: boolean = false){
  //    if(storeEmailDetails){
  //      localStorage.setItem('storedEmailDetails', JSON.stringify(emailDetails));
  //      console.log(emailDetails, "serviceeeeee")
  //      this.emailDetails.next(emailDetails);
  //    }
  //  }

  //  getEmailDetails(){
  //    return this.emailDetails;
  //  }

  // getUpdatedCardDetail(data: any) {
  //   this.maxtrixList.forEach((element, i) => {
  //     if (element.accessId === data.accessId) {
  //       this.id = data.accessId;
  //       this.userDetails = element.accessesDetails;
  //       element.accessesDetails.forEach((detail, j) => {
  //         if (j === data.index) {
  //           detail.contactperosonid = data.contactperosonid,
  //             detail.cardsno = data.cardsno,
  //             detail.role = data.role
  //         }
  //       });
  //     }
  //   });
  //   this.showMatrix = true;
  //   this.router.navigateByUrl('layout/Accessid')
  // }
  getUpdateAccessRights(data: any) {
    if (data) {
      this.rightsId = data.accessId;
      this.rightsDataSource = data.accesses
      this.showRightsMatrix = true;
      this.router.navigateByUrl('layout/rights');

    }
  }
  getUpdatedPhConfig(data: any) {
    if (data) {
      this.holidayDesc = data.holidayDesc;
      this.holidayDate = data.holidayDate;
      this.router.navigateByUrl('layout/ph');
    }
  }
  addHolidayListDetails(data: any) {
    if (data) {
      // this.holidayList.publicHolidayList = data;
      this.holidayList.publicHolidayList.push(data);
      this.router.navigateByUrl('layout/ph');
    }
  }
  addReqForEvoucher() {
    this.evoucherAdd = true;
  }
  updateEvoucher() {
    this.evoucherAdd = false;
  }
  dateConversion(selectedDate: any) {
    const data = selectedDate.split('-');
    return data[1] + '-' + data[0] + '-' + data[2]
  }
  getLandingPage() {
    let data: any[] = []
    this.localStorage.localStorageGet("routingMenuList").forEach((element: any) => {
      data.push(element.mainMenuDetails);
    });
    this.mainMenuList = data.reduce((a, c) => {
      if (!a.some((item: any) => item.menuId === c.menuId)) {
        a.push(c)
      }
      return a;
    }, []);
    this.mainMenuList.forEach((val: any) => {
      let subMenu: any;
      if (this.loggedInRole !== RoleConstants.ROLE_SUPERADMINUSER) {
        subMenu = this.localStorage.localStorageGet("routingMenuList").filter((o: any) => o.mainMenuDetails.menuId === val.menuId)
      }
      let obj = {
        "menuId": val.menuId,
        "menuName": val.menuName,
        "menuDesc": val.menuDesc,
        "menuDisplayOrder": val.menuDisplayOrder,
        "subMenu": subMenu ? subMenu : []
      }
      this.mainList.push(obj)
    })
    if (this.loggedInRole === RoleConstants.ROLE_SUPERADMINUSER) {
      this.mainList.push({
        "menuId": 0,
        "menuName": "Dashboard",
        "menuDesc": "Dashboard",
        "menuDisplayOrder": 0,
        "subMenu": []
      })
    }
    this.mainList.sort((a: any, b: any) => {
      return a.menuDisplayOrder - b.menuDisplayOrder
    })
    this.mainList.forEach((main: any) => {
      /**remove after testing */
      // if(main.menuName === 'View Statement/Usage'){
      //   main.subMenu.push({isMenuActive: true,
      //     menuDesc: "e-Receipt Request",
      //     menuDisplayOrder: 10,
      //     menuId: 1005,
      //     menuName: "e-Receipt Request"})
      // }
      /** end region */
      if (main.subMenu.length > 0) {
        main.subMenu.sort((a: any, b: any) => {
          return a.menuDisplayOrder - b.menuDisplayOrder
        })
        main.subMenu.forEach((sub: any) => {
          delete sub.mainMenuDetails;

        });
      }
    });
    console.log(this.mainList)
    if (this.mainList.length > 0) {
      this.mainList.forEach((mainMenu, i) => {
        let sMenu: any[] = [];
        if (mainMenu.menuName === 'Dashboard') {
          if (this.loggedInRole === RoleConstants.ROLE_SUPERADMINUSER) {
            this.routerList.push({ link: 'admindashboard', linkTitle: mainMenu.menuName, icon: 'dashboard', subMenu: mainMenu.subMenu, isActive: true, isOpen: '' })
          }
        }
        else
          if (mainMenu.menuName === 'View Statement/Usage') {
            if (mainMenu.subMenu.length > 0) {
              mainMenu.subMenu.forEach((submenu: any) => {
                if (submenu.menuName === 'View/Download Invoices') {
                  sMenu.push({ subLink: 'invoices', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'View Statement of Accounts') {
                  sMenu.push({ subLink: 'accStatement', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'View Usage Report') {
                  sMenu.push({ subLink: 'usageReport', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'View Transaction By Card/E-Voucher') {
                  sMenu.push({ subLink: 'viewtransaction', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'View Transaction Summary') {
                  sMenu.push({ subLink: 'transactionsummary', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'Download Trip Report') {
                  sMenu.push({ subLink: 'downloadtripreport', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                //else if (submenu.menuName === 'Card Status Usage Enquiry') {
                else if (submenu.menuName === 'View Card Status/Card Usage') {
                  sMenu.push({ subLink: 'card-status-usage', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'Download Card Holder List') {
                  sMenu.push({ subLink: 'downloadCardHolder', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'Top-Up History') {
                  sMenu.push({ subLink: 'topuphistory', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'E-Receipt Request') {
                  sMenu.push({ subLink: 'eReceiptRequest', subLinkTitle: submenu.menuName, isSubActive: false })
                }
              });
              this.routerList.push({ link: 'dummy', linkTitle: mainMenu.menuName, icon: '/assets/images/view statement-usage.png', subMenu: sMenu, isActive: false, isOpen: 'expand_more' })

            }

          }
          else if (mainMenu.menuName === 'Manage Corporate Policy') {
            if (mainMenu.subMenu.length > 0) {
              mainMenu.subMenu.forEach((submenu: any) => {
                if (submenu.menuName === 'Setup Rules') {
                  // sMenu.push({ subLink: 'localhostUrl', subLinkTitle: submenu.menuName, isSubActive: false })
                  sMenu.push({ subLink: 'setuppolicies/listsetuppolicies', subLinkTitle: submenu.menuName, isSubActive: false })
                }
              });
              this.routerList.push({ link: 'dummy', linkTitle: mainMenu.menuName, icon: '/assets/images/manage corporate rule.png', subMenu: sMenu, isActive: false, isOpen: 'expand_more' })

            }
          }
          else if (mainMenu.menuName === 'Online Services') {
            if (mainMenu.subMenu.length > 0) {
              mainMenu.subMenu?.forEach((submenu: any) => {
                if (submenu.menuName === 'Edit Profile') {
                  sMenu.push({ subLink: 'editprofile', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'Change Password') {
                  sMenu.push({ subLink: 'changeProfile', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'Check Limit & Credit Balance') {
                  sMenu.push({ subLink: 'creditlimitbalance', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                // else if (submenu.subMenuName === 'Report Lost Card') {
                //     sMenu.push({ subLink: 'dummy', subLinkTitle: submenu.subMenuName, isSubActive: false })
                // }
                else if (submenu.menuName === 'Report Lost/Faulty Card') {
                  sMenu.push({ subLink: 'reportlostfaultyCard', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'Loyalty Rewards Redemption') {
                  sMenu.push({ subLink: 'loyaltyRewardsRedemption', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'Feedback form') {
                  sMenu.push({ subLink: 'feedback', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'Request eVouchers') {
                  sMenu.push({ subLink: 'requestevoucher', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'Assign/Return Card') {
                  sMenu.push({ subLink: 'assigncardtouser', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                // else if (submenu.subMenuName === 'Card Return by User') {
                //     sMenu.push({ subLink: 'dummy', subLinkTitle: submenu.subMenuName, isSubActive: false })
                // }
                else if (submenu.menuName === 'Self Service Top-up') {
                  sMenu.push({ subLink: 'selftopup', subLinkTitle: submenu.menuName, isSubActive: false })
                }
                else if (submenu.menuName === 'Self Service Top-up Draft') {
                  sMenu.push({ subLink: 'selftopupdraft', subLinkTitle: submenu.menuName, isSubActive: false })
                }
              });
              this.routerList.push({ link: 'dummy', linkTitle: mainMenu.menuName, icon: '/assets/images/online services.png', subMenu: sMenu, isActive: false, isOpen: 'expand_more' })

            }
          }
          else if (mainMenu.menuName === 'Online Application') {
            // if (mainMenu.subMenu.length > 0) {
            //     mainMenu.subMenu?.forEach((submenu: any) => {
            // if (submenu.menuName === 'Departmental Card') {
            //     sMenu.push({ subLink: 'departmentalCard', subLinkTitle: submenu.menuName, isSubActive: false })
            // }
            //   if (submenu.menuName === 'Online Applications') {
            //     sMenu.push({ subLink: 'onlineApplication', subLinkTitle: submenu.menuName, isSubActive: false })
            // }
            // else if (submenu.menuName === 'Individual Card') {
            //     sMenu.push({ subLink: 'individualCard', subLinkTitle: submenu.menuName, isSubActive: false })
            // }
            // else if (submenu.menuName === 'Virtual Card') {
            //     sMenu.push({ subLink: 'virtualcard', subLinkTitle: submenu.menuName, isSubActive: false })
            // }
            // });
            this.routerList.push({ link: 'onlineApplication', linkTitle: mainMenu.menuName, icon: '/assets/images/online services.png', subMenu: sMenu, isActive: false, isOpen: 'expand_more' })

            // }
          }
          else if (mainMenu.menuName === 'Application Form') {
            if (mainMenu.subMenu.length > 0) {
              mainMenu.subMenu?.forEach((submenu: any) => {
                if (submenu.menuName === 'Supplementary Card Form') {
                  sMenu.push({ subLink: 'supplementarycard', subLinkTitle: submenu.menuName, isSubActive: false })
                }
              });
              this.routerList.push({ link: 'dummy', linkTitle: mainMenu.menuName, icon: '/assets/images/manage corporate rule.png', subMenu: sMenu, isActive: false, isOpen: 'expand_more' })
            }
          }
          else if (mainMenu.menuName === 'Booking Services') {
            this.routerList.push({ link: mainMenu.menuName, linkTitle: mainMenu.menuName, icon: '/assets/images/booking services.png', subMenu: mainMenu.subMenu, isActive: false, isOpen: '' })
          }
          else if (mainMenu.menuName === 'Access Control') {
            this.routerList.push({ link: 'accesscontrol', linkTitle: mainMenu.menuName, icon: 'fact_check', subMenu: mainMenu.subMenu, isActive: false, isOpen: '' })
          }
          else if (mainMenu.menuName === 'Access Id') {
            this.routerList.push({ link: 'Accessid', linkTitle: mainMenu.menuName, icon: 'contact_page', subMenu: mainMenu.subMenu, isActive: false, isOpen: '' })
          }
          else if (mainMenu.menuName === 'Email Configuration') {
            this.routerList.push({ link: 'Emailconfig', linkTitle: mainMenu.menuName, icon: 'email', subMenu: mainMenu.subMenu, isActive: false, isOpen: '' })
          }
          else if (mainMenu.menuName === 'Public Holiday Configuration') {
            this.routerList.push({ link: 'ph', linkTitle: mainMenu.menuName, icon: 'calendar_today', subMenu: mainMenu.subMenu, isActive: false, isOpen: '' })
          }
          else if (mainMenu.menuName === 'Retrieve OTP') {
            this.routerList.push({ link: 'retrieveOtp', linkTitle: mainMenu.menuName, icon: 'contact_page', subMenu: mainMenu.subMenu, isActive: false, isOpen: '' })
          }
        // });
      });
    }
    // if (this.loggedInRole === RoleConstants.ROLE_SUPERADMINUSER) {
    //   this.router.navigateByUrl('/layout/admindashboard');
    // }
    // else if (this.loggedInRole === RoleConstants.ROLE_CORPADMIN) {
    //   this.router.navigateByUrl('/layout/dashboard');
    // }
    // else {
    // }
    if (this.routerList.length > 0) {
      if (this.routerList[0].subMenu.length > 0) {
        this.router.navigateByUrl('/layout/' + this.routerList[0].subMenu[0].subLink);
      }
      else {
        this.router.navigateByUrl('/layout/' + this.routerList[0].link);
      }
    }
    this.localStorage.localStorageSet("routerList", this.routerList)
  }
}