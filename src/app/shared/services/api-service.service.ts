// import { Injectable } from '@angular/core';
// import { HttpClient, HttpHeaders } from '@angular/common/http';
// import { throwError } from 'rxjs';
// import { map } from 'rxjs/operators';
// import { CdgSharedService } from './cdg-shared.service';
// import { LocalStorageService } from './local-storage.service';

// @Injectable({
//   providedIn: 'root'
// })
// export class ApiServiceService {
//   headers: any;
//   constructor(private http: HttpClient, private cdgService: CdgSharedService,public localStorage: LocalStorageService) {
//   }

//   public get(url: any, responseType?: any) {
//     let option: any
//     if (this.cdgService.isLoginSuccessfull) { 
//       this.headers = new HttpHeaders().set('Content-Type', 'application/json').set("Authorization", this.localStorage.localStorageGet("auth"))
//       }
//       else{
//       this.headers = new HttpHeaders().set('Content-Type', 'application/json')
//       }
//     if (responseType) {
//       option = {
//         headers: this.headers,
//         ...responseType,
//         observe: 'response'
//       }
//     }
//     else {
//       option = {
//         headers: this.headers,
//         responseType: 'json',
//         observe: 'response'
//       }
//     }
//     return this.http.get(url, option)
//   }
//   public post(url: any, requestObj: any, responseType?: any) {
//     let option: any;
//     if (this.cdgService.isLoginSuccessfull) { 
//       this.headers = new HttpHeaders().set('Content-Type', 'application/json').set("Authorization", this.localStorage.localStorageGet("auth"))
//       console.log(this.headers)
//       }
//       else{
//       this.headers = new HttpHeaders().set('Content-Type', 'application/json')
//       }
//     if (responseType) {
//       option = {
//         headers: this.headers,
//         ...responseType,
//       }
//     }
//     else {
//       option = {
//         headers: this.headers,
//         responseType: 'json',
//         observe: 'response'
//       }
//     }
//     return this.http.post(url, requestObj, option)
//   }
//   public put(url: any, requestObj: any, responseType?: any) {
//     let option: any;
//     if (this.cdgService.isLoginSuccessfull) { 
//       this.headers = new HttpHeaders().set('Content-Type', 'application/json').set("Authorization", this.localStorage.localStorageGet("auth"))
//       }
//       else{
//       this.headers = new HttpHeaders().set('Content-Type', 'application/json')
//       }
//     if (responseType) {
//       option = {
//         headers: this.headers,
//         ...responseType,
//         observe: 'response'
//       }
//     }
//     else {
//       option = {
//         headers: this.headers,
//         responseType: 'json',
//         observe: 'response'
//       }
//     }
//     return this.http.put(url, requestObj, option)
//   }
//   public delete(url: any, responseType?: any) {
//     if (this.cdgService.isLoginSuccessfull) { 
//       this.headers = new HttpHeaders().set('Content-Type', 'application/json').set("Authorization", this.localStorage.localStorageGet("auth"))
//       }
//       else{
//       this.headers = new HttpHeaders().set('Content-Type', 'application/json')
//       }
//     let option: any
//     if (responseType) {
//       option = {
//         headers: this.headers,
//         ...responseType,
//         observe: 'response'
//       }
//     }
//     else {
//       option = {
//         headers: this.headers,
//         responseType: 'json',
//         observe: 'response'
//       }
//     }
//     return this.http.delete(url, option)
//   }
// }





import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { throwError } from 'rxjs';
import { map } from 'rxjs/operators';
import { CdgSharedService } from './cdg-shared.service';
import { LocalStorageService } from './local-storage.service';

@Injectable({
  providedIn: 'root'
})
export class ApiServiceService {
  headers: any;
  option: any
  constructor(private http: HttpClient, private cdgService: CdgSharedService, public localStorage: LocalStorageService) {
  }

  public get(url: any, responseType?: any) {
    this.option = {}
    if (responseType) {
      this.setOptions(responseType);
    }
    else {
      this.setOptions();
    }
    return this.http.get(url, this.option)
  }
  public post(url: any, requestObj: any, responseType?: any) {
    this.option = {}
    if (responseType) {
      this.setOptions(responseType);
    }
    else {
      this.setOptions();
    }
    return this.http.post(url, requestObj, this.option)
  }
  public put(url: any, requestObj: any, responseType?: any) {
    this.option = {}
    if (responseType) {
      this.setOptions(responseType);
    }
    else {
      this.setOptions();
    }
    return this.http.put(url, requestObj, this.option)
  }
  public delete(url: any, responseType?: any) {
    this.option = {}
    if (responseType) {
      this.setOptions(responseType);
    }
    else {
      this.setOptions();
    }
    return this.http.delete(url, this.option)
  }
  setOptions(responseType?: any) {
    if (this.localStorage.localStorageGet("isLoginSuccessfull")) {
      if (this.localStorage.localStorageGet("isRefreshTokenApiCalled")) {
        this.getAppHeaders();
      } else {
        this.headers = new HttpHeaders().set('Content-Type', 'application/json').set("Authorization", this.localStorage.localStorageGet("auth"))
      }
      console.log(this.headers)
      if (responseType) {
        this.option = {
          headers: this.headers,
          ...responseType,
        }
      }
      else {
        this.option = {
          headers: this.headers,
          responseType: 'json',
          observe: 'response'
        }
      }
    }
    else {
      console.log(this.localStorage.localStorageGet("isTokenApiCalled"));
      if (this.localStorage.localStorageGet("isTokenApiCalled")) {
        this.getAppHeaders();
      }
      else {
        this.headers = new HttpHeaders().set('Content-Type', 'application/json').set("apikey", this.localStorage.localStorageGet("apikey"))
        // this.headers = new HttpHeaders().set('Content-Type', 'application/json').set("Authorization", this.localStorage.localStorageGet("token"))

      }
      this.option = {
        headers: this.headers,
        observe: 'response'
      }
    }
  }

  getAppHeaders() {
    this.headers = new HttpHeaders().set('Content-Type', 'application/x-www-form-urlencoded').set('Access-Control-Allow-Origin', '*');
  }
}
