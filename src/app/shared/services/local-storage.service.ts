import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class LocalStorageService {
  localStorage: Storage;

  constructor() {
    this.localStorage = window.localStorage
  }
  localStorageSet(key: string, value: any):boolean {
    if (this.isLocalStorageSupported) {
      this.localStorage.setItem(key, JSON.stringify(value));
      return true;
    }
    else{
      return false;
    }
  }
  localStorageGet(key: string) :any{
    if (this.isLocalStorageSupported) {
      const data=this.localStorage.getItem(key)
      return data?JSON.parse(data):null;
      
    }
      return null;
  }
  localStorageRemove(key: string) :boolean{
    if (this.isLocalStorageSupported) {
      this.localStorage.removeItem(key);
      return true;
    }
      return false;
  }
  localStorageClear() :boolean{
    if (this.isLocalStorageSupported) {
      this.localStorage.clear();
      return true;
    }
      return false;
  }
  get isLocalStorageSupported(): boolean {
    return !!this.localStorage;
  }
}
