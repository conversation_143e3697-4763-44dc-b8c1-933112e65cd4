import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
// export enum ToasterPosition {
//   topRight = 'toast-top-right',
//   topLeft = 'toast-top-left',
//   bottomRight = 'toast-bottom-right',
//   bottomLeft= 'toast-bottom-left',
//   // Other positions you would like
// }
export class NotificationService {

  constructor(private toastr: ToastrService) { }

  showSuccess(message: any, title: any) {
    this.toastr.success(message, title, {
      positionClass: 'toast-top-center',
      enableHtml:true,
      closeButton:true,
      timeOut:5000
    })
  }

  showError(message: any, title: any) {
    this.toastr.error(message, title, {
      positionClass: 'toast-top-center',
      enableHtml:true,
      closeButton:true,
      timeOut:5000
    })
  }

  showInfo(message: any, title: any) {
    this.toastr.info(message, title, {
      positionClass: 'toast-top-center',
      enableHtml:true,
      closeButton:true,
      timeOut:5000
    })
  }

  showWarning(message: any, title: any) {
    this.toastr.warning(message, title, {
      positionClass: 'toast-top-center',
      enableHtml:true,
      closeButton:true,
      timeOut:5000
    })
  }
}
