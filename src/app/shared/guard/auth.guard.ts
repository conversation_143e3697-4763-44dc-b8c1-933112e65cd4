import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { CdgSharedService } from '../services/cdg-shared.service';
import { LocalStorageService } from '../services/local-storage.service';

@Injectable({
  providedIn: 'root'
})
//CanActivate removed for v16 and later version ; change by navani
export class AuthGuard  { 
  constructor(public localStorage: LocalStorageService,private cdgService: CdgSharedService,public router: Router){}
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    if(this.localStorage.localStorageGet("isLoginSuccessfull")){
      return true;
    }
    else{
      return this.router.parseUrl('/login');
    }
  }
  
}
