import { Component, OnInit, HostListener } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { RoleConstants } from 'src/assets/url-constants/url-constants';
import { LocalStorageService } from '../../services/local-storage.service';
import { SessionTimeoutService } from '../../services/session-timeout.service';
import { CdgSharedService } from './../../services/cdg-shared.service'

@Component({
  selector: 'app-topnav',
  templateUrl: './topnav.component.html',
  styleUrls: ['./topnav.component.scss']
})
export class TopnavComponent implements OnInit {

  //New code for dropDown
  customerNoForm:FormGroup


  public isWebLayout = false;
  public isTabletLayout = false;
  public showCloseIcon = true;
  public innerWidth: any;
  // public menuIconName = 'view_headline';
  public notifications = 8;
  userName: any = "";
  custNo = [
    { view: '', viewValue: '' }
    // {view:'166195',viewValue:'SINGAPORE TELECOMMUNICATIONS LTD (MOBILE CUSTOMER MGMT)'},
    // {view:'166194',viewValue:'SINGAPORE TELECOMMUNICATIONS LTD (MOBILE CUSTOMER MGMT)'},
    // {view:'166193',viewValue:'SINGAPORE TELECOMMUNICATIONS LTD (MOBILE CUSTOMER MGMT)'},

  ]
  landingScreen: any;
  loginUserDetails: any;
  isShowCustNo: boolean = false;
  loggedInRole: any;
  customerNoListMaster: any;
  customerNoSelected: any;

  viewArrMain: any = [];
  customerviewvalue: any;
  custValueChosen:any;
  cus: any;
  customernoload: string;

isShowInfo:boolean=true;
  constructor(public sessionService: SessionTimeoutService,private route: ActivatedRoute, public router: Router, public cdgService: CdgSharedService, public localStorage: LocalStorageService, private formBuilder: FormBuilder) { 
    this.customerNoForm= this.formBuilder.group({
      custNo: ['', [Validators.required]]
    });
  }

  ngOnInit() {
   
    this.landingScreen = this.localStorage.localStorageGet("landingScreenNamesDto")
    this.loginUserDetails = this.localStorage.localStorageGet("loginUserDetails")
if(this.localStorage.localStorageGet("viewvalue")!==null){
   this.cus=this.localStorage.localStorageGet("viewvalue");
   var str = this.cus.toString();
   var newStng = str.split(":")
   this.custValueChosen = newStng[1];
}
    
// Code for getting the Customer Number List for master login 
    this.loggedInRole = this.localStorage.localStorageGet("loggedInRole")
    if (this.loggedInRole === RoleConstants.ROLE_MASTERUSER && this.landingScreen.masterDropdownDto !== null) {
      this.customerNoListMaster = this.landingScreen.masterDropdownDto.customerNumberList
      //  console.log(this.customerNoListMaster, "listt")
      Object.entries(this.customerNoListMaster).forEach(
        ([key, value]) => {
          this.viewArrMain.push([key, value])
        });
      
      this.custNo = [];
        
      this.viewArrMain.forEach((element: any) => {

        this.custNo.push({ 'view': element[0], 'viewValue': element[1][0] + ' : ' + element[1][1] })
        this.localStorage.localStorageSet("custNo", this.custNo[0]);
        //  console.log(this.custNo[0].viewValue);
        var str = this.custNo[0].viewValue.toString();
        var newStng = str.split(":")
       if(!this.custValueChosen){
       this.custValueChosen = newStng[1];
       this.customernoload =  newStng[0];
       this.localStorage.localStorageSet("viewvalueonload",this.custNo[0].viewValue); 
       }
        
        //  console.log(newStng)
      });
    }


    if (this.landingScreen) {
      //  console.log(this.landingScreen);
      if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPADMIN) {
        // || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER
        this.userName = this.landingScreen.accountDetails[0].main_account_name;
        // this.userName = this.landingScreen.main_account_name;
        this.isShowCustNo = true;
      }
    }
    if (this.landingScreen) {
      //  console.log(this.landingScreen);
      if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
        this.userName = this.loginUserDetails.accessId;
        // this.userName = this.landingScreen.main_account_name;
        this.isShowCustNo = true;
        this.isShowInfo=false;
      }
    }
    if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUPERADMINUSER || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_MASTERUSER || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER || this.localStorage.localStorageGet("loggedInRole") === "Personal Card Sub" || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUBAPPLICANT) {
      this.userName = this.loginUserDetails.accessId;
      this.isShowCustNo = false;
    }
    //  console.log(this.landingScreen, this.loginUserDetails, this.userName)
    // else if(this.localStorage.localStorageGet("loggedInRole") === "Subapplicant"){
    // }
    this.innerWidth = window.innerWidth;

    if (this.innerWidth <= 990) { // 768px portrait
      this.cdgService.showMenu = false;
      this.isWebLayout = false;
      this.isTabletLayout = true;
      this.showCloseIcon = true
    }
    else {
      this.cdgService.showMenu = true;
      this.isWebLayout = true;
      this.isTabletLayout = false;
      this.showCloseIcon = true


    }
  }
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 990) { // 768px portrait
      this.cdgService.showMenu = false;
      this.isWebLayout = false;
      this.isTabletLayout = true;
      this.showCloseIcon = true;

    }
    else {
      this.cdgService.showMenu = true;
      this.isWebLayout = true;
      this.isTabletLayout = false;
      this.showCloseIcon = true
    }
  }
  getMoreInfo(): string {
    let val = '';
    if (this.landingScreen) {
      if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPADMIN || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
        if (this.innerWidth <= 990) {
          val = 'Main Account :' + this.landingScreen.accountDetails[0].main_account_name + '\n Sub Account :' + this.landingScreen.accountDetails[0].sub_account_name + '\n Account Manager :' + this.landingScreen.accountDetails[0].salesperson_name + '\n Company :' + this.landingScreen.accountDetails[0].parent_account_name + '\n Account No :' + this.landingScreen.accountDetails[0].account_no;
          // val = 'Main Account :' + this.landingScreen.main_account_name + '\n Sub Account :' + this.landingScreen.sub_account_name + '\n Account Manager :' + this.landingScreen.salesperson_name + '\n Company :' + this.landingScreen.parent_account_name + '\n Account No :' + this.landingScreen.account_no;
        }
        else {
          val = 'Sub Account :' + this.landingScreen.accountDetails[0].sub_account_name + '\n Account Manager :' + this.landingScreen.accountDetails[0].salesperson_name + '\n Company :' + this.landingScreen.accountDetails[0].parent_account_name;
        }
      }
      // else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_PERSCARDHOLDER || this.localStorage.localStorageGet("loggedInRole") === "Personal Card Sub" || this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_SUBAPPLICANT) {
      //   if (this.innerWidth <= 990) {
      //     val = 'Main Account :' + this.landingScreen.main_account_name + '\n Account Manager :' + this.landingScreen.salesperson_name+ '\n Account No :' + this.landingScreen.account_no;
      //   }
      //   else {
      //     val = 'Account Manager :' + this.landingScreen.salesperson_name;
      //   }
      // }
      // else if (this.localStorage.localStorageGet("loggedInRole") === RoleConstants.ROLE_CORPCARDHOLDER) {
      //   if (this.innerWidth <= 990) {
      //     val = 'Main Account :' + this.landingScreen.main_account_name + '\n Account Manager :' + this.landingScreen.salesperson_name + '\n Company :' + this.landingScreen.parent_account_name+ '\n Account No :' + this.landingScreen.account_no;
      //   }
      //   else {
      //     val = 'Account Manager :' + this.landingScreen.salesperson_name + '\n Company :' + this.landingScreen.parent_account_name;
      //   }
      // }
    }
    else {
      val = 'Access Id :' + this.loginUserDetails.accessId;
    }
    return val;
  }
  sideNavToggle(e: any) {
    if (this.innerWidth <= 990) {
      this.cdgService.showMenu = !this.cdgService.showMenu;
      // this.showCloseIcon = !iconVal;
      if (!this.cdgService.showMenu) {
        this.cdgService.menuIconName = 'view_headline';
      }
      else {
        this.cdgService.menuIconName = 'close';
        this.cdgService.wasInside = true;
      }
    }
    else {
      this.cdgService.expSidebar = !this.cdgService.expSidebar;
      if (!this.cdgService.expSidebar) {
        this.cdgService.menuIconName = 'view_headline';
      }
      else {
        this.cdgService.menuIconName = 'close';
        // this.cdgService.wasInside=true;

      }
    }

  }
  contatUs() {
    this.router.navigateByUrl('/contact-us');
  }
  onLoggedout() {
    this.cdgService.isLoginSuccessfull = false;
    this.cdgService.isRegistered = false;
    this.sessionService.stopTimer()
    this.localStorage.localStorageClear();
    this.cdgService.eVoucherDetails.mode="";
    this.cdgService.mainList = [];
    this.cdgService.routerList = [];
    this.router.navigate(['/login']);
  }
  goToSettings() {

  }
  goToNotifications() {

  }

  //New code for dropDown
  getSelectedCustNo(selected: any) {
    this.customerNoSelected = selected.value;
    console.log(this.customerNoSelected);
    this.custNo.forEach((element:any)=>{
    if(element.view===selected.value){
      this.customerviewvalue=element.viewValue;
      this.localStorage.localStorageSet("viewvalue",this.customerviewvalue); 
    }
    });
   
    this.localStorage.localStorageSet("isApplicationReloaded",true);
    this.localStorage.localStorageSet("customerNoSelected", this.customerNoSelected);
    this.localStorage.localStorageSet("customerviewvalue",this.customerviewvalue);
    //  console.log(this.localStorage.localStorageGet("customerviewvalue"));
    var str = this.localStorage.localStorageGet("customerviewvalue");
    var newStng = str.split(":");
    this.custValueChosen = newStng[1];
    this.cdgService.customerNoSelected = this.customerNoSelected;
    window.location.reload();
  }
}
