<!-- 
<div *ngIf="isWebLayout" fxFlex fxLayout="row" fxLayout.lt-md="column">
	<mat-toolbar class="tool-bar-cls p2">
		<div fxFlex=50% fxFlex.gt-lg=30% fxFlexfxLayout="row" class="top-menu-main-div">
			<div fxFlex>
				<img src="/assets/images/cabcharge_logo.png" alt="">
			</div>
			<div fxFlex>
				<button class="hidden-sm" mat-icon-button>
					<span class="top-menu">About Us</span>
				</button>
			</div>
			<div fxFlex>
				<button class="hidden-sm" mat-icon-button>
					<span class="top-menu">Products</span>
				</button>
			</div>
			<div fxFlex>
				<button class="hidden-sm" mat-icon-button (click)="contatUs()">
					<span class="top-menu">Contact Us</span>
				</button>
			</div>
		</div>
		<div fxFlex=50% fxFlex.gt-lg=70% fxLayoutAlign="end center" fxLayout="row">
			<div  f fxLayout="row">
				<div>s</div>
				<div fxLayout="column">
					<span>{{cdgService.loggedInUserDetails.userName}}</span>
					<span>{{cdgService.loggedInUserDetails.company}}</span>

				</div>

			</div>
			<div fxFlex fxLayoutAlign="center end">
			</div>
			<div fxFlex fxLayoutAlign="center end">
			</div>
		</div>

	</mat-toolbar>
</div>
<div *ngIf="isTabletLayout" fxLayout="row">
	<mat-toolbar class="tool-bar-cls p2" fxFlex>

		<div fxLayout="row" fxFlex.lt-sm=70% fxFlex.sm=70% fxFlex.lt-md=85% fxFlex.md=85%>
			<div fxFlex fxLayoutAlign="center end" fxFlex.lt-sm=10% fxFlex.sm=10% fxFlex.lt-md=10% fxFlex.md=10%>
				<button mat-icon-button class="mb10" (click)="sideNavToggle()">
					<mat-icon>{{menuIconName}}</mat-icon>
				</button>

			</div>
			<div fxFlex.lt-sm=90% fxFlex.sm=90% fxFlex.lt-md=90% fxFlex.md=90%>
				<img src="/assets/images/cabcharge_logo.png" alt="">
			</div>
		</div>
		<div fxFlex.lt-sm=30% fxFlex.sm=30% fxFlex.lt-md=15% fxFlex.md=15%  fxLayout="row"
			class="top-menu-main-div">
			<div fxFlex fxLayoutAlign="center end" fxLayout="row">
				<div>s</div>
				<div fxLayout="column">
					<span>{{cdgService.loggedInUserDetails.userName}}</span>
					<span>{{cdgService.loggedInUserDetails.company}}</span>

				</div>

			</div>
			<div fxFlex fxLayoutAlign="center end">
			</div>
			<div fxFlex fxLayoutAlign="center end">
				<button mat-raised-button (click)="onLoggedout()">logout</button>
			</div>
		</div>
	</mat-toolbar>

</div> -->

<div fxFlex fxLayout="row" (window:resize)="onResize($event)">
	<mat-toolbar class="tool-bar-cls p2">
		<div fxFlex fxLayout="row">
			<div *ngIf="showCloseIcon" fxLayoutAlign="center center">
				<button mat-icon-button class="mb10" (click)="sideNavToggle($event)">
					<mat-icon>{{cdgService.menuIconName}}</mat-icon>
				</button>
			</div>
			<div fxLayoutAlign="center center">
				<img src="/assets/images/Cabcharge_white.png" width="84px" alt="">
			</div>

		</div>
		<div fxFlex fxLayout="row" fxLayoutAlign="end center">
			<!-- <div fxLayoutAlign="center center" fxHide.gt-md fxHide.md><button mat-icon-button><mat-icon>info</mat-icon></button></div> -->
			<div fxLayoutAlign="center center" fxLayout="row" fxLayoutGap="15px" fxHide.lt-md
				*ngIf="this.landingScreen">
				<!-- *ngIf="this.landingScreen" -->
				<!-- <div class="user"></div> -->
				<div fxLayout="column">
					<div fxFlex="100%"
						[ngClass]="{'pr42': isShowCustNo && isShowInfo, 'userName': isShowCustNo || !isShowCustNo}"
						fxLayoutAlign="end center">Welcome,</div>
					<div fxLayout="row" [ngClass]=" isShowCustNo ? 'userNames': 'userName' " style="height: 30px;"
						fxLayoutAlign="end center">{{this.userName}}<button *ngIf="isShowCustNo && isShowInfo"
							mat-icon-button class="tool" matTooltip="{{getMoreInfo()}}"><mat-icon
								class="info-icon">info</mat-icon></button></div>
					<div *ngIf="isShowCustNo" class="company pr42" fxLayoutAlign="end center">
						{{this.landingScreen.customer_number}}</div>
					<div class="company custValue pt5" fxLayoutAlign="end center"
						*ngIf="this.loggedInRole === 'ROLE_MASTERUSER'">
						{{custValueChosen}}
					</div>
				</div>
			</div>
			<div fxLayoutAlign="center center" fxLayout="row" fxLayoutGap="15px" fxHide.lt-md
				*ngIf="!this.landingScreen">
				<div fxLayout="column" fxLayoutGap="15px">
					<div class="userName " fxLayoutAlign="end center">Welcome,</div>
					<div class="userName" fxLayoutGap="10px" *ngIf="!this.landingScreen">{{this.userName}}</div>
					<!-- <div *ngIf="this.loggedInRole === 'ROLE_MASTERUSER'">
						{{custValueChosen}}
					</div> -->
				</div>
			</div>
			<div fxLayoutAlign="center center" fxLayout="row" fxLayoutGap="15px" fxHide.gt-md fxHide.md
				*ngIf="!this.landingScreen">
				<!-- <div class="user"></div> -->
				<div fxLayout="column" *ngIf="isShowInfo">
					<div class="userName" fxLayoutGap="10px"><button mat-icon-button
							matTooltip="{{getMoreInfo()}}"><mat-icon
								class="info-icon-mobile cursor-pointer">info</mat-icon></button></div>
					<!-- <div class="company" fxLayoutAlign="end center">{{this.landingScreen.account_no}}</div> -->
				</div>
			</div>
			<hr class="dividerd">
			<!-- Dropdown for Master role for Customer No. Selection -->
			<div *ngIf="this.loggedInRole === 'ROLE_MASTERUSER'" matTooltip="Please Select Customer Account">
				<mat-select class="navTop" name="custNo" formControlName="custNo"
					(selectionChange)="getSelectedCustNo($event)" panelClass="tipPanel">
					<span style="font-size: 12px;"> &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;Please Select Customer
						Account</span>
					<mat-option class="toolTipdd" *ngFor="let item of custNo;"
						[value]="item.view">{{item.viewValue}}</mat-option>
				</mat-select>
			</div>

			<!-- for goToSettings -->


			<hr class="dividerd" *ngIf="this.loggedInRole === 'ROLE_MASTERUSER' ">
			<!-- <div fxLayoutAlign="center center"><button mat-icon-button matTooltip="Notifications" (click)="goToNotifications()"><mat-icon matBadge="{{notifications}}" matBadgeColor="warn">notifications</mat-icon></button></div>
			<hr class="dividerd" fxHide.lt-md>
			<div fxLayoutAlign="center center" fxHide.lt-md><button mat-icon-button matTooltip="Settings" (click)="goToSettings()"><mat-icon>settings</mat-icon></button></div>
			<hr class="dividerd"> -->
			<div fxLayoutAlign="center center"><button mat-icon-button matTooltip="Log Out"
					(click)="onLoggedout()"><mat-icon>logout</mat-icon></button></div>
		</div>

	</mat-toolbar>
</div>