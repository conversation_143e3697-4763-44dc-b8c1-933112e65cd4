@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';

:host{
  --mat-option-label-text-size:12px;
}
.tool-bar-cls{
    width: 100%;
    display: flex;
    padding-top:42px !important;
    padding-bottom: 42px !important;
    
}
.top-menu-main-div{
    display: flex;
}
.top-menu{
    font-family: Roboto;
    font-style: $cdgsz-font-weight-normal;
    font-weight: $cdgsz-font-weight-normal;
    font-size: $cdgsz-font-size-lg;
    line-height: 23px;
    text-align: center;
}
.mat-icon{
    color: $cdgc-font-accent !important;
}
.mat-toolbar {
    background-color:$cdgc-bg-prime !important;
    color: $cdgc-font-accent !important;
}
.dividerd{
    border: 1px solid #f2f2f2;
    height: 27px;
    margin: 0 9px 0 9px !important;
  }
  .userName{
    font-style: $cdgsz-font-weight-normal;
    font-weight: $cdgsz-font-weight-bold;
    line-height: 0px;
    font-size: 14px;  
  }
  .userNames{
    font-style: $cdgsz-font-weight-normal;
    font-weight: $cdgsz-font-weight-bold;
    // line-height: 0px;
    font-size: 14px;  
  }
  .company{
    font-style: $cdgsz-font-weight-normal;
    font-weight: $cdgsz-font-weight-intermediate;
    line-height: 0px;
    font-size: 12px; 
  }
  .custValue{
    font-weight: $cdgsz-font-weight-bold;
  
  }
  .user{
      display: block;
      width: 42px;
      height: 42px;
      border-radius: 50%;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: cover;
      background-image: url('/assets/images/Cabcharge_white.png');
  }
//   .test{
//       white-space: pre-line !important;
//   }
  ::ng-deep .mat-mdc-tooltip{
    white-space: pre-line !important;
}
.info-icon{
    font-size: 20px !important;
    margin-bottom: 20px;
    margin-top: 0px !important; //change by navani info icon 
}
.info-icon-mobile{
  font-size: 20px !important;
}
.pr42{
  padding-right: 42px !important;
}
// .tool.mat-mdc-icon-button {
//   // width: 5px !important;
//   // top:6px !important;
//   // background-color: red;
//   // height: 1px !important;
// }
::ng-deep .mb10.mat-mdc-icon-button{
  margin-top: 10px   !important;
}

.inp{
  //margin: 4px !important;
  width: 20px !important;
  font-size: 12px !important;
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/

:host ::ng-deep .mat-mdc-select-arrow {
  color: white !important; 
  margin-right: 5px !important;
  height:1px !important;
  width: 1px !important;
  
}
/* Change_by_navani (mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
::ng-deep .mat-select__selected-text{
  display: none !important;
}

::ng-deep .tipPanel.mat-mdc-select-panel {
  width: 250px !important;
  height: auto !important;
  outline: 0;
  margin: 0;
  padding: 8px 0;
  list-style-type: none;
  // right: 5px !important;
  min-width: calc(100% + 120px);
  position: absolute !important;
  right: 0;
}

 .mat-mdc-option  {
  font-size: 12px !important;
  font-weight: 400 !important;
  color: #333 !important;
}

:host ::ng-deep .mat-mdc-form-field .mdc-line-ripple { //changed by navani, for month dropdown bottom line
  display: none !important
}
// for hiding dropdown value
:host ::ng-deep .navTop .mat-mdc-select-value{
  display: none !important;
}