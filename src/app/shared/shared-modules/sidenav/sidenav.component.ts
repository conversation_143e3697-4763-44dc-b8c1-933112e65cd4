import { Component, HostListener, OnInit, ViewChild, ElementRef, InputDecorator } from '@angular/core';
import { MatSidenav } from '@angular/material/sidenav';
import { RoleConstants, UrlConstants } from 'src/assets/url-constants/url-constants';
import { LocalStorageService } from '../../services/local-storage.service';
import { CdgSharedService } from './../../services/cdg-shared.service'

@Component({
    selector: 'app-sidenav',
    templateUrl: './sidenav.component.html',
    styleUrls: ['./sidenav.component.scss']
})
export class SidenavComponent implements OnInit {
    // mainList: any[] = [];

    routerList: any[] = [];
    public corporateUser: boolean = false;
    public supplementaryUser: boolean = false;
    public personaluser: boolean = false;
    loggedInRole: any;
    public innerWidth: any;
    constructor(public cdgService: CdgSharedService, public localStorage: LocalStorageService) { }
    @HostListener("click")
    clicked() {
        this.cdgService.wasInside = true;
    }
    @HostListener("document:click")
    clickOut() {
        this.innerWidth = window.innerWidth;
        this.cdgService.isUserIdle = true;
        if (!this.cdgService.wasInside) {
            if (this.innerWidth <= 990) {
                this.cdgService.showMenu = false;
                this.cdgService.menuIconName = 'view_headline';
            }
            // else{
            //     this.cdgService.expSidebar=false;
            // }
        }
        this.cdgService.wasInside = false;
    }
    user: any;
    showSubmenu: boolean = false;
    expanIconName: string = "expand_more";
    mainMenuList: any[] = [];
    userIdleInterval: any;
    ngOnInit() {
        this.routerList = this.localStorage.localStorageGet("routerList");
        this.loggedInRole = this.localStorage.localStorageGet("loggedInRole")
        this.innerWidth = window.innerWidth;
        this.startUserIdleTimer();
    }
    handleClick(item: any, index: number) {
        if (item.linkTitle !== 'Booking Services') {
            item.isSubActive = true;
            if (item.linkTitle === 'Manage Corporate Policy') {
                this.cdgService.isCorpUpdate = false;
            }
            else if (item.linkTitle === 'Online Application') {
                this.cdgService.cardTypeSelected.isComingFromPreview = false;
            }
            else if (item.linkTitle === 'Application Form') {
                this.cdgService.supplementaryPreview.supplementarypreview = false;
            }
        }
        else {
            window.location.href = 'https://webbooking.cdgtaxi.com.sg/home.mvn'
        }
    }
    expandSidebar(index: number, item: any) {
        if (item.linkTitle !== 'Booking Services') {
            this.cdgService.expSidebar = true;
            this.routerList.forEach((element, i) => {
                if (i === index) {
                    element.isActive = true;
                    element.isOpen = 'expand_less';
                }
            });
        }
        else {
            window.location.href = 'https://webbooking.cdgtaxi.com.sg/home.mvn'
        }
    }
    toggleSubMenu(index: any) {
        this.routerList.forEach((element, j) => {
            if (j === index) {
                element.isActive = !element.isActive;
                if (element.isActive) {
                    element.isOpen = 'expand_less';
                }
                else {
                    element.isOpen = 'expand_more';
                }
            }

        });
    }

    startUserIdleTimer() {
        this.userIdleInterval = setInterval((o: any) => {
            this.cdgService.isUserIdle = false;
        }, 20000);
    }
}
