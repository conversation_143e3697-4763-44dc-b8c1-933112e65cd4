@import "@angular/material/theming";
@import "./../../../../styles/colors.scss";
@import "./../../../../styles/sizing.scss";

.iconSidebar {
  width: 71px;
  position: fixed;
  top: 84px;
  bottom: 0;
  background: #006BA8 !important;
  color: white !important;
  // box-shadow: 3px 0 6px rgba(0, 0, 0, 0.24);
  z-index: 999;
  -ms-overflow-y: auto;
  overflow-y: auto;

  .mat-mdc-nav-list {
    width: 100%;

    a {
      .mat-icon {
        margin-right: 0px !important;
        color: white !important;
      }

      display: block;
    }
  }
}



@media screen and (min-width: 300px) {

  .sidebar {
    width: 300px;
    position: fixed;
    top: 84px;
    bottom: 0;
    background: #006BA8 !important;
    color: white !important;
    // box-shadow: 3px 0 6px rgba(0, 0, 0, 0.24);
    z-index: 999;
    -ms-overflow-y: auto;
    overflow-y: auto;

    .mat-mdc-nav-list {
      width: 100% !important;

      a {
        .mat-icon {
          margin-right: 0px !important;
          color: white !important;
        }

        display: flex !important;
      }
    }
  }
}

.nested-menu {
  .nested {
    list-style-type: none;
  }

  .submenu {
    display: none;
    height: 0;
  }

  & .expand {
    &.submenu {
      display: block;
      list-style-type: none;
      height: auto;
      margin: 0;

      li {
        a {
          padding: 10px;
          display: block;
        }
      }
    }
  }
}

// @media screen and (max-width: 600px) {
//     .iconSidebar {
//         left: -71px;
//     }
// }
.mat-mdc-list-base .mdc-list-item__content {
  color: white;
}

/*--*/
.example-container {
  height: 500px;
  border: 1px solid rgba(0, 0, 0, 0.5);
}

.example-sidenav-content {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.example-sidenav {
  user-select: none;
}

.full-width {
  width: 100%;
}

.menu-button {
  transition: 300ms ease-in-out;
  transform: rotate(0deg);
}

.menu-button.rotated {
  transform: rotate(180deg);
}

.submenu {
  overflow-y: hidden;
  transition: transform 300ms ease;
  transform: scaleY(0);
  transform-origin: top;
  padding-left: 30px;
  display: none;
}

.submenu.expanded {
  transform: scaleY(1);
  display: block;
}

.active-list-item {
  background-color: #205674 !important;
}

.nav-side {
  background-color: $hue !important;
  height: 48px !important;
}

.nav {
  background-color: transparent !important;
  height: 48px !important;
}

.mat-icon {
  font-size: 30px !important;
  color: white !important;
  height: 30px !important;
  width: 30px !important;
  margin-top: 5px !important;
}

.main-link {
  font-size: 14px !important;
  color: white !important;
}

// ::ng-deep .sub-link{
//   font-size: 12px !important;
//   color: white !important;

// }
:host ::ng-deep .mdc-list-item__primary-text {
  // font-family: 'SFNS Display Regular';
  // line-height: var(--mdc-list-list-item-label-text-line-height);
  font-size: 12px;
  font-weight: 400;
  letter-spacing: normal;
  color: white !important;
}

//  ::ng-deep .parent.mat-mdc-list-base .mdc-list-item__content{
//   margin-right: 0px !important;
// }

// .mat-mdc-list-base .mdc-list-item__start, .mat-mdc-list-base .mdc-list-item__end, .mat-mdc-list-base .mdc-list-item__content{
//   margin-right: 0px !important;
// }
// .mdc-list-item.mdc-list-item--with-one-line{

// }