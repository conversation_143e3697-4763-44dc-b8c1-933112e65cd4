<ng-container *ngIf="cdgService.showMenu">
     <div [ngClass]="cdgService.expSidebar ? 'sidebar' : 'iconSidebar'">
          <mat-nav-list>
               <ng-container *ngIf="!cdgService.expSidebar">
                    <mat-list-item *ngFor="let item of this.routerList;let i= index" class="parent"
                         routerLinkActive="active-list-item">
                         <ng-container *ngIf="item.link !== 'dummy'">
                              <div (click)="expandSidebar(i,item)" [routerLink]="item.link">
                                   <div *ngIf="this.loggedInRole == 'ROLE_SUPERADMINUSER'">
                                        <mat-icon class="material-icons-outlined mat-icon">{{item.icon}}</mat-icon>
                                   </div>
                                   <div *ngIf="this.loggedInRole != 'ROLE_SUPERADMINUSER'">
                                        <img src={{item.icon}} alt="">
                                   </div>
                              </div>
                         </ng-container>
                         <ng-container *ngIf="item.link == 'dummy'">
                              <div (click)="expandSidebar(i,item)" >
                                   <div *ngIf="this.loggedInRole == 'ROLE_SUPERADMINUSER'">
                                        <mat-icon class="material-icons-outlined mat-icon">{{item.icon}}</mat-icon>
                                   </div>
                                   <div *ngIf="this.loggedInRole != 'ROLE_SUPERADMINUSER'">
                                        <img src={{item.icon}} alt="">
                                   </div>
                              </div>
                         </ng-container>
                    </mat-list-item>
               </ng-container>
               <ng-container *ngIf="cdgService.expSidebar">
                    <ng-container *ngFor="let item of this.routerList;let i= index">
                         <mat-list-item (click)="handleClick(item,i)" class="parent" fxLayoutGap="15px">
                              <div fxFlex fxLayout="row" fxLayoutGap="15px">
                                   <div fxFlex="10%" fxLayoutAlign="center center">
                                        <!-- <mat-icon class="material-icons-outlined">{{item.icon}}</mat-icon> -->
                                        <div *ngIf="this.loggedInRole == 'ROLE_SUPERADMINUSER'">
                                             <mat-icon class="material-icons-outlined mat-icon">{{item.icon}}</mat-icon>
                                        </div>
                                        <div *ngIf="this.loggedInRole != 'ROLE_SUPERADMINUSER'">
                                             <img src={{item.icon}} alt="">
                                        </div>
                                   </div>
                                   <div fxFlex="80%" fxLayoutAlign="center center">
                                        <ng-container *ngIf="item.linkTitle !== 'Booking Services'">
                                             <a class="full-width main-link"
                                                  [routerLink]="item.link">{{item.linkTitle}}</a>
                                        </ng-container>
                                        <ng-container *ngIf="item.linkTitle == 'Booking Services'">
                                             <a class="full-width main-link">{{item.linkTitle}}</a>
                                        </ng-container>
                                   </div>
                                   <div fxFlex="10%" fxLayoutAlign="center center" *ngIf="item.subMenu.length >0">
                                        <mat-icon class="menu-button" (click)="toggleSubMenu(i)">
                                             {{item.isOpen}}</mat-icon>
                                   </div>
                              </div>
                         </mat-list-item>
                         <ng-container *ngIf="item.isActive">
                              <ng-container *ngFor="let val of item.subMenu;let j= index">
                                   <div (click)="handleClick(val,j)">
                                        <a mat-list-item class="sub-link" [routerLink]="val.subLink"
                                             routerLinkActive="active-list-item">{{val.subLinkTitle}}</a>
                                   </div>
                              </ng-container>
                         </ng-container>
                    </ng-container>

               </ng-container>
          </mat-nav-list>
     </div>
</ng-container>