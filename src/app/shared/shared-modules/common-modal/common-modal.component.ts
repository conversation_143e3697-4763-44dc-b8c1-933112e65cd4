import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
export interface DialogData {
  title: "";
  msg: "",
  btnClose: "",
  btnConfirm: "",
  btnerror:"",
  func:"delete"
}
@Component({
  selector: 'app-common-modal',
  templateUrl: './common-modal.component.html',
  styleUrls: ['./common-modal.component.scss']
})
export class CommonModalComponent implements OnInit {

  constructor(public matDialogRef:MatDialogRef<CommonModalComponent>,@Inject(MAT_DIALOG_DATA) public data: DialogData) { }

  ngOnInit(): void {
    // console.log(this.data)
  }

}
