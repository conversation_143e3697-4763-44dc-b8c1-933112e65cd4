<div fxLayout="column">
    <div class="title" [ngStyle]="{'background-color': data.func == 'delete' ? ' #ff0000' :  '#006BA8'}">
        <h2 mat-dialog-title>{{data.title}}</h2>
    </div>
    <mat-dialog-content>
        <div class="padding">
            {{data.msg}}
        </div>
    </mat-dialog-content>
    <mat-dialog-actions fxLayoutAlign="center end" class="pr10">
        <ng-container *ngIf="data.btnClose">
            <button mat-raised-button mat-dialog-close class="closeBtn">{{data.btnClose}}</button>
        </ng-container>
        <ng-container *ngIf="data.btnConfirm">
            <button mat-raised-button [mat-dialog-close]="true" cdkFocusInitial [ngStyle]="{'background-color': data.func == 'delete' ? ' #ff0000' :  '#006BA8'}" class="okBtn">{{data.btnConfirm}}</button>
        </ng-container>
        <ng-container *ngIf="data.btnerror">
            <button mat-raised-button [mat-dialog-close]="false" cdkFocusInitial [ngStyle]="{'background-color': data.func == 'delete' ? ' #ff0000' :  '#006BA8'}" class="okkBtn">{{data.btnerror}}</button>
        </ng-container>
    </mat-dialog-actions>
</div>