
@import '@angular/material/theming';
@import './../../../../styles/colors.scss';
@import './../../../../styles/sizing.scss';
@import './../../../../styles/main.scss';
::ng-deep .mat-mdc-dialog-container {
    display: block;
    padding: 0px !important;
    border-radius: 10px;
    box-sizing: border-box;
    overflow: unset !important; 
    outline: 0;
    width: 365px;
    // height: 150px;
    height: 180px;
    min-height: inherit;
    max-height: inherit;
}
::ng-deep .mat-typography h2 {
    margin: 0 0 0px !important;
}
.title{
    // padding:10px 15px 10px 15px !important;
    // border-top-left-radius: 10px;
    // border-top-right-radius: 10px;
    color: $cdgc-font-accent !important;
    text-align: center !important;    
}
.padding{
    padding:10px 15px 10px 15px !important;
}
.closeBtn{
    width: 105px !important;
    border-radius: 10px !important;
    border:1px solid $cdgc-prime !important;
    background-color:$cdgc-accent !important;
    color: $cdgc-prime !important;
}
.okBtn{
    width: 105px !important;
    border-radius: 10px !important;
    border:1px solid $cdgc-accent !important;
    // background-color:$cdgc-prime !important;
    color: $cdgc-accent !important;
}

