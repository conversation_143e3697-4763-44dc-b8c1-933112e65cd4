import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TopnavComponent } from './topnav/topnav.component';
import { SidenavComponent } from './sidenav/sidenav.component';
import {MatDialogModule} from '@angular/material/dialog';

import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import {MatBadgeModule} from '@angular/material/badge';
import { RouterModule } from '@angular/router';
import { FlexLayoutModule } from '@angular/flex-layout';
import {MatTooltipModule} from '@angular/material/tooltip';
import { PromptComponent } from './prompt/prompt.component';
import { CommonModalComponent } from './common-modal/common-modal.component';
import {MatSelectModule} from '@angular/material/select';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
  declarations: [TopnavComponent,SidenavComponent, CommonModalComponent],
   exports: [TopnavComponent,SidenavComponent],
  imports: [
     RouterModule,
    CommonModule,
    MatButtonModule,
        MatSidenavModule,
        MatIconModule,
        MatInputModule,
        MatSelectModule,
        MatMenuModule,
        MatListModule,
        MatBadgeModule,
        MatToolbarModule,
        MatTooltipModule,
        MatDialogModule,
        ReactiveFormsModule,
        FlexLayoutModule.withConfig({addFlexToParent: false})
  ],
  // entryComponents:[CommonModalComponent]
})
export class SharedModulesModule { }
