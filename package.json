{"name": "cdg-cabcharge", "version": "2.1.4", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "prod": "ng build --configuration production", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "clean": "<PERSON><PERSON><PERSON> dist"}, "private": true, "dependencies": {"@angular-material-components/datetime-picker": "^16.0.1", "@angular/animations": "^18.2.13", "@angular/cdk": "^16.2.13", "@angular/common": "^18.2.13", "@angular/compiler": "^18.2.13", "@angular/core": "^18.2.13", "@angular/flex-layout": "^15.0.0-beta.42", "@angular/forms": "~18.2.13", "@angular/material": "^16.2.13", "@angular/material-moment-adapter": "^16.2.13", "@angular/platform-browser": "~18.2.13", "@angular/platform-browser-dynamic": "~18.2.13", "@angular/router": "~18.2.13", "@angular/service-worker": "~18.2.13", "@magloft/material-carousel": "^16.2.0", "@ng-matero/extensions": "^16.2.0", "angular-ismobile": "^1.0.0", "chart.js": "^2.9.4", "crypto-js": "^4.2.0", "file-saver": "^2.0.5", "moment": "^2.30.1", "ngx-cookie-service": "^17.1.0", "ngx-pagination": "^6.0.3", "ngx-toastr": "^15.2.0", "rxjs": "~6.6.0", "tslib": "^2.3.0", "xlsx": "^0.16.9", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "~18.2.12", "@angular/cli": "^18.2.12", "@angular/compiler-cli": "~18.2.13", "@types/chart.js": "^2.9.31", "@types/core-js": "^2.5.4", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.1", "@types/jasmine": "~3.6.0", "@types/node": "^12.11.1", "@types/webpack-env": "^1.16.0", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.2", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-htmlfile-reporter": "~0.3.8", "karma-jasmine-html-reporter": "~2.1.0", "protractor": "~7.0.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "^5.5.4", "rimraf": "^3.0.2"}}