{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": "1ce08eba-a5d4-40cc-b85d-2a4c23f50fff"}, "version": 1, "newProjectRoot": "projects", "projects": {"cdg-cabcharge": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/cdg-cabcharge", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/ngx-toastr/toastr.css", "src/styles/styles.scss"], "scripts": [], "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": {"scripts": true, "styles": true, "fonts": false}, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "5.5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "15kb"}], "serviceWorker": true, "ngswConfigPath": "ngsw-config.json"}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"buildTarget": "cdg-cabcharge:build"}, "configurations": {"production": {"buildTarget": "cdg-cabcharge:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "cdg-cabcharge:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/ngx-toastr/toastr.css", "src/styles/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "cdg-cabcharge:serve"}, "configurations": {"production": {"devServerTarget": "cdg-cabcharge:serve:production"}}}}}}}