# Stage 1: Build frontend
FROM node:18.19-alpine AS build
 
WORKDIR /usr/src/app

COPY ./ .
RUN npm install -g @angular/cli

# RUN npm install --legacy-peer-deps
RUN npm install --force


RUN npm run prod 
 
  

# Stage 2: Serve it using Ngnix
FROM nginx:stable-alpine
RUN rm -rf /usr/share/nginx/html/index.html
COPY --from=build /usr/src/app/dist/cdg-cabcharge/ /usr/share/nginx/html 
COPY ../deployment/nginx.conf /etc/nginx/nginx.conf 
COPY ../deployment/cdg* /etc/nginx/

EXPOSE 443
EXPOSE 80

ENTRYPOINT ["nginx","-g","daemon off;"] 
 
  