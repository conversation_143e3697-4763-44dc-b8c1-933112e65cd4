# Stage 1: Build frontend
FROM node:18.19-alpine AS build
 
WORKDIR /usr/src/app

COPY ./ .
RUN npm install -g @angular/cli

RUN npm install --force

RUN npm run prod
 
  

# Stage 2: Serve it using Ngnix
# FROM nginx:stable-alpine
# COPY --from=build /usr/src/app/dist/cdg-cabcharge/ /usr/share/nginx/html 
FROM nginx:stable-alpine
RUN rm -rf /usr/share/nginx/html/index.html
COPY --from=build /usr/src/app/dist/cdg-cabcharge/ /usr/share/nginx/html 
COPY ../deployment/nginx.conf /etc/nginx/nginx.conf 
COPY ../deployment/*.pem /etc/nginx/

EXPOSE 443
EXPOSE 80

# EXPOSE 80
# ENTRYPOINT ["nginx","-g","daemon off;"] 
ENTRYPOINT ["nginx","-g","daemon off;"] 
 

#huawei cloud
FROM swr.ap-southeast-3.myhuaweicloud.com/cdg-public/nginx:1.21-alpine
RUN rm -rf /usr/share/nginx/html/index.html
COPY --from=build /usr/src/app/dist/cdg-cabcharge/ /usr/share/nginx/html 
#COPY ./nginx.conf /etc/nginx/conf.d/default.conf  
COPY  ~/deployment/nginx.conf /etc/nginx/nginx.conf 


